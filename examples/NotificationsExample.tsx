// examples/NotificationsExample.tsx - Example usage of the notifications store with Socket.IO

'use client'

import React, { useEffect, useState } from 'react'
import { useNotificationsStore } from '@/stores/notificationsStore'
import type { Notification, NotificationType, NotificationPriority } from '@/types/frontend'

// Connection Status Component
function ConnectionStatus() {
  const {
    isConnected,
    connectionStatus,
    lastHeartbeat,
    connectSocket,
    disconnectSocket,
    reconnectSocket
  } = useNotificationsStore()

  const handleConnect = async () => {
    await connectSocket('user-123', 'auth-token-example')
  }

  const handleDisconnect = () => {
    disconnectSocket()
  }

  const handleReconnect = async () => {
    await reconnectSocket()
  }

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-600 bg-green-100'
      case 'connecting': return 'text-yellow-600 bg-yellow-100'
      case 'disconnected': return 'text-gray-600 bg-gray-100'
      case 'error': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="bg-white rounded-lg p-4 mb-6 border">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">Socket.IO Connection</h2>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor()}`}>
          {connectionStatus.charAt(0).toUpperCase() + connectionStatus.slice(1)}
        </div>
      </div>

      <div className="flex items-center space-x-4 mb-4">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className="text-sm">{isConnected ? 'Connected' : 'Disconnected'}</span>
        </div>
        
        {lastHeartbeat && (
          <div className="text-sm text-gray-500">
            Last heartbeat: {new Date(lastHeartbeat).toLocaleTimeString()}
          </div>
        )}
      </div>

      <div className="flex space-x-2">
        <button
          onClick={handleConnect}
          disabled={isConnected}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          Connect
        </button>
        <button
          onClick={handleDisconnect}
          disabled={!isConnected}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
        >
          Disconnect
        </button>
        <button
          onClick={handleReconnect}
          className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
        >
          Reconnect
        </button>
      </div>
    </div>
  )
}

// Notification Stats Component
function NotificationStats() {
  const {
    unreadCount,
    totalCount,
    priorityCount,
    systemAlerts,
    userMessages
  } = useNotificationsStore()

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-blue-600">Total</h3>
        <p className="text-2xl font-bold text-blue-900">{totalCount}</p>
      </div>

      <div className="bg-orange-50 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-orange-600">Unread</h3>
        <p className="text-2xl font-bold text-orange-900">{unreadCount}</p>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-gray-600">Low Priority</h3>
        <p className="text-2xl font-bold text-gray-900">{priorityCount.low}</p>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-yellow-600">Medium Priority</h3>
        <p className="text-2xl font-bold text-yellow-900">{priorityCount.medium}</p>
      </div>

      <div className="bg-orange-50 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-orange-600">High Priority</h3>
        <p className="text-2xl font-bold text-orange-900">{priorityCount.high}</p>
      </div>

      <div className="bg-red-50 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-red-600">Urgent</h3>
        <p className="text-2xl font-bold text-red-900">{priorityCount.urgent}</p>
      </div>
    </div>
  )
}

// Send Notification Component
function SendNotification() {
  const {
    sendNotification,
    broadcastNotification,
    sendSystemAlert,
    sendUserMessage,
    isConnected
  } = useNotificationsStore()

  const [notificationData, setNotificationData] = useState({
    title: '',
    message: '',
    type: 'info' as NotificationType,
    priority: 'medium' as NotificationPriority,
    recipientId: ''
  })

  const handleSendNotification = async () => {
    if (!notificationData.title || !notificationData.message) {
      alert('Please fill in title and message')
      return
    }

    const result = await sendNotification(notificationData.recipientId || 'user-456', {
      title: notificationData.title,
      message: notificationData.message,
      type: notificationData.type,
      priority: notificationData.priority
    })

    if (result.success) {
      alert('Notification sent successfully!')
      setNotificationData({
        title: '',
        message: '',
        type: 'info',
        priority: 'medium',
        recipientId: ''
      })
    } else {
      alert(`Failed to send notification: ${result.error}`)
    }
  }

  const handleBroadcast = async () => {
    if (!notificationData.title || !notificationData.message) {
      alert('Please fill in title and message')
      return
    }

    const result = await broadcastNotification({
      title: notificationData.title,
      message: notificationData.message,
      type: notificationData.type,
      priority: notificationData.priority
    })

    if (result.success) {
      alert('Broadcast sent successfully!')
    } else {
      alert(`Failed to broadcast: ${result.error}`)
    }
  }

  const handleSystemAlert = async () => {
    const result = await sendSystemAlert({
      title: 'System Maintenance Alert',
      message: 'Scheduled maintenance will begin in 30 minutes',
      alertType: 'maintenance',
      severity: 'medium',
      affectedSystems: ['database', 'api'],
      estimatedDuration: '2 hours'
    })

    if (result.success) {
      alert('System alert sent!')
    } else {
      alert(`Failed to send system alert: ${result.error}`)
    }
  }

  const handleUserMessage = async () => {
    const result = await sendUserMessage({
      title: 'Welcome Message',
      message: 'Welcome to FathahiTech! We\'re glad to have you.',
      messageType: 'welcome',
      recipientId: 'user-456'
    })

    if (result.success) {
      alert('User message sent!')
    } else {
      alert(`Failed to send user message: ${result.error}`)
    }
  }

  return (
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-2xl font-bold mb-6">Send Notifications</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium mb-2">Title</label>
          <input
            type="text"
            value={notificationData.title}
            onChange={(e) => setNotificationData(prev => ({ ...prev, title: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Notification title"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Recipient ID (Optional)</label>
          <input
            type="text"
            value={notificationData.recipientId}
            onChange={(e) => setNotificationData(prev => ({ ...prev, recipientId: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Leave empty for broadcast"
          />
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Message</label>
        <textarea
          value={notificationData.message}
          onChange={(e) => setNotificationData(prev => ({ ...prev, message: e.target.value }))}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Notification message"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium mb-2">Type</label>
          <select
            value={notificationData.type}
            onChange={(e) => setNotificationData(prev => ({ ...prev, type: e.target.value as NotificationType }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="info">Info</option>
            <option value="success">Success</option>
            <option value="warning">Warning</option>
            <option value="error">Error</option>
            <option value="system">System</option>
            <option value="message">Message</option>
            <option value="reminder">Reminder</option>
            <option value="update">Update</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Priority</label>
          <select
            value={notificationData.priority}
            onChange={(e) => setNotificationData(prev => ({ ...prev, priority: e.target.value as NotificationPriority }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>
      </div>

      <div className="flex flex-wrap gap-2">
        <button
          onClick={handleSendNotification}
          disabled={!isConnected}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
        >
          Send to User
        </button>
        <button
          onClick={handleBroadcast}
          disabled={!isConnected}
          className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
        >
          Broadcast to All
        </button>
        <button
          onClick={handleSystemAlert}
          disabled={!isConnected}
          className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50"
        >
          Send System Alert
        </button>
        <button
          onClick={handleUserMessage}
          disabled={!isConnected}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50"
        >
          Send User Message
        </button>
      </div>

      {!isConnected && (
        <p className="text-red-600 text-sm mt-2">
          Connect to Socket.IO to send real-time notifications
        </p>
      )}
    </div>
  )
}

// Notifications List Component
function NotificationsList() {
  const {
    notifications,
    unreadNotifications,
    isLoading,
    error,
    currentPage,
    totalPages,
    fetchNotifications,
    markAsRead,
    markAsUnread,
    markAllAsRead,
    archiveNotification,
    deleteNotification,
    clearError,
    setCurrentPage,
    getNotificationIcon,
    getNotificationColor,
    getPriorityLabel,
    formatNotificationTime,
    isNotificationExpired
  } = useNotificationsStore()

  useEffect(() => {
    fetchNotifications()
  }, [fetchNotifications])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchNotifications({ page, limit: 20 })
  }

  const handleMarkAsRead = async (notificationId: string) => {
    const result = await markAsRead(notificationId)
    if (!result.success) {
      alert(`Failed to mark as read: ${result.error}`)
    }
  }

  const handleMarkAsUnread = async (notificationId: string) => {
    const result = await markAsUnread(notificationId)
    if (!result.success) {
      alert(`Failed to mark as unread: ${result.error}`)
    }
  }

  const handleMarkAllAsRead = async () => {
    const result = await markAllAsRead()
    if (result.success) {
      alert('All notifications marked as read!')
    } else {
      alert(`Failed to mark all as read: ${result.error}`)
    }
  }

  const handleArchive = async (notificationId: string) => {
    const result = await archiveNotification(notificationId)
    if (!result.success) {
      alert(`Failed to archive: ${result.error}`)
    }
  }

  const handleDelete = async (notificationId: string) => {
    if (confirm('Are you sure you want to delete this notification?')) {
      const result = await deleteNotification(notificationId)
      if (!result.success) {
        alert(`Failed to delete: ${result.error}`)
      }
    }
  }

  if (isLoading) {
    return <div className="text-center py-8">Loading notifications...</div>
  }

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Notifications</h2>
        <div className="flex items-center space-x-4">
          {error && (
            <div className="text-red-600 bg-red-50 px-4 py-2 rounded flex items-center">
              {error}
              <button onClick={clearError} className="ml-2 text-red-800 hover:text-red-900">
                ×
              </button>
            </div>
          )}
          {unreadNotifications.length > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Mark All Read ({unreadNotifications.length})
            </button>
          )}
        </div>
      </div>

      <div className="space-y-4">
        {notifications.map((notification) => {
          const icon = getNotificationIcon(notification.type)
          const color = getNotificationColor(notification.priority)
          const priorityLabel = getPriorityLabel(notification.priority)
          const timeAgo = formatNotificationTime(notification.createdAt)
          const expired = isNotificationExpired(notification)

          return (
            <div
              key={notification._id}
              className={`border rounded-lg p-4 ${
                notification.status === 'unread' ? 'bg-blue-50 border-blue-200' : 'bg-white'
              } ${expired ? 'opacity-60' : ''}`}
            >
              <div className="flex justify-between items-start mb-3">
                <div className="flex items-start space-x-3 flex-1">
                  <span className="text-2xl">{icon}</span>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="font-semibold text-lg">{notification.title}</h3>
                      {notification.status === 'unread' && (
                        <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                      )}
                      {expired && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          Expired
                        </span>
                      )}
                    </div>
                    
                    <p className="text-gray-700 mb-2">{notification.message}</p>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>From: {notification.createdByName}</span>
                      <span>•</span>
                      <span>{timeAgo}</span>
                      <span>•</span>
                      <span className={`px-2 py-1 rounded text-xs ${
                        color === 'red' ? 'bg-red-100 text-red-800' :
                        color === 'orange' ? 'bg-orange-100 text-orange-800' :
                        color === 'blue' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {priorityLabel}
                      </span>
                    </div>

                    {notification.data && (
                      <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                        <strong>Additional Data:</strong>
                        <pre className="text-xs mt-1 overflow-x-auto">
                          {JSON.stringify(notification.data, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                {notification.status === 'unread' ? (
                  <button
                    onClick={() => handleMarkAsRead(notification._id)}
                    className="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
                  >
                    Mark as Read
                  </button>
                ) : (
                  <button
                    onClick={() => handleMarkAsUnread(notification._id)}
                    className="text-xs bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700"
                  >
                    Mark as Unread
                  </button>
                )}
                
                <button
                  onClick={() => handleArchive(notification._id)}
                  className="text-xs bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700"
                >
                  Archive
                </button>
                
                <button
                  onClick={() => handleDelete(notification._id)}
                  className="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          )
        })}
      </div>

      {notifications.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No notifications found
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-6">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 border rounded ${
                currentPage === page ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'
              }`}
            >
              {page}
            </button>
          ))}
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}

// Push Notifications Component
function PushNotifications() {
  const {
    pushEnabled,
    pushSubscription,
    subscribeToPush,
    unsubscribeFromPush,
    sendPushNotification
  } = useNotificationsStore()

  const handleSubscribe = async () => {
    const result = await subscribeToPush()
    if (result.success) {
      alert('Successfully subscribed to push notifications!')
    } else {
      alert(`Failed to subscribe: ${result.error}`)
    }
  }

  const handleUnsubscribe = async () => {
    const result = await unsubscribeFromPush()
    if (result.success) {
      alert('Successfully unsubscribed from push notifications!')
    } else {
      alert(`Failed to unsubscribe: ${result.error}`)
    }
  }

  const handleSendPush = async () => {
    const result = await sendPushNotification({
      title: 'Test Push Notification',
      message: 'This is a test push notification from FathahiTech',
      type: 'info',
      priority: 'medium'
    })

    if (result.success) {
      alert('Push notification sent!')
    } else {
      alert(`Failed to send push notification: ${result.error}`)
    }
  }

  return (
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-2xl font-bold mb-4">Push Notifications</h2>
      
      <div className="flex items-center space-x-4 mb-4">
        <div className={`w-3 h-3 rounded-full ${pushEnabled ? 'bg-green-500' : 'bg-red-500'}`}></div>
        <span>{pushEnabled ? 'Push notifications enabled' : 'Push notifications disabled'}</span>
      </div>

      {pushSubscription && (
        <div className="mb-4 p-3 bg-gray-50 rounded">
          <p className="text-sm font-medium">Subscription Details:</p>
          <p className="text-xs text-gray-600 break-all">
            Endpoint: {pushSubscription.endpoint}
          </p>
        </div>
      )}

      <div className="flex space-x-2">
        {!pushEnabled ? (
          <button
            onClick={handleSubscribe}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
          >
            Enable Push Notifications
          </button>
        ) : (
          <>
            <button
              onClick={handleUnsubscribe}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              Disable Push Notifications
            </button>
            <button
              onClick={handleSendPush}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Send Test Push
            </button>
          </>
        )}
      </div>
    </div>
  )
}

// Main Notifications Example Component
export default function NotificationsExample() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">
          Notifications Store Example 🔔
        </h1>
        
        <ConnectionStatus />
        <NotificationStats />
        <SendNotification />
        <PushNotifications />
        <NotificationsList />
      </div>
    </div>
  )
}
