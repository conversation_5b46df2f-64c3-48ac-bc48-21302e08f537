// examples/BranchesExample.tsx - Example usage of the branches store

'use client'

import React, { useEffect, useState } from 'react'
import { useBranchesStore } from '@/stores/branchesStore'
import type { BranchStatus, CreateBranchData } from '@/types/frontend'

// Branch List Component
function BranchList() {
  const {
    branches,
    isLoading,
    error,
    currentPage,
    totalPages,
    totalBranches,
    fetchBranches,
    setCurrentPage,
    updateBranchStatus,
    activateBranch,
    deactivateBranch,
    setMaintenanceMode,
    deleteBranch,
    clearError
  } = useBranchesStore()

  useEffect(() => {
    fetchBranches()
  }, [fetchBranches])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchBranches({ page, limit: 20 })
  }

  const handleStatusUpdate = async (branchId: string, status: BranchStatus) => {
    const result = await updateBranchStatus(branchId, status)
    if (result.success) {
      alert('Branch status updated successfully!')
    } else {
      alert(`Failed to update status: ${result.error}`)
    }
  }

  const handleActivate = async (branchId: string) => {
    const result = await activateBranch(branchId)
    if (result.success) {
      alert('Branch activated successfully!')
    } else {
      alert(`Failed to activate branch: ${result.error}`)
    }
  }

  const handleDeactivate = async (branchId: string) => {
    const reason = prompt('Enter deactivation reason:')
    if (reason) {
      const result = await deactivateBranch(branchId, reason)
      if (result.success) {
        alert('Branch deactivated successfully!')
      } else {
        alert(`Failed to deactivate branch: ${result.error}`)
      }
    }
  }

  const handleMaintenance = async (branchId: string) => {
    const reason = prompt('Enter maintenance reason:')
    if (reason) {
      const result = await setMaintenanceMode(branchId, reason)
      if (result.success) {
        alert('Branch set to maintenance mode!')
      } else {
        alert(`Failed to set maintenance mode: ${result.error}`)
      }
    }
  }

  const handleDeleteBranch = async (branchId: string) => {
    if (confirm('Are you sure you want to delete this branch?')) {
      const result = await deleteBranch(branchId)
      if (result.success) {
        alert('Branch deleted successfully!')
      } else {
        alert(`Failed to delete branch: ${result.error}`)
      }
    }
  }

  if (isLoading) {
    return <div className="text-center py-8">Loading branches...</div>
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Branches ({totalBranches})</h2>
        {error && (
          <div className="text-red-600 bg-red-50 px-4 py-2 rounded flex items-center">
            {error}
            <button onClick={clearError} className="ml-2 text-red-800 hover:text-red-900">
              ×
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {branches.map((branch) => (
          <div key={branch._id} className="border rounded-lg p-4 shadow-sm">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="font-semibold text-lg">{branch.name}</h3>
                <p className="text-gray-600 text-sm">{branch.code}</p>
                <p className="text-sm text-gray-500">
                  {branch.address.city}, {branch.address.region}
                </p>
              </div>
              
              <div className="text-right">
                <span className={`px-2 py-1 rounded text-xs ${
                  branch.status === 'Active' ? 'bg-green-100 text-green-800' :
                  branch.status === 'Inactive' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {branch.status}
                </span>
                {branch.isMainBranch && (
                  <div className="mt-1">
                    <span className="px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                      Main Branch
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="text-sm text-gray-600 mb-3">
              <p><strong>Manager:</strong> {branch.managerName}</p>
              <p><strong>Phone:</strong> {branch.phone}</p>
              <p><strong>Email:</strong> {branch.email}</p>
              <p><strong>Capacity:</strong> {branch.capacity} people</p>
              <p><strong>Current Staff:</strong> {branch.currentStaff}</p>
              <p><strong>Services:</strong> {branch.services.join(', ')}</p>
            </div>

            <div className="text-xs text-gray-500 mb-3">
              <p><strong>Address:</strong> {branch.address.street}, {branch.address.city}</p>
              <p><strong>Coordinates:</strong> {branch.coordinates.latitude}, {branch.coordinates.longitude}</p>
            </div>

            <div className="flex flex-wrap gap-2">
              <select
                value={branch.status}
                onChange={(e) => handleStatusUpdate(branch._id, e.target.value as BranchStatus)}
                className="text-xs border rounded px-2 py-1"
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
                <option value="Maintenance">Maintenance</option>
              </select>

              {branch.status !== 'Active' && (
                <button
                  onClick={() => handleActivate(branch._id)}
                  className="text-xs bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700"
                >
                  Activate
                </button>
              )}

              {branch.status === 'Active' && (
                <>
                  <button
                    onClick={() => handleDeactivate(branch._id)}
                    className="text-xs bg-orange-600 text-white px-3 py-1 rounded hover:bg-orange-700"
                  >
                    Deactivate
                  </button>
                  <button
                    onClick={() => handleMaintenance(branch._id)}
                    className="text-xs bg-yellow-600 text-white px-3 py-1 rounded hover:bg-yellow-700"
                  >
                    Maintenance
                  </button>
                </>
              )}

              <button
                onClick={() => handleDeleteBranch(branch._id)}
                className="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-6">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 border rounded ${
                currentPage === page ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'
              }`}
            >
              {page}
            </button>
          ))}
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}

// Branch Status Dashboard
function BranchStatusDashboard() {
  const {
    activeBranches,
    inactiveBranches,
    maintenanceBranches,
    fetchBranches,
    fetchBranchesByStatus
  } = useBranchesStore()

  useEffect(() => {
    fetchBranches()
  }, [fetchBranches])

  const handleStatusFilter = async (status: BranchStatus) => {
    await fetchBranchesByStatus(status)
  }

  const statusGroups = [
    { label: 'Active', count: activeBranches.length, color: 'green', status: 'Active' as BranchStatus },
    { label: 'Inactive', count: inactiveBranches.length, color: 'red', status: 'Inactive' as BranchStatus },
    { label: 'Maintenance', count: maintenanceBranches.length, color: 'yellow', status: 'Maintenance' as BranchStatus }
  ]

  return (
    <div className="mb-8">
      <h3 className="text-xl font-bold mb-4">Branch Status Overview</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {statusGroups.map((group) => (
          <button
            key={group.status}
            onClick={() => handleStatusFilter(group.status)}
            className={`p-4 rounded-lg text-center hover:opacity-80 transition-opacity ${
              group.color === 'green' ? 'bg-green-50 border border-green-200' :
              group.color === 'red' ? 'bg-red-50 border border-red-200' :
              'bg-yellow-50 border border-yellow-200'
            }`}
          >
            <div className={`text-2xl font-bold ${
              group.color === 'green' ? 'text-green-800' :
              group.color === 'red' ? 'text-red-800' :
              'text-yellow-800'
            }`}>
              {group.count}
            </div>
            <div className={`text-sm ${
              group.color === 'green' ? 'text-green-600' :
              group.color === 'red' ? 'text-red-600' :
              'text-yellow-600'
            }`}>
              {group.label}
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}

// Branch Search Component
function BranchSearch() {
  const { searchBranches, setFilters, clearFilters, fetchBranchesByRegion } = useBranchesStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setLocalFilters] = useState({
    status: '',
    region: '',
    managerId: ''
  })

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    await searchBranches(searchQuery, { page: 1, limit: 20 }, {
      ...(filters.status && { status: filters.status as BranchStatus }),
      ...(filters.region && { region: filters.region }),
      ...(filters.managerId && { managerId: filters.managerId })
    })
  }

  const handleRegionFilter = async (region: string) => {
    if (region) {
      await fetchBranchesByRegion(region)
    }
  }

  const handleClearFilters = () => {
    setSearchQuery('')
    setLocalFilters({ status: '', region: '', managerId: '' })
    clearFilters()
  }

  return (
    <div className="mb-6">
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search branches by name, code, city..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Search
          </button>
        </div>

        <div className="flex flex-wrap gap-2">
          <select
            value={filters.status}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Statuses</option>
            <option value="Active">Active</option>
            <option value="Inactive">Inactive</option>
            <option value="Maintenance">Maintenance</option>
          </select>

          <select
            value={filters.region}
            onChange={(e) => {
              setLocalFilters(prev => ({ ...prev, region: e.target.value }))
              handleRegionFilter(e.target.value)
            }}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Regions</option>
            <option value="Northern">Northern</option>
            <option value="Central">Central</option>
            <option value="Southern">Southern</option>
          </select>

          <input
            type="text"
            value={filters.managerId}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, managerId: e.target.value }))}
            placeholder="Manager ID"
            className="px-3 py-1 border rounded text-sm"
          />

          <button
            type="button"
            onClick={handleClearFilters}
            className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
          >
            Clear Filters
          </button>
        </div>
      </form>
    </div>
  )
}

// Branch Analytics Component
function BranchAnalytics() {
  const { 
    topPerformingBranches, 
    recentBranches,
    fetchTopPerformingBranches, 
    fetchRecentBranches,
    fetchBranchPerformance,
    branchPerformanceData,
    isLoading 
  } = useBranchesStore()

  useEffect(() => {
    fetchTopPerformingBranches(5, 'revenue')
    fetchRecentBranches(5)
  }, [fetchTopPerformingBranches, fetchRecentBranches])

  const handleViewPerformance = async (branchId: string) => {
    await fetchBranchPerformance(branchId, 'month')
  }

  if (isLoading) {
    return <div>Loading analytics...</div>
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
      {/* Top Performing Branches */}
      <div className="bg-white p-4 rounded-lg border">
        <h3 className="text-lg font-bold mb-3">Top Performing Branches</h3>
        <div className="space-y-2">
          {topPerformingBranches.map((branch, index) => (
            <div key={branch._id} className="flex justify-between items-center">
              <div>
                <span className="font-medium">#{index + 1} {branch.name}</span>
                <div className="text-sm text-gray-500">
                  {branch.address.city}, {branch.address.region}
                </div>
              </div>
              <div className="text-right">
                <button
                  onClick={() => handleViewPerformance(branch._id)}
                  className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                >
                  View Performance
                </button>
                {branchPerformanceData[branch._id] && (
                  <div className="text-xs text-gray-500 mt-1">
                    Revenue: MWK {branchPerformanceData[branch._id].revenue?.toLocaleString() || 'N/A'}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Branches */}
      <div className="bg-white p-4 rounded-lg border">
        <h3 className="text-lg font-bold mb-3">Recent Branches</h3>
        <div className="space-y-2">
          {recentBranches.map((branch) => (
            <div key={branch._id} className="flex justify-between items-center">
              <div>
                <span className="font-medium">{branch.name}</span>
                <div className="text-sm text-gray-500">
                  {branch.address.city}, {branch.address.region}
                </div>
              </div>
              <div className="text-xs text-gray-500">
                {new Date(branch.createdAt).toLocaleDateString()}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Branch Statistics Component
function BranchStatistics() {
  const { branches, calculateBranchDistance, formatBranchAddress, formatBranchContact } = useBranchesStore()

  const totalBranches = branches.length
  const activeBranches = branches.filter(b => b.status === 'Active').length
  const totalCapacity = branches.reduce((sum, branch) => sum + branch.capacity, 0)
  const totalStaff = branches.reduce((sum, branch) => sum + branch.currentStaff, 0)
  const averageCapacity = totalBranches > 0 ? totalCapacity / totalBranches : 0

  // Calculate distance between first two branches for demo
  const sampleDistance = branches.length >= 2 ? 
    calculateBranchDistance(branches[0], branches[1]) : 0

  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-blue-600">Total Branches</h4>
        <p className="text-2xl font-bold text-blue-900">{totalBranches}</p>
      </div>
      
      <div className="bg-green-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-green-600">Active Branches</h4>
        <p className="text-2xl font-bold text-green-900">{activeBranches}</p>
      </div>
      
      <div className="bg-purple-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-purple-600">Total Capacity</h4>
        <p className="text-2xl font-bold text-purple-900">{totalCapacity}</p>
      </div>
      
      <div className="bg-orange-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-orange-600">Total Staff</h4>
        <p className="text-2xl font-bold text-orange-900">{totalStaff}</p>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-yellow-600">Avg Capacity</h4>
        <p className="text-2xl font-bold text-yellow-900">
          {Math.round(averageCapacity)}
        </p>
      </div>
    </div>
  )
}

// Branch Utilities Demo
function BranchUtilities() {
  const { 
    branches, 
    findNearestBranches, 
    getBranchOperatingHours, 
    isBranchOpen,
    formatBranchAddress,
    formatBranchContact 
  } = useBranchesStore()

  const [userLocation, setUserLocation] = useState({ latitude: -13.9626, longitude: 33.7741 })
  const [nearestBranches, setNearestBranches] = useState<any[]>([])

  const handleFindNearest = () => {
    const nearest = findNearestBranches(userLocation.latitude, userLocation.longitude, 3)
    setNearestBranches(nearest)
  }

  return (
    <div className="bg-white p-4 rounded-lg border mb-6">
      <h3 className="text-lg font-bold mb-3">Branch Utilities Demo</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium mb-2">Find Nearest Branches</h4>
          <div className="space-y-2">
            <div className="flex space-x-2">
              <input
                type="number"
                value={userLocation.latitude}
                onChange={(e) => setUserLocation(prev => ({ ...prev, latitude: Number(e.target.value) }))}
                placeholder="Latitude"
                className="px-2 py-1 border rounded text-sm"
                step="0.0001"
              />
              <input
                type="number"
                value={userLocation.longitude}
                onChange={(e) => setUserLocation(prev => ({ ...prev, longitude: Number(e.target.value) }))}
                placeholder="Longitude"
                className="px-2 py-1 border rounded text-sm"
                step="0.0001"
              />
              <button
                onClick={handleFindNearest}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Find
              </button>
            </div>
            
            {nearestBranches.length > 0 && (
              <div className="space-y-1">
                {nearestBranches.map((branch, index) => (
                  <div key={branch._id} className="text-sm">
                    {index + 1}. {branch.name} - {branch.distance?.toFixed(2)} km
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-2">Branch Information</h4>
          {branches.slice(0, 2).map((branch) => (
            <div key={branch._id} className="text-sm space-y-1 mb-3">
              <div><strong>{branch.name}</strong></div>
              <div>Address: {formatBranchAddress(branch)}</div>
              <div>Contact: {formatBranchContact(branch)}</div>
              <div>
                Status: {isBranchOpen(branch) ? 
                  <span className="text-green-600">Open</span> : 
                  <span className="text-red-600">Closed</span>
                }
              </div>
              <div>
                Hours: {getBranchOperatingHours(branch).open} - {getBranchOperatingHours(branch).close}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Main Branches Example Component
export default function BranchesExample() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">
          Branches Store Example
        </h1>
        
        <BranchStatistics />
        <BranchStatusDashboard />
        <BranchAnalytics />
        <BranchUtilities />
        <BranchSearch />
        <BranchList />
      </div>
    </div>
  )
}
