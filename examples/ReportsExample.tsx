// examples/ReportsExample.tsx - Example usage of the reports store

'use client'

import React, { useEffect, useState } from 'react'
import { useReportsStore } from '@/stores/reportsStore'
import type { Report, ReportType, DateRange, ExportFormat } from '@/types/frontend'

// Dashboard Component
function Dashboard() {
  const {
    dashboardData,
    dashboardLoading,
    lastUpdated,
    fetchDashboardData,
    refreshDashboard
  } = useReportsStore()

  useEffect(() => {
    fetchDashboardData()
  }, [fetchDashboardData])

  const handleRefresh = async () => {
    await refreshDashboard()
  }

  if (dashboardLoading) {
    return <div className="text-center py-8">Loading dashboard...</div>
  }

  if (!dashboardData) {
    return <div className="text-center py-8">No dashboard data available</div>
  }

  return (
    <div className="bg-white rounded-lg p-6 mb-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Business Dashboard</h2>
        <div className="flex items-center space-x-4">
          {lastUpdated && (
            <span className="text-sm text-gray-500">
              Last updated: {new Date(lastUpdated).toLocaleString()}
            </span>
          )}
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Refresh
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-blue-600">Total Sales</h3>
          <p className="text-2xl font-bold text-blue-900">
            MWK {dashboardData.totalSales.toLocaleString()}
          </p>
          <p className="text-sm text-green-600">
            +{dashboardData.salesGrowth}% from last period
          </p>
        </div>

        <div className="bg-green-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-green-600">Total Orders</h3>
          <p className="text-2xl font-bold text-green-900">
            {dashboardData.totalOrders}
          </p>
          <p className="text-sm text-green-600">
            +{dashboardData.orderGrowth}% from last period
          </p>
        </div>

        <div className="bg-purple-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-purple-600">Total Customers</h3>
          <p className="text-2xl font-bold text-purple-900">
            {dashboardData.totalCustomers}
          </p>
          <p className="text-sm text-green-600">
            +{dashboardData.customerGrowth}% from last period
          </p>
        </div>

        <div className="bg-orange-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-orange-600">Total Products</h3>
          <p className="text-2xl font-bold text-orange-900">
            {dashboardData.totalProducts}
          </p>
        </div>
      </div>

      {/* Top Products */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <h3 className="text-lg font-semibold mb-3">Top Products</h3>
          <div className="space-y-2">
            {dashboardData.topProducts.map((product) => (
              <div key={product.productId} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <div>
                  <div className="font-medium">{product.productName}</div>
                  <div className="text-sm text-gray-600">Qty: {product.quantity}</div>
                </div>
                <div className="text-right">
                  <div className="font-bold">MWK {product.sales.toLocaleString()}</div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3">Top Customers</h3>
          <div className="space-y-2">
            {dashboardData.topCustomers.map((customer) => (
              <div key={customer.customerId} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <div className="font-medium">{customer.customerName}</div>
                <div className="font-bold">MWK {customer.totalSpent.toLocaleString()}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Sales by Branch */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Sales by Branch</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {dashboardData.salesByBranch.map((branch) => (
            <div key={branch.branchId} className="bg-gray-50 p-4 rounded">
              <div className="font-medium">{branch.branchName}</div>
              <div className="text-xl font-bold">MWK {branch.sales.toLocaleString()}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Alerts */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h4 className="font-medium text-yellow-800">Low Stock Items</h4>
          <p className="text-2xl font-bold text-yellow-900">{dashboardData.lowStockItems}</p>
        </div>

        <div className="bg-red-50 p-4 rounded-lg">
          <h4 className="font-medium text-red-800">Critical Stock Items</h4>
          <p className="text-2xl font-bold text-red-900">{dashboardData.criticalStockItems}</p>
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-800">Pending Orders</h4>
          <p className="text-2xl font-bold text-blue-900">{dashboardData.pendingOrders}</p>
        </div>
      </div>
    </div>
  )
}

// Report Generation Component
function ReportGeneration() {
  const {
    generateSalesReport,
    generateInventoryReport,
    generateCustomerReport,
    generateBranchReport,
    generateFinancialReport,
    isLoading
  } = useReportsStore()

  const [reportType, setReportType] = useState<ReportType>('Sales')
  const [dateRange, setDateRange] = useState<DateRange>({
    startDate: '2024-01-01',
    endDate: '2024-01-31'
  })
  const [branchId, setBranchId] = useState('')
  const [format, setFormat] = useState<ExportFormat>('PDF')

  const handleGenerateReport = async () => {
    try {
      let result
      
      switch (reportType) {
        case 'Sales':
          result = await generateSalesReport(dateRange, branchId || undefined, format)
          break
        case 'Inventory':
          result = await generateInventoryReport(branchId || undefined, true, format)
          break
        case 'Customer':
          result = await generateCustomerReport('summary', dateRange, format)
          break
        case 'Branch':
          result = await generateBranchReport(branchId || undefined, dateRange, format)
          break
        case 'Financial':
          result = await generateFinancialReport('profit_loss', dateRange, format)
          break
        default:
          return
      }

      if (result.success) {
        alert('Report generated successfully!')
      } else {
        alert(`Failed to generate report: ${result.error}`)
      }
    } catch (error) {
      alert('Error generating report')
    }
  }

  return (
    <div className="bg-white rounded-lg p-6 mb-6">
      <h2 className="text-2xl font-bold mb-6">Generate Reports</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div>
          <label className="block text-sm font-medium mb-2">Report Type</label>
          <select
            value={reportType}
            onChange={(e) => setReportType(e.target.value as ReportType)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="Sales">Sales Report</option>
            <option value="Inventory">Inventory Report</option>
            <option value="Customer">Customer Report</option>
            <option value="Branch">Branch Report</option>
            <option value="Financial">Financial Report</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Start Date</label>
          <input
            type="date"
            value={dateRange.startDate}
            onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">End Date</label>
          <input
            type="date"
            value={dateRange.endDate}
            onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Format</label>
          <select
            value={format}
            onChange={(e) => setFormat(e.target.value as ExportFormat)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="PDF">PDF</option>
            <option value="Excel">Excel</option>
            <option value="CSV">CSV</option>
            <option value="JSON">JSON</option>
          </select>
        </div>
      </div>

      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Branch ID (Optional)</label>
        <input
          type="text"
          value={branchId}
          onChange={(e) => setBranchId(e.target.value)}
          placeholder="Leave empty for all branches"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <button
        onClick={handleGenerateReport}
        disabled={isLoading}
        className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50"
      >
        {isLoading ? 'Generating...' : 'Generate Report'}
      </button>
    </div>
  )
}

// Reports List Component
function ReportsList() {
  const {
    reports,
    isLoading,
    error,
    currentPage,
    totalPages,
    totalReports,
    fetchReports,
    setCurrentPage,
    exportReport,
    shareReport,
    deleteReport,
    clearError,
    getReportTypeIcon,
    getReportStatusColor,
    getExportFormatIcon,
    formatReportSize,
    isReportExpired
  } = useReportsStore()

  useEffect(() => {
    fetchReports()
  }, [fetchReports])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchReports({ page, limit: 20 })
  }

  const handleExportReport = async (reportId: string, format: ExportFormat) => {
    const result = await exportReport(reportId, format)
    if (result.success && result.downloadUrl) {
      window.open(result.downloadUrl, '_blank')
    } else {
      alert(`Failed to export report: ${result.error}`)
    }
  }

  const handleShareReport = async (reportId: string) => {
    const recipients = prompt('Enter email addresses (comma-separated):')
    if (recipients) {
      const emailList = recipients.split(',').map(email => email.trim())
      const message = prompt('Enter message (optional):')
      
      const result = await shareReport(reportId, emailList, message || undefined)
      if (result.success) {
        alert('Report shared successfully!')
      } else {
        alert(`Failed to share report: ${result.error}`)
      }
    }
  }

  const handleDeleteReport = async (reportId: string) => {
    if (confirm('Are you sure you want to delete this report?')) {
      const result = await deleteReport(reportId)
      if (result.success) {
        alert('Report deleted successfully!')
      } else {
        alert(`Failed to delete report: ${result.error}`)
      }
    }
  }

  if (isLoading) {
    return <div className="text-center py-8">Loading reports...</div>
  }

  return (
    <div className="bg-white rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Reports ({totalReports})</h2>
        {error && (
          <div className="text-red-600 bg-red-50 px-4 py-2 rounded flex items-center">
            {error}
            <button onClick={clearError} className="ml-2 text-red-800 hover:text-red-900">
              ×
            </button>
          </div>
        )}
      </div>

      <div className="space-y-4">
        {reports.map((report) => {
          const typeIcon = getReportTypeIcon(report.reportType)
          const statusColor = getReportStatusColor(report.status)
          const formatIcon = getExportFormatIcon(report.format)
          const fileSize = formatReportSize(report.fileSize)
          const isExpired = isReportExpired(report)

          return (
            <div key={report._id} className={`border rounded-lg p-4 ${isExpired ? 'bg-gray-50 opacity-75' : 'bg-white'}`}>
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">{typeIcon}</span>
                    <h3 className="font-semibold text-lg">{report.title}</h3>
                    {isExpired && (
                      <span className="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">
                        Expired
                      </span>
                    )}
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-2">{report.description}</p>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>Created by: {report.createdByName}</span>
                    <span>•</span>
                    <span>Created: {new Date(report.createdAt).toLocaleDateString()}</span>
                    <span>•</span>
                    <span>Size: {fileSize}</span>
                    {report.generationTime && (
                      <>
                        <span>•</span>
                        <span>Generated in: {(report.generationTime / 1000).toFixed(1)}s</span>
                      </>
                    )}
                  </div>

                  {report.tags && report.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {report.tags.map((tag) => (
                        <span key={tag} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
                
                <div className="text-right">
                  <div className={`px-3 py-1 rounded text-sm font-medium mb-2 ${
                    statusColor === 'green' ? 'bg-green-100 text-green-800' :
                    statusColor === 'blue' ? 'bg-blue-100 text-blue-800' :
                    statusColor === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                    statusColor === 'red' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {report.status}
                  </div>
                  
                  <div className="flex items-center justify-end space-x-1 text-sm text-gray-500">
                    <span>{formatIcon}</span>
                    <span>{report.format}</span>
                  </div>
                </div>
              </div>

              {report.status === 'Completed' && (
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => handleExportReport(report._id, 'PDF')}
                    className="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
                  >
                    Export PDF
                  </button>
                  <button
                    onClick={() => handleExportReport(report._id, 'Excel')}
                    className="text-xs bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700"
                  >
                    Export Excel
                  </button>
                  <button
                    onClick={() => handleShareReport(report._id)}
                    className="text-xs bg-purple-600 text-white px-3 py-1 rounded hover:bg-purple-700"
                  >
                    Share
                  </button>
                  {report.downloadUrl && (
                    <a
                      href={report.downloadUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs bg-gray-600 text-white px-3 py-1 rounded hover:bg-gray-700"
                    >
                      Download
                    </a>
                  )}
                  <button
                    onClick={() => handleDeleteReport(report._id)}
                    className="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
                  >
                    Delete
                  </button>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-6">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 border rounded ${
                currentPage === page ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'
              }`}
            >
              {page}
            </button>
          ))}
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}

// Report Search Component
function ReportSearch() {
  const { searchReports, setFilters, clearFilters } = useReportsStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setLocalFilters] = useState({
    reportType: '',
    status: '',
    createdBy: '',
    format: ''
  })

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    await searchReports(searchQuery, { page: 1, limit: 20 }, {
      ...(filters.reportType && { reportType: filters.reportType as ReportType }),
      ...(filters.status && { status: filters.status }),
      ...(filters.createdBy && { createdBy: filters.createdBy }),
      ...(filters.format && { format: filters.format as ExportFormat })
    })
  }

  const handleClearFilters = () => {
    setSearchQuery('')
    setLocalFilters({ reportType: '', status: '', createdBy: '', format: '' })
    clearFilters()
  }

  return (
    <div className="mb-6">
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search reports by title, description, tags..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Search
          </button>
        </div>

        <div className="flex flex-wrap gap-2">
          <select
            value={filters.reportType}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, reportType: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Types</option>
            <option value="Sales">Sales</option>
            <option value="Inventory">Inventory</option>
            <option value="Customer">Customer</option>
            <option value="Branch">Branch</option>
            <option value="Financial">Financial</option>
            <option value="Custom">Custom</option>
          </select>

          <select
            value={filters.status}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Status</option>
            <option value="Pending">Pending</option>
            <option value="Processing">Processing</option>
            <option value="Completed">Completed</option>
            <option value="Failed">Failed</option>
            <option value="Cancelled">Cancelled</option>
          </select>

          <select
            value={filters.format}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, format: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Formats</option>
            <option value="PDF">PDF</option>
            <option value="Excel">Excel</option>
            <option value="CSV">CSV</option>
            <option value="JSON">JSON</option>
          </select>

          <input
            type="text"
            value={filters.createdBy}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, createdBy: e.target.value }))}
            placeholder="Created by"
            className="px-3 py-1 border rounded text-sm"
          />

          <button
            type="button"
            onClick={handleClearFilters}
            className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
          >
            Clear Filters
          </button>
        </div>
      </form>
    </div>
  )
}

// Main Reports Example Component
export default function ReportsExample() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">
          Reports & Analytics Store Example
        </h1>
        
        <Dashboard />
        <ReportGeneration />
        <ReportSearch />
        <ReportsList />
      </div>
    </div>
  )
}
