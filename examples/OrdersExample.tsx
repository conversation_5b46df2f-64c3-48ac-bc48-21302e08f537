// examples/OrdersExample.tsx - Example usage of the orders store

'use client'

import React, { useEffect, useState } from 'react'
import { useOrdersStore } from '@/stores/ordersStore'
import type { OrderStatus, PaymentStatus, PaymentMethod } from '@/types/frontend'

// Order List Component
function OrderList() {
  const {
    orders,
    isLoading,
    error,
    currentPage,
    totalPages,
    totalOrders,
    fetchOrders,
    setCurrentPage,
    updateOrderStatus,
    updatePaymentStatus,
    cancelOrder,
    clearError
  } = useOrdersStore()

  useEffect(() => {
    fetchOrders()
  }, [fetchOrders])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchOrders({ page, limit: 20 })
  }

  const handleStatusUpdate = async (orderId: string, status: OrderStatus) => {
    const result = await updateOrderStatus(orderId, status)
    if (result.success) {
      alert('Order status updated successfully!')
    } else {
      alert(`Failed to update status: ${result.error}`)
    }
  }

  const handlePaymentStatusUpdate = async (orderId: string, paymentStatus: PaymentStatus) => {
    const result = await updatePaymentStatus(orderId, paymentStatus)
    if (result.success) {
      alert('Payment status updated successfully!')
    } else {
      alert(`Failed to update payment status: ${result.error}`)
    }
  }

  const handleCancelOrder = async (orderId: string) => {
    if (confirm('Are you sure you want to cancel this order?')) {
      const result = await cancelOrder(orderId, 'Cancelled by admin')
      if (result.success) {
        alert('Order cancelled successfully!')
      } else {
        alert(`Failed to cancel order: ${result.error}`)
      }
    }
  }

  if (isLoading) {
    return <div className="text-center py-8">Loading orders...</div>
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Orders ({totalOrders})</h2>
        {error && (
          <div className="text-red-600 bg-red-50 px-4 py-2 rounded flex items-center">
            {error}
            <button onClick={clearError} className="ml-2 text-red-800 hover:text-red-900">
              ×
            </button>
          </div>
        )}
      </div>

      <div className="space-y-4">
        {orders.map((order) => (
          <div key={order._id} className="border rounded-lg p-4 shadow-sm">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="font-semibold text-lg">{order.orderNumber}</h3>
                <p className="text-gray-600">{order.customerName}</p>
                <p className="text-sm text-gray-500">{order.branchName}</p>
              </div>
              
              <div className="text-right">
                <p className="text-lg font-bold text-blue-600">
                  MWK {order.total.toLocaleString()}
                </p>
                <p className="text-sm text-gray-500">
                  {new Date(order.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mb-3">
              <span className={`px-2 py-1 rounded text-xs ${
                order.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                order.status === 'Processing' ? 'bg-blue-100 text-blue-800' :
                order.status === 'Shipped' ? 'bg-purple-100 text-purple-800' :
                order.status === 'Delivered' ? 'bg-green-100 text-green-800' :
                order.status === 'Cancelled' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {order.status}
              </span>
              
              <span className={`px-2 py-1 rounded text-xs ${
                order.paymentStatus === 'Paid' ? 'bg-green-100 text-green-800' :
                order.paymentStatus === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                order.paymentStatus === 'Failed' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                Payment: {order.paymentStatus}
              </span>
              
              <span className="px-2 py-1 rounded text-xs bg-gray-100 text-gray-800">
                {order.paymentMethod}
              </span>
            </div>

            <div className="text-sm text-gray-600 mb-3">
              <p><strong>Items:</strong> {order.items.length}</p>
              <p><strong>Subtotal:</strong> MWK {order.subtotal.toLocaleString()}</p>
              <p><strong>Tax:</strong> MWK {order.tax.toLocaleString()}</p>
              <p><strong>Shipping:</strong> MWK {order.shipping.toLocaleString()}</p>
              {order.trackingNumber && (
                <p><strong>Tracking:</strong> {order.trackingNumber}</p>
              )}
            </div>

            <div className="flex flex-wrap gap-2">
              <select
                value={order.status}
                onChange={(e) => handleStatusUpdate(order._id, e.target.value as OrderStatus)}
                className="text-xs border rounded px-2 py-1"
              >
                <option value="Pending">Pending</option>
                <option value="Processing">Processing</option>
                <option value="Shipped">Shipped</option>
                <option value="Delivered">Delivered</option>
                <option value="Cancelled">Cancelled</option>
              </select>

              <select
                value={order.paymentStatus}
                onChange={(e) => handlePaymentStatusUpdate(order._id, e.target.value as PaymentStatus)}
                className="text-xs border rounded px-2 py-1"
              >
                <option value="Pending">Pending</option>
                <option value="Paid">Paid</option>
                <option value="Failed">Failed</option>
                <option value="Refunded">Refunded</option>
              </select>

              {(order.status === 'Pending' || order.status === 'Processing') && (
                <button
                  onClick={() => handleCancelOrder(order._id)}
                  className="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
                >
                  Cancel
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-6">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 border rounded ${
                currentPage === page ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'
              }`}
            >
              {page}
            </button>
          ))}
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}

// Order Status Dashboard
function OrderStatusDashboard() {
  const {
    pendingOrders,
    processingOrders,
    shippedOrders,
    deliveredOrders,
    cancelledOrders,
    fetchOrders,
    fetchOrdersByStatus
  } = useOrdersStore()

  useEffect(() => {
    fetchOrders()
  }, [fetchOrders])

  const handleStatusFilter = async (status: OrderStatus) => {
    await fetchOrdersByStatus(status)
  }

  const statusGroups = [
    { label: 'Pending', count: pendingOrders.length, color: 'yellow', status: 'Pending' as OrderStatus },
    { label: 'Processing', count: processingOrders.length, color: 'blue', status: 'Processing' as OrderStatus },
    { label: 'Shipped', count: shippedOrders.length, color: 'purple', status: 'Shipped' as OrderStatus },
    { label: 'Delivered', count: deliveredOrders.length, color: 'green', status: 'Delivered' as OrderStatus },
    { label: 'Cancelled', count: cancelledOrders.length, color: 'red', status: 'Cancelled' as OrderStatus }
  ]

  return (
    <div className="mb-8">
      <h3 className="text-xl font-bold mb-4">Order Status Overview</h3>
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        {statusGroups.map((group) => (
          <button
            key={group.status}
            onClick={() => handleStatusFilter(group.status)}
            className={`p-4 rounded-lg text-center hover:opacity-80 transition-opacity ${
              group.color === 'yellow' ? 'bg-yellow-50 border border-yellow-200' :
              group.color === 'blue' ? 'bg-blue-50 border border-blue-200' :
              group.color === 'purple' ? 'bg-purple-50 border border-purple-200' :
              group.color === 'green' ? 'bg-green-50 border border-green-200' :
              'bg-red-50 border border-red-200'
            }`}
          >
            <div className={`text-2xl font-bold ${
              group.color === 'yellow' ? 'text-yellow-800' :
              group.color === 'blue' ? 'text-blue-800' :
              group.color === 'purple' ? 'text-purple-800' :
              group.color === 'green' ? 'text-green-800' :
              'text-red-800'
            }`}>
              {group.count}
            </div>
            <div className={`text-sm ${
              group.color === 'yellow' ? 'text-yellow-600' :
              group.color === 'blue' ? 'text-blue-600' :
              group.color === 'purple' ? 'text-purple-600' :
              group.color === 'green' ? 'text-green-600' :
              'text-red-600'
            }`}>
              {group.label}
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}

// Order Search Component
function OrderSearch() {
  const { searchOrders, setFilters, clearFilters } = useOrdersStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setLocalFilters] = useState({
    status: '',
    paymentStatus: '',
    paymentMethod: ''
  })

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    await searchOrders(searchQuery, { page: 1, limit: 20 }, {
      ...(filters.status && { status: filters.status as OrderStatus }),
      ...(filters.paymentStatus && { paymentStatus: filters.paymentStatus as PaymentStatus }),
      ...(filters.paymentMethod && { paymentMethod: filters.paymentMethod as PaymentMethod })
    })
  }

  const handleClearFilters = () => {
    setSearchQuery('')
    setLocalFilters({ status: '', paymentStatus: '', paymentMethod: '' })
    clearFilters()
  }

  return (
    <div className="mb-6">
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search orders by number, customer name..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Search
          </button>
        </div>

        <div className="flex flex-wrap gap-2">
          <select
            value={filters.status}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, status: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Statuses</option>
            <option value="Pending">Pending</option>
            <option value="Processing">Processing</option>
            <option value="Shipped">Shipped</option>
            <option value="Delivered">Delivered</option>
            <option value="Cancelled">Cancelled</option>
          </select>

          <select
            value={filters.paymentStatus}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, paymentStatus: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Payment Statuses</option>
            <option value="Pending">Pending</option>
            <option value="Paid">Paid</option>
            <option value="Failed">Failed</option>
            <option value="Refunded">Refunded</option>
          </select>

          <select
            value={filters.paymentMethod}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, paymentMethod: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Payment Methods</option>
            <option value="Cash">Cash</option>
            <option value="Card">Card</option>
            <option value="Mobile Money">Mobile Money</option>
            <option value="Bank Transfer">Bank Transfer</option>
            <option value="Credit">Credit</option>
          </select>

          <button
            type="button"
            onClick={handleClearFilters}
            className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
          >
            Clear Filters
          </button>
        </div>
      </form>
    </div>
  )
}

// Recent Orders Component
function RecentOrders() {
  const { recentOrders, fetchRecentOrders, isLoading } = useOrdersStore()

  useEffect(() => {
    fetchRecentOrders(5)
  }, [fetchRecentOrders])

  if (isLoading) {
    return <div>Loading recent orders...</div>
  }

  return (
    <div className="mb-8">
      <h3 className="text-xl font-bold mb-4">Recent Orders</h3>
      <div className="space-y-2">
        {recentOrders.map((order) => (
          <div key={order._id} className="flex justify-between items-center p-3 bg-gray-50 rounded">
            <div>
              <span className="font-medium">{order.orderNumber}</span>
              <span className="text-gray-600 ml-2">{order.customerName}</span>
            </div>
            <div className="text-right">
              <div className="font-bold">MWK {order.total.toLocaleString()}</div>
              <div className="text-sm text-gray-500">{order.status}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// Order Statistics Component
function OrderStatistics() {
  const { orders, calculateOrderTotals } = useOrdersStore()

  const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0)
  const averageOrderValue = orders.length > 0 ? totalRevenue / orders.length : 0
  const totalOrders = orders.length

  // Sample calculation with order items
  const sampleItems = orders.flatMap(order => order.items)
  const sampleTotals = calculateOrderTotals(sampleItems, 0.18, 5000, 0)

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-blue-600">Total Orders</h4>
        <p className="text-2xl font-bold text-blue-900">{totalOrders}</p>
      </div>
      
      <div className="bg-green-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-green-600">Total Revenue</h4>
        <p className="text-2xl font-bold text-green-900">
          MWK {totalRevenue.toLocaleString()}
        </p>
      </div>
      
      <div className="bg-purple-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-purple-600">Average Order Value</h4>
        <p className="text-2xl font-bold text-purple-900">
          MWK {Math.round(averageOrderValue).toLocaleString()}
        </p>
      </div>
      
      <div className="bg-orange-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-orange-600">Items Sold</h4>
        <p className="text-2xl font-bold text-orange-900">
          {sampleItems.reduce((sum, item) => sum + item.quantity, 0)}
        </p>
      </div>
    </div>
  )
}

// Main Orders Example Component
export default function OrdersExample() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">
          Orders Store Example
        </h1>
        
        <OrderStatistics />
        <OrderStatusDashboard />
        <RecentOrders />
        <OrderSearch />
        <OrderList />
      </div>
    </div>
  )
}
