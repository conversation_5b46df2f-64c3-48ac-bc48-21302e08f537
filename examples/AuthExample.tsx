// examples/AuthExample.tsx - Example usage of the authentication store

'use client'

import React, { useState } from 'react'
import { useAuthStore } from '@/stores/authStore'
import type { UserRole } from '@/types/frontend'

// Login Form Component
function LoginForm() {
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
    rememberMe: false
  })

  const { login, isLoading, error, clearError } = useAuthStore()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()

    const result = await login(credentials)
    if (result.success) {
      console.log('Login successful!')
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-md mx-auto p-6 border rounded-lg">
      <h2 className="text-2xl font-bold">Login</h2>
      
      <div>
        <label htmlFor="username" className="block text-sm font-medium">
          Username
        </label>
        <input
          id="username"
          type="text"
          value={credentials.username}
          onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div>
        <label htmlFor="password" className="block text-sm font-medium">
          Password
        </label>
        <input
          id="password"
          type="password"
          value={credentials.password}
          onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div className="flex items-center">
        <input
          id="rememberMe"
          type="checkbox"
          checked={credentials.rememberMe}
          onChange={(e) => setCredentials(prev => ({ ...prev, rememberMe: e.target.checked }))}
          className="mr-2"
        />
        <label htmlFor="rememberMe" className="text-sm">
          Remember me
        </label>
      </div>

      {error && (
        <div className="text-red-600 text-sm bg-red-50 p-3 rounded">
          {error}
        </div>
      )}

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
      >
        {isLoading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  )
}

// Register Form Component
function RegisterForm() {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
    role: 'branch_manager' as UserRole,
    branchId: ''
  })

  const { register, isLoading, error, clearError } = useAuthStore()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()

    const result = await register(formData)
    if (result.success) {
      console.log('Registration successful!')
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-md mx-auto p-6 border rounded-lg">
      <h2 className="text-2xl font-bold">Register</h2>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="firstName" className="block text-sm font-medium">
            First Name
          </label>
          <input
            id="firstName"
            type="text"
            value={formData.firstName}
            onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>

        <div>
          <label htmlFor="lastName" className="block text-sm font-medium">
            Last Name
          </label>
          <input
            id="lastName"
            type="text"
            value={formData.lastName}
            onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
      </div>

      <div>
        <label htmlFor="username" className="block text-sm font-medium">
          Username
        </label>
        <input
          id="username"
          type="text"
          value={formData.username}
          onChange={(e) => setFormData(prev => ({ ...prev, username: e.target.value }))}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium">
          Email
        </label>
        <input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="password" className="block text-sm font-medium">
            Password
          </label>
          <input
            id="password"
            type="password"
            value={formData.password}
            onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>

        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium">
            Confirm Password
          </label>
          <input
            id="confirmPassword"
            type="password"
            value={formData.confirmPassword}
            onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
      </div>

      {error && (
        <div className="text-red-600 text-sm bg-red-50 p-3 rounded">
          {error}
        </div>
      )}

      <button
        type="submit"
        disabled={isLoading}
        className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50"
      >
        {isLoading ? 'Registering...' : 'Register'}
      </button>
    </form>
  )
}

// User Profile Component
function UserProfile() {
  const { 
    user, 
    logout, 
    getDisplayName, 
    userRole, 
    userBranch, 
    isAdmin, 
    isBranchManager,
    hasPermission,
    canAccessBranch
  } = useAuthStore()

  if (!user) return null

  return (
    <div className="max-w-md mx-auto p-6 border rounded-lg">
      <h2 className="text-2xl font-bold mb-4">Profile</h2>
      
      <div className="space-y-2">
        <p><strong>Name:</strong> {getDisplayName()}</p>
        <p><strong>Username:</strong> {user.username}</p>
        <p><strong>Email:</strong> {user.email}</p>
        <p><strong>Role:</strong> {userRole}</p>
        <p><strong>Branch:</strong> {user.branchName || 'All branches'}</p>
        <p><strong>Is Admin:</strong> {isAdmin ? 'Yes' : 'No'}</p>
        <p><strong>Is Branch Manager:</strong> {isBranchManager ? 'Yes' : 'No'}</p>
      </div>

      <div className="mt-4 space-y-2">
        <h3 className="font-semibold">Permissions:</h3>
        <p>Can manage overall system: {hasPermission('overall_admin') ? 'Yes' : 'No'}</p>
        <p>Can manage branches: {hasPermission('branch_manager') ? 'Yes' : 'No'}</p>
        {userBranch && (
          <p>Can access own branch: {canAccessBranch(userBranch) ? 'Yes' : 'No'}</p>
        )}
      </div>

      <button
        onClick={logout}
        className="mt-6 w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700"
      >
        Logout
      </button>
    </div>
  )
}

// Main Auth Example Component
export default function AuthExample() {
  const { isAuthenticated } = useAuthStore()
  const [showRegister, setShowRegister] = useState(false)

  if (isAuthenticated) {
    return <UserProfile />
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">
          Authentication Store Example
        </h1>
        
        <div className="flex justify-center mb-6">
          <button
            onClick={() => setShowRegister(false)}
            className={`px-4 py-2 mr-2 rounded ${
              !showRegister 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Login
          </button>
          <button
            onClick={() => setShowRegister(true)}
            className={`px-4 py-2 rounded ${
              showRegister 
                ? 'bg-green-600 text-white' 
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            Register
          </button>
        </div>

        {showRegister ? <RegisterForm /> : <LoginForm />}
      </div>
    </div>
  )
}
