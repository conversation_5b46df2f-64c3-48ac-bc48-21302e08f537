// examples/CustomersExample.tsx - Example usage of the customers store

'use client'

import React, { useEffect, useState } from 'react'
import { useCustomersStore } from '@/stores/customersStore'
import type { CustomerTier, CreateCustomerData } from '@/types/frontend'

// Customer List Component
function CustomerList() {
  const {
    customers,
    isLoading,
    error,
    currentPage,
    totalPages,
    totalCustomers,
    fetchCustomers,
    setCurrentPage,
    updateCustomerTier,
    addLoyaltyPoints,
    redeemLoyaltyPoints,
    deleteCustomer,
    clearError
  } = useCustomersStore()

  useEffect(() => {
    fetchCustomers()
  }, [fetchCustomers])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchCustomers({ page, limit: 20 })
  }

  const handleTierUpdate = async (customerId: string, tier: CustomerTier) => {
    const result = await updateCustomerTier(customerId, tier)
    if (result.success) {
      alert('Customer tier updated successfully!')
    } else {
      alert(`Failed to update tier: ${result.error}`)
    }
  }

  const handleAddPoints = async (customerId: string) => {
    const points = prompt('Enter points to add:')
    if (points && !isNaN(Number(points))) {
      const result = await addLoyaltyPoints(customerId, Number(points), 'Manual addition')
      if (result.success) {
        alert('Loyalty points added successfully!')
      } else {
        alert(`Failed to add points: ${result.error}`)
      }
    }
  }

  const handleRedeemPoints = async (customerId: string) => {
    const points = prompt('Enter points to redeem:')
    if (points && !isNaN(Number(points))) {
      const result = await redeemLoyaltyPoints(customerId, Number(points), 'Manual redemption')
      if (result.success) {
        alert('Loyalty points redeemed successfully!')
      } else {
        alert(`Failed to redeem points: ${result.error}`)
      }
    }
  }

  const handleDeleteCustomer = async (customerId: string) => {
    if (confirm('Are you sure you want to delete this customer?')) {
      const result = await deleteCustomer(customerId)
      if (result.success) {
        alert('Customer deleted successfully!')
      } else {
        alert(`Failed to delete customer: ${result.error}`)
      }
    }
  }

  if (isLoading) {
    return <div className="text-center py-8">Loading customers...</div>
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Customers ({totalCustomers})</h2>
        {error && (
          <div className="text-red-600 bg-red-50 px-4 py-2 rounded flex items-center">
            {error}
            <button onClick={clearError} className="ml-2 text-red-800 hover:text-red-900">
              ×
            </button>
          </div>
        )}
      </div>

      <div className="space-y-4">
        {customers.map((customer) => (
          <div key={customer._id} className="border rounded-lg p-4 shadow-sm">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="font-semibold text-lg">
                  {customer.firstName} {customer.lastName}
                </h3>
                <p className="text-gray-600">{customer.email}</p>
                <p className="text-sm text-gray-500">{customer.phone}</p>
                {customer.address && (
                  <p className="text-sm text-gray-500">
                    {customer.address.city}, {customer.address.region}
                  </p>
                )}
              </div>
              
              <div className="text-right">
                <p className="text-lg font-bold text-blue-600">
                  {customer.loyaltyPoints} points
                </p>
                <p className="text-sm text-gray-500">
                  MWK {customer.totalSpent?.toLocaleString() || 0} spent
                </p>
                <p className="text-xs text-gray-400">
                  {customer.totalOrders || 0} orders
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mb-3">
              <span className={`px-2 py-1 rounded text-xs ${
                customer.loyaltyPoints >= 10000 ? 'bg-purple-100 text-purple-800' :
                customer.loyaltyPoints >= 5000 ? 'bg-yellow-100 text-yellow-800' :
                customer.loyaltyPoints >= 2000 ? 'bg-gray-100 text-gray-800' :
                customer.loyaltyPoints >= 500 ? 'bg-orange-100 text-orange-800' :
                'bg-blue-100 text-blue-800'
              }`}>
                {customer.loyaltyPoints >= 10000 ? 'VIP' :
                 customer.loyaltyPoints >= 5000 ? 'Gold' :
                 customer.loyaltyPoints >= 2000 ? 'Silver' :
                 customer.loyaltyPoints >= 500 ? 'Bronze' : 'Regular'}
              </span>
              
              <span className={`px-2 py-1 rounded text-xs ${
                customer.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {customer.isActive ? 'Active' : 'Inactive'}
              </span>
              
              {customer.lastOrderDate && (
                <span className="px-2 py-1 rounded text-xs bg-gray-100 text-gray-800">
                  Last order: {new Date(customer.lastOrderDate).toLocaleDateString()}
                </span>
              )}
            </div>

            {customer.notes && (
              <p className="text-sm text-gray-600 mb-3 italic">"{customer.notes}"</p>
            )}

            <div className="flex flex-wrap gap-2">
              <select
                value={customer.loyaltyPoints >= 10000 ? 'VIP' :
                      customer.loyaltyPoints >= 5000 ? 'Gold' :
                      customer.loyaltyPoints >= 2000 ? 'Silver' :
                      customer.loyaltyPoints >= 500 ? 'Bronze' : 'Regular'}
                onChange={(e) => handleTierUpdate(customer._id, e.target.value as CustomerTier)}
                className="text-xs border rounded px-2 py-1"
              >
                <option value="Regular">Regular</option>
                <option value="Bronze">Bronze</option>
                <option value="Silver">Silver</option>
                <option value="Gold">Gold</option>
                <option value="VIP">VIP</option>
              </select>

              <button
                onClick={() => handleAddPoints(customer._id)}
                className="text-xs bg-green-600 text-white px-3 py-1 rounded hover:bg-green-700"
              >
                Add Points
              </button>

              <button
                onClick={() => handleRedeemPoints(customer._id)}
                className="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700"
              >
                Redeem Points
              </button>

              <button
                onClick={() => handleDeleteCustomer(customer._id)}
                className="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-6">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 border rounded ${
                currentPage === page ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'
              }`}
            >
              {page}
            </button>
          ))}
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}

// Customer Tier Dashboard
function CustomerTierDashboard() {
  const {
    vipCustomers,
    goldCustomers,
    silverCustomers,
    bronzeCustomers,
    regularCustomers,
    fetchCustomers,
    fetchCustomersByTier
  } = useCustomersStore()

  useEffect(() => {
    fetchCustomers()
  }, [fetchCustomers])

  const handleTierFilter = async (tier: CustomerTier) => {
    await fetchCustomersByTier(tier)
  }

  const tierGroups = [
    { label: 'VIP', count: vipCustomers.length, color: 'purple', tier: 'VIP' as CustomerTier },
    { label: 'Gold', count: goldCustomers.length, color: 'yellow', tier: 'Gold' as CustomerTier },
    { label: 'Silver', count: silverCustomers.length, color: 'gray', tier: 'Silver' as CustomerTier },
    { label: 'Bronze', count: bronzeCustomers.length, color: 'orange', tier: 'Bronze' as CustomerTier },
    { label: 'Regular', count: regularCustomers.length, color: 'blue', tier: 'Regular' as CustomerTier }
  ]

  return (
    <div className="mb-8">
      <h3 className="text-xl font-bold mb-4">Customer Tier Overview</h3>
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        {tierGroups.map((group) => (
          <button
            key={group.tier}
            onClick={() => handleTierFilter(group.tier)}
            className={`p-4 rounded-lg text-center hover:opacity-80 transition-opacity ${
              group.color === 'purple' ? 'bg-purple-50 border border-purple-200' :
              group.color === 'yellow' ? 'bg-yellow-50 border border-yellow-200' :
              group.color === 'gray' ? 'bg-gray-50 border border-gray-200' :
              group.color === 'orange' ? 'bg-orange-50 border border-orange-200' :
              'bg-blue-50 border border-blue-200'
            }`}
          >
            <div className={`text-2xl font-bold ${
              group.color === 'purple' ? 'text-purple-800' :
              group.color === 'yellow' ? 'text-yellow-800' :
              group.color === 'gray' ? 'text-gray-800' :
              group.color === 'orange' ? 'text-orange-800' :
              'text-blue-800'
            }`}>
              {group.count}
            </div>
            <div className={`text-sm ${
              group.color === 'purple' ? 'text-purple-600' :
              group.color === 'yellow' ? 'text-yellow-600' :
              group.color === 'gray' ? 'text-gray-600' :
              group.color === 'orange' ? 'text-orange-600' :
              'text-blue-600'
            }`}>
              {group.label}
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}

// Customer Search Component
function CustomerSearch() {
  const { searchCustomers, setFilters, clearFilters } = useCustomersStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setLocalFilters] = useState({
    tier: '',
    isActive: '',
    preferredBranch: ''
  })

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    await searchCustomers(searchQuery, { page: 1, limit: 20 }, {
      ...(filters.tier && { tier: filters.tier as CustomerTier }),
      ...(filters.isActive && { isActive: filters.isActive === 'true' }),
      ...(filters.preferredBranch && { preferredBranch: filters.preferredBranch })
    })
  }

  const handleClearFilters = () => {
    setSearchQuery('')
    setLocalFilters({ tier: '', isActive: '', preferredBranch: '' })
    clearFilters()
  }

  return (
    <div className="mb-6">
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search customers by name, email, phone..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Search
          </button>
        </div>

        <div className="flex flex-wrap gap-2">
          <select
            value={filters.tier}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, tier: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Tiers</option>
            <option value="VIP">VIP</option>
            <option value="Gold">Gold</option>
            <option value="Silver">Silver</option>
            <option value="Bronze">Bronze</option>
            <option value="Regular">Regular</option>
          </select>

          <select
            value={filters.isActive}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, isActive: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Status</option>
            <option value="true">Active</option>
            <option value="false">Inactive</option>
          </select>

          <input
            type="text"
            value={filters.preferredBranch}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, preferredBranch: e.target.value }))}
            placeholder="Branch ID"
            className="px-3 py-1 border rounded text-sm"
          />

          <button
            type="button"
            onClick={handleClearFilters}
            className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
          >
            Clear Filters
          </button>
        </div>
      </form>
    </div>
  )
}

// Customer Analytics Component
function CustomerAnalytics() {
  const { 
    topCustomers, 
    recentCustomers, 
    inactiveCustomers,
    fetchTopCustomers, 
    fetchRecentCustomers, 
    fetchInactiveCustomers,
    calculateCustomerValue,
    calculateCustomerLifetimeValue,
    isLoading 
  } = useCustomersStore()

  useEffect(() => {
    fetchTopCustomers(5)
    fetchRecentCustomers(5)
    fetchInactiveCustomers(90)
  }, [fetchTopCustomers, fetchRecentCustomers, fetchInactiveCustomers])

  if (isLoading) {
    return <div>Loading analytics...</div>
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      {/* Top Customers */}
      <div className="bg-white p-4 rounded-lg border">
        <h3 className="text-lg font-bold mb-3">Top Customers</h3>
        <div className="space-y-2">
          {topCustomers.map((customer, index) => (
            <div key={customer._id} className="flex justify-between items-center">
              <div>
                <span className="font-medium">#{index + 1} {customer.firstName} {customer.lastName}</span>
                <div className="text-sm text-gray-500">
                  {customer.loyaltyPoints} points
                </div>
              </div>
              <div className="text-right">
                <div className="font-bold">MWK {calculateCustomerValue(customer).toLocaleString()}</div>
                <div className="text-xs text-gray-500">
                  LTV: MWK {calculateCustomerLifetimeValue(customer).toLocaleString()}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Customers */}
      <div className="bg-white p-4 rounded-lg border">
        <h3 className="text-lg font-bold mb-3">Recent Customers</h3>
        <div className="space-y-2">
          {recentCustomers.map((customer) => (
            <div key={customer._id} className="flex justify-between items-center">
              <div>
                <span className="font-medium">{customer.firstName} {customer.lastName}</span>
                <div className="text-sm text-gray-500">{customer.email}</div>
              </div>
              <div className="text-xs text-gray-500">
                {new Date(customer.createdAt).toLocaleDateString()}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Inactive Customers */}
      <div className="bg-white p-4 rounded-lg border">
        <h3 className="text-lg font-bold mb-3">Inactive Customers</h3>
        <div className="space-y-2">
          {inactiveCustomers.slice(0, 5).map((customer) => (
            <div key={customer._id} className="flex justify-between items-center">
              <div>
                <span className="font-medium">{customer.firstName} {customer.lastName}</span>
                <div className="text-sm text-gray-500">
                  {customer.lastOrderDate ? 
                    `Last order: ${new Date(customer.lastOrderDate).toLocaleDateString()}` :
                    'No orders'
                  }
                </div>
              </div>
              <div className="text-xs text-red-500">
                Inactive
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Customer Statistics Component
function CustomerStatistics() {
  const { customers, calculateCustomerValue, calculateCustomerLifetimeValue } = useCustomersStore()

  const totalCustomers = customers.length
  const activeCustomers = customers.filter(c => c.isActive).length
  const totalValue = customers.reduce((sum, customer) => sum + calculateCustomerValue(customer), 0)
  const averageValue = totalCustomers > 0 ? totalValue / totalCustomers : 0
  const totalLoyaltyPoints = customers.reduce((sum, customer) => sum + customer.loyaltyPoints, 0)

  return (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-blue-600">Total Customers</h4>
        <p className="text-2xl font-bold text-blue-900">{totalCustomers}</p>
      </div>
      
      <div className="bg-green-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-green-600">Active Customers</h4>
        <p className="text-2xl font-bold text-green-900">{activeCustomers}</p>
      </div>
      
      <div className="bg-purple-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-purple-600">Total Value</h4>
        <p className="text-2xl font-bold text-purple-900">
          MWK {totalValue.toLocaleString()}
        </p>
      </div>
      
      <div className="bg-orange-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-orange-600">Average Value</h4>
        <p className="text-2xl font-bold text-orange-900">
          MWK {Math.round(averageValue).toLocaleString()}
        </p>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-yellow-600">Total Points</h4>
        <p className="text-2xl font-bold text-yellow-900">
          {totalLoyaltyPoints.toLocaleString()}
        </p>
      </div>
    </div>
  )
}

// Main Customers Example Component
export default function CustomersExample() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">
          Customers Store Example
        </h1>
        
        <CustomerStatistics />
        <CustomerTierDashboard />
        <CustomerAnalytics />
        <CustomerSearch />
        <CustomerList />
      </div>
    </div>
  )
}
