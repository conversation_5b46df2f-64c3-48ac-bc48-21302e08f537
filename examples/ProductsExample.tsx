// examples/ProductsExample.tsx - Example usage of the products store

'use client'

import React, { useEffect, useState } from 'react'
import { useProductsStore } from '@/stores/productsStore'
import type { ProductCategory, ProductStatus, CreateProductData } from '@/types/frontend'

// Product List Component
function ProductList() {
  const {
    products,
    isLoading,
    error,
    currentPage,
    totalPages,
    totalProducts,
    fetchProducts,
    setCurrentPage,
    deleteProduct,
    clearError
  } = useProductsStore()

  useEffect(() => {
    fetchProducts()
  }, [fetchProducts])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchProducts({ page, limit: 20 })
  }

  const handleDeleteProduct = async (productId: string) => {
    if (confirm('Are you sure you want to delete this product?')) {
      const result = await deleteProduct(productId)
      if (result.success) {
        alert('Product deleted successfully!')
      } else {
        alert(`Failed to delete product: ${result.error}`)
      }
    }
  }

  if (isLoading) {
    return <div className="text-center py-8">Loading products...</div>
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Products ({totalProducts})</h2>
        {error && (
          <div className="text-red-600 bg-red-50 px-4 py-2 rounded flex items-center">
            {error}
            <button onClick={clearError} className="ml-2 text-red-800 hover:text-red-900">
              ×
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {products.map((product) => (
          <div key={product._id} className="border rounded-lg p-4 shadow-sm">
            <div className="flex justify-between items-start mb-2">
              <h3 className="font-semibold text-lg">{product.name}</h3>
              <span className={`px-2 py-1 rounded text-xs ${
                product.status === 'Available' ? 'bg-green-100 text-green-800' :
                product.status === 'Out of Stock' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {product.status}
              </span>
            </div>
            
            <p className="text-gray-600 text-sm mb-2">{product.category}</p>
            <p className="text-gray-800 mb-2">{product.description}</p>
            
            <div className="flex justify-between items-center mb-3">
              <span className="text-lg font-bold text-blue-600">
                MWK {product.price.toLocaleString()}
              </span>
              <span className={`text-sm ${
                product.stock <= product.minStockLevel ? 'text-red-600' : 'text-green-600'
              }`}>
                Stock: {product.stock}
              </span>
            </div>

            {product.isFeatured && (
              <span className="inline-block bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded mb-2">
                Featured
              </span>
            )}

            <div className="flex space-x-2">
              <button className="flex-1 bg-blue-600 text-white py-1 px-3 rounded text-sm hover:bg-blue-700">
                Edit
              </button>
              <button 
                onClick={() => handleDeleteProduct(product._id)}
                className="flex-1 bg-red-600 text-white py-1 px-3 rounded text-sm hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-6">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 border rounded ${
                currentPage === page ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'
              }`}
            >
              {page}
            </button>
          ))}
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}

// Category Filter Component
function CategoryFilter() {
  const {
    categories,
    selectedCategory,
    setSelectedCategory,
    fetchProductsByCategory,
    fetchProducts,
    clearFilters
  } = useProductsStore()

  const handleCategoryChange = async (category: ProductCategory | null) => {
    setSelectedCategory(category)
    if (category) {
      await fetchProductsByCategory(category)
    } else {
      await fetchProducts()
    }
  }

  return (
    <div className="mb-6">
      <h3 className="text-lg font-semibold mb-3">Filter by Category</h3>
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => handleCategoryChange(null)}
          className={`px-3 py-1 rounded ${
            !selectedCategory ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          All Categories
        </button>
        
        {categories.map((category) => (
          <button
            key={category}
            onClick={() => handleCategoryChange(category)}
            className={`px-3 py-1 rounded ${
              selectedCategory === category 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            {category}
          </button>
        ))}
      </div>
      
      {selectedCategory && (
        <button
          onClick={clearFilters}
          className="mt-2 text-sm text-blue-600 hover:text-blue-800"
        >
          Clear all filters
        </button>
      )}
    </div>
  )
}

// Search Component
function ProductSearch() {
  const { searchQuery, setSearchQuery, searchProducts } = useProductsStore()
  const [localQuery, setLocalQuery] = useState(searchQuery)

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    setSearchQuery(localQuery)
    await searchProducts(localQuery)
  }

  return (
    <form onSubmit={handleSearch} className="mb-6">
      <div className="flex space-x-2">
        <input
          type="text"
          value={localQuery}
          onChange={(e) => setLocalQuery(e.target.value)}
          placeholder="Search products..."
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Search
        </button>
      </div>
    </form>
  )
}

// Featured Products Component
function FeaturedProducts() {
  const { featuredProducts, fetchFeaturedProducts, isLoading } = useProductsStore()

  useEffect(() => {
    fetchFeaturedProducts()
  }, [fetchFeaturedProducts])

  if (isLoading) {
    return <div>Loading featured products...</div>
  }

  return (
    <div className="mb-8">
      <h3 className="text-xl font-bold mb-4">Featured Products</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {featuredProducts.map((product) => (
          <div key={product._id} className="border rounded-lg p-3 shadow-sm bg-yellow-50">
            <h4 className="font-semibold">{product.name}</h4>
            <p className="text-sm text-gray-600">{product.category}</p>
            <p className="text-lg font-bold text-blue-600 mt-2">
              MWK {product.price.toLocaleString()}
            </p>
            <span className="inline-block bg-yellow-200 text-yellow-800 text-xs px-2 py-1 rounded mt-1">
              Featured
            </span>
          </div>
        ))}
      </div>
    </div>
  )
}

// Low Stock Alert Component
function LowStockAlert() {
  const { lowStockProducts, fetchLowStockProducts, isProductLowStock } = useProductsStore()

  useEffect(() => {
    fetchLowStockProducts()
  }, [fetchLowStockProducts])

  if (lowStockProducts.length === 0) {
    return null
  }

  return (
    <div className="mb-6 bg-orange-50 border border-orange-200 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-orange-800 mb-2">
        Low Stock Alert ({lowStockProducts.length} items)
      </h3>
      <div className="space-y-2">
        {lowStockProducts.slice(0, 5).map((product) => (
          <div key={product._id} className="flex justify-between items-center text-sm">
            <span>{product.name}</span>
            <span className="text-orange-600 font-medium">
              {product.stock} left (min: {product.minStockLevel})
            </span>
          </div>
        ))}
        {lowStockProducts.length > 5 && (
          <p className="text-sm text-orange-600">
            ...and {lowStockProducts.length - 5} more items
          </p>
        )}
      </div>
    </div>
  )
}

// Statistics Component
function ProductStatistics() {
  const { products, calculateTotalValue, getProductsByStatus } = useProductsStore()

  const totalValue = calculateTotalValue()
  const availableProducts = getProductsByStatus('Available').length
  const outOfStockProducts = getProductsByStatus('Out of Stock').length

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-blue-600">Total Products</h4>
        <p className="text-2xl font-bold text-blue-900">{products.length}</p>
      </div>
      
      <div className="bg-green-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-green-600">Available</h4>
        <p className="text-2xl font-bold text-green-900">{availableProducts}</p>
      </div>
      
      <div className="bg-red-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-red-600">Out of Stock</h4>
        <p className="text-2xl font-bold text-red-900">{outOfStockProducts}</p>
      </div>
      
      <div className="bg-purple-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-purple-600">Total Value</h4>
        <p className="text-2xl font-bold text-purple-900">
          MWK {totalValue.toLocaleString()}
        </p>
      </div>
    </div>
  )
}

// Main Products Example Component
export default function ProductsExample() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">
          Products Store Example
        </h1>
        
        <ProductStatistics />
        <LowStockAlert />
        <FeaturedProducts />
        <ProductSearch />
        <CategoryFilter />
        <ProductList />
      </div>
    </div>
  )
}
