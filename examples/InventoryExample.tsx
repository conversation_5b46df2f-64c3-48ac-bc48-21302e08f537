// examples/InventoryExample.tsx - Example usage of the inventory store

'use client'

import React, { useEffect, useState } from 'react'
import { useInventoryStore } from '@/stores/inventoryStore'
import type { InventoryItem, StockMovementType } from '@/types/frontend'

// Inventory List Component
function InventoryList() {
  const {
    inventoryItems,
    isLoading,
    error,
    currentPage,
    totalPages,
    totalItems,
    fetchInventoryItems,
    setCurrentPage,
    updateStock,
    transferStock,
    reserveStock,
    releaseStock,
    updateReorderPoint,
    deleteInventoryItem,
    clearError,
    getStockLevel,
    getAvailableStock,
    formatStockStatus,
    isLowStock,
    isCriticalStock,
    isOutOfStock
  } = useInventoryStore()

  useEffect(() => {
    fetchInventoryItems()
  }, [fetchInventoryItems])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchInventoryItems({ page, limit: 20 })
  }

  const handleStockUpdate = async (itemId: string, branchId: string) => {
    const quantity = prompt('Enter quantity to add/remove (use negative for removal):')
    if (quantity && !isNaN(Number(quantity))) {
      const result = await updateStock(itemId, Number(quantity), branchId, 'Manual adjustment')
      if (result.success) {
        alert('Stock updated successfully!')
      } else {
        alert(`Failed to update stock: ${result.error}`)
      }
    }
  }

  const handleStockTransfer = async (itemId: string, fromBranchId: string) => {
    const toBranchId = prompt('Enter destination branch ID:')
    const quantity = prompt('Enter quantity to transfer:')
    
    if (toBranchId && quantity && !isNaN(Number(quantity))) {
      const result = await transferStock(itemId, fromBranchId, toBranchId, Number(quantity), 'Branch transfer')
      if (result.success) {
        alert('Stock transferred successfully!')
      } else {
        alert(`Failed to transfer stock: ${result.error}`)
      }
    }
  }

  const handleReserveStock = async (itemId: string, branchId: string) => {
    const quantity = prompt('Enter quantity to reserve:')
    const orderId = prompt('Enter order ID (optional):')
    
    if (quantity && !isNaN(Number(quantity))) {
      const result = await reserveStock(itemId, Number(quantity), branchId, orderId || undefined)
      if (result.success) {
        alert('Stock reserved successfully!')
      } else {
        alert(`Failed to reserve stock: ${result.error}`)
      }
    }
  }

  const handleReleaseStock = async (itemId: string, branchId: string) => {
    const quantity = prompt('Enter quantity to release:')
    const orderId = prompt('Enter order ID (optional):')
    
    if (quantity && !isNaN(Number(quantity))) {
      const result = await releaseStock(itemId, Number(quantity), branchId, orderId || undefined)
      if (result.success) {
        alert('Stock released successfully!')
      } else {
        alert(`Failed to release stock: ${result.error}`)
      }
    }
  }

  const handleUpdateReorderPoint = async (itemId: string, branchId: string) => {
    const reorderPoint = prompt('Enter new reorder point:')
    
    if (reorderPoint && !isNaN(Number(reorderPoint))) {
      const result = await updateReorderPoint(itemId, branchId, Number(reorderPoint))
      if (result.success) {
        alert('Reorder point updated successfully!')
      } else {
        alert(`Failed to update reorder point: ${result.error}`)
      }
    }
  }

  const handleDeleteItem = async (itemId: string) => {
    if (confirm('Are you sure you want to delete this inventory item?')) {
      const result = await deleteInventoryItem(itemId)
      if (result.success) {
        alert('Inventory item deleted successfully!')
      } else {
        alert(`Failed to delete item: ${result.error}`)
      }
    }
  }

  if (isLoading) {
    return <div className="text-center py-8">Loading inventory...</div>
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Inventory ({totalItems})</h2>
        {error && (
          <div className="text-red-600 bg-red-50 px-4 py-2 rounded flex items-center">
            {error}
            <button onClick={clearError} className="ml-2 text-red-800 hover:text-red-900">
              ×
            </button>
          </div>
        )}
      </div>

      <div className="space-y-4">
        {inventoryItems.map((item) => (
          <div key={item._id} className="border rounded-lg p-4 shadow-sm">
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="font-semibold text-lg">{item.productName}</h3>
                <p className="text-gray-600 text-sm">{item.sku}</p>
                <p className="text-sm text-gray-500">
                  Category: {item.category} | Supplier: {item.supplierName}
                </p>
                <p className="text-sm text-gray-500">
                  Cost: MWK {item.costPrice.toLocaleString()} | 
                  Selling: MWK {item.sellingPrice.toLocaleString()}
                </p>
              </div>
              
              <div className="text-right">
                <p className="text-lg font-bold text-blue-600">
                  Total: {item.totalQuantity} units
                </p>
                <p className="text-sm text-gray-500">
                  Reserved: {item.totalReserved} units
                </p>
                <p className="text-xs text-gray-400">
                  Available: {item.totalQuantity - item.totalReserved} units
                </p>
              </div>
            </div>

            {/* Branch Stock Information */}
            <div className="mb-3">
              <h4 className="font-medium text-sm mb-2">Branch Stock:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {item.branchStock.map((branchStock) => {
                  const stockLevel = getStockLevel(item, branchStock.branchId)
                  const availableStock = getAvailableStock(item, branchStock.branchId)
                  const stockStatus = formatStockStatus(item, branchStock.branchId)
                  const isLow = isLowStock(item, branchStock.branchId)
                  const isCritical = isCriticalStock(item, branchStock.branchId)
                  const isOut = isOutOfStock(item, branchStock.branchId)

                  return (
                    <div key={branchStock.branchId} className="border rounded p-2 text-xs">
                      <div className="font-medium">{branchStock.branchName}</div>
                      <div>Location: {branchStock.location}</div>
                      <div>Stock: {stockLevel} / Available: {availableStock}</div>
                      <div>Reserved: {branchStock.reservedQuantity}</div>
                      <div>Reorder Point: {branchStock.reorderPoint}</div>
                      <div>Max Stock: {branchStock.maxStock}</div>
                      
                      <div className={`mt-1 px-2 py-1 rounded text-center ${
                        isOut ? 'bg-red-100 text-red-800' :
                        isCritical ? 'bg-orange-100 text-orange-800' :
                        isLow ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {stockStatus}
                      </div>

                      <div className="flex flex-wrap gap-1 mt-2">
                        <button
                          onClick={() => handleStockUpdate(item._id, branchStock.branchId)}
                          className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                        >
                          Update
                        </button>
                        <button
                          onClick={() => handleStockTransfer(item._id, branchStock.branchId)}
                          className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                        >
                          Transfer
                        </button>
                        <button
                          onClick={() => handleReserveStock(item._id, branchStock.branchId)}
                          className="text-xs bg-yellow-600 text-white px-2 py-1 rounded hover:bg-yellow-700"
                        >
                          Reserve
                        </button>
                        <button
                          onClick={() => handleReleaseStock(item._id, branchStock.branchId)}
                          className="text-xs bg-gray-600 text-white px-2 py-1 rounded hover:bg-gray-700"
                        >
                          Release
                        </button>
                        <button
                          onClick={() => handleUpdateReorderPoint(item._id, branchStock.branchId)}
                          className="text-xs bg-purple-600 text-white px-2 py-1 rounded hover:bg-purple-700"
                        >
                          Reorder
                        </button>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Item Details */}
            <div className="text-xs text-gray-500 mb-3">
              {item.batchNumber && <span>Batch: {item.batchNumber} | </span>}
              {item.expiryDate && <span>Expires: {new Date(item.expiryDate).toLocaleDateString()} | </span>}
              {item.lastRestockDate && <span>Last Restock: {new Date(item.lastRestockDate).toLocaleDateString()}</span>}
            </div>

            <div className="flex justify-end">
              <button
                onClick={() => handleDeleteItem(item._id)}
                className="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
              >
                Delete Item
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-6">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 border rounded ${
                currentPage === page ? 'bg-blue-600 text-white' : 'hover:bg-gray-100'
              }`}
            >
              {page}
            </button>
          ))}
          
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
}

// Stock Alerts Component
function StockAlerts() {
  const {
    lowStockAlerts,
    criticalStockItems,
    fetchLowStockAlerts,
    fetchCriticalStockItems,
    dismissAlert,
    getLowStockItems,
    getCriticalStockItems,
    getOutOfStockItems
  } = useInventoryStore()

  useEffect(() => {
    fetchLowStockAlerts()
    fetchCriticalStockItems()
  }, [fetchLowStockAlerts, fetchCriticalStockItems])

  const handleDismissAlert = async (alertId: string) => {
    const result = await dismissAlert(alertId)
    if (result.success) {
      alert('Alert dismissed!')
    } else {
      alert(`Failed to dismiss alert: ${result.error}`)
    }
  }

  const lowStockItems = getLowStockItems()
  const criticalItems = getCriticalStockItems()
  const outOfStockItems = getOutOfStockItems()

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      {/* Low Stock Alerts */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="text-lg font-bold text-yellow-800 mb-3">
          Low Stock ({lowStockItems.length})
        </h3>
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {lowStockAlerts.map((alert) => (
            <div key={alert._id} className="flex justify-between items-center text-sm">
              <div>
                <div className="font-medium">{alert.productName}</div>
                <div className="text-yellow-600">
                  {alert.branchName}: {alert.currentQuantity} / {alert.reorderPoint}
                </div>
              </div>
              <button
                onClick={() => handleDismissAlert(alert._id)}
                className="text-yellow-800 hover:text-yellow-900"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Critical Stock */}
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-lg font-bold text-red-800 mb-3">
          Critical Stock ({criticalItems.length})
        </h3>
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {criticalItems.map((item) => (
            <div key={item._id} className="text-sm">
              <div className="font-medium">{item.productName}</div>
              <div className="text-red-600">
                Total: {item.totalQuantity} units
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Out of Stock */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-bold text-gray-800 mb-3">
          Out of Stock ({outOfStockItems.length})
        </h3>
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {outOfStockItems.map((item) => (
            <div key={item._id} className="text-sm">
              <div className="font-medium">{item.productName}</div>
              <div className="text-gray-600">
                SKU: {item.sku}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// Stock Movements Component
function StockMovements() {
  const {
    recentMovements,
    fetchRecentMovements,
    getMovementTypeColor,
    getStockMovementsByType
  } = useInventoryStore()

  useEffect(() => {
    fetchRecentMovements(20)
  }, [fetchRecentMovements])

  const movementTypes: StockMovementType[] = ['Stock In', 'Stock Out', 'Transfer In', 'Transfer Out', 'Adjustment', 'Reserved', 'Released']

  return (
    <div className="bg-white border rounded-lg p-4 mb-6">
      <h3 className="text-lg font-bold mb-3">Recent Stock Movements</h3>
      
      {/* Movement Type Filters */}
      <div className="flex flex-wrap gap-2 mb-4">
        {movementTypes.map((type) => {
          const count = getStockMovementsByType(type).length
          const color = getMovementTypeColor(type)
          
          return (
            <span
              key={type}
              className={`px-2 py-1 rounded text-xs ${
                color === 'green' ? 'bg-green-100 text-green-800' :
                color === 'red' ? 'bg-red-100 text-red-800' :
                color === 'blue' ? 'bg-blue-100 text-blue-800' :
                color === 'orange' ? 'bg-orange-100 text-orange-800' :
                color === 'purple' ? 'bg-purple-100 text-purple-800' :
                color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}
            >
              {type} ({count})
            </span>
          )
        })}
      </div>

      <div className="space-y-2 max-h-60 overflow-y-auto">
        {recentMovements.map((movement) => {
          const color = getMovementTypeColor(movement.type)
          
          return (
            <div key={movement._id} className="flex justify-between items-center p-2 border rounded">
              <div>
                <div className="font-medium">{movement.productName}</div>
                <div className="text-sm text-gray-600">
                  {movement.branchName} | {movement.reason}
                </div>
                {movement.reference && (
                  <div className="text-xs text-gray-500">Ref: {movement.reference}</div>
                )}
              </div>
              
              <div className="text-right">
                <div className={`px-2 py-1 rounded text-xs ${
                  color === 'green' ? 'bg-green-100 text-green-800' :
                  color === 'red' ? 'bg-red-100 text-red-800' :
                  color === 'blue' ? 'bg-blue-100 text-blue-800' :
                  color === 'orange' ? 'bg-orange-100 text-orange-800' :
                  color === 'purple' ? 'bg-purple-100 text-purple-800' :
                  color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {movement.type}
                </div>
                <div className="text-sm font-medium mt-1">
                  {movement.type.includes('Out') || movement.type === 'Reserved' ? '-' : '+'}
                  {movement.quantity} units
                </div>
                <div className="text-xs text-gray-500">
                  {movement.previousQuantity} → {movement.newQuantity}
                </div>
                <div className="text-xs text-gray-400">
                  {new Date(movement.createdAt).toLocaleDateString()}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

// Inventory Statistics Component
function InventoryStatistics() {
  const { 
    inventoryItems, 
    getTotalStockValue,
    getLowStockItems,
    getCriticalStockItems,
    getOutOfStockItems
  } = useInventoryStore()

  const totalItems = inventoryItems.length
  const totalValue = getTotalStockValue()
  const totalQuantity = inventoryItems.reduce((sum, item) => sum + item.totalQuantity, 0)
  const totalReserved = inventoryItems.reduce((sum, item) => sum + item.totalReserved, 0)
  const lowStockCount = getLowStockItems().length
  const criticalStockCount = getCriticalStockItems().length
  const outOfStockCount = getOutOfStockItems().length

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-6">
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-blue-600">Total Items</h4>
        <p className="text-2xl font-bold text-blue-900">{totalItems}</p>
      </div>
      
      <div className="bg-green-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-green-600">Total Value</h4>
        <p className="text-2xl font-bold text-green-900">
          MWK {totalValue.toLocaleString()}
        </p>
      </div>
      
      <div className="bg-purple-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-purple-600">Total Quantity</h4>
        <p className="text-2xl font-bold text-purple-900">{totalQuantity}</p>
      </div>
      
      <div className="bg-orange-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-orange-600">Reserved</h4>
        <p className="text-2xl font-bold text-orange-900">{totalReserved}</p>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-yellow-600">Low Stock</h4>
        <p className="text-2xl font-bold text-yellow-900">{lowStockCount}</p>
      </div>

      <div className="bg-red-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-red-600">Critical</h4>
        <p className="text-2xl font-bold text-red-900">{criticalStockCount}</p>
      </div>

      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-600">Out of Stock</h4>
        <p className="text-2xl font-bold text-gray-900">{outOfStockCount}</p>
      </div>
    </div>
  )
}

// Inventory Search Component
function InventorySearch() {
  const { searchInventory, setFilters, clearFilters, fetchInventoryByBranch } = useInventoryStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [filters, setLocalFilters] = useState({
    category: '',
    branchId: '',
    supplierId: '',
    stockStatus: ''
  })

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault()
    await searchInventory(searchQuery, { page: 1, limit: 20 }, {
      ...(filters.category && { category: filters.category }),
      ...(filters.branchId && { branchId: filters.branchId }),
      ...(filters.supplierId && { supplierId: filters.supplierId }),
      ...(filters.stockStatus && { stockStatus: filters.stockStatus })
    })
  }

  const handleBranchFilter = async (branchId: string) => {
    if (branchId) {
      await fetchInventoryByBranch(branchId)
    }
  }

  const handleClearFilters = () => {
    setSearchQuery('')
    setLocalFilters({ category: '', branchId: '', supplierId: '', stockStatus: '' })
    clearFilters()
  }

  return (
    <div className="mb-6">
      <form onSubmit={handleSearch} className="space-y-4">
        <div className="flex space-x-2">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search inventory by name, SKU, category..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Search
          </button>
        </div>

        <div className="flex flex-wrap gap-2">
          <select
            value={filters.category}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, category: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Categories</option>
            <option value="Electronics">Electronics</option>
            <option value="Clothing">Clothing</option>
            <option value="Books">Books</option>
            <option value="Home">Home</option>
          </select>

          <select
            value={filters.branchId}
            onChange={(e) => {
              setLocalFilters(prev => ({ ...prev, branchId: e.target.value }))
              handleBranchFilter(e.target.value)
            }}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Branches</option>
            <option value="branch-123">Main Branch</option>
            <option value="branch-124">North Branch</option>
            <option value="branch-125">South Branch</option>
          </select>

          <select
            value={filters.stockStatus}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, stockStatus: e.target.value }))}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="">All Stock Status</option>
            <option value="In Stock">In Stock</option>
            <option value="Low Stock">Low Stock</option>
            <option value="Critical">Critical</option>
            <option value="Out of Stock">Out of Stock</option>
          </select>

          <input
            type="text"
            value={filters.supplierId}
            onChange={(e) => setLocalFilters(prev => ({ ...prev, supplierId: e.target.value }))}
            placeholder="Supplier ID"
            className="px-3 py-1 border rounded text-sm"
          />

          <button
            type="button"
            onClick={handleClearFilters}
            className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
          >
            Clear Filters
          </button>
        </div>
      </form>
    </div>
  )
}

// Main Inventory Example Component
export default function InventoryExample() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-8">
          Inventory Store Example
        </h1>
        
        <InventoryStatistics />
        <StockAlerts />
        <StockMovements />
        <InventorySearch />
        <InventoryList />
      </div>
    </div>
  )
}
