// services/frontend/employeeService.ts - Employee management service

import { apiClient, buildQueryParams } from './api-client'
import type {
  ApiResponse,
  PaginationParams,
  Employee,
  CreateEmployeeData,
  UpdateEmployeeData,
  EmployeeFilters
} from '@/types/frontend'

// ============================================================================
// Employee Service Class
// ============================================================================

class EmployeeService {
  private readonly baseEndpoint = '/api/employees'

  /**
   * Get all employees with pagination and filtering
   */
  async getEmployees(
    pagination: PaginationParams,
    filters: EmployeeFilters = {}
  ): Promise<ApiResponse<Employee[]>> {
    try {
      const params = buildQueryParams(pagination, filters)
      console.log('EmployeeService: Calling API with params:', params)
      const response = await apiClient.get<Employee[]>(this.baseEndpoint, params)
      console.log('🔥 EmployeeService: API response received!', {
        success: response.success,
        dataLength: response.data?.length || 0,
        error: response.error,
        hasData: !!response.data,
        actualData: response.data
      })
      return response
    } catch (error) {
      console.error('EmployeeService.getEmployees error:', error)
      throw error
    }
  }

  /**
   * Get employee by ID
   */
  async getEmployeeById(employeeId: string): Promise<ApiResponse<Employee>> {
    try {
      return await apiClient.get<Employee>(`${this.baseEndpoint}/${employeeId}`)
    } catch (error) {
      console.error('EmployeeService.getEmployeeById error:', error)
      throw error
    }
  }

  /**
   * Create new employee
   */
  async createEmployee(employeeData: CreateEmployeeData): Promise<ApiResponse<Employee>> {
    try {
      console.log('=== EMPLOYEE SERVICE CREATE START ===')
      console.log('EmployeeService: Creating employee with data:', JSON.stringify(employeeData, null, 2))
      console.log('EmployeeService: Base endpoint:', this.baseEndpoint)
      console.log('EmployeeService: Full URL will be constructed by apiClient')
      console.log('EmployeeService: About to call apiClient.post...')

      const response = await apiClient.post<Employee>(this.baseEndpoint, employeeData)

      console.log('EmployeeService: API response received:', JSON.stringify(response, null, 2))
      console.log('EmployeeService: Response success:', response.success)
      console.log('EmployeeService: Response data:', response.data)
      console.log('EmployeeService: Response error:', response.error)
      console.log('=== EMPLOYEE SERVICE CREATE END ===')

      return response
    } catch (error) {
      console.error('=== EMPLOYEE SERVICE CREATE ERROR ===')
      console.error('EmployeeService.createEmployee error:', error)
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      })
      throw error
    }
  }

  /**
   * Update employee
   */
  async updateEmployee(employeeId: string, updates: UpdateEmployeeData): Promise<ApiResponse<Employee>> {
    try {
      return await apiClient.put<Employee>(`${this.baseEndpoint}/${employeeId}`, updates)
    } catch (error) {
      console.error('EmployeeService.updateEmployee error:', error)
      throw error
    }
  }

  /**
   * Delete employee (soft delete)
   */
  async deleteEmployee(employeeId: string): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.delete<boolean>(`${this.baseEndpoint}/${employeeId}`)
    } catch (error) {
      console.error('EmployeeService.deleteEmployee error:', error)
      throw error
    }
  }

  /**
   * Get employees by branch
   */
  async getEmployeesByBranch(
    branchId: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Employee[]>> {
    try {
      const params = buildQueryParams(pagination, { branchId })
      return await apiClient.get<Employee[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('EmployeeService.getEmployeesByBranch error:', error)
      throw error
    }
  }

  /**
   * Get employees by department
   */
  async getEmployeesByDepartment(
    department: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Employee[]>> {
    try {
      const params = buildQueryParams(pagination, { department })
      return await apiClient.get<Employee[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('EmployeeService.getEmployeesByDepartment error:', error)
      throw error
    }
  }

  /**
   * Search employees
   */
  async searchEmployees(
    query: string,
    pagination: PaginationParams = { page: 1, limit: 20 },
    filters: Omit<EmployeeFilters, 'search'> = {}
  ): Promise<ApiResponse<Employee[]>> {
    try {
      const params = buildQueryParams(pagination, { ...filters, search: query })
      return await apiClient.get<Employee[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('EmployeeService.searchEmployees error:', error)
      throw error
    }
  }

  /**
   * Get active employees only
   */
  async getActiveEmployees(
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Employee[]>> {
    try {
      const params = buildQueryParams(pagination, { isActive: true })
      return await apiClient.get<Employee[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('EmployeeService.getActiveEmployees error:', error)
      throw error
    }
  }

  /**
   * Get common departments
   */
  getDepartments(): string[] {
    return [
      'Sales',
      'Technical Support',
      'Administration',
      'Finance',
      'Marketing',
      'Human Resources',
      'Operations',
      'Customer Service',
      'IT Support',
      'Management'
    ]
  }

  /**
   * Get common positions
   */
  getPositions(): string[] {
    return [
      'Sales Representative',
      'Sales Manager',
      'Technical Specialist',
      'Customer Service Representative',
      'Branch Manager',
      'Assistant Manager',
      'Accountant',
      'Marketing Coordinator',
      'IT Technician',
      'Operations Coordinator',
      'HR Specialist',
      'Administrative Assistant'
    ]
  }

  /**
   * Validate employee data before submission
   */
  validateEmployeeData(employeeData: CreateEmployeeData | UpdateEmployeeData): {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  } {
    const errors: Array<{ field: string; message: string }> = []

    // First name validation
    if ('firstName' in employeeData && (!employeeData.firstName || employeeData.firstName.trim().length < 1)) {
      errors.push({ field: 'firstName', message: 'First name is required' })
    }

    // Last name validation
    if ('lastName' in employeeData && (!employeeData.lastName || employeeData.lastName.trim().length < 1)) {
      errors.push({ field: 'lastName', message: 'Last name is required' })
    }

    // Email validation
    if ('email' in employeeData && employeeData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(employeeData.email)) {
        errors.push({ field: 'email', message: 'Please enter a valid email address' })
      }
    }

    // Phone validation
    if ('phone' in employeeData && employeeData.phone) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
      if (!phoneRegex.test(employeeData.phone)) {
        errors.push({ field: 'phone', message: 'Please enter a valid phone number' })
      }
    }

    // Position validation
    if ('position' in employeeData && (!employeeData.position || employeeData.position.trim().length < 1)) {
      errors.push({ field: 'position', message: 'Position is required' })
    }

    // Department validation
    if ('department' in employeeData && (!employeeData.department || employeeData.department.trim().length < 1)) {
      errors.push({ field: 'department', message: 'Department is required' })
    }

    // Salary validation
    if ('salary' in employeeData && (employeeData.salary === undefined || employeeData.salary < 0)) {
      errors.push({ field: 'salary', message: 'Salary must be a positive number' })
    }

    // Hire date validation
    if ('hireDate' in employeeData && employeeData.hireDate) {
      const hireDate = new Date(employeeData.hireDate)
      const today = new Date()
      if (hireDate > today) {
        errors.push({ field: 'hireDate', message: 'Hire date cannot be in the future' })
      }
    }

    // Address validation
    if ('address' in employeeData && employeeData.address) {
      const { address } = employeeData
      if (!address.street || address.street.trim().length < 1) {
        errors.push({ field: 'address.street', message: 'Street address is required' })
      }
      if (!address.city || address.city.trim().length < 1) {
        errors.push({ field: 'address.city', message: 'City is required' })
      }
      if (!address.region || address.region.trim().length < 1) {
        errors.push({ field: 'address.region', message: 'Region is required' })
      }
      if (!address.country || address.country.trim().length < 1) {
        errors.push({ field: 'address.country', message: 'Country is required' })
      }
    }

    // Emergency contact validation
    if ('emergencyContact' in employeeData && employeeData.emergencyContact) {
      const { emergencyContact } = employeeData
      if (!emergencyContact.name || emergencyContact.name.trim().length < 1) {
        errors.push({ field: 'emergencyContact.name', message: 'Emergency contact name is required' })
      }
      if (!emergencyContact.relationship || emergencyContact.relationship.trim().length < 1) {
        errors.push({ field: 'emergencyContact.relationship', message: 'Emergency contact relationship is required' })
      }
      if (!emergencyContact.phone || emergencyContact.phone.trim().length < 1) {
        errors.push({ field: 'emergencyContact.phone', message: 'Emergency contact phone is required' })
      } else {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
        if (!phoneRegex.test(emergencyContact.phone)) {
          errors.push({ field: 'emergencyContact.phone', message: 'Please enter a valid emergency contact phone number' })
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Format employee full name
   */
  formatEmployeeName(employee: Employee): string {
    return `${employee.firstName} ${employee.lastName}`.trim()
  }

  /**
   * Format employee address
   */
  formatEmployeeAddress(employee: Employee): string {
    const { address } = employee
    return `${address.street}, ${address.city}, ${address.region}, ${address.country} ${address.postalCode}`
  }

  /**
   * Calculate years of service
   */
  calculateYearsOfService(hireDate: string): number {
    const hire = new Date(hireDate)
    const today = new Date()
    const diffTime = Math.abs(today.getTime() - hire.getTime())
    const diffYears = diffTime / (1000 * 60 * 60 * 24 * 365.25)
    return Math.floor(diffYears)
  }

  /**
   * Format salary for display
   */
  formatSalary(salary: number, currency: string = 'MWK'): string {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(salary)
  }

  /**
   * Get employee status badge color
   */
  getEmployeeStatusColor(isActive: boolean): string {
    return isActive ? 'green' : 'red'
  }

  /**
   * Get employee status text
   */
  getEmployeeStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive'
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const employeeService = new EmployeeService()
export default employeeService
