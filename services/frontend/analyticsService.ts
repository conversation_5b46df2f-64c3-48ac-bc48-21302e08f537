// services/frontend/analyticsService.ts - Analytics and reporting service

import { apiClient, buildQueryParams } from './api-client'
import type {
  ApiResponse,
  DashboardMetrics,
  SalesAnalytics
} from '@/types/frontend'

// ============================================================================
// Analytics Service Class
// ============================================================================

class AnalyticsService {
  private readonly baseEndpoint = 'analytics'

  /**
   * Get dashboard metrics
   */
  async getDashboardMetrics(
    branchId?: string,
    period: number = 30,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<DashboardMetrics>> {
    try {
      const params: Record<string, string | number> = { period }
      if (branchId) params.branchId = branchId
      if (startDate) params.startDate = startDate
      if (endDate) params.endDate = endDate

      return await apiClient.get<DashboardMetrics>(`${this.baseEndpoint}/dashboard`, params)
    } catch (error) {
      console.error('AnalyticsService.getDashboardMetrics error:', error)
      throw error
    }
  }

  /**
   * Get sales analytics
   */
  async getSalesAnalytics(
    branchId?: string,
    startDate?: string,
    endDate?: string,
    groupBy: string = 'day',
    category?: string
  ): Promise<ApiResponse<SalesAnalytics>> {
    try {
      const params: Record<string, string> = { groupBy }
      if (branchId) params.branchId = branchId
      if (startDate) params.startDate = startDate
      if (endDate) params.endDate = endDate
      if (category) params.category = category

      return await apiClient.get<SalesAnalytics>(`${this.baseEndpoint}/sales`, params)
    } catch (error) {
      console.error('AnalyticsService.getSalesAnalytics error:', error)
      throw error
    }
  }

  /**
   * Get inventory analytics
   */
  async getInventoryAnalytics(branchId?: string): Promise<ApiResponse<{
    totalItems: number
    totalValue: number
    lowStockItems: number
    outOfStockItems: number
    topCategories: Array<{
      category: string
      itemCount: number
      totalValue: number
    }>
    stockDistribution: Array<{
      status: string
      count: number
      percentage: number
    }>
  }>> {
    try {
      const params: Record<string, string> = {}
      if (branchId) params.branchId = branchId

      return await apiClient.get(`${this.baseEndpoint}/inventory`, params)
    } catch (error) {
      console.error('AnalyticsService.getInventoryAnalytics error:', error)
      throw error
    }
  }

  /**
   * Get customer analytics
   */
  async getCustomerAnalytics(branchId?: string): Promise<ApiResponse<{
    totalCustomers: number
    activeCustomers: number
    newCustomersThisMonth: number
    averageOrderValue: number
    customerGrowth: number
    topCustomers: Array<{
      _id: string
      name: string
      totalSpent: number
      orderCount: number
    }>
    customersByTier: Array<{
      tier: string
      count: number
      percentage: number
    }>
  }>> {
    try {
      const params: Record<string, string> = {}
      if (branchId) params.branchId = branchId

      return await apiClient.get(`${this.baseEndpoint}/customers`, params)
    } catch (error) {
      console.error('AnalyticsService.getCustomerAnalytics error:', error)
      throw error
    }
  }

  /**
   * Get employee analytics
   */
  async getEmployeeAnalytics(branchId?: string): Promise<ApiResponse<{
    totalEmployees: number
    activeEmployees: number
    employeesByDepartment: Array<{
      department: string
      count: number
      percentage: number
    }>
    employeesByPosition: Array<{
      position: string
      count: number
      averageSalary: number
    }>
    averageTenure: number
    salaryDistribution: Array<{
      range: string
      count: number
      percentage: number
    }>
  }>> {
    try {
      const params: Record<string, string> = {}
      if (branchId) params.branchId = branchId

      return await apiClient.get(`${this.baseEndpoint}/employees`, params)
    } catch (error) {
      console.error('AnalyticsService.getEmployeeAnalytics error:', error)
      throw error
    }
  }

  /**
   * Get financial analytics
   */
  async getFinancialAnalytics(
    branchId?: string,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<{
    revenue: {
      total: number
      growth: number
      trend: Array<{ date: string; amount: number }>
    }
    expenses: {
      total: number
      breakdown: Array<{ category: string; amount: number; percentage: number }>
    }
    profit: {
      total: number
      margin: number
      trend: Array<{ date: string; amount: number }>
    }
    cashFlow: {
      inflow: number
      outflow: number
      net: number
    }
  }>> {
    try {
      const params: Record<string, string> = {}
      if (branchId) params.branchId = branchId
      if (startDate) params.startDate = startDate
      if (endDate) params.endDate = endDate

      return await apiClient.get(`${this.baseEndpoint}/financial`, params)
    } catch (error) {
      console.error('AnalyticsService.getFinancialAnalytics error:', error)
      throw error
    }
  }

  /**
   * Export analytics data
   */
  async exportAnalytics(
    type: 'sales' | 'inventory' | 'customers' | 'employees' | 'financial',
    format: 'csv' | 'excel' | 'pdf',
    branchId?: string,
    startDate?: string,
    endDate?: string
  ): Promise<Blob> {
    try {
      const params: Record<string, string> = { format }
      if (branchId) params.branchId = branchId
      if (startDate) params.startDate = startDate
      if (endDate) params.endDate = endDate

      const response = await fetch(
        `${this.baseEndpoint}/${type}/export?${new URLSearchParams(params)}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.getAuthToken()}`
          }
        }
      )

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`)
      }

      return await response.blob()
    } catch (error) {
      console.error('AnalyticsService.exportAnalytics error:', error)
      throw error
    }
  }

  /**
   * Get available date ranges
   */
  getDateRanges(): Array<{ value: string; label: string; days: number }> {
    return [
      { value: 'today', label: 'Today', days: 1 },
      { value: 'yesterday', label: 'Yesterday', days: 1 },
      { value: 'last_7_days', label: 'Last 7 Days', days: 7 },
      { value: 'last_30_days', label: 'Last 30 Days', days: 30 },
      { value: 'last_90_days', label: 'Last 90 Days', days: 90 },
      { value: 'this_month', label: 'This Month', days: 30 },
      { value: 'last_month', label: 'Last Month', days: 30 },
      { value: 'this_quarter', label: 'This Quarter', days: 90 },
      { value: 'last_quarter', label: 'Last Quarter', days: 90 },
      { value: 'this_year', label: 'This Year', days: 365 },
      { value: 'last_year', label: 'Last Year', days: 365 },
      { value: 'custom', label: 'Custom Range', days: 0 }
    ]
  }

  /**
   * Get grouping options for analytics
   */
  getGroupingOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'hour', label: 'Hourly' },
      { value: 'day', label: 'Daily' },
      { value: 'week', label: 'Weekly' },
      { value: 'month', label: 'Monthly' },
      { value: 'quarter', label: 'Quarterly' },
      { value: 'year', label: 'Yearly' }
    ]
  }

  /**
   * Calculate date range from preset
   */
  calculateDateRange(preset: string): { startDate: string; endDate: string } {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    let startDate: Date
    let endDate: Date = new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1) // End of today

    switch (preset) {
      case 'today':
        startDate = today
        break
      case 'yesterday':
        startDate = new Date(today.getTime() - 24 * 60 * 60 * 1000)
        endDate = new Date(today.getTime() - 1)
        break
      case 'last_7_days':
        startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'last_30_days':
        startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case 'last_90_days':
        startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case 'this_month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
        break
      case 'last_month':
        startDate = new Date(now.getFullYear(), now.getMonth() - 1, 1)
        endDate = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59)
        break
      case 'this_quarter':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3
        startDate = new Date(now.getFullYear(), quarterStart, 1)
        break
      case 'last_quarter':
        const lastQuarterStart = Math.floor(now.getMonth() / 3) * 3 - 3
        startDate = new Date(now.getFullYear(), lastQuarterStart, 1)
        endDate = new Date(now.getFullYear(), lastQuarterStart + 3, 0, 23, 59, 59)
        break
      case 'this_year':
        startDate = new Date(now.getFullYear(), 0, 1)
        break
      case 'last_year':
        startDate = new Date(now.getFullYear() - 1, 0, 1)
        endDate = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59)
        break
      default:
        startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    }
  }

  /**
   * Format currency for display
   */
  formatCurrency(amount: number, currency: string = 'MWK'): string {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount)
  }

  /**
   * Format percentage for display
   */
  formatPercentage(value: number, decimals: number = 1): string {
    return `${value.toFixed(decimals)}%`
  }

  /**
   * Format large numbers for display
   */
  formatNumber(value: number): string {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    }
    return value.toString()
  }

  /**
   * Get trend direction and color
   */
  getTrendInfo(value: number): { direction: 'up' | 'down' | 'neutral'; color: string; icon: string } {
    if (value > 0) {
      return { direction: 'up', color: 'green', icon: '↗' }
    } else if (value < 0) {
      return { direction: 'down', color: 'red', icon: '↘' }
    } else {
      return { direction: 'neutral', color: 'gray', icon: '→' }
    }
  }

  /**
   * Get auth token (helper method)
   */
  private getAuthToken(): string {
    // This should be implemented based on your auth system
    return localStorage.getItem('authToken') || ''
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const analyticsService = new AnalyticsService()
export default analyticsService
