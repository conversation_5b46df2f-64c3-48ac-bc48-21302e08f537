// services/frontend/userService.ts - User management service

import { apiClient, buildQueryParams } from './api-client'
import type {
  ApiResponse,
  PaginationParams,
  User,
  CreateUserData,
  UpdateUserData,
  UserFilters,
  UserRole
} from '@/types/frontend'

// ============================================================================
// User Service Class
// ============================================================================

class UserService {
  private readonly baseEndpoint = '/users'

  /**
   * Get all users with pagination and filtering
   */
  async getUsers(
    pagination: PaginationParams,
    filters: UserFilters = {}
  ): Promise<ApiResponse<User[]>> {
    try {
      const params = buildQueryParams(pagination, filters)
      return await apiClient.get<User[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('UserService.getUsers error:', error)
      throw error
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(userId: string): Promise<ApiResponse<User>> {
    try {
      return await apiClient.get<User>(`${this.baseEndpoint}/${userId}`)
    } catch (error) {
      console.error('UserService.getUserById error:', error)
      throw error
    }
  }

  /**
   * Create new user
   */
  async createUser(userData: CreateUserData): Promise<ApiResponse<User>> {
    try {
      return await apiClient.post<User>(this.baseEndpoint, userData)
    } catch (error) {
      console.error('UserService.createUser error:', error)
      throw error
    }
  }

  /**
   * Update user
   */
  async updateUser(userId: string, updates: UpdateUserData): Promise<ApiResponse<User>> {
    try {
      return await apiClient.put<User>(`${this.baseEndpoint}/${userId}`, updates)
    } catch (error) {
      console.error('UserService.updateUser error:', error)
      throw error
    }
  }

  /**
   * Delete user (soft delete)
   */
  async deleteUser(userId: string): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.delete<boolean>(`${this.baseEndpoint}/${userId}`)
    } catch (error) {
      console.error('UserService.deleteUser error:', error)
      throw error
    }
  }

  /**
   * Change user password
   */
  async changeUserPassword(userId: string, newPassword: string): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.post<boolean>(`${this.baseEndpoint}/${userId}/change-password`, {
        newPassword
      })
    } catch (error) {
      console.error('UserService.changeUserPassword error:', error)
      throw error
    }
  }

  /**
   * Get users by role
   */
  async getUsersByRole(
    role: UserRole,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<User[]>> {
    try {
      const params = buildQueryParams(pagination, { role })
      return await apiClient.get<User[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('UserService.getUsersByRole error:', error)
      throw error
    }
  }

  /**
   * Get users by branch
   */
  async getUsersByBranch(
    branchId: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<User[]>> {
    try {
      const params = buildQueryParams(pagination, { branchId })
      return await apiClient.get<User[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('UserService.getUsersByBranch error:', error)
      throw error
    }
  }

  /**
   * Search users
   */
  async searchUsers(
    query: string,
    pagination: PaginationParams = { page: 1, limit: 20 },
    filters: Omit<UserFilters, 'search'> = {}
  ): Promise<ApiResponse<User[]>> {
    try {
      const params = buildQueryParams(pagination, { ...filters, search: query })
      return await apiClient.get<User[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('UserService.searchUsers error:', error)
      throw error
    }
  }

  /**
   * Get active users only
   */
  async getActiveUsers(
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<User[]>> {
    try {
      const params = buildQueryParams(pagination, { isActive: true })
      return await apiClient.get<User[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('UserService.getActiveUsers error:', error)
      throw error
    }
  }

  /**
   * Get user roles
   */
  getUserRoles(): Array<{ value: UserRole; label: string; description: string }> {
    return [
      {
        value: 'overall_admin',
        label: 'Overall Administrator',
        description: 'Full system access across all branches'
      },
      {
        value: 'branch_manager',
        label: 'Branch Manager',
        description: 'Management access for assigned branch only'
      }
    ]
  }

  /**
   * Validate user data before submission
   */
  validateUserData(userData: CreateUserData | UpdateUserData): {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  } {
    const errors: Array<{ field: string; message: string }> = []

    // Username validation
    if ('username' in userData && (!userData.username || userData.username.trim().length < 3)) {
      errors.push({ field: 'username', message: 'Username must be at least 3 characters long' })
    }

    // Email validation
    if ('email' in userData && userData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(userData.email)) {
        errors.push({ field: 'email', message: 'Please enter a valid email address' })
      }
    }

    // Password validation (for create operations)
    if ('password' in userData && userData.password) {
      if (userData.password.length < 6) {
        errors.push({ field: 'password', message: 'Password must be at least 6 characters long' })
      }
      if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(userData.password)) {
        errors.push({ 
          field: 'password', 
          message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number' 
        })
      }
    }

    // Role validation
    if ('role' in userData && userData.role) {
      const validRoles = this.getUserRoles().map(r => r.value)
      if (!validRoles.includes(userData.role)) {
        errors.push({ field: 'role', message: 'Please select a valid role' })
      }
    }

    // First name validation
    if ('firstName' in userData && (!userData.firstName || userData.firstName.trim().length < 1)) {
      errors.push({ field: 'firstName', message: 'First name is required' })
    }

    // Last name validation
    if ('lastName' in userData && (!userData.lastName || userData.lastName.trim().length < 1)) {
      errors.push({ field: 'lastName', message: 'Last name is required' })
    }

    // Phone validation
    if ('phone' in userData && userData.phone) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
      if (!phoneRegex.test(userData.phone)) {
        errors.push({ field: 'phone', message: 'Please enter a valid phone number' })
      }
    }

    // Branch validation for branch managers
    if ('role' in userData && userData.role === 'branch_manager' && 
        'branchId' in userData && (!userData.branchId || userData.branchId.trim().length === 0)) {
      errors.push({ field: 'branchId', message: 'Branch is required for branch managers' })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validate password change
   */
  validatePasswordChange(newPassword: string, confirmPassword: string): {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  } {
    const errors: Array<{ field: string; message: string }> = []

    // Password length validation
    if (newPassword.length < 6) {
      errors.push({ field: 'newPassword', message: 'Password must be at least 6 characters long' })
    }

    // Password complexity validation
    if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)) {
      errors.push({ 
        field: 'newPassword', 
        message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number' 
      })
    }

    // Password confirmation validation
    if (newPassword !== confirmPassword) {
      errors.push({ field: 'confirmPassword', message: 'Passwords do not match' })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Format user full name
   */
  formatUserName(user: User): string {
    return `${user.firstName} ${user.lastName}`.trim()
  }

  /**
   * Get role display name
   */
  getRoleDisplayName(role: UserRole): string {
    const roleInfo = this.getUserRoles().find(r => r.value === role)
    return roleInfo?.label || role
  }

  /**
   * Get role color
   */
  getRoleColor(role: UserRole): string {
    const roleColors: Record<UserRole, string> = {
      'overall_admin': 'purple',
      'branch_manager': 'blue'
    }
    return roleColors[role] || 'gray'
  }

  /**
   * Get user status color
   */
  getUserStatusColor(isActive: boolean): string {
    return isActive ? 'green' : 'red'
  }

  /**
   * Get user status text
   */
  getUserStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive'
  }

  /**
   * Format last login date
   */
  formatLastLogin(lastLogin: string | undefined): string {
    if (!lastLogin) return 'Never'
    
    const date = new Date(lastLogin)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return 'Yesterday'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`
    return `${Math.ceil(diffDays / 365)} years ago`
  }

  /**
   * Check if user can manage other user
   */
  canManageUser(currentUser: User, targetUser: User): boolean {
    // Overall admins can manage anyone
    if (currentUser.role === 'overall_admin') return true
    
    // Branch managers can only manage users in their branch
    if (currentUser.role === 'branch_manager') {
      return currentUser.branchId === targetUser.branchId && targetUser.role !== 'overall_admin'
    }
    
    return false
  }

  /**
   * Generate strong password
   */
  generateStrongPassword(length: number = 12): string {
    const lowercase = 'abcdefghijklmnopqrstuvwxyz'
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
    const numbers = '0123456789'
    const symbols = '!@#$%^&*'
    
    const allChars = lowercase + uppercase + numbers + symbols
    let password = ''
    
    // Ensure at least one character from each category
    password += lowercase[Math.floor(Math.random() * lowercase.length)]
    password += uppercase[Math.floor(Math.random() * uppercase.length)]
    password += numbers[Math.floor(Math.random() * numbers.length)]
    password += symbols[Math.floor(Math.random() * symbols.length)]
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)]
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('')
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const userService = new UserService()
export default userService
