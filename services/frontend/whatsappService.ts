"use client"

import type { CartItem } from '@/stores/cartStore'

export interface WhatsAppContact {
  type: 'branch' | 'admin' | 'fallback'
  branchId?: string
  branchName?: string
  branchPhone?: string
  branchEmail?: string
  branchAddress?: string
  branchLocation?: string
  adminId?: string
  adminName?: string
  adminPhone?: string
  adminEmail?: string
  manager?: {
    name: string
    phone: string
    role: string
  }
  operatingHours?: {
    open: string
    close: string
    timezone: string
  }
  primaryContact: string
  role?: string
  note?: string
}

export interface CheckoutData {
  customerInfo: {
    name: string
    phone: string
    email?: string
    address?: string
  }
  items: CartItem[]
  summary: {
    subtotal: number
    shipping: number
    tax: number
    total: number
  }
  deliveryMethod: 'pickup' | 'delivery'
  paymentMethod: 'cash' | 'mobile_money' | 'bank_transfer'
  notes?: string
}

class WhatsAppService {
  private formatCurrency(amount: number, currency: string = 'MWK'): string {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency === 'MWK' ? 'USD' : currency, // Fallback for MWK
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('$', currency === 'MWK' ? 'MWK ' : '$')
  }

  private formatPhoneNumber(phone: string): string {
    // Remove any non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '')
    
    // If it doesn't start with +, assume it's a Malawi number and add +265
    if (!cleaned.startsWith('+')) {
      if (cleaned.startsWith('265')) {
        return `+${cleaned}`
      } else if (cleaned.startsWith('0')) {
        return `+265${cleaned.substring(1)}`
      } else {
        return `+265${cleaned}`
      }
    }
    
    return cleaned
  }

  async getWhatsAppContact(branchId?: string): Promise<WhatsAppContact | null> {
    try {
      const params = branchId ? `?branchId=${branchId}` : ''
      const response = await fetch(`/api/checkout/whatsapp-contact${params}`)
      const result = await response.json()

      if (!response.ok || !result.success) {
        console.error('Failed to get WhatsApp contact:', result.error)
        return null
      }

      return result.data
    } catch (error) {
      console.error('Error fetching WhatsApp contact:', error)
      return null
    }
  }

  generateOrderMessage(checkoutData: CheckoutData, contact: WhatsAppContact): string {
    const { customerInfo, items, summary, deliveryMethod, paymentMethod, notes } = checkoutData
    
    let message = `🛒 *NEW ORDER REQUEST*\n\n`
    
    // Customer Information
    message += `👤 *Customer Details:*\n`
    message += `Name: ${customerInfo.name}\n`
    message += `Phone: ${customerInfo.phone}\n`
    if (customerInfo.email) {
      message += `Email: ${customerInfo.email}\n`
    }
    if (customerInfo.address) {
      message += `Address: ${customerInfo.address}\n`
    }
    message += `\n`

    // Order Items
    message += `📦 *Order Items:*\n`
    items.forEach((item, index) => {
      message += `${index + 1}. *${item.name}*\n`
      message += `   Qty: ${item.quantity} × ${this.formatCurrency(item.price)}\n`
      message += `   Subtotal: ${this.formatCurrency(item.price * item.quantity)}\n`
      if (item.category) {
        message += `   Category: ${item.category}\n`
      }
      if (item.sku) {
        message += `   SKU: ${item.sku}\n`
      }
      if (item.branchName) {
        message += `   Branch: ${item.branchName}\n`
      }
      message += `\n`
    })

    // Order Summary
    message += `💰 *Order Summary:*\n`
    message += `Subtotal: ${this.formatCurrency(summary.subtotal)}\n`
    if (summary.shipping > 0) {
      message += `Shipping: ${this.formatCurrency(summary.shipping)}\n`
    }
    if (summary.tax > 0) {
      message += `Tax: ${this.formatCurrency(summary.tax)}\n`
    }
    message += `*Total: ${this.formatCurrency(summary.total)}*\n\n`

    // Delivery & Payment
    message += `🚚 *Delivery Method:* ${deliveryMethod === 'pickup' ? 'Store Pickup' : 'Home Delivery'}\n`
    message += `💳 *Payment Method:* ${this.formatPaymentMethod(paymentMethod)}\n\n`

    // Additional Notes
    if (notes) {
      message += `📝 *Additional Notes:*\n${notes}\n\n`
    }

    // Contact Information
    if (contact.type === 'branch' && contact.branchName) {
      message += `🏢 *Branch Information:*\n`
      message += `Branch: ${contact.branchName}\n`
      if (contact.branchLocation) {
        message += `Location: ${contact.branchLocation}\n`
      }
      if (contact.operatingHours) {
        message += `Hours: ${contact.operatingHours.open} - ${contact.operatingHours.close}\n`
      }
    }

    message += `\n⏰ Order placed: ${new Date().toLocaleString()}\n`
    message += `\nPlease confirm this order and provide pickup/delivery details. Thank you! 🙏`

    return message
  }

  private formatPaymentMethod(method: string): string {
    switch (method) {
      case 'cash':
        return 'Cash on Delivery/Pickup'
      case 'mobile_money':
        return 'Mobile Money'
      case 'bank_transfer':
        return 'Bank Transfer'
      default:
        return method
    }
  }

  createWhatsAppUrl(phoneNumber: string, message: string): string {
    const formattedPhone = this.formatPhoneNumber(phoneNumber)
    const encodedMessage = encodeURIComponent(message)
    
    // Use WhatsApp Web URL for better compatibility
    return `https://wa.me/${formattedPhone.replace('+', '')}?text=${encodedMessage}`
  }

  async initiateWhatsAppCheckout(checkoutData: CheckoutData, branchId?: string): Promise<{
    success: boolean
    whatsappUrl?: string
    contact?: WhatsAppContact
    error?: string
  }> {
    try {
      // Get contact information
      const contact = await this.getWhatsAppContact(branchId)
      
      if (!contact) {
        return {
          success: false,
          error: 'No contact information available for checkout'
        }
      }

      // Generate order message
      const message = this.generateOrderMessage(checkoutData, contact)
      
      // Create WhatsApp URL
      const whatsappUrl = this.createWhatsAppUrl(contact.primaryContact, message)

      return {
        success: true,
        whatsappUrl,
        contact
      }
    } catch (error) {
      console.error('WhatsApp checkout error:', error)
      return {
        success: false,
        error: 'Failed to initiate WhatsApp checkout'
      }
    }
  }
}

export const whatsappService = new WhatsAppService()
export default whatsappService
