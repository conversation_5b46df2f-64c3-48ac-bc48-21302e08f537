// services/frontend/index.ts - Frontend services index

// ============================================================================
// Export API Client
// ============================================================================

import { apiClient, ApiError, NetworkError, ValidationError } from './api-client'
export { apiClient, ApiError, NetworkError, ValidationError }
export type { ApiRequestConfig as RequestConfig } from './api-client'

// ============================================================================
// Export Service Instances
// ============================================================================

// Shop Service
import { shopService } from './shopService'
export { shopService }
export { default as ShopService } from './shopService'

// Product Service
import { productService } from './productService'
export { productService }
export { default as ProductService } from './productService'

// Category Service
import { categoryService } from './categoryService'
export { categoryService }
export { default as CategoryService } from './categoryService'

// Customer Service
import { customerService } from './customerService'
export { customerService }
export { default as CustomerService } from './customerService'

// Order Service
import { orderService } from './orderService'
export { orderService }
export { default as OrderService } from './orderService'

// Employee Service
import { employeeService } from './employeeService'
export { employeeService }
export { default as EmployeeService } from './employeeService'

// Inventory Service
import { inventoryService } from './inventoryService'
export { inventoryService }
export { default as InventoryService } from './inventoryService'

// User Service
import { userService } from './userService'
export { userService }
export { default as UserService } from './userService'

// Analytics Service
import { analyticsService } from './analyticsService'
export { analyticsService }
export { default as AnalyticsService } from './analyticsService'

// Activity Log Service
import { activityLogService } from './activityLogService'
export { activityLogService }
export { default as ActivityLogService } from './activityLogService'

// Notifications Service
import { notificationsService } from './notificationsService'
export { notificationsService }
export { default as NotificationsService } from './notificationsService'

// Socket Service
import { socketService } from './socketService'
export { socketService }
export { default as SocketService } from './socketService'

// ============================================================================
// Utility Functions
// ============================================================================

export { buildQueryParams, buildFilterParams, buildPaginationParams } from './api-client'

// ============================================================================
// Service Collection for Easy Access
// ============================================================================

export const services = {
  shop: shopService,
  product: productService,
  category: categoryService,
  customer: customerService,
  order: orderService,
  employee: employeeService,
  inventory: inventoryService,
  user: userService,
  analytics: analyticsService,
  activityLog: activityLogService,
  notifications: notificationsService,
  socket: socketService
} as const

// ============================================================================
// Service Initialization
// ============================================================================

/**
 * Initialize all frontend services with authentication token
 */
export function initializeServices(authToken: string | null): void {
  apiClient.setAuthToken(authToken)
}

/**
 * Clear authentication from all services
 */
export function clearServiceAuth(): void {
  apiClient.setAuthToken(null)
}

// ============================================================================
// Service Health Check
// ============================================================================

/**
 * Check if services are properly configured and accessible
 */
export async function checkServiceHealth(): Promise<{
  isHealthy: boolean
  services: Record<string, boolean>
  errors: string[]
}> {
  const serviceChecks: Record<string, boolean> = {}
  const errors: string[] = []

  try {
    // Test basic API connectivity
    await apiClient.get('/health')
    serviceChecks.api = true
  } catch (error) {
    serviceChecks.api = false
    errors.push('API connectivity failed')
  }

  // Check individual services (you can add more specific health checks)
  const serviceNames = Object.keys(services)
  serviceNames.forEach(serviceName => {
    try {
      // Basic service instantiation check
      const service = services[serviceName as keyof typeof services]
      serviceChecks[serviceName] = !!service
    } catch (error) {
      serviceChecks[serviceName] = false
      errors.push(`${serviceName} service initialization failed`)
    }
  })

  const isHealthy = Object.values(serviceChecks).every(Boolean) && errors.length === 0

  return {
    isHealthy,
    services: serviceChecks,
    errors
  }
}

// ============================================================================
// Error Handling Utilities
// ============================================================================

/**
 * Handle service errors consistently across the application
 */
export function handleServiceError(error: unknown): {
  message: string
  type: 'validation' | 'network' | 'api' | 'unknown'
  details?: unknown
} {
  if (error instanceof ValidationError) {
    return {
      message: error.message,
      type: 'validation',
      details: error.details
    }
  }

  if (error instanceof NetworkError) {
    return {
      message: 'Network connection failed. Please check your internet connection.',
      type: 'network',
      details: error.originalError
    }
  }

  if (error instanceof ApiError) {
    return {
      message: error.message,
      type: 'api',
      details: error.response
    }
  }

  if (error instanceof Error) {
    return {
      message: error.message,
      type: 'unknown',
      details: error
    }
  }

  return {
    message: 'An unexpected error occurred',
    type: 'unknown',
    details: error
  }
}

/**
 * Retry a service operation with exponential backoff
 */
export async function retryServiceOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: unknown

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error

      // Don't retry on validation errors or client errors (4xx)
      if (error instanceof ValidationError || 
          (error instanceof ApiError && error.status >= 400 && error.status < 500)) {
        throw error
      }

      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break
      }

      // Wait before retrying with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError
}

// ============================================================================
// Service Configuration
// ============================================================================

export interface ServiceConfig {
  apiBaseUrl?: string
  timeout?: number
  retryAttempts?: number
  retryDelay?: number
}

/**
 * Configure global service settings
 */
export function configureServices(config: ServiceConfig): void {
  // This would update the API client configuration
  // Implementation depends on how you want to handle configuration
  console.log('Service configuration updated:', config)
}

// ============================================================================
// Type Exports for Convenience
// ============================================================================

// Re-export all types from the types file
export type * from '@/types/frontend'
