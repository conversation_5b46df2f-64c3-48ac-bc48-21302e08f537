// services/frontend/notificationsService.ts - Notifications service for managing notifications and real-time communication

import { apiClient, buildQueryParams } from './api-client'
import type { 
  ApiResponse, 
  PaginationParams,
  Notification,
  NotificationType,
  NotificationPriority,
  NotificationStatus,
  NotificationFilters,
  NotificationSettings,
  NotificationTemplate,
  NotificationChannel,
  EmailNotification,
  SMSNotification,
  SystemAlert,
  UserMessage,
  PushSubscription
} from '@/types/frontend'

// ============================================================================
// Notifications Service Types
// ============================================================================

export interface ValidationError {
  field: string
  message: string
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
}

export interface NotificationMetrics {
  totalNotifications: number
  completedNotifications: number
  failedNotifications: number
  averageGenerationTime: number
}

// ============================================================================
// Notifications Service Implementation
// ============================================================================

class NotificationsService {
  private readonly baseEndpoint = '/notifications'

  // ============================================================================
  // CRUD Operations
  // ============================================================================

  /**
   * Get all notifications with pagination and filtering
   */
  async getNotifications(
    pagination: PaginationParams,
    filters: NotificationFilters = {}
  ): Promise<ApiResponse<Notification[]>> {
    try {
      const params = buildQueryParams(pagination, filters)
      return await apiClient.get<Notification[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('NotificationsService.getNotifications error:', error)
      throw error
    }
  }

  /**
   * Get notification by ID
   */
  async getNotificationById(notificationId: string): Promise<ApiResponse<Notification>> {
    try {
      return await apiClient.get<Notification>(`${this.baseEndpoint}/${notificationId}`)
    } catch (error) {
      console.error('NotificationsService.getNotificationById error:', error)
      throw error
    }
  }

  /**
   * Create new notification
   */
  async createNotification(notificationData: Partial<Notification>): Promise<ApiResponse<Notification>> {
    try {
      return await apiClient.post<Notification>(this.baseEndpoint, notificationData)
    } catch (error) {
      console.error('NotificationsService.createNotification error:', error)
      throw error
    }
  }

  /**
   * Update notification
   */
  async updateNotification(notificationId: string, updates: Partial<Notification>): Promise<ApiResponse<Notification>> {
    try {
      return await apiClient.put<Notification>(`${this.baseEndpoint}/${notificationId}`, updates)
    } catch (error) {
      console.error('NotificationsService.updateNotification error:', error)
      throw error
    }
  }

  /**
   * Delete notification
   */
  async deleteNotification(notificationId: string): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.delete<boolean>(`${this.baseEndpoint}/${notificationId}`)
    } catch (error) {
      console.error('NotificationsService.deleteNotification error:', error)
      throw error
    }
  }

  // ============================================================================
  // Notification Management
  // ============================================================================

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.post<boolean>(`${this.baseEndpoint}/mark-all-read`)
    } catch (error) {
      console.error('NotificationsService.markAllAsRead error:', error)
      throw error
    }
  }

  /**
   * Delete multiple notifications
   */
  async deleteMultiple(notificationIds: string[]): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.post<boolean>(`${this.baseEndpoint}/delete-multiple`, { notificationIds })
    } catch (error) {
      console.error('NotificationsService.deleteMultiple error:', error)
      throw error
    }
  }

  // ============================================================================
  // Real-time Events
  // ============================================================================

  /**
   * Send notification to specific user
   */
  async sendNotification(recipientId: string, notification: Partial<Notification>): Promise<ApiResponse<Notification>> {
    try {
      return await apiClient.post<Notification>(`${this.baseEndpoint}/send`, {
        recipientId,
        ...notification
      })
    } catch (error) {
      console.error('NotificationsService.sendNotification error:', error)
      throw error
    }
  }

  /**
   * Broadcast notification to multiple users
   */
  async broadcastNotification(notification: Partial<Notification>, userIds?: string[]): Promise<ApiResponse<Notification>> {
    try {
      return await apiClient.post<Notification>(`${this.baseEndpoint}/broadcast`, {
        ...notification,
        userIds
      })
    } catch (error) {
      console.error('NotificationsService.broadcastNotification error:', error)
      throw error
    }
  }

  /**
   * Send system alert
   */
  async sendSystemAlert(alert: Partial<SystemAlert>): Promise<ApiResponse<SystemAlert>> {
    try {
      return await apiClient.post<SystemAlert>(`${this.baseEndpoint}/system-alert`, alert)
    } catch (error) {
      console.error('NotificationsService.sendSystemAlert error:', error)
      throw error
    }
  }

  /**
   * Send user message
   */
  async sendUserMessage(message: Partial<UserMessage>): Promise<ApiResponse<UserMessage>> {
    try {
      return await apiClient.post<UserMessage>(`${this.baseEndpoint}/user-message`, message)
    } catch (error) {
      console.error('NotificationsService.sendUserMessage error:', error)
      throw error
    }
  }

  // ============================================================================
  // Push Notifications
  // ============================================================================

  /**
   * Subscribe to push notifications
   */
  async subscribeToPush(): Promise<ApiResponse<PushSubscription>> {
    try {
      // Request notification permission
      if ('Notification' in window) {
        const permission = await Notification.requestPermission()
        if (permission !== 'granted') {
          throw new Error('Notification permission denied')
        }
      }

      // Register service worker and get push subscription
      if ('serviceWorker' in navigator && 'PushManager' in window) {
        const registration = await navigator.serviceWorker.register('/sw.js')
        const subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
        })

        // Send subscription to server
        return await apiClient.post<PushSubscription>(`${this.baseEndpoint}/push/subscribe`, {
          subscription: subscription.toJSON()
        })
      } else {
        throw new Error('Push notifications not supported')
      }
    } catch (error) {
      console.error('NotificationsService.subscribeToPush error:', error)
      throw error
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribeFromPush(): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.post<boolean>(`${this.baseEndpoint}/push/unsubscribe`)
    } catch (error) {
      console.error('NotificationsService.unsubscribeFromPush error:', error)
      throw error
    }
  }

  /**
   * Send push notification
   */
  async sendPushNotification(notification: Partial<Notification>): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.post<boolean>(`${this.baseEndpoint}/push/send`, notification)
    } catch (error) {
      console.error('NotificationsService.sendPushNotification error:', error)
      throw error
    }
  }

  // ============================================================================
  // Email & SMS
  // ============================================================================

  /**
   * Send email notification
   */
  async sendEmailNotification(email: Partial<EmailNotification>): Promise<ApiResponse<EmailNotification>> {
    try {
      return await apiClient.post<EmailNotification>(`${this.baseEndpoint}/email`, email)
    } catch (error) {
      console.error('NotificationsService.sendEmailNotification error:', error)
      throw error
    }
  }

  /**
   * Send SMS notification
   */
  async sendSMSNotification(sms: Partial<SMSNotification>): Promise<ApiResponse<SMSNotification>> {
    try {
      return await apiClient.post<SMSNotification>(`${this.baseEndpoint}/sms`, sms)
    } catch (error) {
      console.error('NotificationsService.sendSMSNotification error:', error)
      throw error
    }
  }

  // ============================================================================
  // Settings & Templates
  // ============================================================================

  /**
   * Get notification settings
   */
  async getNotificationSettings(): Promise<ApiResponse<NotificationSettings>> {
    try {
      return await apiClient.get<NotificationSettings>(`${this.baseEndpoint}/settings`)
    } catch (error) {
      console.error('NotificationsService.getNotificationSettings error:', error)
      throw error
    }
  }

  /**
   * Update notification settings
   */
  async updateNotificationSettings(settings: Partial<NotificationSettings>): Promise<ApiResponse<NotificationSettings>> {
    try {
      return await apiClient.put<NotificationSettings>(`${this.baseEndpoint}/settings`, settings)
    } catch (error) {
      console.error('NotificationsService.updateNotificationSettings error:', error)
      throw error
    }
  }

  /**
   * Get notification templates
   */
  async getNotificationTemplates(): Promise<ApiResponse<NotificationTemplate[]>> {
    try {
      return await apiClient.get<NotificationTemplate[]>(`${this.baseEndpoint}/templates`)
    } catch (error) {
      console.error('NotificationsService.getNotificationTemplates error:', error)
      throw error
    }
  }

  /**
   * Create notification template
   */
  async createNotificationTemplate(template: Partial<NotificationTemplate>): Promise<ApiResponse<NotificationTemplate>> {
    try {
      return await apiClient.post<NotificationTemplate>(`${this.baseEndpoint}/templates`, template)
    } catch (error) {
      console.error('NotificationsService.createNotificationTemplate error:', error)
      throw error
    }
  }

  /**
   * Update notification template
   */
  async updateNotificationTemplate(templateId: string, updates: Partial<NotificationTemplate>): Promise<ApiResponse<NotificationTemplate>> {
    try {
      return await apiClient.put<NotificationTemplate>(`${this.baseEndpoint}/templates/${templateId}`, updates)
    } catch (error) {
      console.error('NotificationsService.updateNotificationTemplate error:', error)
      throw error
    }
  }

  /**
   * Delete notification template
   */
  async deleteNotificationTemplate(templateId: string): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.delete<boolean>(`${this.baseEndpoint}/templates/${templateId}`)
    } catch (error) {
      console.error('NotificationsService.deleteNotificationTemplate error:', error)
      throw error
    }
  }

  // ============================================================================
  // Channels & Preferences
  // ============================================================================

  /**
   * Enable notification channel
   */
  async enableNotificationChannel(channel: NotificationChannel): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.post<boolean>(`${this.baseEndpoint}/channels/enable`, { channel })
    } catch (error) {
      console.error('NotificationsService.enableNotificationChannel error:', error)
      throw error
    }
  }

  /**
   * Disable notification channel
   */
  async disableNotificationChannel(channel: NotificationChannel): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.post<boolean>(`${this.baseEndpoint}/channels/disable`, { channel })
    } catch (error) {
      console.error('NotificationsService.disableNotificationChannel error:', error)
      throw error
    }
  }

  /**
   * Update channel preferences
   */
  async updateChannelPreferences(channel: NotificationChannel, preferences: any): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.put<boolean>(`${this.baseEndpoint}/channels/${channel}/preferences`, preferences)
    } catch (error) {
      console.error('NotificationsService.updateChannelPreferences error:', error)
      throw error
    }
  }

  // ============================================================================
  // Search & Analytics
  // ============================================================================

  /**
   * Search notifications
   */
  async searchNotifications(
    query: string,
    pagination: PaginationParams,
    filters: Omit<NotificationFilters, 'search'> = {}
  ): Promise<ApiResponse<Notification[]>> {
    try {
      const params = buildQueryParams(pagination, { ...filters, search: query })
      return await apiClient.get<Notification[]>(`${this.baseEndpoint}/search`, params)
    } catch (error) {
      console.error('NotificationsService.searchNotifications error:', error)
      throw error
    }
  }

  // ============================================================================
  // Validation & Utility Methods
  // ============================================================================

  /**
   * Validate notification data
   */
  validateNotificationData(data: Partial<Notification>): ValidationResult {
    const errors: ValidationError[] = []

    if (!data.title || data.title.trim().length === 0) {
      errors.push({ field: 'title', message: 'Title is required' })
    }

    if (!data.message || data.message.trim().length === 0) {
      errors.push({ field: 'message', message: 'Message is required' })
    }

    if (!data.type) {
      errors.push({ field: 'type', message: 'Notification type is required' })
    }

    if (!data.priority) {
      errors.push({ field: 'priority', message: 'Priority is required' })
    }

    if (data.title && data.title.length > 100) {
      errors.push({ field: 'title', message: 'Title must be less than 100 characters' })
    }

    if (data.message && data.message.length > 500) {
      errors.push({ field: 'message', message: 'Message must be less than 500 characters' })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Calculate notification metrics
   */
  calculateNotificationMetrics(notifications: Notification[]): NotificationMetrics {
    const totalNotifications = notifications.length
    const completedNotifications = notifications.filter(n => n.status === 'read').length
    const failedNotifications = notifications.filter(n => n.status === 'failed').length
    
    // Calculate average generation time (mock implementation)
    const averageGenerationTime = notifications.reduce((sum, n) => {
      return sum + (Math.random() * 5000) // Mock generation time
    }, 0) / totalNotifications || 0

    return {
      totalNotifications,
      completedNotifications,
      failedNotifications,
      averageGenerationTime
    }
  }

  /**
   * Format notification time
   */
  formatNotificationTime(timestamp: string): string {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
    return `${Math.floor(diffMins / 1440)}d ago`
  }

  /**
   * Get notification icon
   */
  getNotificationIcon(type: NotificationType): string {
    const icons: Record<NotificationType, string> = {
      'info': 'ℹ️',
      'success': '✅',
      'warning': '⚠️',
      'error': '❌',
      'system': '⚙️',
      'message': '💬',
      'reminder': '⏰',
      'update': '🔄'
    }
    return icons[type] || 'ℹ️'
  }

  /**
   * Get notification color
   */
  getNotificationColor(priority: NotificationPriority): string {
    const colors: Record<NotificationPriority, string> = {
      'low': 'gray',
      'medium': 'blue',
      'high': 'orange',
      'urgent': 'red'
    }
    return colors[priority] || 'gray'
  }

  /**
   * Get priority label
   */
  getPriorityLabel(priority: NotificationPriority): string {
    const labels: Record<NotificationPriority, string> = {
      'low': 'Low Priority',
      'medium': 'Medium Priority',
      'high': 'High Priority',
      'urgent': 'Urgent'
    }
    return labels[priority] || 'Unknown'
  }

  /**
   * Check if notification is expired
   */
  isNotificationExpired(notification: Notification): boolean {
    if (!notification.expiresAt) return false
    return new Date() > new Date(notification.expiresAt)
  }

  /**
   * Check if user can access notification
   */
  canUserAccessNotification(notification: Notification, userId: string): boolean {
    return notification.recipientId === userId || notification.createdBy === userId
  }

  /**
   * Get notification summary
   */
  getNotificationSummary(notification: Notification): string {
    return `${notification.type} notification: ${notification.title}`
  }

  /**
   * Check if notification should be shown
   */
  shouldShowNotification(notification: Notification, settings: NotificationSettings): boolean {
    if (!settings.enabled) return false
    
    // Check quiet hours
    if (settings.quietHours?.enabled) {
      const now = new Date()
      const currentHour = now.getHours()
      const startHour = parseInt(settings.quietHours.startTime.split(':')[0])
      const endHour = parseInt(settings.quietHours.endTime.split(':')[0])
      
      if (startHour <= endHour) {
        if (currentHour >= startHour && currentHour < endHour) return false
      } else {
        if (currentHour >= startHour || currentHour < endHour) return false
      }
    }
    
    // Check notification type preferences
    return settings.types?.[notification.type] !== false
  }

  /**
   * Play notification sound
   */
  playNotificationSound(priority: NotificationPriority): void {
    if (!('Audio' in window)) return
    
    try {
      const soundFiles: Record<NotificationPriority, string> = {
        'low': '/sounds/notification-low.mp3',
        'medium': '/sounds/notification-medium.mp3',
        'high': '/sounds/notification-high.mp3',
        'urgent': '/sounds/notification-urgent.mp3'
      }
      
      const audio = new Audio(soundFiles[priority] || soundFiles.medium)
      audio.volume = 0.5
      audio.play().catch(console.error)
    } catch (error) {
      console.error('Failed to play notification sound:', error)
    }
  }

  /**
   * Show browser notification
   */
  showBrowserNotification(notification: Notification): void {
    if (!('Notification' in window) || Notification.permission !== 'granted') {
      return
    }
    
    try {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/badge-icon.png',
        tag: notification._id,
        requireInteraction: notification.priority === 'urgent',
        silent: notification.priority === 'low'
      })
      
      // Auto-close after 5 seconds for non-urgent notifications
      if (notification.priority !== 'urgent') {
        setTimeout(() => {
          browserNotification.close()
        }, 5000)
      }
      
      browserNotification.onclick = () => {
        window.focus()
        browserNotification.close()
      }
    } catch (error) {
      console.error('Failed to show browser notification:', error)
    }
  }
}

// ============================================================================
// Service Instance
// ============================================================================

export const notificationsService = new NotificationsService()
export default NotificationsService
