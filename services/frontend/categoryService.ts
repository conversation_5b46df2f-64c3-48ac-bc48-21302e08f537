import { apiClient } from './api-client'
import type { ApiResponse, PaginationParams } from '@/types/frontend'

export interface ProductCategory {
  id: string
  name: string
  description: string
  slug: string
  isActive: boolean
  productCount: number
  // Image fields
  featuredImage?: string | null
  icon?: string | null
  iconType?: 'image' | 'lucide'
  iconName?: string | null
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

export interface CreateCategoryData {
  name: string
  description: string
  isActive?: boolean
  // Image fields
  featuredImage?: string | null
  icon?: string | null
  iconType?: 'image' | 'lucide'
  iconName?: string | null
}

export interface UpdateCategoryData {
  name?: string
  description?: string
  isActive?: boolean
  // Image fields
  featuredImage?: string | null
  icon?: string | null
  iconType?: 'image' | 'lucide'
  iconName?: string | null
}

export interface CategoryImageUploadData {
  featuredImage?: File | null
  icon?: File | null
  iconType?: 'image' | 'lucide'
  iconName?: string | null
}

export interface CategoryFilters {
  search?: string
  activeOnly?: boolean
}

class CategoryService {
  private baseEndpoint = 'categories'

  /**
   * Get all categories with pagination and filtering
   */
  async getCategories(
    pagination: PaginationParams = { page: 1, limit: 50 },
    filters: CategoryFilters = {}
  ): Promise<ApiResponse<ProductCategory[]>> {
    try {
      const params = new URLSearchParams()
      
      // Add pagination
      params.append('page', pagination.page.toString())
      params.append('limit', pagination.limit.toString())
      
      // Add filters
      if (filters.search) {
        params.append('search', filters.search)
      }
      if (filters.activeOnly !== undefined) {
        params.append('activeOnly', filters.activeOnly.toString())
      }

      return await apiClient.get<ProductCategory[]>(
        `${this.baseEndpoint}?${params.toString()}`
      )
    } catch (error) {
      console.error('CategoryService.getCategories error:', error)
      throw error
    }
  }

  /**
   * Get category by ID
   */
  async getCategoryById(categoryId: string): Promise<ApiResponse<ProductCategory>> {
    try {
      return await apiClient.get<ProductCategory>(`${this.baseEndpoint}/${categoryId}`)
    } catch (error) {
      console.error('CategoryService.getCategoryById error:', error)
      throw error
    }
  }

  /**
   * Create new category
   */
  async createCategory(data: CreateCategoryData): Promise<ApiResponse<ProductCategory>> {
    try {
      return await apiClient.post<ProductCategory>(this.baseEndpoint, data)
    } catch (error) {
      console.error('CategoryService.createCategory error:', error)
      throw error
    }
  }

  /**
   * Update category
   */
  async updateCategory(
    categoryId: string, 
    data: UpdateCategoryData
  ): Promise<ApiResponse<ProductCategory>> {
    try {
      return await apiClient.put<ProductCategory>(`${this.baseEndpoint}/${categoryId}`, data)
    } catch (error) {
      console.error('CategoryService.updateCategory error:', error)
      throw error
    }
  }

  /**
   * Delete category (soft delete)
   */
  async deleteCategory(categoryId: string): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.delete<boolean>(`${this.baseEndpoint}/${categoryId}`)
    } catch (error) {
      console.error('CategoryService.deleteCategory error:', error)
      throw error
    }
  }

  /**
   * Get active categories for dropdowns
   */
  async getActiveCategories(): Promise<ApiResponse<ProductCategory[]>> {
    try {
      return await this.getCategories(
        { page: 1, limit: 100 },
        { activeOnly: true }
      )
    } catch (error) {
      console.error('CategoryService.getActiveCategories error:', error)
      throw error
    }
  }

  /**
   * Upload images for a category
   */
  async uploadCategoryImages(
    categoryId: string,
    imageData: CategoryImageUploadData
  ): Promise<ApiResponse<{ category: ProductCategory; uploadedImages: any }>> {
    try {
      const formData = new FormData()

      if (imageData.featuredImage) {
        formData.append('featuredImage', imageData.featuredImage)
      }

      if (imageData.icon) {
        formData.append('icon', imageData.icon)
      }

      if (imageData.iconType) {
        formData.append('iconType', imageData.iconType)
      }

      if (imageData.iconName) {
        formData.append('iconName', imageData.iconName)
      }

      const response = await fetch(`/api/categories/${categoryId}/images`, {
        method: 'POST',
        body: formData,
        credentials: 'include'
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      return result
    } catch (error) {
      console.error('CategoryService.uploadCategoryImages error:', error)
      throw error
    }
  }

  /**
   * Remove category images
   */
  async removeCategoryImages(
    categoryId: string,
    imageType?: 'featured' | 'icon'
  ): Promise<ApiResponse<{ category: ProductCategory; removedImageType: string }>> {
    try {
      const params = new URLSearchParams()
      if (imageType) {
        params.append('type', imageType)
      }

      const url = `${this.baseEndpoint}/${categoryId}/images${params.toString() ? `?${params.toString()}` : ''}`
      return await apiClient.delete<{ category: ProductCategory; removedImageType: string }>(url)
    } catch (error) {
      console.error('CategoryService.removeCategoryImages error:', error)
      throw error
    }
  }

  /**
   * Create category with images
   */
  async createCategoryWithImages(
    categoryData: CreateCategoryData,
    imageData?: CategoryImageUploadData
  ): Promise<ApiResponse<ProductCategory>> {
    try {
      // First create the category
      const categoryResult = await this.createCategory(categoryData)

      if (!categoryResult.success || !categoryResult.data) {
        return categoryResult
      }

      // If images are provided, upload them
      if (imageData && (imageData.featuredImage || imageData.icon)) {
        const uploadResult = await this.uploadCategoryImages(categoryResult.data.id, imageData)

        if (uploadResult.success && uploadResult.data) {
          // Return the updated category with images
          return {
            success: true,
            data: uploadResult.data.category
          }
        }
      }

      return categoryResult
    } catch (error) {
      console.error('CategoryService.createCategoryWithImages error:', error)
      throw error
    }
  }

  /**
   * Update category with images
   */
  async updateCategoryWithImages(
    categoryId: string,
    categoryData: UpdateCategoryData,
    imageData?: CategoryImageUploadData
  ): Promise<ApiResponse<ProductCategory>> {
    try {
      // First update the category data
      const categoryResult = await this.updateCategory(categoryId, categoryData)

      if (!categoryResult.success || !categoryResult.data) {
        return categoryResult
      }

      // If images are provided, upload them
      if (imageData && (imageData.featuredImage || imageData.icon)) {
        const uploadResult = await this.uploadCategoryImages(categoryId, imageData)

        if (uploadResult.success && uploadResult.data) {
          // Return the updated category with images
          return {
            success: true,
            data: uploadResult.data.category
          }
        }
      }

      return categoryResult
    } catch (error) {
      console.error('CategoryService.updateCategoryWithImages error:', error)
      throw error
    }
  }
}

export const categoryService = new CategoryService()
export default categoryService
