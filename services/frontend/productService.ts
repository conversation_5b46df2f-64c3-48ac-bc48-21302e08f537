// services/frontend/productService.ts - Product management service

import { apiClient, buildQueryParams } from './api-client'
import type {
  ApiResponse,
  PaginationParams,
  Product,
  CreateProductData,
  UpdateProductData,
  ProductFilters,
  ProductCategory,
  ProductStatus
} from '@/types/frontend'

// ============================================================================
// Product Service Class
// ============================================================================

class ProductService {
  private readonly baseEndpoint = 'products'

  /**
   * Get all products with pagination and filtering
   */
  async getProducts(
    pagination: PaginationParams,
    filters: ProductFilters = {}
  ): Promise<ApiResponse<Product[]>> {
    try {
      const params = buildQueryParams(pagination, filters)
      return await apiClient.get<Product[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ProductService.getProducts error:', error)
      throw error
    }
  }

  /**
   * Get product by ID
   */
  async getProductById(productId: string): Promise<ApiResponse<Product>> {
    try {
      return await apiClient.get<Product>(`${this.baseEndpoint}/${productId}`)
    } catch (error) {
      console.error('ProductService.getProductById error:', error)
      throw error
    }
  }

  /**
   * Create new product
   */
  async createProduct(productData: CreateProductData): Promise<ApiResponse<Product>> {
    try {
      console.log('🚀 ProductService.createProduct called')
      console.log('📋 Product data:', JSON.stringify(productData, null, 2))
      console.log('🎯 Endpoint:', this.baseEndpoint)

      const result = await apiClient.post<Product>(this.baseEndpoint, productData)

      console.log('✅ ProductService.createProduct result:', JSON.stringify(result, null, 2))
      return result
    } catch (error) {
      console.error('❌ ProductService.createProduct error:', error)
      throw error
    }
  }

  /**
   * Update product
   */
  async updateProduct(productId: string, updates: UpdateProductData): Promise<ApiResponse<Product>> {
    try {
      console.log('🔄 ProductService.updateProduct called')
      console.log('📝 Product ID:', productId)
      console.log('📦 Updates:', JSON.stringify(updates, null, 2))
      console.log('🎯 Endpoint:', `${this.baseEndpoint}/${productId}`)

      const result = await apiClient.put<Product>(`${this.baseEndpoint}/${productId}`, updates)

      console.log('✅ ProductService.updateProduct result:', JSON.stringify(result, null, 2))
      return result
    } catch (error) {
      console.error('❌ ProductService.updateProduct error:', error)
      throw error
    }
  }

  /**
   * Delete product (soft delete)
   */
  async deleteProduct(productId: string): Promise<ApiResponse<boolean>> {
    try {
      console.log('🗑️ ProductService.deleteProduct called')
      console.log('📝 Product ID:', productId)
      console.log('🎯 Endpoint:', `${this.baseEndpoint}/${productId}`)

      const result = await apiClient.delete<boolean>(`${this.baseEndpoint}/${productId}`)

      console.log('✅ ProductService.deleteProduct result:', JSON.stringify(result, null, 2))
      return result
    } catch (error) {
      console.error('❌ ProductService.deleteProduct error:', error)
      throw error
    }
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(
    category: ProductCategory,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Product[]>> {
    try {
      const params = buildQueryParams(pagination, { category })
      return await apiClient.get<Product[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ProductService.getProductsByCategory error:', error)
      throw error
    }
  }

  /**
   * Get products by branch
   */
  async getProductsByBranch(
    branchId: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Product[]>> {
    try {
      const params = buildQueryParams(pagination, { branchId })
      return await apiClient.get<Product[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ProductService.getProductsByBranch error:', error)
      throw error
    }
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts(
    pagination: PaginationParams = { page: 1, limit: 12 }
  ): Promise<ApiResponse<Product[]>> {
    try {
      const params = buildQueryParams(pagination, { featured: true })
      return await apiClient.get<Product[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ProductService.getFeaturedProducts error:', error)
      throw error
    }
  }

  /**
   * Get low stock products
   */
  async getLowStockProducts(
    branchId?: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Product[]>> {
    try {
      return await apiClient.get<Product[]>(`${this.baseEndpoint}/low-stock`, {
        ...buildQueryParams(pagination),
        ...(branchId && { branchId })
      })
    } catch (error) {
      console.error('ProductService.getLowStockProducts error:', error)
      throw error
    }
  }

  /**
   * Search products
   */
  async searchProducts(
    query: string,
    pagination: PaginationParams = { page: 1, limit: 20 },
    filters: Omit<ProductFilters, 'search'> = {}
  ): Promise<ApiResponse<Product[]>> {
    try {
      const params = buildQueryParams(pagination, { ...filters, search: query })
      return await apiClient.get<Product[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ProductService.searchProducts error:', error)
      throw error
    }
  }

  /**
   * Update product status
   */
  async updateProductStatus(
    productId: string,
    status: ProductStatus
  ): Promise<ApiResponse<Product>> {
    try {
      return await apiClient.put<Product>(`${this.baseEndpoint}/${productId}`, { status })
    } catch (error) {
      console.error('ProductService.updateProductStatus error:', error)
      throw error
    }
  }

  /**
   * Update product stock
   */
  async updateProductStock(
    productId: string,
    stock: number
  ): Promise<ApiResponse<Product>> {
    try {
      return await apiClient.put<Product>(`${this.baseEndpoint}/${productId}`, { stock })
    } catch (error) {
      console.error('ProductService.updateProductStock error:', error)
      throw error
    }
  }

  /**
   * Toggle product featured status
   */
  async toggleFeatured(productId: string, isFeatured: boolean): Promise<ApiResponse<Product>> {
    try {
      return await apiClient.put<Product>(`${this.baseEndpoint}/${productId}`, { isFeatured })
    } catch (error) {
      console.error('ProductService.toggleFeatured error:', error)
      throw error
    }
  }

  /**
   * Upload product images
   */
  async uploadProductImages(
    productId: string,
    imageFiles: File[]
  ): Promise<ApiResponse<{ imageUrls: string[] }>> {
    try {
      const formData = new FormData()
      imageFiles.forEach((file, index) => {
        formData.append(`image_${index}`, file)
      })

      return await apiClient.upload<{ imageUrls: string[] }>(
        `${this.baseEndpoint}/${productId}/images`,
        imageFiles[0], // Primary file
        { additionalImages: imageFiles.slice(1).length }
      )
    } catch (error) {
      console.error('ProductService.uploadProductImages error:', error)
      throw error
    }
  }

  /**
   * Get product categories
   */
  getProductCategories(): ProductCategory[] {
    return [
      'Desktops',
      'Laptops',
      'TV Sets',
      'Server Systems',
      'Amplifiers',
      'Network Devices',
      'Security Software',
      'Application Software',
      'Mobile Devices',
      'Audio Equipment',
      'Gaming',
      'Accessories'
    ]
  }

  /**
   * Get product statuses
   */
  getProductStatuses(): ProductStatus[] {
    return ['Available', 'Out of Stock', 'Discontinued', 'Coming Soon']
  }

  /**
   * Validate product data before submission
   */
  validateProductData(productData: CreateProductData | UpdateProductData): {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  } {
    const errors: Array<{ field: string; message: string }> = []

    // Name validation
    if ('name' in productData && (!productData.name || productData.name.trim().length < 2)) {
      errors.push({ field: 'name', message: 'Product name must be at least 2 characters long' })
    }

    // Price validation
    if ('price' in productData && (productData.price === undefined || productData.price < 0)) {
      errors.push({ field: 'price', message: 'Price must be a positive number' })
    }

    // Stock validation
    if ('stock' in productData && (productData.stock === undefined || productData.stock < 0)) {
      errors.push({ field: 'stock', message: 'Stock must be a non-negative number' })
    }

    // Min stock level validation
    if ('minStockLevel' in productData && (productData.minStockLevel === undefined || productData.minStockLevel < 0)) {
      errors.push({ field: 'minStockLevel', message: 'Minimum stock level must be a non-negative number' })
    }

    // Category validation
    if ('category' in productData && productData.category) {
      const validCategories = this.getProductCategories()
      if (!validCategories.includes(productData.category)) {
        errors.push({ field: 'category', message: 'Please select a valid category' })
      }
    }

    // SKU validation (for create operations)
    if ('name' in productData && 'category' in productData && 'brand' in productData) {
      // SKU will be generated on backend, but we can validate required fields
      if (!productData.name || !productData.category || !productData.brand) {
        errors.push({ field: 'sku', message: 'Name, category, and brand are required for SKU generation' })
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Format price for display
   */
  formatPrice(price: number, currency: string = 'MWK'): string {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price)
  }

  /**
   * Calculate discount percentage
   */
  calculateDiscountPercentage(originalPrice: number, currentPrice: number): number {
    if (originalPrice <= currentPrice) return 0
    return Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const productService = new ProductService()
export default productService
