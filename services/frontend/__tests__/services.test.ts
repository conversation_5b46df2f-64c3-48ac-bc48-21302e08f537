// services/frontend/__tests__/services.test.ts - Basic service tests

import { services, initializeServices, clearServiceAuth, handleServiceError } from '../index'
import { ApiError, NetworkError, ValidationError } from '../api-client'

describe('Frontend Services', () => {
  beforeEach(() => {
    // Initialize services with a mock token
    initializeServices('mock-auth-token')
  })

  afterEach(() => {
    // Clear auth after each test
    clearServiceAuth()
  })

  describe('Service Initialization', () => {
    test('should have all required services', () => {
      expect(services.shop).toBeDefined()
      expect(services.product).toBeDefined()
      expect(services.customer).toBeDefined()
      expect(services.order).toBeDefined()
      expect(services.employee).toBeDefined()
      expect(services.inventory).toBeDefined()
      expect(services.user).toBeDefined()
      expect(services.analytics).toBeDefined()
      expect(services.activityLog).toBeDefined()
    })

    test('should initialize services with auth token', () => {
      expect(() => initializeServices('test-token')).not.toThrow()
    })

    test('should clear service auth', () => {
      expect(() => clearServiceAuth()).not.toThrow()
    })
  })

  describe('Service Validation', () => {
    test('shop service should validate shop data', () => {
      const validData = {
        name: 'Test Shop',
        location: 'Test Location',
        country: 'Malawi',
        region: 'Central',
        managerId: 'manager-123',
        description: 'Test Description',
        address: 'Test Address',
        phone: '+265123456789',
        email: '<EMAIL>',
        operatingHours: {
          open: '08:00',
          close: '18:00',
          timezone: 'Africa/Blantyre'
        },
        image: 'test-image.jpg'
      }

      const result = services.shop.validateShopData(validData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    test('product service should validate product data', () => {
      const validData = {
        name: 'Test Product',
        category: 'Laptops' as const,
        price: 100000,
        stock: 10,
        minStockLevel: 5,
        images: ['image1.jpg'],
        description: 'Test Description',
        specifications: ['Spec 1', 'Spec 2'],
        branchId: 'branch-123',
        brand: 'Test Brand',
        model: 'Test Model',
        warranty: '1 year',
        tags: ['tag1', 'tag2']
      }

      const result = services.product.validateProductData(validData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    test('customer service should validate customer data', () => {
      const validData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+265123456789',
        address: {
          street: '123 Main St',
          city: 'Lilongwe',
          region: 'Central',
          country: 'Malawi',
          postalCode: '12345'
        }
      }

      const result = services.customer.validateCustomerData(validData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    test('user service should validate user data', () => {
      const validData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'TestPass123',
        role: 'branch_manager' as const,
        firstName: 'Test',
        lastName: 'User',
        branchId: 'branch-123'
      }

      const result = services.user.validateUserData(validData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })
  })

  describe('Error Handling', () => {
    test('should handle ValidationError correctly', () => {
      const error = new ValidationError('Validation failed', [
        { field: 'email', message: 'Invalid email' }
      ])

      const result = handleServiceError(error)
      expect(result.type).toBe('validation')
      expect(result.message).toBe('Validation failed')
      expect(result.details).toEqual([{ field: 'email', message: 'Invalid email' }])
    })

    test('should handle NetworkError correctly', () => {
      const originalError = new Error('Network failed')
      const error = new NetworkError('Network connection failed', originalError)

      const result = handleServiceError(error)
      expect(result.type).toBe('network')
      expect(result.message).toBe('Network connection failed. Please check your internet connection.')
    })

    test('should handle ApiError correctly', () => {
      const error = new ApiError('API Error', 400, { error: 'Bad Request' })

      const result = handleServiceError(error)
      expect(result.type).toBe('api')
      expect(result.message).toBe('API Error')
    })

    test('should handle unknown errors correctly', () => {
      const error = new Error('Unknown error')

      const result = handleServiceError(error)
      expect(result.type).toBe('unknown')
      expect(result.message).toBe('Unknown error')
    })
  })

  describe('Utility Functions', () => {
    test('product service should format price correctly', () => {
      const formatted = services.product.formatPrice(100000)
      expect(formatted).toContain('100,000')
    })

    test('customer service should format customer name correctly', () => {
      const customer = {
        _id: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+265123456789',
        address: {
          street: '123 Main St',
          city: 'Lilongwe',
          region: 'Central',
          country: 'Malawi',
          postalCode: '12345'
        },
        preferredBranch: 'branch-1',
        totalOrders: 5,
        totalSpent: 500000,
        loyaltyPoints: 500,
        isActive: true,
        lastOrderDate: '2024-01-01',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01'
      }

      const name = services.customer.formatCustomerName(customer)
      expect(name).toBe('John Doe')
    })

    test('order service should calculate order totals correctly', () => {
      const items = [
        {
          id: '1',
          productId: 'prod-1',
          productName: 'Product 1',
          sku: 'SKU-1',
          price: 100000,
          quantity: 2,
          subtotal: 200000
        }
      ]

      const totals = services.order.calculateOrderTotals(items, 0.18, 5000, 10000)
      expect(totals.subtotal).toBe(200000)
      expect(totals.tax).toBe(36000) // 18% of 200000
      expect(totals.shipping).toBe(5000)
      expect(totals.discount).toBe(10000)
      expect(totals.total).toBe(231000) // 200000 + 36000 + 5000 - 10000
    })

    test('inventory service should check stock status correctly', () => {
      const inventory = {
        _id: '1',
        productId: 'prod-1',
        productName: 'Product 1',
        sku: 'SKU-1',
        branchId: 'branch-1',
        branchName: 'Branch 1',
        stock: 5,
        minStockLevel: 10,
        maxStockLevel: 100,
        cost: 50000,
        location: 'A1',
        lastRestocked: '2024-01-01',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01'
      }

      const status = services.inventory.getStockStatus(inventory)
      expect(status.status).toBe('low_stock')
      expect(status.color).toBe('orange')
    })
  })
})
