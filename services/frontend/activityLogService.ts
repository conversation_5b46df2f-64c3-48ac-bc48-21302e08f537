// services/frontend/activityLogService.ts - Activity log management service

import { apiClient, buildQueryParams } from './api-client'
import type {
  ApiResponse,
  PaginationParams,
  ActivityLog,
  CreateActivityLogData,
  ActivityLogFilters,
  ActivityLogStats,
  ActivityLogType
} from '@/types/frontend'

// ============================================================================
// Activity Log Service Class
// ============================================================================

class ActivityLogService {
  private readonly baseEndpoint = 'activity-logs'

  /**
   * Get all activity logs with pagination and filtering
   */
  async getActivityLogs(
    pagination: PaginationParams,
    filters: ActivityLogFilters = {}
  ): Promise<ApiResponse<ActivityLog[]>> {
    try {
      const params = buildQueryParams(pagination, filters)
      return await apiClient.get<ActivityLog[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ActivityLogService.getActivityLogs error:', error)
      throw error
    }
  }

  /**
   * Create new activity log (for manual logging)
   */
  async createActivityLog(logData: CreateActivityLogData): Promise<ApiResponse<ActivityLog>> {
    try {
      return await apiClient.post<ActivityLog>(this.baseEndpoint, logData)
    } catch (error) {
      console.error('ActivityLogService.createActivityLog error:', error)
      throw error
    }
  }

  /**
   * Get activity log statistics
   */
  async getActivityLogStats(
    branchId?: string,
    period: number = 30,
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<ActivityLogStats>> {
    try {
      const params: Record<string, string | number> = { period }
      if (branchId) params.branchId = branchId
      if (startDate) params.startDate = startDate
      if (endDate) params.endDate = endDate

      return await apiClient.get<ActivityLogStats>(`${this.baseEndpoint}/stats`, params)
    } catch (error) {
      console.error('ActivityLogService.getActivityLogStats error:', error)
      throw error
    }
  }

  /**
   * Get activity logs by type
   */
  async getActivityLogsByType(
    type: ActivityLogType,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<ActivityLog[]>> {
    try {
      const params = buildQueryParams(pagination, { type })
      return await apiClient.get<ActivityLog[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ActivityLogService.getActivityLogsByType error:', error)
      throw error
    }
  }

  /**
   * Get activity logs by user
   */
  async getActivityLogsByUser(
    userId: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<ActivityLog[]>> {
    try {
      const params = buildQueryParams(pagination, { userId })
      return await apiClient.get<ActivityLog[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ActivityLogService.getActivityLogsByUser error:', error)
      throw error
    }
  }

  /**
   * Get activity logs by branch
   */
  async getActivityLogsByBranch(
    branchId: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<ActivityLog[]>> {
    try {
      const params = buildQueryParams(pagination, { branchId })
      return await apiClient.get<ActivityLog[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ActivityLogService.getActivityLogsByBranch error:', error)
      throw error
    }
  }

  /**
   * Search activity logs
   */
  async searchActivityLogs(
    query: string,
    pagination: PaginationParams = { page: 1, limit: 20 },
    filters: Omit<ActivityLogFilters, 'search'> = {}
  ): Promise<ApiResponse<ActivityLog[]>> {
    try {
      const params = buildQueryParams(pagination, { ...filters, search: query })
      return await apiClient.get<ActivityLog[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ActivityLogService.searchActivityLogs error:', error)
      throw error
    }
  }

  /**
   * Get recent activity logs
   */
  async getRecentActivityLogs(
    limit: number = 10,
    branchId?: string
  ): Promise<ApiResponse<ActivityLog[]>> {
    try {
      const params: Record<string, string | number> = {
        limit,
        sortBy: 'timestamp',
        sortOrder: 'desc'
      }
      if (branchId) params.branchId = branchId

      return await apiClient.get<ActivityLog[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ActivityLogService.getRecentActivityLogs error:', error)
      throw error
    }
  }

  /**
   * Export activity logs
   */
  async exportActivityLogs(
    format: 'csv' | 'excel' | 'pdf',
    filters: ActivityLogFilters = {}
  ): Promise<Blob> {
    try {
      const params = { ...filters, format }
      
      const response = await fetch(
        `${this.baseEndpoint}/export?${new URLSearchParams(params as Record<string, string>)}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.getAuthToken()}`
          }
        }
      )

      if (!response.ok) {
        throw new Error(`Export failed: ${response.statusText}`)
      }

      return await response.blob()
    } catch (error) {
      console.error('ActivityLogService.exportActivityLogs error:', error)
      throw error
    }
  }

  /**
   * Get activity log types
   */
  getActivityLogTypes(): Array<{ value: ActivityLogType; label: string; color: string; icon: string }> {
    return [
      { value: 'Order', label: 'Order', color: 'blue', icon: '🛒' },
      { value: 'Inventory', label: 'Inventory', color: 'green', icon: '📦' },
      { value: 'Sale', label: 'Sale', color: 'purple', icon: '💰' },
      { value: 'Delivery', label: 'Delivery', color: 'orange', icon: '🚚' },
      { value: 'Product', label: 'Product', color: 'teal', icon: '📱' },
      { value: 'User', label: 'User', color: 'red', icon: '👤' }
    ]
  }

  /**
   * Validate activity log data before submission
   */
  validateActivityLogData(logData: CreateActivityLogData): {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  } {
    const errors: Array<{ field: string; message: string }> = []

    // Type validation
    const validTypes = this.getActivityLogTypes().map(t => t.value)
    if (!logData.type || !validTypes.includes(logData.type)) {
      errors.push({ field: 'type', message: 'Please select a valid activity type' })
    }

    // Description validation
    if (!logData.description || logData.description.trim().length < 1) {
      errors.push({ field: 'description', message: 'Description is required' })
    } else if (logData.description.length > 500) {
      errors.push({ field: 'description', message: 'Description must be less than 500 characters' })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Get activity type info
   */
  getActivityTypeInfo(type: ActivityLogType): {
    label: string
    color: string
    icon: string
  } {
    const typeInfo = this.getActivityLogTypes().find(t => t.value === type)
    return typeInfo || { label: type, color: 'gray', icon: '📝' }
  }

  /**
   * Format activity timestamp
   */
  formatActivityTimestamp(timestamp: string): {
    date: string
    time: string
    relative: string
  } {
    const date = new Date(timestamp)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffMinutes = Math.ceil(diffTime / (1000 * 60))
    const diffHours = Math.ceil(diffTime / (1000 * 60 * 60))
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    let relative: string
    if (diffMinutes < 60) {
      relative = `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`
    } else if (diffHours < 24) {
      relative = `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`
    } else if (diffDays < 7) {
      relative = `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`
    } else {
      relative = date.toLocaleDateString()
    }

    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString(),
      relative
    }
  }

  /**
   * Group activity logs by date
   */
  groupActivityLogsByDate(logs: ActivityLog[]): Record<string, ActivityLog[]> {
    const grouped: Record<string, ActivityLog[]> = {}
    
    logs.forEach(log => {
      const date = new Date(log.timestamp).toLocaleDateString()
      if (!grouped[date]) {
        grouped[date] = []
      }
      grouped[date].push(log)
    })
    
    return grouped
  }

  /**
   * Filter activity logs by date range
   */
  filterLogsByDateRange(
    logs: ActivityLog[],
    startDate: string,
    endDate: string
  ): ActivityLog[] {
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    return logs.filter(log => {
      const logDate = new Date(log.timestamp)
      return logDate >= start && logDate <= end
    })
  }

  /**
   * Get activity summary for period
   */
  getActivitySummary(logs: ActivityLog[]): {
    totalActivities: number
    activitiesByType: Record<ActivityLogType, number>
    activitiesByUser: Record<string, { count: number; userName: string }>
    mostActiveDay: string
    mostActiveHour: number
  } {
    const activitiesByType: Record<ActivityLogType, number> = {
      'Order': 0,
      'Inventory': 0,
      'Sale': 0,
      'Delivery': 0,
      'Product': 0,
      'User': 0
    }
    
    const activitiesByUser: Record<string, { count: number; userName: string }> = {}
    const activitiesByDay: Record<string, number> = {}
    const activitiesByHour: Record<number, number> = {}

    logs.forEach(log => {
      // Count by type
      activitiesByType[log.type]++
      
      // Count by user
      if (!activitiesByUser[log.userId]) {
        activitiesByUser[log.userId] = { count: 0, userName: log.userName }
      }
      activitiesByUser[log.userId].count++
      
      // Count by day
      const day = new Date(log.timestamp).toLocaleDateString()
      activitiesByDay[day] = (activitiesByDay[day] || 0) + 1
      
      // Count by hour
      const hour = new Date(log.timestamp).getHours()
      activitiesByHour[hour] = (activitiesByHour[hour] || 0) + 1
    })

    // Find most active day and hour
    const mostActiveDay = Object.entries(activitiesByDay)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || ''
    
    const mostActiveHour = Object.entries(activitiesByHour)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 0

    return {
      totalActivities: logs.length,
      activitiesByType,
      activitiesByUser,
      mostActiveDay,
      mostActiveHour: parseInt(String(mostActiveHour))
    }
  }

  /**
   * Get auth token (helper method)
   */
  private getAuthToken(): string {
    // This should be implemented based on your auth system
    return localStorage.getItem('authToken') || ''
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const activityLogService = new ActivityLogService()
export default activityLogService
