// services/frontend/socketService.ts - Socket.IO client service for real-time communication

import { io, Socket } from 'socket.io-client'
import { isVercelEnvironment, isWebSocketSupported, getConnectionUrl, getConnectionStrategy } from '@/lib/utils/environment'
import type { ApiResponse } from '@/types/frontend'

// ============================================================================
// Socket Service Types
// ============================================================================

export interface SocketServiceConfig {
  url?: string
  autoConnect?: boolean
  reconnection?: boolean
  reconnectionAttempts?: number
  reconnectionDelay?: number
  timeout?: number
}

export interface SocketConnectionResult {
  success: boolean
  error?: string
}

// ============================================================================
// Socket Service Implementation
// ============================================================================

class SocketService {
  private socket: Socket | null = null
  private isConnected: boolean = false
  private config: SocketServiceConfig
  private eventListeners: Map<string, Function[]> = new Map()

  constructor(config: SocketServiceConfig = {}) {
    // Determine Socket.IO URL based on environment
    const getSocketUrl = () => {
      if (process.env.NEXT_PUBLIC_SOCKET_URL) {
        return process.env.NEXT_PUBLIC_SOCKET_URL
      }

      // Fallback to current origin for production or localhost for development
      if (typeof window !== 'undefined') {
        const isProduction = window.location.hostname !== 'localhost'
        return isProduction ? window.location.origin : 'http://localhost:3001'
      }

      return 'http://localhost:3001'
    }

    this.config = {
      url: getSocketUrl(),
      autoConnect: false,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 20000,
      ...config
    }
  }

  /**
   * Connect to Socket.IO server with authentication
   * Falls back to HTTP polling if Socket.IO is not available (e.g., on Vercel)
   */
  async connect(userId: string, token: string): Promise<SocketConnectionResult> {
    try {
      if (this.socket && this.isConnected) {
        return { success: true }
      }

      // Check connection strategy based on environment
      const strategy = getConnectionStrategy()

      if (strategy === 'http' || !isWebSocketSupported()) {
        // Use HTTP fallback for Vercel deployment
        console.log('Using HTTP fallback for real-time notifications (Vercel deployment)')

        try {
          const response = await fetch(`${this.config.url}/api/socket`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              event: 'connect',
              data: { userId }
            })
          })

          if (response.ok) {
            this.isConnected = true
            console.log('HTTP fallback connected successfully')

            // Start polling for notifications
            this.startPolling(userId, token)

            return { success: true }
          } else {
            return { success: false, error: 'HTTP fallback connection failed' }
          }
        } catch (error) {
          console.error('HTTP fallback error:', error)
          return { success: false, error: 'HTTP fallback failed' }
        }
      }

      // Try Socket.IO connection for non-Vercel environments
      this.socket = io(this.config.url!, {
        autoConnect: this.config.autoConnect,
        reconnection: this.config.reconnection,
        reconnectionAttempts: this.config.reconnectionAttempts,
        reconnectionDelay: this.config.reconnectionDelay,
        timeout: this.config.timeout,
        auth: {
          token,
          userId
        },
        transports: ['websocket', 'polling']
      })

      // Set up connection event handlers
      this.setupConnectionHandlers()

      // Connect and wait for connection
      return new Promise((resolve) => {
        const connectTimeout = setTimeout(() => {
          console.log('Socket.IO connection timeout, falling back to HTTP polling')

          // Clean up the failed socket connection
          if (this.socket) {
            try {
              this.socket.disconnect()
            } catch (e) {
              console.warn('Error disconnecting socket during timeout:', e)
            }
            this.socket = null
          }

          this.startPolling(userId, token)
          this.isConnected = true
          resolve({ success: true, fallback: 'http' })
        }, this.config.timeout)

        this.socket!.on('connect', () => {
          clearTimeout(connectTimeout)
          this.isConnected = true
          console.log('Socket.IO connected successfully')
          resolve({ success: true })
        })

        this.socket!.on('connect_error', (error) => {
          clearTimeout(connectTimeout)
          console.error('Socket.IO connection error, falling back to HTTP polling:', error)

          // Clean up the failed socket connection
          if (this.socket) {
            this.socket.disconnect()
            this.socket = null
          }

          this.startPolling(userId, token)
          this.isConnected = true
          resolve({ success: true })
        })

        this.socket!.on('error', (error) => {
          console.error('Socket.IO error:', error)

          // Clean up the socket connection on error
          if (this.socket) {
            this.socket.disconnect()
            this.socket = null
          }
        })

        try {
          this.socket!.connect()
        } catch (error) {
          clearTimeout(connectTimeout)
          console.error('Failed to connect socket:', error)
          this.startPolling(userId, token)
          this.isConnected = true
          resolve({ success: true })
        }
      })
    } catch (error) {
      console.error('Socket connection error:', error)
      return { success: false, error: 'Failed to establish connection' }
    }
  }

  /**
   * Start HTTP polling as fallback for real-time notifications
   */
  private pollingInterval: NodeJS.Timeout | null = null
  private lastPollTimestamp: number = 0

  private startPolling(userId: string, token: string): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
    }

    this.lastPollTimestamp = Date.now()

    this.pollingInterval = setInterval(async () => {
      try {
        const response = await fetch(
          `${this.config.url}/api/notifications/poll?userId=${userId}&since=${this.lastPollTimestamp}&limit=10`,
          {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          }
        )

        if (response.ok) {
          const data = await response.json()

          if (data.success && data.data.notifications.length > 0) {
            // Emit notifications to registered listeners
            data.data.notifications.forEach((notification: any) => {
              this.emitToListeners('notification:received', notification)
            })
          }

          this.lastPollTimestamp = data.data.timestamp
        }
      } catch (error) {
        console.error('Polling error:', error)
      }
    }, 5000) // Poll every 5 seconds
  }

  /**
   * Emit events to registered listeners (for HTTP fallback)
   */
  private emitToListeners(event: string, data: any): void {
    const listeners = this.eventListeners.get(event)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error('Error in event listener:', error)
        }
      })
    }
  }

  /**
   * Disconnect from Socket.IO server
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }

    // Clean up polling
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
      this.pollingInterval = null
    }

    this.isConnected = false
    this.eventListeners.clear()
    console.log('Socket.IO/HTTP polling disconnected')
  }

  /**
   * Reconnect to Socket.IO server
   */
  async reconnect(): Promise<SocketConnectionResult> {
    try {
      if (this.socket) {
        this.socket.connect()
        
        return new Promise((resolve) => {
          const reconnectTimeout = setTimeout(() => {
            resolve({ success: false, error: 'Reconnection timeout' })
          }, this.config.timeout)

          this.socket!.on('connect', () => {
            clearTimeout(reconnectTimeout)
            this.isConnected = true
            console.log('Socket.IO reconnected successfully')
            resolve({ success: true })
          })

          this.socket!.on('connect_error', (error) => {
            clearTimeout(reconnectTimeout)
            console.error('Socket.IO reconnection error:', error)
            resolve({ success: false, error: error.message || 'Reconnection failed' })
          })
        })
      } else {
        return { success: false, error: 'No socket connection to reconnect' }
      }
    } catch (error) {
      console.error('Socket reconnection error:', error)
      return { success: false, error: 'Failed to reconnect' }
    }
  }

  /**
   * Emit event to server (with HTTP fallback)
   */
  async emit(event: string, data?: any): Promise<void> {
    if (this.socket && this.socket.connected) {
      this.socket.emit(event, data)
      return
    }

    // HTTP fallback for Vercel deployment
    if (this.isConnected) {
      try {
        const response = await fetch(`${this.config.url}/api/socket`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            event,
            data
          })
        })

        if (!response.ok) {
          console.warn('HTTP emit failed:', response.statusText)
        }
      } catch (error) {
        console.warn('HTTP emit error:', error)
      }
    } else {
      console.warn('Cannot emit event: Not connected')
    }
  }

  /**
   * Listen for events from server
   */
  on(event: string, callback: Function): void {
    if (this.socket) {
      this.socket.on(event, callback)
      
      // Store listener for cleanup
      if (!this.eventListeners.has(event)) {
        this.eventListeners.set(event, [])
      }
      this.eventListeners.get(event)!.push(callback)
    }
  }

  /**
   * Remove event listener
   */
  off(event: string, callback?: Function): void {
    if (this.socket) {
      if (callback) {
        this.socket.off(event, callback)
        
        // Remove from stored listeners
        const listeners = this.eventListeners.get(event)
        if (listeners) {
          const index = listeners.indexOf(callback)
          if (index > -1) {
            listeners.splice(index, 1)
          }
        }
      } else {
        this.socket.off(event)
        this.eventListeners.delete(event)
      }
    }
  }

  /**
   * Get connection status
   */
  getConnectionStatus(): {
    connected: boolean
    id?: string
    transport?: string
  } {
    if (this.socket) {
      return {
        connected: this.isConnected,
        id: this.socket.id,
        transport: this.socket.io.engine?.transport?.name
      }
    }
    
    return { connected: false }
  }

  /**
   * Check if socket is connected
   */
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true
  }

  /**
   * Setup connection event handlers
   */
  private setupConnectionHandlers(): void {
    if (!this.socket) return

    this.socket.on('connect', () => {
      this.isConnected = true
      console.log('Socket.IO connected:', this.socket!.id)
    })

    this.socket.on('disconnect', (reason) => {
      this.isConnected = false
      console.log('Socket.IO disconnected:', reason)
    })

    this.socket.on('reconnect', (attemptNumber) => {
      this.isConnected = true
      console.log('Socket.IO reconnected after', attemptNumber, 'attempts')
    })

    this.socket.on('reconnect_attempt', (attemptNumber) => {
      console.log('Socket.IO reconnection attempt:', attemptNumber)
    })

    this.socket.on('reconnect_error', (error) => {
      console.error('Socket.IO reconnection error:', error)
    })

    this.socket.on('reconnect_failed', () => {
      console.error('Socket.IO reconnection failed')
    })

    // Handle authentication errors
    this.socket.on('auth_error', (error) => {
      console.error('Socket.IO authentication error:', error)
      this.disconnect()
    })

    // Handle server errors
    this.socket.on('error', (error) => {
      console.error('Socket.IO error:', error)
    })
  }

  /**
   * Join a room
   */
  joinRoom(roomId: string): void {
    this.emit('join_room', { roomId })
  }

  /**
   * Leave a room
   */
  leaveRoom(roomId: string): void {
    this.emit('leave_room', { roomId })
  }

  /**
   * Send heartbeat to maintain connection
   */
  sendHeartbeat(): void {
    this.emit('heartbeat', { timestamp: new Date().toISOString() })
  }

  /**
   * Get socket instance (for advanced usage)
   */
  getSocket(): Socket | null {
    return this.socket
  }
}

// ============================================================================
// Socket Service Instance
// ============================================================================

// Create singleton instance
export const socketService = new SocketService()

// Export class for custom instances
export default SocketService

// ============================================================================
// Socket Event Types (for type safety)
// ============================================================================

export interface SocketEvents {
  // Connection events
  'connect': () => void
  'disconnect': (reason: string) => void
  'reconnect': (attemptNumber: number) => void
  'connect_error': (error: Error) => void
  'auth_error': (error: string) => void

  // Notification events
  'notification:received': (notification: any) => void
  'notification:updated': (notification: any) => void
  'notification:deleted': (notificationId: string) => void
  'system:alert': (alert: any) => void
  'user:message': (message: any) => void
  'connection:status': (status: string) => void

  // Room events
  'room:joined': (roomId: string) => void
  'room:left': (roomId: string) => void
  'room:error': (error: string) => void

  // Heartbeat
  'heartbeat': (data: { timestamp: string }) => void
  'heartbeat_ack': (data: { timestamp: string }) => void
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Create a typed socket service instance
 */
export function createTypedSocketService(config?: SocketServiceConfig): SocketService {
  return new SocketService(config)
}

/**
 * Handle socket service errors
 */
export function handleSocketError(error: any): { message: string; type: string } {
  if (error?.message) {
    return {
      message: error.message,
      type: 'socket_error'
    }
  }

  if (typeof error === 'string') {
    return {
      message: error,
      type: 'socket_error'
    }
  }

  return {
    message: 'Unknown socket error',
    type: 'socket_error'
  }
}

/**
 * Socket connection health check
 */
export function checkSocketHealth(): {
  connected: boolean
  latency?: number
  transport?: string
} {
  const status = socketService.getConnectionStatus()
  
  return {
    connected: status.connected,
    transport: status.transport
  }
}
