// services/frontend/orderService.ts - Order management service

import { apiClient, buildQueryParams } from './api-client'
import type {
  ApiResponse,
  PaginationParams,
  Order,
  OrderItem,
  CreateOrderData,
  UpdateOrderData,
  OrderFilters,
  OrderStatus,
  PaymentStatus,
  PaymentMethod
} from '@/types/frontend'

// ============================================================================
// Order Service Class
// ============================================================================

class OrderService {
  private readonly baseEndpoint = '/orders'

  /**
   * Get all orders with pagination and filtering
   */
  async getOrders(
    pagination: PaginationParams,
    filters: OrderFilters = {}
  ): Promise<ApiResponse<Order[]>> {
    try {
      const params = buildQueryParams(pagination, filters)
      return await apiClient.get<Order[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('OrderService.getOrders error:', error)
      throw error
    }
  }

  /**
   * Get order by ID
   */
  async getOrderById(orderId: string): Promise<ApiResponse<Order>> {
    try {
      return await apiClient.get<Order>(`${this.baseEndpoint}/${orderId}`)
    } catch (error) {
      console.error('OrderService.getOrderById error:', error)
      throw error
    }
  }

  /**
   * Create new order
   */
  async createOrder(orderData: CreateOrderData): Promise<ApiResponse<Order>> {
    try {
      return await apiClient.post<Order>(this.baseEndpoint, orderData)
    } catch (error) {
      console.error('OrderService.createOrder error:', error)
      throw error
    }
  }

  /**
   * Update order
   */
  async updateOrder(orderId: string, updates: UpdateOrderData): Promise<ApiResponse<Order>> {
    try {
      return await apiClient.put<Order>(`${this.baseEndpoint}/${orderId}`, updates)
    } catch (error) {
      console.error('OrderService.updateOrder error:', error)
      throw error
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(orderId: string, reason?: string): Promise<ApiResponse<Order>> {
    try {
      return await apiClient.put<Order>(`${this.baseEndpoint}/${orderId}`, {
        status: 'Cancelled',
        notes: reason ? `Cancelled: ${reason}` : 'Order cancelled'
      })
    } catch (error) {
      console.error('OrderService.cancelOrder error:', error)
      throw error
    }
  }

  /**
   * Get orders by customer
   */
  async getOrdersByCustomer(
    customerId: string,
    pagination: PaginationParams = { page: 1, limit: 10 }
  ): Promise<ApiResponse<Order[]>> {
    try {
      const params = buildQueryParams(pagination, { customerId })
      return await apiClient.get<Order[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('OrderService.getOrdersByCustomer error:', error)
      throw error
    }
  }

  /**
   * Get orders by branch
   */
  async getOrdersByBranch(
    branchId: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Order[]>> {
    try {
      const params = buildQueryParams(pagination, { branchId })
      return await apiClient.get<Order[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('OrderService.getOrdersByBranch error:', error)
      throw error
    }
  }

  /**
   * Get orders by status
   */
  async getOrdersByStatus(
    status: OrderStatus,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Order[]>> {
    try {
      const params = buildQueryParams(pagination, { status })
      return await apiClient.get<Order[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('OrderService.getOrdersByStatus error:', error)
      throw error
    }
  }

  /**
   * Get recent orders
   */
  async getRecentOrders(
    limit: number = 10,
    branchId?: string
  ): Promise<ApiResponse<Order[]>> {
    try {
      const params: Record<string, string | number> = {
        limit,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      }
      if (branchId) params.branchId = branchId

      return await apiClient.get<Order[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('OrderService.getRecentOrders error:', error)
      throw error
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(orderId: string, status: OrderStatus): Promise<ApiResponse<Order>> {
    try {
      return await apiClient.put<Order>(`${this.baseEndpoint}/${orderId}`, { status })
    } catch (error) {
      console.error('OrderService.updateOrderStatus error:', error)
      throw error
    }
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(orderId: string, paymentStatus: PaymentStatus): Promise<ApiResponse<Order>> {
    try {
      return await apiClient.put<Order>(`${this.baseEndpoint}/${orderId}`, { paymentStatus })
    } catch (error) {
      console.error('OrderService.updatePaymentStatus error:', error)
      throw error
    }
  }

  /**
   * Add tracking number
   */
  async addTrackingNumber(orderId: string, trackingNumber: string): Promise<ApiResponse<Order>> {
    try {
      return await apiClient.put<Order>(`${this.baseEndpoint}/${orderId}`, {
        trackingNumber,
        status: 'Shipped'
      })
    } catch (error) {
      console.error('OrderService.addTrackingNumber error:', error)
      throw error
    }
  }

  /**
   * Get order statuses
   */
  getOrderStatuses(): OrderStatus[] {
    return ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled', 'Returned']
  }

  /**
   * Get payment statuses
   */
  getPaymentStatuses(): PaymentStatus[] {
    return ['Pending', 'Paid', 'Failed', 'Refunded', 'Partially Refunded']
  }

  /**
   * Get payment methods
   */
  getPaymentMethods(): PaymentMethod[] {
    return ['Cash', 'Card', 'Mobile Money', 'Bank Transfer', 'Credit']
  }

  /**
   * Calculate order totals
   */
  calculateOrderTotals(
    items: OrderItem[],
    taxRate: number = 0.18,
    shippingCost: number = 0,
    discountAmount: number = 0
  ): {
    subtotal: number
    tax: number
    shipping: number
    discount: number
    total: number
  } {
    const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0)
    const tax = subtotal * taxRate
    const total = subtotal + tax + shippingCost - discountAmount

    return {
      subtotal,
      tax,
      shipping: shippingCost,
      discount: discountAmount,
      total: Math.max(0, total) // Ensure total is not negative
    }
  }

  /**
   * Validate order data before submission
   */
  validateOrderData(orderData: CreateOrderData): {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  } {
    const errors: Array<{ field: string; message: string }> = []

    // Customer validation
    if (!orderData.customerId || orderData.customerId.trim().length === 0) {
      errors.push({ field: 'customerId', message: 'Customer is required' })
    }

    // Branch validation
    if (!orderData.branchId || orderData.branchId.trim().length === 0) {
      errors.push({ field: 'branchId', message: 'Branch is required' })
    }

    // Items validation
    if (!orderData.items || orderData.items.length === 0) {
      errors.push({ field: 'items', message: 'At least one item is required' })
    } else {
      orderData.items.forEach((item, index) => {
        if (!item.productId || item.productId.trim().length === 0) {
          errors.push({ field: `items.${index}.productId`, message: 'Product is required' })
        }
        if (!item.quantity || item.quantity <= 0) {
          errors.push({ field: `items.${index}.quantity`, message: 'Quantity must be greater than 0' })
        }
      })
    }

    // Payment method validation
    const validPaymentMethods = this.getPaymentMethods()
    if (!orderData.paymentMethod || !validPaymentMethods.includes(orderData.paymentMethod)) {
      errors.push({ field: 'paymentMethod', message: 'Please select a valid payment method' })
    }

    // Shipping address validation
    if (!orderData.shippingAddress) {
      errors.push({ field: 'shippingAddress', message: 'Shipping address is required' })
    } else {
      const { shippingAddress } = orderData
      if (!shippingAddress.street || shippingAddress.street.trim().length === 0) {
        errors.push({ field: 'shippingAddress.street', message: 'Street address is required' })
      }
      if (!shippingAddress.city || shippingAddress.city.trim().length === 0) {
        errors.push({ field: 'shippingAddress.city', message: 'City is required' })
      }
      if (!shippingAddress.region || shippingAddress.region.trim().length === 0) {
        errors.push({ field: 'shippingAddress.region', message: 'Region is required' })
      }
      if (!shippingAddress.country || shippingAddress.country.trim().length === 0) {
        errors.push({ field: 'shippingAddress.country', message: 'Country is required' })
      }
    }

    // Billing address validation
    if (!orderData.billingAddress) {
      errors.push({ field: 'billingAddress', message: 'Billing address is required' })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Format order number for display
   */
  formatOrderNumber(orderNumber: string): string {
    return orderNumber.toUpperCase()
  }

  /**
   * Get order status color
   */
  getOrderStatusColor(status: OrderStatus): string {
    const statusColors: Record<OrderStatus, string> = {
      'Pending': 'yellow',
      'Processing': 'blue',
      'Shipped': 'purple',
      'Delivered': 'green',
      'Cancelled': 'red',
      'Returned': 'orange'
    }
    return statusColors[status] || 'gray'
  }

  /**
   * Get payment status color
   */
  getPaymentStatusColor(status: PaymentStatus): string {
    const statusColors: Record<PaymentStatus, string> = {
      'Pending': 'yellow',
      'Paid': 'green',
      'Failed': 'red',
      'Refunded': 'orange',
      'Partially Refunded': 'orange'
    }
    return statusColors[status] || 'gray'
  }

  /**
   * Check if order can be cancelled
   */
  canCancelOrder(order: Order): boolean {
    return ['Pending', 'Processing'].includes(order.status)
  }

  /**
   * Check if order can be returned
   */
  canReturnOrder(order: Order): boolean {
    return order.status === 'Delivered' && order.paymentStatus === 'Paid'
  }

  /**
   * Format order total for display
   */
  formatOrderTotal(total: number, currency: string = 'MWK'): string {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(total)
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const orderService = new OrderService()
export default orderService
