// services/frontend/shopService.ts - Shop/Branch management service

import { apiClient, buildQueryParams } from './api-client'
import type {
  ApiResponse,
  PaginationParams,
  Shop,
  CreateShopData,
  UpdateShopData,
  ShopFilters
} from '@/types/frontend'

// ============================================================================
// Shop Service Class
// ============================================================================

class ShopService {
  private readonly baseEndpoint = '/shops'

  /**
   * Get all shops with pagination and filtering
   */
  async getShops(
    pagination: PaginationParams,
    filters: ShopFilters = {}
  ): Promise<ApiResponse<Shop[]>> {
    try {
      const params = buildQueryParams(pagination, filters)
      return await apiClient.get<Shop[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ShopService.getShops error:', error)
      throw error
    }
  }

  /**
   * Get shop by ID
   */
  async getShopById(shopId: string): Promise<ApiResponse<Shop>> {
    try {
      return await apiClient.get<Shop>(`${this.baseEndpoint}/${shopId}`)
    } catch (error) {
      console.error('ShopService.getShopById error:', error)
      throw error
    }
  }

  /**
   * Create new shop
   */
  async createShop(shopData: CreateShopData): Promise<ApiResponse<Shop>> {
    try {
      return await apiClient.post<Shop>(this.baseEndpoint, shopData)
    } catch (error) {
      console.error('ShopService.createShop error:', error)
      throw error
    }
  }

  /**
   * Update shop
   */
  async updateShop(shopId: string, updates: UpdateShopData): Promise<ApiResponse<Shop>> {
    try {
      return await apiClient.put<Shop>(`${this.baseEndpoint}/${shopId}`, updates)
    } catch (error) {
      console.error('ShopService.updateShop error:', error)
      throw error
    }
  }

  /**
   * Delete shop (soft delete)
   */
  async deleteShop(shopId: string): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.delete<boolean>(`${this.baseEndpoint}/${shopId}`)
    } catch (error) {
      console.error('ShopService.deleteShop error:', error)
      throw error
    }
  }

  /**
   * Get shops by country
   */
  async getShopsByCountry(country: string): Promise<ApiResponse<Shop[]>> {
    try {
      const params = { country }
      return await apiClient.get<Shop[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ShopService.getShopsByCountry error:', error)
      throw error
    }
  }

  /**
   * Get shops by region
   */
  async getShopsByRegion(country: string, region: string): Promise<ApiResponse<Shop[]>> {
    try {
      const params = { country, region }
      return await apiClient.get<Shop[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ShopService.getShopsByRegion error:', error)
      throw error
    }
  }

  /**
   * Get shops by manager
   */
  async getShopsByManager(managerId: string): Promise<ApiResponse<Shop[]>> {
    try {
      const params = { managerId }
      return await apiClient.get<Shop[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ShopService.getShopsByManager error:', error)
      throw error
    }
  }

  /**
   * Search shops
   */
  async searchShops(
    query: string,
    pagination: PaginationParams = { page: 1, limit: 10 }
  ): Promise<ApiResponse<Shop[]>> {
    try {
      const params = buildQueryParams(pagination, { search: query })
      return await apiClient.get<Shop[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ShopService.searchShops error:', error)
      throw error
    }
  }

  /**
   * Get active shops only
   */
  async getActiveShops(
    pagination: PaginationParams = { page: 1, limit: 50 }
  ): Promise<ApiResponse<Shop[]>> {
    try {
      const params = buildQueryParams(pagination, { status: 'Active' })
      return await apiClient.get<Shop[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('ShopService.getActiveShops error:', error)
      throw error
    }
  }

  /**
   * Update shop status
   */
  async updateShopStatus(
    shopId: string, 
    status: 'Active' | 'Inactive' | 'Opening Soon' | 'Maintenance'
  ): Promise<ApiResponse<Shop>> {
    try {
      return await apiClient.put<Shop>(`${this.baseEndpoint}/${shopId}`, { status })
    } catch (error) {
      console.error('ShopService.updateShopStatus error:', error)
      throw error
    }
  }

  /**
   * Get shop statistics
   */
  async getShopStats(shopId: string): Promise<ApiResponse<{
    totalProducts: number
    totalSales: number
    totalOrders: number
    totalCustomers: number
  }>> {
    try {
      return await apiClient.get<{
        totalProducts: number
        totalSales: number
        totalOrders: number
        totalCustomers: number
      }>(`${this.baseEndpoint}/${shopId}/stats`)
    } catch (error) {
      console.error('ShopService.getShopStats error:', error)
      throw error
    }
  }

  /**
   * Upload shop image
   */
  async uploadShopImage(shopId: string, imageFile: File): Promise<ApiResponse<{ imageUrl: string }>> {
    try {
      return await apiClient.upload<{ imageUrl: string }>(
        `${this.baseEndpoint}/${shopId}/image`,
        imageFile
      )
    } catch (error) {
      console.error('ShopService.uploadShopImage error:', error)
      throw error
    }
  }

  /**
   * Get nearby shops (if coordinates are provided)
   */
  async getNearbyShops(
    latitude: number,
    longitude: number,
    radiusKm: number = 50
  ): Promise<ApiResponse<Shop[]>> {
    try {
      const params = {
        lat: latitude,
        lng: longitude,
        radius: radiusKm
      }
      return await apiClient.get<Shop[]>(`${this.baseEndpoint}/nearby`, params)
    } catch (error) {
      console.error('ShopService.getNearbyShops error:', error)
      throw error
    }
  }

  /**
   * Validate shop data before submission
   */
  validateShopData(shopData: CreateShopData | UpdateShopData): {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  } {
    const errors: Array<{ field: string; message: string }> = []

    // Name validation
    if ('name' in shopData && (!shopData.name || shopData.name.trim().length < 2)) {
      errors.push({ field: 'name', message: 'Shop name must be at least 2 characters long' })
    }

    // Email validation
    if ('email' in shopData && shopData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(shopData.email)) {
        errors.push({ field: 'email', message: 'Please enter a valid email address' })
      }
    }

    // Phone validation
    if ('phone' in shopData && shopData.phone) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
      if (!phoneRegex.test(shopData.phone)) {
        errors.push({ field: 'phone', message: 'Please enter a valid phone number' })
      }
    }

    // Operating hours validation
    if ('operatingHours' in shopData && shopData.operatingHours) {
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
      if (!timeRegex.test(shopData.operatingHours.open)) {
        errors.push({ field: 'operatingHours.open', message: 'Please enter a valid opening time (HH:MM)' })
      }
      if (!timeRegex.test(shopData.operatingHours.close)) {
        errors.push({ field: 'operatingHours.close', message: 'Please enter a valid closing time (HH:MM)' })
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const shopService = new ShopService()
export default shopService
