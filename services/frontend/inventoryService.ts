// services/frontend/inventoryService.ts - Inventory management service

import { apiClient, buildQueryParams } from './api-client'
import type {
  ApiResponse,
  PaginationParams,
  Inventory,
  CreateInventoryData,
  UpdateInventoryData,
  StockMovement,
  InventoryFilters
} from '@/types/frontend'

// ============================================================================
// Inventory Service Class
// ============================================================================

class InventoryService {
  private readonly baseEndpoint = '/inventory'

  /**
   * Get all inventory items with pagination and filtering
   */
  async getInventory(
    pagination: PaginationParams,
    filters: InventoryFilters = {}
  ): Promise<ApiResponse<Inventory[]>> {
    try {
      const params = buildQueryParams(pagination, filters)
      return await apiClient.get<Inventory[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('InventoryService.getInventory error:', error)
      throw error
    }
  }

  /**
   * Get inventory item by ID
   */
  async getInventoryById(inventoryId: string): Promise<ApiResponse<Inventory>> {
    try {
      return await apiClient.get<Inventory>(`${this.baseEndpoint}/${inventoryId}`)
    } catch (error) {
      console.error('InventoryService.getInventoryById error:', error)
      throw error
    }
  }

  /**
   * Create new inventory item
   */
  async createInventory(inventoryData: CreateInventoryData): Promise<ApiResponse<Inventory>> {
    try {
      return await apiClient.post<Inventory>(this.baseEndpoint, inventoryData)
    } catch (error) {
      console.error('InventoryService.createInventory error:', error)
      throw error
    }
  }

  /**
   * Update inventory item
   */
  async updateInventory(inventoryId: string, updates: UpdateInventoryData): Promise<ApiResponse<Inventory>> {
    try {
      return await apiClient.put<Inventory>(`${this.baseEndpoint}/${inventoryId}`, updates)
    } catch (error) {
      console.error('InventoryService.updateInventory error:', error)
      throw error
    }
  }

  /**
   * Update stock levels with movement tracking
   */
  async updateStock(inventoryId: string, movement: StockMovement): Promise<ApiResponse<Inventory>> {
    try {
      return await apiClient.patch<Inventory>(`${this.baseEndpoint}/${inventoryId}`, movement)
    } catch (error) {
      console.error('InventoryService.updateStock error:', error)
      throw error
    }
  }

  /**
   * Get inventory by branch
   */
  async getInventoryByBranch(
    branchId: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Inventory[]>> {
    try {
      const params = buildQueryParams(pagination, { branchId })
      return await apiClient.get<Inventory[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('InventoryService.getInventoryByBranch error:', error)
      throw error
    }
  }



  /**
   * Search inventory items
   */
  async searchInventory(
    query: string,
    pagination: PaginationParams = { page: 1, limit: 20 },
    filters: Omit<InventoryFilters, 'search'> = {}
  ): Promise<ApiResponse<Inventory[]>> {
    try {
      const params = buildQueryParams(pagination, { ...filters, search: query })
      return await apiClient.get<Inventory[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('InventoryService.searchInventory error:', error)
      throw error
    }
  }

  /**
   * Get low stock items
   */
  async getLowStockItems(
    branchId?: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Inventory[]>> {
    try {
      const params = buildQueryParams(pagination, {
        lowStock: true,
        ...(branchId && { branchId })
      })
      return await apiClient.get<Inventory[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('InventoryService.getLowStockItems error:', error)
      throw error
    }
  }

  /**
   * Get stock movement types
   */
  getStockMovementTypes(): Array<{ value: 'in' | 'out' | 'adjustment'; label: string }> {
    return [
      { value: 'in', label: 'Stock In' },
      { value: 'out', label: 'Stock Out' },
      { value: 'adjustment', label: 'Stock Adjustment' }
    ]
  }

  /**
   * Validate inventory data before submission
   */
  validateInventoryData(inventoryData: CreateInventoryData | UpdateInventoryData): {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  } {
    const errors: Array<{ field: string; message: string }> = []

    // Product name validation
    if ('productName' in inventoryData && (!inventoryData.productName || inventoryData.productName.trim().length < 1)) {
      errors.push({ field: 'productName', message: 'Product name is required' })
    }

    // SKU validation
    if ('sku' in inventoryData && (!inventoryData.sku || inventoryData.sku.trim().length < 1)) {
      errors.push({ field: 'sku', message: 'SKU is required' })
    }

    // Stock validation
    if ('stock' in inventoryData && (inventoryData.stock === undefined || inventoryData.stock < 0)) {
      errors.push({ field: 'stock', message: 'Stock must be a non-negative number' })
    }

    // Min stock level validation
    if ('minStockLevel' in inventoryData && (inventoryData.minStockLevel === undefined || inventoryData.minStockLevel < 0)) {
      errors.push({ field: 'minStockLevel', message: 'Minimum stock level must be a non-negative number' })
    }

    // Max stock level validation
    if ('maxStockLevel' in inventoryData && (inventoryData.maxStockLevel === undefined || inventoryData.maxStockLevel < 0)) {
      errors.push({ field: 'maxStockLevel', message: 'Maximum stock level must be a non-negative number' })
    }

    // Min/Max stock level relationship validation
    if ('minStockLevel' in inventoryData && 'maxStockLevel' in inventoryData && 
        inventoryData.minStockLevel !== undefined && inventoryData.maxStockLevel !== undefined &&
        inventoryData.minStockLevel > inventoryData.maxStockLevel) {
      errors.push({ field: 'maxStockLevel', message: 'Maximum stock level must be greater than minimum stock level' })
    }

    // Cost validation
    if ('cost' in inventoryData && (inventoryData.cost === undefined || inventoryData.cost < 0)) {
      errors.push({ field: 'cost', message: 'Cost must be a non-negative number' })
    }

    // Location validation
    if ('location' in inventoryData && (!inventoryData.location || inventoryData.location.trim().length < 1)) {
      errors.push({ field: 'location', message: 'Location is required' })
    }

    // Expiry date validation
    if ('expiryDate' in inventoryData && inventoryData.expiryDate) {
      const expiryDate = new Date(inventoryData.expiryDate)
      const today = new Date()
      if (expiryDate < today) {
        errors.push({ field: 'expiryDate', message: 'Expiry date cannot be in the past' })
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validate stock movement data
   */
  validateStockMovement(movement: StockMovement): {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  } {
    const errors: Array<{ field: string; message: string }> = []

    // Type validation
    const validTypes = ['in', 'out', 'adjustment']
    if (!movement.type || !validTypes.includes(movement.type)) {
      errors.push({ field: 'type', message: 'Please select a valid movement type' })
    }

    // Quantity validation
    if (movement.quantity === undefined || movement.quantity <= 0) {
      errors.push({ field: 'quantity', message: 'Quantity must be greater than 0' })
    }

    // Reason validation
    if (!movement.reason || movement.reason.trim().length < 1) {
      errors.push({ field: 'reason', message: 'Reason is required' })
    }

    // Cost validation (optional)
    if (movement.cost !== undefined && movement.cost < 0) {
      errors.push({ field: 'cost', message: 'Cost must be a non-negative number' })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Check if item is low stock
   */
  isLowStock(inventory: Inventory): boolean {
    return inventory.stock <= inventory.minStockLevel
  }

  /**
   * Check if item is out of stock
   */
  isOutOfStock(inventory: Inventory): boolean {
    return inventory.stock === 0
  }

  /**
   * Get stock status
   */
  getStockStatus(inventory: Inventory): {
    status: 'out_of_stock' | 'low_stock' | 'in_stock' | 'overstocked'
    color: string
    label: string
  } {
    if (this.isOutOfStock(inventory)) {
      return { status: 'out_of_stock', color: 'red', label: 'Out of Stock' }
    } else if (this.isLowStock(inventory)) {
      return { status: 'low_stock', color: 'orange', label: 'Low Stock' }
    } else if (inventory.stock > inventory.maxStockLevel) {
      return { status: 'overstocked', color: 'purple', label: 'Overstocked' }
    } else {
      return { status: 'in_stock', color: 'green', label: 'In Stock' }
    }
  }

  /**
   * Calculate stock percentage
   */
  calculateStockPercentage(inventory: Inventory): number {
    if (inventory.maxStockLevel === 0) return 0
    return Math.min(100, (inventory.stock / inventory.maxStockLevel) * 100)
  }

  /**
   * Format cost for display
   */
  formatCost(cost: number, currency: string = 'MWK'): string {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(cost)
  }

  /**
   * Calculate total inventory value
   */
  calculateInventoryValue(inventoryItems: Inventory[]): number {
    return inventoryItems.reduce((total, item) => total + (item.stock * item.cost), 0)
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const inventoryService = new InventoryService()
export default inventoryService
