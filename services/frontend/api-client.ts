// services/frontend/api-client.ts - Base API client for all frontend services

import type { ApiResponse, PaginationParams } from '@/types/frontend'

// ============================================================================
// API Configuration
// ============================================================================

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api'

// ============================================================================
// Error Classes
// ============================================================================

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: unknown
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

export class NetworkError extends Error {
  constructor(message: string, public originalError: Error) {
    super(message)
    this.name = 'NetworkError'
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public details: Array<{ field: string; message: string }>
  ) {
    super(message)
    this.name = 'ValidationError'
  }
}

// ============================================================================
// Request Configuration
// ============================================================================

export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
  headers?: Record<string, string>
  body?: unknown
  params?: Record<string, string | number | boolean | undefined>
  timeout?: number
}

// ============================================================================
// API Client Class
// ============================================================================

class ApiClient {
  private baseURL: string
  private defaultHeaders: Record<string, string>
  private timeout: number
  private authToken: string | null = null
  private isRefreshing: boolean = false
  private refreshPromise: Promise<boolean> | null = null
  private failedQueue: Array<{
    resolve: (value: boolean) => void
    reject: (error: any) => void
  }> = []

  constructor() {
    this.baseURL = API_BASE_URL
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    }
    this.timeout = 30000 // 30 seconds

    // Debug API configuration
    console.log('=== API CLIENT INITIALIZATION ===')
    console.log('API_BASE_URL environment variable:', process.env.NEXT_PUBLIC_API_URL)
    console.log('Resolved baseURL:', this.baseURL)
    console.log('Default headers:', this.defaultHeaders)
    console.log('=== API CLIENT INITIALIZATION END ===')
  }

  /**
   * Set authentication token with validation
   */
  setAuthToken(token: string | null): void {
    this.authToken = token
    if (token) {
      // Validate token before setting (client-side basic validation)
      try {
        const parts = token.split('.')
        if (parts.length === 3) {
          const payload = JSON.parse(atob(parts[1]))
          if (!payload.exp || payload.exp * 1000 < Date.now()) {
            console.warn('Attempting to set expired token')
            this.authToken = null
            delete this.defaultHeaders['Authorization']
            return
          }
          this.defaultHeaders['Authorization'] = `Bearer ${token}`
        } else {
          console.error('Invalid token format')
          this.authToken = null
          delete this.defaultHeaders['Authorization']
        }
      } catch (error) {
        console.error('Invalid token format:', error)
        this.authToken = null
        delete this.defaultHeaders['Authorization']
      }
    } else {
      delete this.defaultHeaders['Authorization']
    }
  }

  /**
   * Get current auth token
   */
  getAuthToken(): string | null {
    return this.authToken
  }

  /**
   * Refresh authentication token
   */
  private async refreshAuthToken(): Promise<boolean> {
    if (this.isRefreshing) {
      // If already refreshing, wait for the existing refresh to complete
      return new Promise((resolve, reject) => {
        this.failedQueue.push({ resolve, reject })
      })
    }

    this.isRefreshing = true

    try {
      // Fix the URL construction - remove duplicate /api
      const refreshUrl = this.baseURL === '/api'
        ? '/api/auth/refresh'
        : `${this.baseURL}/auth/refresh`

      console.log('=== TOKEN REFRESH ATTEMPT ===')
      console.log('Refresh URL:', refreshUrl)

      const response = await fetch(refreshUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for refresh token
      })

      console.log('Refresh response status:', response.status)
      console.log('Refresh response ok:', response.ok)

      if (response.ok) {
        const data = await response.json()
        console.log('Refresh response data:', data)

        if (data.success && data.accessToken) {
          this.setAuthToken(data.accessToken)

          // Process failed queue
          this.failedQueue.forEach(({ resolve }) => resolve(true))
          this.failedQueue = []

          console.log('=== TOKEN REFRESH SUCCESS ===')
          return true
        }
      }

      // Refresh failed
      console.log('=== TOKEN REFRESH FAILED ===')
      this.setAuthToken(null)
      this.failedQueue.forEach(({ reject }) => reject(new Error('Token refresh failed')))
      this.failedQueue = []

      return false
    } catch (error) {
      console.error('=== TOKEN REFRESH EXCEPTION ===', error)
      this.setAuthToken(null)
      this.failedQueue.forEach(({ reject }) => reject(error))
      this.failedQueue = []

      return false
    } finally {
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  /**
   * Build URL with query parameters
   */
  private buildURL(endpoint: string, params?: Record<string, string | number | boolean | undefined>): string {
    console.log('=== API CLIENT BUILD URL ===')
    console.log('Input endpoint:', endpoint)
    console.log('Input params:', params)
    console.log('Base URL:', this.baseURL)
    console.log('Window location origin:', typeof window !== 'undefined' ? window.location.origin : 'Server-side')

    // Handle relative base URLs by constructing absolute URL
    let fullURL: string

    if (this.baseURL.startsWith('http')) {
      // Base URL is already absolute
      fullURL = new URL(endpoint, this.baseURL).toString()
      console.log('Using absolute base URL, constructed:', fullURL)
    } else {
      // Base URL is relative, construct absolute URL using current origin
      const baseURL = typeof window !== 'undefined'
        ? `${window.location.origin}${this.baseURL}`
        : this.baseURL

      console.log('Using relative base URL, resolved baseURL:', baseURL)

      if (endpoint.startsWith('/')) {
        // Endpoint is absolute path
        fullURL = typeof window !== 'undefined'
          ? `${window.location.origin}${endpoint}`
          : endpoint
        console.log('Endpoint is absolute path, constructed:', fullURL)
      } else {
        // Endpoint is relative to base
        fullURL = `${baseURL}/${endpoint}`.replace(/\/+/g, '/').replace(':/', '://')
        console.log('Endpoint is relative to base, constructed:', fullURL)
      }
    }

    // Add query parameters if provided
    if (params) {
      const url = new URL(fullURL)
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          url.searchParams.append(key, String(value))
        }
      })
      const finalURL = url.toString()
      console.log('Added query params, final URL:', finalURL)
      console.log('=== API CLIENT BUILD URL END ===')
      return finalURL
    }

    console.log('No query params, final URL:', fullURL)
    console.log('=== API CLIENT BUILD URL END ===')
    return fullURL
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    console.log('=== API CLIENT HANDLE RESPONSE ===')
    console.log('Response status:', response.status)
    console.log('Response ok:', response.ok)

    const contentType = response.headers.get('content-type')
    const isJson = contentType?.includes('application/json')
    console.log('Content type:', contentType)
    console.log('Is JSON:', isJson)

    let data: unknown
    try {
      data = isJson ? await response.json() : await response.text()
      console.log('Parsed response data:', data)
    } catch (error) {
      console.error('Failed to parse response:', error)
      throw new ApiError('Failed to parse response', response.status, error)
    }

    if (!response.ok) {
      const errorData = data as { error?: string; details?: Array<{ field: string; message: string }> }
      
      if (response.status === 422 && errorData.details) {
        throw new ValidationError(errorData.error || 'Validation failed', errorData.details)
      }
      
      throw new ApiError(
        errorData.error || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        data
      )
    }

    const result = data as ApiResponse<T>
    console.log('Final API response result:', result)
    console.log('=== API CLIENT HANDLE RESPONSE END ===')
    return result
  }

  /**
   * Make HTTP request
   */
  async request<T>(endpoint: string, config: RequestConfig = {}): Promise<ApiResponse<T>> {
    return this.makeRequest<T>(endpoint, config, false)
  }

  /**
   * Internal request method with automatic token refresh
   */
  private async makeRequest<T>(
    endpoint: string,
    config: RequestConfig = {},
    isRetry: boolean = false
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      params,
      timeout = this.timeout
    } = config

    const url = this.buildURL(endpoint, params)
    const requestHeaders = { ...this.defaultHeaders, ...headers }

    // Create abort controller for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      console.log('=== API CLIENT REQUEST ===')
      console.log('URL:', url)
      console.log('Method:', method)
      console.log('Headers:', requestHeaders)
      console.log('Body:', body ? JSON.stringify(body, null, 2) : 'undefined')
      console.log('Is Retry:', isRetry)

      const response = await fetch(url, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined,
        signal: controller.signal,
        credentials: 'include' // Include cookies for refresh token
      })

      console.log('=== API CLIENT RESPONSE ===')
      console.log('Status:', response.status)
      console.log('Status Text:', response.statusText)
      console.log('Response Headers:', Object.fromEntries(response.headers.entries()))

      clearTimeout(timeoutId)

      // Handle 401 Unauthorized - attempt token refresh
      if (response.status === 401 && !isRetry && this.authToken && !endpoint.includes('/auth/')) {
        console.log('=== 401 DETECTED - ATTEMPTING TOKEN REFRESH ===')
        try {
          const refreshSuccess = await this.refreshAuthToken()
          if (refreshSuccess) {
            console.log('=== TOKEN REFRESH SUCCESS - RETRYING REQUEST ===')
            // Retry the original request with new token
            return this.makeRequest<T>(endpoint, config, true)
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError)
        }

        // If refresh failed, clear token and redirect to login
        console.log('=== TOKEN REFRESH FAILED - CLEARING AUTH ===')
        this.setAuthToken(null)

        // Redirect to login if in browser
        if (typeof window !== 'undefined') {
          window.location.href = '/login?reason=session-expired'
          return { success: false, error: 'Session expired' } as ApiResponse<T>
        }
      }

      return await this.handleResponse<T>(response)
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof ApiError || error instanceof ValidationError) {
        throw error
      }

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new NetworkError('Request timeout', error)
        }
        throw new NetworkError('Network request failed', error)
      }

      throw new NetworkError('Unknown network error', new Error(String(error)))
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, params?: Record<string, string | number | boolean | undefined>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', params })
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, body?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body })
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, body?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body })
  }

  /**
   * PATCH request
   */
  async patch<T>(endpoint: string, body?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PATCH', body })
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }

  /**
   * Upload file
   */
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, unknown>): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    const headers = { ...this.defaultHeaders }
    delete headers['Content-Type'] // Let browser set multipart boundary

    return this.request<T>(endpoint, {
      method: 'POST',
      headers,
      body: formData
    })
  }
}

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Build pagination parameters
 */
export function buildPaginationParams(pagination: PaginationParams): Record<string, string | number> {
  return {
    page: pagination.page,
    limit: pagination.limit
  }
}

/**
 * Build filter parameters (removes undefined values)
 */
export function buildFilterParams(filters: Record<string, unknown> | object): Record<string, string | number | boolean> {
  const params: Record<string, string | number | boolean> = {}

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        params[key] = value
      }
    }
  })

  return params
}

/**
 * Combine pagination and filter parameters
 */
export function buildQueryParams(
  pagination: PaginationParams,
  filters: Record<string, unknown> | object = {}
): Record<string, string | number | boolean> {
  return {
    ...buildPaginationParams(pagination),
    ...buildFilterParams(filters)
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const apiClient = new ApiClient()

// Export types for use in services
export type { RequestConfig as ApiRequestConfig }
