// services/frontend/customerService.ts - Customer management service

import { apiClient, buildQueryParams } from './api-client'
import type {
  ApiResponse,
  PaginationParams,
  Customer,
  CustomerWithOrders,
  CreateCustomerData,
  UpdateCustomerData,
  CustomerFilters
} from '@/types/frontend'

// ============================================================================
// Customer Service Class
// ============================================================================

class CustomerService {
  private readonly baseEndpoint = '/customers'

  /**
   * Get all customers with pagination and filtering
   */
  async getCustomers(
    pagination: PaginationParams,
    filters: CustomerFilters = {}
  ): Promise<ApiResponse<Customer[]>> {
    try {
      const params = buildQueryParams(pagination, filters)
      return await apiClient.get<Customer[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('CustomerService.getCustomers error:', error)
      throw error
    }
  }

  /**
   * Get customer by ID
   */
  async getCustomerById(
    customerId: string,
    includeOrders: boolean = false
  ): Promise<ApiResponse<CustomerWithOrders>> {
    try {
      const params = includeOrders ? { includeOrders: 'true' } : {}
      return await apiClient.get<CustomerWithOrders>(`${this.baseEndpoint}/${customerId}`, params)
    } catch (error) {
      console.error('CustomerService.getCustomerById error:', error)
      throw error
    }
  }

  /**
   * Create new customer
   */
  async createCustomer(customerData: CreateCustomerData): Promise<ApiResponse<Customer>> {
    try {
      return await apiClient.post<Customer>(this.baseEndpoint, customerData)
    } catch (error) {
      console.error('CustomerService.createCustomer error:', error)
      throw error
    }
  }

  /**
   * Update customer
   */
  async updateCustomer(customerId: string, updates: UpdateCustomerData): Promise<ApiResponse<Customer>> {
    try {
      return await apiClient.put<Customer>(`${this.baseEndpoint}/${customerId}`, updates)
    } catch (error) {
      console.error('CustomerService.updateCustomer error:', error)
      throw error
    }
  }

  /**
   * Delete customer (soft delete)
   */
  async deleteCustomer(customerId: string): Promise<ApiResponse<boolean>> {
    try {
      return await apiClient.delete<boolean>(`${this.baseEndpoint}/${customerId}`)
    } catch (error) {
      console.error('CustomerService.deleteCustomer error:', error)
      throw error
    }
  }

  /**
   * Search customers
   */
  async searchCustomers(
    query: string,
    pagination: PaginationParams = { page: 1, limit: 20 },
    filters: Omit<CustomerFilters, 'search'> = {}
  ): Promise<ApiResponse<Customer[]>> {
    try {
      const params = buildQueryParams(pagination, { ...filters, search: query })
      return await apiClient.get<Customer[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('CustomerService.searchCustomers error:', error)
      throw error
    }
  }

  /**
   * Get customers by branch
   */
  async getCustomersByBranch(
    branchId: string,
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Customer[]>> {
    try {
      const params = buildQueryParams(pagination, { preferredBranch: branchId })
      return await apiClient.get<Customer[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('CustomerService.getCustomersByBranch error:', error)
      throw error
    }
  }

  /**
   * Get top customers by spending
   */
  async getTopCustomers(
    limit: number = 10,
    branchId?: string
  ): Promise<ApiResponse<Customer[]>> {
    try {
      const params: Record<string, string | number> = {
        limit,
        sortBy: 'totalSpent',
        sortOrder: 'desc'
      }
      if (branchId) params.preferredBranch = branchId

      return await apiClient.get<Customer[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('CustomerService.getTopCustomers error:', error)
      throw error
    }
  }

  /**
   * Get active customers only
   */
  async getActiveCustomers(
    pagination: PaginationParams = { page: 1, limit: 20 }
  ): Promise<ApiResponse<Customer[]>> {
    try {
      const params = buildQueryParams(pagination, { isActive: true })
      return await apiClient.get<Customer[]>(this.baseEndpoint, params)
    } catch (error) {
      console.error('CustomerService.getActiveCustomers error:', error)
      throw error
    }
  }

  /**
   * Update customer loyalty points
   */
  async updateLoyaltyPoints(
    customerId: string,
    points: number,
    operation: 'add' | 'subtract' | 'set' = 'add'
  ): Promise<ApiResponse<Customer>> {
    try {
      return await apiClient.patch<Customer>(`${this.baseEndpoint}/${customerId}/loyalty`, {
        points,
        operation
      })
    } catch (error) {
      console.error('CustomerService.updateLoyaltyPoints error:', error)
      throw error
    }
  }

  /**
   * Get customer statistics
   */
  async getCustomerStats(customerId: string): Promise<ApiResponse<{
    totalOrders: number
    totalSpent: number
    averageOrderValue: number
    loyaltyPoints: number
    lastOrderDate: string | null
    favoriteCategory: string | null
    orderFrequency: number
  }>> {
    try {
      return await apiClient.get<{
        totalOrders: number
        totalSpent: number
        averageOrderValue: number
        loyaltyPoints: number
        lastOrderDate: string | null
        favoriteCategory: string | null
        orderFrequency: number
      }>(`${this.baseEndpoint}/${customerId}/stats`)
    } catch (error) {
      console.error('CustomerService.getCustomerStats error:', error)
      throw error
    }
  }

  /**
   * Validate customer data before submission
   */
  validateCustomerData(customerData: CreateCustomerData | UpdateCustomerData): {
    isValid: boolean
    errors: Array<{ field: string; message: string }>
  } {
    const errors: Array<{ field: string; message: string }> = []

    // First name validation
    if ('firstName' in customerData && (!customerData.firstName || customerData.firstName.trim().length < 1)) {
      errors.push({ field: 'firstName', message: 'First name is required' })
    }

    // Last name validation
    if ('lastName' in customerData && (!customerData.lastName || customerData.lastName.trim().length < 1)) {
      errors.push({ field: 'lastName', message: 'Last name is required' })
    }

    // Email validation
    if ('email' in customerData && customerData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(customerData.email)) {
        errors.push({ field: 'email', message: 'Please enter a valid email address' })
      }
    }

    // Phone validation
    if ('phone' in customerData && customerData.phone) {
      const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
      if (!phoneRegex.test(customerData.phone)) {
        errors.push({ field: 'phone', message: 'Please enter a valid phone number' })
      }
    }

    // Address validation
    if ('address' in customerData && customerData.address) {
      const { address } = customerData
      if (!address.street || address.street.trim().length < 1) {
        errors.push({ field: 'address.street', message: 'Street address is required' })
      }
      if (!address.city || address.city.trim().length < 1) {
        errors.push({ field: 'address.city', message: 'City is required' })
      }
      if (!address.region || address.region.trim().length < 1) {
        errors.push({ field: 'address.region', message: 'Region is required' })
      }
      if (!address.country || address.country.trim().length < 1) {
        errors.push({ field: 'address.country', message: 'Country is required' })
      }
      if (!address.postalCode || address.postalCode.trim().length < 1) {
        errors.push({ field: 'address.postalCode', message: 'Postal code is required' })
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Format customer full name
   */
  formatCustomerName(customer: Customer): string {
    return `${customer.firstName} ${customer.lastName}`.trim()
  }

  /**
   * Format customer address
   */
  formatCustomerAddress(customer: Customer): string {
    const { address } = customer
    return `${address.street}, ${address.city}, ${address.region}, ${address.country} ${address.postalCode}`
  }

  /**
   * Calculate customer tier based on total spent
   */
  getCustomerTier(totalSpent: number): {
    tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum'
    nextTierThreshold: number | null
    benefits: string[]
  } {
    if (totalSpent >= 1000000) { // 1M MWK
      return {
        tier: 'Platinum',
        nextTierThreshold: null,
        benefits: ['20% discount', 'Free shipping', 'Priority support', 'Exclusive products']
      }
    } else if (totalSpent >= 500000) { // 500K MWK
      return {
        tier: 'Gold',
        nextTierThreshold: 1000000,
        benefits: ['15% discount', 'Free shipping', 'Priority support']
      }
    } else if (totalSpent >= 100000) { // 100K MWK
      return {
        tier: 'Silver',
        nextTierThreshold: 500000,
        benefits: ['10% discount', 'Free shipping on orders over 50K']
      }
    } else {
      return {
        tier: 'Bronze',
        nextTierThreshold: 100000,
        benefits: ['5% discount on orders over 20K']
      }
    }
  }

  /**
   * Calculate loyalty points for purchase
   */
  calculateLoyaltyPoints(purchaseAmount: number): number {
    // 1 point per 1000 MWK spent
    return Math.floor(purchaseAmount / 1000)
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const customerService = new CustomerService()
export default customerService
