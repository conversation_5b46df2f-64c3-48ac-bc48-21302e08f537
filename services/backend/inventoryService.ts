// services/backend/inventoryService.ts
import { Inventory, Product, Shop, connectDB, logActivity } from '@/lib/database'
import type { IInventory } from '@/lib/database'

export interface CreateInventoryData {
  productId: string
  productName: string
  sku: string
  branchId: string
  stock: number
  minStockLevel: number
  maxStockLevel: number
  cost: number
  location: string
  supplier?: string
  batchNumber?: string
  expiryDate?: Date
  lastRestocked: Date
}

export interface UpdateInventoryData {
  stock?: number
  minStockLevel?: number
  maxStockLevel?: number
  cost?: number
  location?: string
  supplier?: string
  batchNumber?: string
  expiryDate?: Date
  lastRestocked?: Date
}

export interface InventoryFilters {
  search?: string
  branchId?: string
  productId?: string
  lowStock?: boolean
  outOfStock?: boolean
  location?: string
  supplier?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface StockMovement {
  type: 'in' | 'out' | 'adjustment'
  quantity: number
  reason: string
  reference?: string
  cost?: number
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

/**
 * Get all inventory items with filtering and pagination
 */
export async function getInventory(
  filters: InventoryFilters,
  pagination: { page: number; limit: number },
  userRole: string,
  userBranchId?: string
): Promise<ServiceResponse<IInventory[]>> {
  try {
    await connectDB()

    const { page, limit } = pagination
    const { search, branchId, productId, lowStock, outOfStock, location, supplier, sortBy = 'createdAt', sortOrder = 'desc' } = filters

    // Build filter query
    const filter: any = {}
    
    if (search) {
      filter.$or = [
        { productName: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } }
      ]
    }

    if (productId) filter.productId = productId
    if (location) filter.location = { $regex: location, $options: 'i' }
    if (supplier) filter.supplier = { $regex: supplier, $options: 'i' }

    // Stock level filters
    if (lowStock) {
      filter.$expr = { $lte: ['$stock', '$minStockLevel'] }
    }
    if (outOfStock) {
      filter.stock = 0
    }

    // Apply role-based filtering
    if (userRole === 'branch_manager' && userBranchId) {
      filter.branchId = userBranchId
    } else if (branchId) {
      filter.branchId = branchId
    }

    const skip = (page - 1) * limit
    const sort: any = {}
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1

    const [inventory, total] = await Promise.all([
      Inventory.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Inventory.countDocuments(filter)
    ])

    return {
      success: true,
      data: inventory.map((item: any) => ({
        _id: item._id.toString(),
        productId: item.productId,
        productName: item.productName,
        sku: item.sku,
        branchId: item.branchId,
        branchName: item.branchName,
        stock: item.stock,
        minStockLevel: item.minStockLevel,
        maxStockLevel: item.maxStockLevel,
        cost: item.cost,
        location: item.location,
        supplier: item.supplier,
        batchNumber: item.batchNumber,
        expiryDate: item.expiryDate,
        lastRestocked: item.lastRestocked,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      })) as IInventory[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Get inventory service error:', error)
    return {
      success: false,
      error: 'Failed to fetch inventory'
    }
  }
}

/**
 * Get inventory item by ID
 */
export async function getInventoryById(inventoryId: string): Promise<ServiceResponse<IInventory>> {
  try {
    await connectDB()

    const inventory = await Inventory.findById(inventoryId).lean()

    if (!inventory) {
      return {
        success: false,
        error: 'Inventory item not found'
      }
    }

    return {
      success: true,
      data: {
        _id: (inventory as any)._id.toString(),
        productId: (inventory as any).productId,
        productName: (inventory as any).productName,
        sku: (inventory as any).sku,
        branchId: (inventory as any).branchId,
        branchName: (inventory as any).branchName,
        stock: (inventory as any).stock,
        minStockLevel: (inventory as any).minStockLevel,
        maxStockLevel: (inventory as any).maxStockLevel,
        cost: (inventory as any).cost,
        location: (inventory as any).location,
        supplier: (inventory as any).supplier,
        batchNumber: (inventory as any).batchNumber,
        expiryDate: (inventory as any).expiryDate,
        lastRestocked: (inventory as any).lastRestocked,
        createdAt: (inventory as any).createdAt,
        updatedAt: (inventory as any).updatedAt
      } as IInventory
    }
  } catch (error) {
    console.error('Get inventory by ID service error:', error)
    return {
      success: false,
      error: 'Failed to fetch inventory item'
    }
  }
}

/**
 * Create new inventory item
 */
export async function createInventory(
  data: CreateInventoryData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IInventory>> {
  try {
    await connectDB()

    // Check if inventory item already exists for this product and branch
    const existingInventory = await Inventory.findOne({
      productId: data.productId,
      branchId: data.branchId
    })

    if (existingInventory) {
      return {
        success: false,
        error: 'Inventory item already exists for this product and branch'
      }
    }

    // Verify product exists
    const product = await Product.findById(data.productId)
    if (!product) {
      return {
        success: false,
        error: 'Product not found'
      }
    }

    // Verify branch exists
    const branch = await Shop.findById(data.branchId)
    if (!branch) {
      return {
        success: false,
        error: 'Branch not found'
      }
    }

    // Create inventory item
    const inventory = await Inventory.create({
      ...data,
      branchName: branch.name
    })

    // Log activity
    await logActivity({
      type: 'Inventory',
      description: `New inventory item created for "${data.productName}" at ${branch.name}`,
      userId,
      userName,
      branchId: data.branchId,
      branchName: branch.name,
      metadata: { 
        inventoryId: inventory._id.toString(),
        productId: data.productId,
        productName: data.productName,
        sku: data.sku,
        initialStock: data.stock
      }
    })

    return {
      success: true,
      data: {
        _id: inventory._id.toString(),
        productId: inventory.productId,
        productName: inventory.productName,
        sku: inventory.sku,
        branchId: inventory.branchId,
        branchName: inventory.branchName,
        stock: inventory.stock,
        minStockLevel: inventory.minStockLevel,
        maxStockLevel: inventory.maxStockLevel,
        cost: inventory.cost,
        location: inventory.location,
        supplier: inventory.supplier,
        batchNumber: inventory.batchNumber,
        expiryDate: inventory.expiryDate,
        lastRestocked: inventory.lastRestocked,
        createdAt: inventory.createdAt,
        updatedAt: inventory.updatedAt
      } as IInventory
    }
  } catch (error) {
    console.error('Create inventory service error:', error)
    return {
      success: false,
      error: 'Failed to create inventory item'
    }
  }
}

/**
 * Update inventory item
 */
export async function updateInventory(
  inventoryId: string,
  data: UpdateInventoryData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IInventory>> {
  try {
    await connectDB()

    const existingInventory = await Inventory.findById(inventoryId)
    if (!existingInventory) {
      return {
        success: false,
        error: 'Inventory item not found'
      }
    }

    // Update inventory
    const inventory = await Inventory.findByIdAndUpdate(
      inventoryId,
      {
        ...data,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    )

    // Log activity
    await logActivity({
      type: 'Inventory',
      description: `Inventory item "${inventory!.productName}" updated`,
      userId,
      userName,
      branchId: inventory!.branchId,
      branchName: inventory!.branchName,
      metadata: {
        inventoryId: inventory!._id.toString(),
        productName: inventory!.productName,
        updatedFields: Object.keys(data)
      }
    })

    return {
      success: true,
      data: {
        _id: inventory!._id.toString(),
        productId: inventory!.productId,
        productName: inventory!.productName,
        sku: inventory!.sku,
        branchId: inventory!.branchId,
        branchName: inventory!.branchName,
        stock: inventory!.stock,
        minStockLevel: inventory!.minStockLevel,
        maxStockLevel: inventory!.maxStockLevel,
        cost: inventory!.cost,
        location: inventory!.location,
        supplier: inventory!.supplier,
        batchNumber: inventory!.batchNumber,
        expiryDate: inventory!.expiryDate,
        lastRestocked: inventory!.lastRestocked,
        createdAt: inventory!.createdAt,
        updatedAt: inventory!.updatedAt
      } as IInventory
    }
  } catch (error) {
    console.error('Update inventory service error:', error)
    return {
      success: false,
      error: 'Failed to update inventory item'
    }
  }
}

/**
 * Update stock levels with movement tracking
 */
export async function updateStock(
  inventoryId: string,
  movement: StockMovement,
  userId: string,
  userName: string
): Promise<ServiceResponse<IInventory>> {
  try {
    await connectDB()

    const inventory = await Inventory.findById(inventoryId)
    if (!inventory) {
      return {
        success: false,
        error: 'Inventory item not found'
      }
    }

    let newStock = inventory.stock
    let description = ''

    switch (movement.type) {
      case 'in':
        newStock += movement.quantity
        description = `Stock increased by ${movement.quantity} for "${inventory.productName}"`
        break
      case 'out':
        if (inventory.stock < movement.quantity) {
          return {
            success: false,
            error: 'Insufficient stock for this operation'
          }
        }
        newStock -= movement.quantity
        description = `Stock decreased by ${movement.quantity} for "${inventory.productName}"`
        break
      case 'adjustment':
        newStock = movement.quantity
        description = `Stock adjusted to ${movement.quantity} for "${inventory.productName}"`
        break
    }

    // Update inventory
    const updatedInventory = await Inventory.findByIdAndUpdate(
      inventoryId,
      {
        stock: newStock,
        ...(movement.type === 'in' && { lastRestocked: new Date() }),
        updatedAt: new Date()
      },
      { new: true }
    )

    // Log activity
    await logActivity({
      type: 'Inventory',
      description: `${description}. Reason: ${movement.reason}`,
      userId,
      userName,
      branchId: inventory.branchId,
      branchName: inventory.branchName,
      metadata: {
        inventoryId: inventory._id.toString(),
        productName: inventory.productName,
        movementType: movement.type,
        quantity: movement.quantity,
        previousStock: inventory.stock,
        newStock,
        reason: movement.reason,
        reference: movement.reference
      }
    })

    return {
      success: true,
      data: {
        _id: updatedInventory!._id.toString(),
        productId: updatedInventory!.productId,
        productName: updatedInventory!.productName,
        sku: updatedInventory!.sku,
        branchId: updatedInventory!.branchId,
        branchName: updatedInventory!.branchName,
        stock: updatedInventory!.stock,
        minStockLevel: updatedInventory!.minStockLevel,
        maxStockLevel: updatedInventory!.maxStockLevel,
        cost: updatedInventory!.cost,
        location: updatedInventory!.location,
        supplier: updatedInventory!.supplier,
        batchNumber: updatedInventory!.batchNumber,
        expiryDate: updatedInventory!.expiryDate,
        lastRestocked: updatedInventory!.lastRestocked,
        createdAt: updatedInventory!.createdAt,
        updatedAt: updatedInventory!.updatedAt
      } as IInventory
    }
  } catch (error) {
    console.error('Update stock service error:', error)
    return {
      success: false,
      error: 'Failed to update stock'
    }
  }
}

/**
 * Get low stock items
 */
export async function getLowStockItems(branchId?: string): Promise<ServiceResponse<IInventory[]>> {
  try {
    await connectDB()

    const filter: any = {
      $expr: { $lte: ['$stock', '$minStockLevel'] }
    }

    if (branchId) {
      filter.branchId = branchId
    }

    const lowStockItems = await Inventory.find(filter)
      .sort({ stock: 1 })
      .lean()

    return {
      success: true,
      data: lowStockItems.map((item: any) => ({
        _id: item._id.toString(),
        productId: item.productId,
        productName: item.productName,
        sku: item.sku,
        branchId: item.branchId,
        branchName: item.branchName,
        stock: item.stock,
        minStockLevel: item.minStockLevel,
        maxStockLevel: item.maxStockLevel,
        cost: item.cost,
        location: item.location,
        supplier: item.supplier,
        batchNumber: item.batchNumber,
        expiryDate: item.expiryDate,
        lastRestocked: item.lastRestocked,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      })) as IInventory[]
    }
  } catch (error) {
    console.error('Get low stock items service error:', error)
    return {
      success: false,
      error: 'Failed to fetch low stock items'
    }
  }
}
