import { Order, Product, Customer, Shop, connectDB } from '@/lib/database'

export interface DashboardMetrics {
  overview: {
    totalSales: number
    totalOrders: number
    totalCustomers: number
    totalProducts: number
    salesGrowth: number
    ordersGrowth: number
  }
  recentOrders: any[]
  topProducts: any[]
  salesByStatus: any[]
  salesTrend: any[]
  lowStockProducts: any[]
  alerts: {
    lowStockCount: number
    pendingOrders: number
  }
}

export interface SalesAnalytics {
  summary: {
    totalRevenue: number
    totalOrders: number
    averageOrderValue: number
    totalTax: number
    totalShipping: number
    totalDiscount: number
    conversionRate: number
  }
  salesTrend: any[]
  salesByPaymentMethod: any[]
  salesByCategory: any[]
  topCustomers: any[]
  filters: {
    branchId?: string
    startDate?: string
    endDate?: string
    groupBy: string
    category?: string
  }
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
}

/**
 * Get dashboard metrics
 */
export async function getDashboardMetrics(
  branchId?: string,
  period: number = 30,
  startDate?: Date,
  endDate?: Date,
  userRole?: string,
  userBranchId?: string
): Promise<ServiceResponse<DashboardMetrics>> {
  try {
    await connectDB()

    // Build date filter
    const dateFilter: any = {}
    if (startDate && endDate) {
      dateFilter.createdAt = {
        $gte: startDate,
        $lte: endDate
      }
    } else {
      dateFilter.createdAt = {
        $gte: new Date(Date.now() - period * 24 * 60 * 60 * 1000)
      }
    }

    // Build branch filter
    const branchFilter: any = {}
    if (userRole === 'branch_manager' && userBranchId) {
      branchFilter.branchId = userBranchId
    } else if (branchId) {
      branchFilter.branchId = branchId
    }

    // Combine filters
    const orderFilter = { ...dateFilter, ...branchFilter }
    const productFilter = { ...branchFilter, isActive: true }
    const customerFilter = { ...branchFilter, isActive: true }

    // Get metrics in parallel
    const [
      totalSales,
      totalOrders,
      totalCustomers,
      totalProducts,
      recentOrders,
      topProducts,
      salesByStatus,
      salesTrend,
      lowStockProducts,
      previousSales,
      previousOrders
    ] = await Promise.all([
      // Current period metrics
      Order.aggregate([
        { $match: orderFilter },
        { $group: { _id: null, total: { $sum: '$total' } } }
      ]),

      Order.countDocuments(orderFilter),
      Customer.countDocuments(customerFilter),
      Product.countDocuments(productFilter),

      // Recent orders
      Order.find(orderFilter)
        .sort({ createdAt: -1 })
        .limit(5)
        .select('orderNumber customerName total status createdAt')
        .lean(),

      // Top selling products
      Order.aggregate([
        { $match: orderFilter },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.productId',
            productName: { $first: '$items.productName' },
            totalQuantity: { $sum: '$items.quantity' },
            totalRevenue: { $sum: '$items.subtotal' }
          }
        },
        { $sort: { totalQuantity: -1 } },
        { $limit: 5 }
      ]),

      // Sales by order status
      Order.aggregate([
        { $match: orderFilter },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            total: { $sum: '$total' }
          }
        }
      ]),

      // Sales trend (daily)
      Order.aggregate([
        { $match: orderFilter },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            sales: { $sum: '$total' },
            orders: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ]),

      // Low stock products
      Product.find({
        ...productFilter,
        $expr: { $lte: ['$stock', '$minStockLevel'] }
      })
        .select('name sku stock minStockLevel status')
        .limit(10)
        .lean(),

      // Previous period for growth calculation
      Order.aggregate([
        {
          $match: {
            ...branchFilter,
            createdAt: {
              $gte: new Date(Date.now() - period * 2 * 24 * 60 * 60 * 1000),
              $lt: new Date(Date.now() - period * 24 * 60 * 60 * 1000)
            }
          }
        },
        { $group: { _id: null, total: { $sum: '$total' } } }
      ]),

      Order.countDocuments({
        ...branchFilter,
        createdAt: {
          $gte: new Date(Date.now() - period * 2 * 24 * 60 * 60 * 1000),
          $lt: new Date(Date.now() - period * 24 * 60 * 60 * 1000)
        }
      })
    ])

    // Calculate growth rates
    const currentSales = totalSales[0]?.total || 0
    const prevSales = previousSales[0]?.total || 0
    const salesGrowth = prevSales > 0 ? ((currentSales - prevSales) / prevSales) * 100 : 0
    const ordersGrowth = previousOrders > 0 ? ((totalOrders - previousOrders) / previousOrders) * 100 : 0

    const dashboard: DashboardMetrics = {
      overview: {
        totalSales: currentSales,
        totalOrders,
        totalCustomers,
        totalProducts,
        salesGrowth: Math.round(salesGrowth * 100) / 100,
        ordersGrowth: Math.round(ordersGrowth * 100) / 100
      },
      recentOrders,
      topProducts,
      salesByStatus,
      salesTrend: salesTrend.map(item => ({
        date: `${item._id.year}-${String(item._id.month).padStart(2, '0')}-${String(item._id.day).padStart(2, '0')}`,
        sales: item.sales,
        orders: item.orders
      })),
      lowStockProducts,
      alerts: {
        lowStockCount: lowStockProducts.length,
        pendingOrders: salesByStatus.find(s => s._id === 'Pending')?.count || 0
      }
    }

    return {
      success: true,
      data: dashboard
    }
  } catch (error) {
    console.error('Dashboard analytics service error:', error)
    return {
      success: false,
      error: 'Failed to fetch dashboard data'
    }
  }
}

/**
 * Get detailed sales analytics
 */
export async function getSalesAnalytics(
  branchId?: string,
  startDate?: Date,
  endDate?: Date,
  groupBy: string = 'day',
  category?: string,
  userRole?: string,
  userBranchId?: string
): Promise<ServiceResponse<SalesAnalytics>> {
  try {
    await connectDB()

    // Build date filter
    const dateFilter: any = {}
    if (startDate && endDate) {
      dateFilter.createdAt = { $gte: startDate, $lte: endDate }
    } else {
      // Default to last 30 days
      dateFilter.createdAt = {
        $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      }
    }

    // Build branch filter
    const branchFilter: any = {}
    if (userRole === 'branch_manager' && userBranchId) {
      branchFilter.branchId = userBranchId
    } else if (branchId) {
      branchFilter.branchId = branchId
    }

    const baseFilter = { ...dateFilter, ...branchFilter }

    // Build grouping based on period
    let groupStage: any
    switch (groupBy) {
      case 'hour':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' },
          hour: { $hour: '$createdAt' }
        }
        break
      case 'day':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        }
        break
      case 'week':
        groupStage = {
          year: { $year: '$createdAt' },
          week: { $week: '$createdAt' }
        }
        break
      case 'month':
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' }
        }
        break
      case 'year':
        groupStage = { year: { $year: '$createdAt' } }
        break
      default:
        groupStage = {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        }
    }

    const [
      salesTrend,
      salesByPaymentMethod,
      salesByCategory,
      topCustomers,
      performanceMetrics
    ] = await Promise.all([
      // Sales trend
      Order.aggregate([
        { $match: baseFilter },
        {
          $group: {
            _id: groupStage,
            totalSales: { $sum: '$total' },
            totalOrders: { $sum: 1 },
            averageOrderValue: { $avg: '$total' },
            totalTax: { $sum: '$tax' },
            totalShipping: { $sum: '$shipping' },
            totalDiscount: { $sum: '$discount' }
          }
        },
        { $sort: { '_id': 1 } }
      ]),

      // Sales by payment method
      Order.aggregate([
        { $match: baseFilter },
        {
          $group: {
            _id: '$paymentMethod',
            totalSales: { $sum: '$total' },
            orderCount: { $sum: 1 }
          }
        },
        { $sort: { totalSales: -1 } }
      ]),

      // Sales by category (if category filter is applied)
      category ? Order.aggregate([
        { $match: baseFilter },
        { $unwind: '$items' },
        {
          $lookup: {
            from: 'products',
            localField: 'items.productId',
            foreignField: '_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        { $match: { 'product.category': category } },
        {
          $group: {
            _id: '$product.category',
            totalSales: { $sum: '$items.subtotal' },
            totalQuantity: { $sum: '$items.quantity' },
            orderCount: { $sum: 1 }
          }
        },
        { $sort: { totalSales: -1 } }
      ]) : [],

      // Top customers
      Order.aggregate([
        { $match: baseFilter },
        {
          $group: {
            _id: '$customerId',
            customerName: { $first: '$customerName' },
            customerEmail: { $first: '$customerEmail' },
            totalSales: { $sum: '$total' },
            orderCount: { $sum: 1 },
            averageOrderValue: { $avg: '$total' }
          }
        },
        { $sort: { totalSales: -1 } },
        { $limit: 10 }
      ]),

      // Performance metrics
      Order.aggregate([
        { $match: baseFilter },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$total' },
            totalOrders: { $sum: 1 },
            averageOrderValue: { $avg: '$total' },
            totalTax: { $sum: '$tax' },
            totalShipping: { $sum: '$shipping' },
            totalDiscount: { $sum: '$discount' },
            conversionRate: {
              $avg: {
                $cond: [{ $eq: ['$status', 'Delivered'] }, 1, 0]
              }
            }
          }
        }
      ])
    ])

    const analytics: SalesAnalytics = {
      summary: performanceMetrics[0] || {
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0,
        totalTax: 0,
        totalShipping: 0,
        totalDiscount: 0,
        conversionRate: 0
      },
      salesTrend: salesTrend.map(item => ({
        period: formatPeriod(item._id, groupBy),
        totalSales: item.totalSales,
        totalOrders: item.totalOrders,
        averageOrderValue: item.averageOrderValue,
        totalTax: item.totalTax,
        totalShipping: item.totalShipping,
        totalDiscount: item.totalDiscount
      })),
      salesByPaymentMethod,
      salesByCategory,
      topCustomers,
      filters: {
        branchId,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
        groupBy,
        category
      }
    }

    return {
      success: true,
      data: analytics
    }
  } catch (error) {
    console.error('Sales analytics service error:', error)
    return {
      success: false,
      error: 'Failed to fetch sales analytics'
    }
  }
}

// Helper function to format period based on groupBy
function formatPeriod(period: any, groupBy: string): string {
  switch (groupBy) {
    case 'hour':
      return `${period.year}-${String(period.month).padStart(2, '0')}-${String(period.day).padStart(2, '0')} ${String(period.hour).padStart(2, '0')}:00`
    case 'day':
      return `${period.year}-${String(period.month).padStart(2, '0')}-${String(period.day).padStart(2, '0')}`
    case 'week':
      return `${period.year}-W${String(period.week).padStart(2, '0')}`
    case 'month':
      return `${period.year}-${String(period.month).padStart(2, '0')}`
    case 'year':
      return `${period.year}`
    default:
      return `${period.year}-${String(period.month).padStart(2, '0')}-${String(period.day).padStart(2, '0')}`
  }
}
