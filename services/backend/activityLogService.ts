// services/backend/activityLogService.ts
import { ActivityLog, connectDB } from '@/lib/database'
import type { IActivityLog } from '@/lib/database'

export interface CreateActivityLogData {
  type: 'Order' | 'Inventory' | 'Sale' | 'Delivery' | 'Product' | 'User'
  description: string
  metadata?: Record<string, any>
  branchId?: string
  branchName?: string
}

export interface ActivityLogFilters {
  type?: string
  userId?: string
  branchId?: string
  search?: string
  startDate?: Date
  endDate?: Date
}

export interface ActivityLogStats {
  summary: {
    totalActivities: number
    period: number
    startDate: string
    endDate: string
  }
  activitiesByType: Array<{
    type: string
    count: number
    percentage: number
  }>
  activitiesByUser: Array<{
    userId: string
    userName: string
    count: number
    percentage: number
  }>
  activityTrend: Array<{
    date: string
    count: number
  }>
  recentActivities: IActivityLog[]
  filters: {
    branchId?: string
    period: number
    startDate?: string
    endDate?: string
  }
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

/**
 * Get activity logs with filtering and pagination
 */
export async function getActivityLogs(
  filters: ActivityLogFilters,
  pagination: { page: number; limit: number },
  userRole: string,
  userBranchId?: string
): Promise<ServiceResponse<IActivityLog[]>> {
  try {
    await connectDB()

    const { page, limit } = pagination
    const { type, userId, branchId, search, startDate, endDate } = filters

    // Build filter query
    const filter: any = {}
    
    if (type) filter.type = type
    if (userId) filter.userId = userId

    if (search) {
      filter.$text = { $search: search }
    }

    if (startDate || endDate) {
      filter.timestamp = {}
      if (startDate) filter.timestamp.$gte = startDate
      if (endDate) filter.timestamp.$lte = endDate
    }

    // Apply role-based filtering
    if (userRole === 'branch_manager' && userBranchId) {
      filter.branchId = userBranchId
    } else if (branchId) {
      filter.branchId = branchId
    }

    const skip = (page - 1) * limit

    const [logs, total] = await Promise.all([
      ActivityLog.find(filter)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      ActivityLog.countDocuments(filter)
    ])

    return {
      success: true,
      data: logs.map((log: any) => ({
        _id: log._id.toString(),
        type: log.type,
        description: log.description,
        userId: log.userId,
        userName: log.userName,
        branchId: log.branchId,
        branchName: log.branchName,
        metadata: log.metadata || {},
        timestamp: log.timestamp,
        createdAt: log.createdAt,
        updatedAt: log.updatedAt
      })) as IActivityLog[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Get activity logs service error:', error)
    return {
      success: false,
      error: 'Failed to fetch activity logs'
    }
  }
}

/**
 * Create new activity log
 */
export async function createActivityLog(
  data: CreateActivityLogData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IActivityLog>> {
  try {
    await connectDB()

    const log = await ActivityLog.create({
      ...data,
      userId,
      userName,
      timestamp: new Date()
    })

    return {
      success: true,
      data: {
        _id: log._id.toString(),
        type: log.type,
        description: log.description,
        userId: log.userId,
        userName: log.userName,
        branchId: log.branchId,
        branchName: log.branchName,
        metadata: log.metadata || {},
        timestamp: log.timestamp,
        createdAt: log.createdAt,
        updatedAt: log.updatedAt
      } as IActivityLog
    }
  } catch (error) {
    console.error('Create activity log service error:', error)
    return {
      success: false,
      error: 'Failed to create activity log'
    }
  }
}

/**
 * Get activity log statistics
 */
export async function getActivityLogStats(
  branchId?: string,
  period: number = 30,
  startDate?: Date,
  endDate?: Date,
  userRole?: string,
  userBranchId?: string
): Promise<ServiceResponse<ActivityLogStats>> {
  try {
    await connectDB()

    // Build date filter
    const dateFilter: any = {}
    if (startDate && endDate) {
      dateFilter.timestamp = {
        $gte: startDate,
        $lte: endDate
      }
    } else {
      dateFilter.timestamp = {
        $gte: new Date(Date.now() - period * 24 * 60 * 60 * 1000)
      }
    }

    // Build branch filter
    const branchFilter: any = {}
    if (userRole === 'branch_manager' && userBranchId) {
      branchFilter.branchId = userBranchId
    } else if (branchId) {
      branchFilter.branchId = branchId
    }

    const baseFilter = { ...dateFilter, ...branchFilter }

    const [
      totalActivities,
      activitiesByType,
      activitiesByUser,
      activitiesByDay,
      recentActivities
    ] = await Promise.all([
      // Total activities count
      ActivityLog.countDocuments(baseFilter),

      // Activities by type
      ActivityLog.aggregate([
        { $match: baseFilter },
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]),

      // Activities by user
      ActivityLog.aggregate([
        { $match: baseFilter },
        {
          $group: {
            _id: '$userId',
            userName: { $first: '$userName' },
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),

      // Activities by day (for trend chart)
      ActivityLog.aggregate([
        { $match: baseFilter },
        {
          $group: {
            _id: {
              year: { $year: '$timestamp' },
              month: { $month: '$timestamp' },
              day: { $dayOfMonth: '$timestamp' }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ]),

      // Recent activities
      ActivityLog.find(baseFilter)
        .sort({ timestamp: -1 })
        .limit(10)
        .select('type description userName timestamp branchName')
        .lean()
    ])

    const stats: ActivityLogStats = {
      summary: {
        totalActivities,
        period,
        startDate: startDate?.toISOString() || new Date(Date.now() - period * 24 * 60 * 60 * 1000).toISOString(),
        endDate: endDate?.toISOString() || new Date().toISOString()
      },
      activitiesByType: activitiesByType.map(item => ({
        type: item._id,
        count: item.count,
        percentage: totalActivities > 0 ? Math.round((item.count / totalActivities) * 100) : 0
      })),
      activitiesByUser: activitiesByUser.map(item => ({
        userId: item._id,
        userName: item.userName,
        count: item.count,
        percentage: totalActivities > 0 ? Math.round((item.count / totalActivities) * 100) : 0
      })),
      activityTrend: activitiesByDay.map(item => ({
        date: `${item._id.year}-${String(item._id.month).padStart(2, '0')}-${String(item._id.day).padStart(2, '0')}`,
        count: item.count
      })),
      recentActivities: recentActivities.map((activity: any) => ({
        _id: activity._id.toString(),
        type: activity.type,
        description: activity.description,
        userId: activity.userId,
        userName: activity.userName,
        branchId: activity.branchId,
        branchName: activity.branchName,
        metadata: activity.metadata || {},
        timestamp: activity.timestamp,
        createdAt: activity.createdAt,
        updatedAt: activity.updatedAt
      })) as IActivityLog[],
      filters: {
        branchId,
        period,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString()
      }
    }

    return {
      success: true,
      data: stats
    }
  } catch (error) {
    console.error('Activity log stats service error:', error)
    return {
      success: false,
      error: 'Failed to fetch activity log statistics'
    }
  }
}

/**
 * Get activities by user
 */
export async function getActivitiesByUser(
  userId: string,
  pagination: { page: number; limit: number },
  startDate?: Date,
  endDate?: Date
): Promise<ServiceResponse<IActivityLog[]>> {
  try {
    await connectDB()

    const { page, limit } = pagination

    const filter: any = { userId }

    if (startDate || endDate) {
      filter.timestamp = {}
      if (startDate) filter.timestamp.$gte = startDate
      if (endDate) filter.timestamp.$lte = endDate
    }

    const skip = (page - 1) * limit

    const [activities, total] = await Promise.all([
      ActivityLog.find(filter)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      ActivityLog.countDocuments(filter)
    ])

    return {
      success: true,
      data: activities.map((activity: any) => ({
        _id: activity._id.toString(),
        type: activity.type,
        description: activity.description,
        userId: activity.userId,
        userName: activity.userName,
        branchId: activity.branchId,
        branchName: activity.branchName,
        metadata: activity.metadata || {},
        timestamp: activity.timestamp,
        createdAt: activity.createdAt,
        updatedAt: activity.updatedAt
      })) as IActivityLog[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Get activities by user service error:', error)
    return {
      success: false,
      error: 'Failed to fetch user activities'
    }
  }
}

/**
 * Get activities by type
 */
export async function getActivitiesByType(
  type: string,
  pagination: { page: number; limit: number },
  branchId?: string,
  startDate?: Date,
  endDate?: Date
): Promise<ServiceResponse<IActivityLog[]>> {
  try {
    await connectDB()

    const { page, limit } = pagination

    const filter: any = { type }

    if (branchId) filter.branchId = branchId

    if (startDate || endDate) {
      filter.timestamp = {}
      if (startDate) filter.timestamp.$gte = startDate
      if (endDate) filter.timestamp.$lte = endDate
    }

    const skip = (page - 1) * limit

    const [activities, total] = await Promise.all([
      ActivityLog.find(filter)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      ActivityLog.countDocuments(filter)
    ])

    return {
      success: true,
      data: activities.map((activity: any) => ({
        _id: activity._id.toString(),
        type: activity.type,
        description: activity.description,
        userId: activity.userId,
        userName: activity.userName,
        branchId: activity.branchId,
        branchName: activity.branchName,
        metadata: activity.metadata || {},
        timestamp: activity.timestamp,
        createdAt: activity.createdAt,
        updatedAt: activity.updatedAt
      })) as IActivityLog[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Get activities by type service error:', error)
    return {
      success: false,
      error: 'Failed to fetch activities by type'
    }
  }
}

/**
 * Delete old activity logs (cleanup)
 */
export async function cleanupOldActivityLogs(daysToKeep: number = 90): Promise<ServiceResponse<{ deletedCount: number }>> {
  try {
    await connectDB()

    const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000)

    const result = await ActivityLog.deleteMany({
      timestamp: { $lt: cutoffDate }
    })

    return {
      success: true,
      data: { deletedCount: result.deletedCount }
    }
  } catch (error) {
    console.error('Cleanup activity logs service error:', error)
    return {
      success: false,
      error: 'Failed to cleanup old activity logs'
    }
  }
}
