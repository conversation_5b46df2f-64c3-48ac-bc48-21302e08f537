// services/backend/customerService.ts
import { Customer, Order, connectDB, logActivity } from '@/lib/database'
import type { ICustomer, IOrder } from '@/lib/database'

export interface CreateCustomerData {
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  preferredBranch?: string
}

export interface UpdateCustomerData {
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  address?: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  preferredBranch?: string
  loyaltyPoints?: number
  isActive?: boolean
}

export interface CustomerFilters {
  search?: string
  preferredBranch?: string
  isActive?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface CustomerWithOrders extends ICustomer {
  orders?: IOrder[]
}

/**
 * Get all customers with filtering and pagination
 */
export async function getCustomers(
  filters: CustomerFilters,
  pagination: { page: number; limit: number },
  userRole: string,
  userBranchId?: string
): Promise<ServiceResponse<ICustomer[]>> {
  try {
    await connectDB()

    const { page, limit } = pagination
    const { search, preferredBranch, isActive, sortBy = 'createdAt', sortOrder = 'desc' } = filters

    // Build filter query
    const filter: any = {}
    
    if (search) {
      filter.$text = { $search: search }
    }

    if (isActive !== undefined) {
      filter.isActive = isActive
    }

    // Apply role-based filtering
    if (userRole === 'branch_manager' && userBranchId) {
      filter.preferredBranch = userBranchId
    } else if (preferredBranch) {
      filter.preferredBranch = preferredBranch
    }

    const skip = (page - 1) * limit
    const sort: any = {}
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1

    const [customers, total] = await Promise.all([
      Customer.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Customer.countDocuments(filter)
    ])

    return {
      success: true,
      data: customers.map((customer: any) => ({
        _id: customer._id.toString(),
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        preferredBranch: customer.preferredBranch,
        totalOrders: customer.totalOrders,
        totalSpent: customer.totalSpent,
        loyaltyPoints: customer.loyaltyPoints,
        isActive: customer.isActive,
        lastOrderDate: customer.lastOrderDate,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt
      })) as ICustomer[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Get customers service error:', error)
    return {
      success: false,
      error: 'Failed to fetch customers'
    }
  }
}

/**
 * Get customer by ID with optional order history
 */
export async function getCustomerById(
  customerId: string,
  includeOrders: boolean = false,
  userRole?: string,
  userBranchId?: string
): Promise<ServiceResponse<CustomerWithOrders>> {
  try {
    await connectDB()

    const customer = await Customer.findById(customerId).lean()

    if (!customer) {
      return {
        success: false,
        error: 'Customer not found'
      }
    }

    let orders: IOrder[] = []
    if (includeOrders) {
      const orderFilter: any = { customerId }
      
      // Apply branch filter for branch managers
      if (userRole === 'branch_manager' && userBranchId) {
        orderFilter.branchId = userBranchId
      }

      const orderResults = await Order.find(orderFilter)
        .sort({ createdAt: -1 })
        .limit(10)
        .lean()

      orders = orderResults.map((order: any) => ({
        _id: order._id.toString(),
        orderNumber: order.orderNumber,
        customerId: order.customerId,
        customerName: order.customerName,
        customerEmail: order.customerEmail,
        customerPhone: order.customerPhone,
        items: order.items,
        subtotal: order.subtotal,
        tax: order.tax,
        shipping: order.shipping,
        discount: order.discount,
        total: order.total,
        status: order.status,
        paymentStatus: order.paymentStatus,
        paymentMethod: order.paymentMethod,
        branchId: order.branchId,
        branchName: order.branchName,
        shippingAddress: order.shippingAddress,
        billingAddress: order.billingAddress,
        notes: order.notes,
        trackingNumber: order.trackingNumber,
        estimatedDelivery: order.estimatedDelivery,
        createdAt: order.createdAt,
        updatedAt: order.updatedAt
      })) as IOrder[]
    }

    const customerData: ICustomer = {
      _id: (customer as any)._id.toString(),
      firstName: (customer as any).firstName,
      lastName: (customer as any).lastName,
      email: (customer as any).email,
      phone: (customer as any).phone,
      address: (customer as any).address,
      preferredBranch: (customer as any).preferredBranch,
      totalOrders: (customer as any).totalOrders,
      totalSpent: (customer as any).totalSpent,
      loyaltyPoints: (customer as any).loyaltyPoints,
      isActive: (customer as any).isActive,
      lastOrderDate: (customer as any).lastOrderDate,
      createdAt: (customer as any).createdAt,
      updatedAt: (customer as any).updatedAt
    } as ICustomer

    const customerWithOrders = {
      ...customerData,
      orders: includeOrders ? orders : undefined
    } as CustomerWithOrders

    return {
      success: true,
      data: customerWithOrders
    }
  } catch (error) {
    console.error('Get customer by ID service error:', error)
    return {
      success: false,
      error: 'Failed to fetch customer'
    }
  }
}

/**
 * Create new customer
 */
export async function createCustomer(
  data: CreateCustomerData,
  userId: string,
  userName: string,
  userBranchId?: string
): Promise<ServiceResponse<ICustomer>> {
  try {
    await connectDB()

    // Check if customer already exists
    const existingCustomer = await Customer.findOne({
      $or: [
        { email: data.email },
        { phone: data.phone }
      ]
    })

    if (existingCustomer) {
      return {
        success: false,
        error: existingCustomer.email === data.email 
          ? 'Email already registered' 
          : 'Phone number already registered'
      }
    }

    // Create customer
    const customer = await Customer.create({
      ...data,
      totalOrders: 0,
      totalSpent: 0,
      loyaltyPoints: 0,
      isActive: true
    })

    // Log activity
    await logActivity({
      type: 'User',
      description: `New customer "${customer.firstName} ${customer.lastName}" created`,
      userId,
      userName,
      branchId: data.preferredBranch || userBranchId,
      metadata: { 
        customerId: customer._id.toString(),
        customerName: `${customer.firstName} ${customer.lastName}`,
        email: customer.email
      }
    })

    return {
      success: true,
      data: {
        _id: customer._id.toString(),
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        preferredBranch: customer.preferredBranch,
        totalOrders: customer.totalOrders,
        totalSpent: customer.totalSpent,
        loyaltyPoints: customer.loyaltyPoints,
        isActive: customer.isActive,
        lastOrderDate: customer.lastOrderDate,
        createdAt: customer.createdAt,
        updatedAt: customer.updatedAt
      } as ICustomer
    }
  } catch (error) {
    console.error('Create customer service error:', error)
    return {
      success: false,
      error: 'Failed to create customer'
    }
  }
}

/**
 * Update customer
 */
export async function updateCustomer(
  customerId: string,
  updates: UpdateCustomerData,
  userId: string,
  userName: string
): Promise<ServiceResponse<ICustomer>> {
  try {
    await connectDB()

    const existingCustomer = await Customer.findById(customerId)
    if (!existingCustomer) {
      return {
        success: false,
        error: 'Customer not found'
      }
    }

    // Check if email or phone is being changed and already exists
    if (updates.email || updates.phone) {
      const conflictFilter: any = { _id: { $ne: customerId } }
      const conflictConditions = []
      
      if (updates.email) conflictConditions.push({ email: updates.email })
      if (updates.phone) conflictConditions.push({ phone: updates.phone })
      
      if (conflictConditions.length > 0) {
        conflictFilter.$or = conflictConditions
        
        const existingConflict = await Customer.findOne(conflictFilter)
        if (existingConflict) {
          return {
            success: false,
            error: existingConflict.email === updates.email 
              ? 'Email already registered' 
              : 'Phone number already registered'
          }
        }
      }
    }

    const customer = await Customer.findByIdAndUpdate(
      customerId,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    )

    // Log activity
    await logActivity({
      type: 'User',
      description: `Customer "${customer!.firstName} ${customer!.lastName}" updated`,
      userId,
      userName,
      branchId: customer!.preferredBranch,
      metadata: { 
        customerId: customer!._id.toString(),
        customerName: `${customer!.firstName} ${customer!.lastName}`,
        updates 
      }
    })

    return {
      success: true,
      data: {
        _id: customer!._id.toString(),
        firstName: customer!.firstName,
        lastName: customer!.lastName,
        email: customer!.email,
        phone: customer!.phone,
        address: customer!.address,
        preferredBranch: customer!.preferredBranch,
        totalOrders: customer!.totalOrders,
        totalSpent: customer!.totalSpent,
        loyaltyPoints: customer!.loyaltyPoints,
        isActive: customer!.isActive,
        lastOrderDate: customer!.lastOrderDate,
        createdAt: customer!.createdAt,
        updatedAt: customer!.updatedAt
      } as ICustomer
    }
  } catch (error) {
    console.error('Update customer service error:', error)
    return {
      success: false,
      error: 'Failed to update customer'
    }
  }
}

/**
 * Deactivate customer (soft delete)
 */
export async function deactivateCustomer(
  customerId: string,
  userId: string,
  userName: string
): Promise<ServiceResponse<void>> {
  try {
    await connectDB()

    const customer = await Customer.findById(customerId)
    if (!customer) {
      return {
        success: false,
        error: 'Customer not found'
      }
    }

    // Soft delete - set isActive to false
    await Customer.findByIdAndUpdate(customerId, { 
      isActive: false,
      updatedAt: new Date()
    })

    // Log activity
    await logActivity({
      type: 'User',
      description: `Customer "${customer.firstName} ${customer.lastName}" deactivated`,
      userId,
      userName,
      branchId: customer.preferredBranch,
      metadata: { 
        customerId: customer._id.toString(),
        customerName: `${customer.firstName} ${customer.lastName}`
      }
    })

    return {
      success: true
    }
  } catch (error) {
    console.error('Deactivate customer service error:', error)
    return {
      success: false,
      error: 'Failed to deactivate customer'
    }
  }
}

/**
 * Get customer statistics
 */
export async function getCustomerStats(branchId?: string): Promise<ServiceResponse<any>> {
  try {
    await connectDB()

    const filter: any = { isActive: true }
    if (branchId) {
      filter.preferredBranch = branchId
    }

    const [
      totalCustomers,
      topCustomers,
      customersByRegion
    ] = await Promise.all([
      Customer.countDocuments(filter),
      
      Customer.find(filter)
        .sort({ totalSpent: -1 })
        .limit(10)
        .select('firstName lastName email totalSpent totalOrders loyaltyPoints')
        .lean(),
      
      Customer.aggregate([
        { $match: filter },
        {
          $group: {
            _id: '$address.region',
            count: { $sum: 1 },
            totalSpent: { $sum: '$totalSpent' }
          }
        },
        { $sort: { count: -1 } }
      ])
    ])

    return {
      success: true,
      data: {
        totalCustomers,
        topCustomers,
        customersByRegion
      }
    }
  } catch (error) {
    console.error('Get customer stats service error:', error)
    return {
      success: false,
      error: 'Failed to fetch customer statistics'
    }
  }
}

/**
 * Update customer loyalty points
 */
export async function updateLoyaltyPoints(
  customerId: string,
  points: number,
  operation: 'add' | 'subtract' | 'set',
  userId: string,
  userName: string
): Promise<ServiceResponse<ICustomer>> {
  try {
    await connectDB()

    const customer = await Customer.findById(customerId)
    if (!customer) {
      return {
        success: false,
        error: 'Customer not found'
      }
    }

    let updateOperation: any
    switch (operation) {
      case 'add':
        updateOperation = { $inc: { loyaltyPoints: points } }
        break
      case 'subtract':
        updateOperation = { $inc: { loyaltyPoints: -points } }
        break
      case 'set':
        updateOperation = { $set: { loyaltyPoints: points } }
        break
      default:
        return {
          success: false,
          error: 'Invalid operation'
        }
    }

    const updatedCustomer = await Customer.findByIdAndUpdate(
      customerId,
      updateOperation,
      { new: true }
    )

    // Log activity
    await logActivity({
      type: 'User',
      description: `Loyalty points ${operation}ed for customer "${customer.firstName} ${customer.lastName}"`,
      userId,
      userName,
      branchId: customer.preferredBranch,
      metadata: { 
        customerId: customer._id.toString(),
        customerName: `${customer.firstName} ${customer.lastName}`,
        operation,
        points,
        newTotal: updatedCustomer!.loyaltyPoints
      }
    })

    return {
      success: true,
      data: {
        _id: updatedCustomer!._id.toString(),
        firstName: updatedCustomer!.firstName,
        lastName: updatedCustomer!.lastName,
        email: updatedCustomer!.email,
        phone: updatedCustomer!.phone,
        address: updatedCustomer!.address,
        preferredBranch: updatedCustomer!.preferredBranch,
        totalOrders: updatedCustomer!.totalOrders,
        totalSpent: updatedCustomer!.totalSpent,
        loyaltyPoints: updatedCustomer!.loyaltyPoints,
        isActive: updatedCustomer!.isActive,
        lastOrderDate: updatedCustomer!.lastOrderDate,
        createdAt: updatedCustomer!.createdAt,
        updatedAt: updatedCustomer!.updatedAt
      } as ICustomer
    }
  } catch (error) {
    console.error('Update loyalty points service error:', error)
    return {
      success: false,
      error: 'Failed to update loyalty points'
    }
  }
}
