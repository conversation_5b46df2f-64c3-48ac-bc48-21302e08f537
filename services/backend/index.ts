// Shop/Branch Services
export {
  getShops,
  getShopById,
  createShop,
  updateShop,
  deleteShop,
  getShopStats
} from './shopService'

export type {
  CreateShopData,
  UpdateShopData,
  ShopFilters
} from './shopService'

// Product Services
export {
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  getLowStockProducts
} from './productService'

export type {
  CreateProductData,
  UpdateProductData,
  ProductFilters
} from './productService'

// Order Services
export {
  getOrders,
  getOrderById,
  createOrder,
  updateOrder,
  cancelOrder
} from './orderService'

export type {
  CreateOrderData,
  UpdateOrderData,
  OrderFilters
} from './orderService'

// Customer Services
export {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deactivateCustomer,
  getCustomerStats,
  updateLoyaltyPoints
} from './customerService'

export type {
  CreateCustomerData,
  UpdateCustomerData,
  CustomerFilters,
  CustomerWithOrders
} from './customerService'

// Analytics Services
export {
  getDashboardMetrics,
  getSalesAnalytics
} from './analyticsService'

export type {
  DashboardMetrics,
  SalesAnalytics
} from './analyticsService'

// Activity Log Services
export {
  getActivityLogs,
  createActivityLog,
  getActivityLogStats,
  getActivitiesByUser,
  getActivitiesByType,
  cleanupOldActivityLogs
} from './activityLogService'

export type {
  CreateActivityLogData,
  ActivityLogFilters,
  ActivityLogStats
} from './activityLogService'

// Notifications Services
export {
  initializeSocketIO,
  getSocketIOServer,
  sendRealtimeNotification,
  broadcastRealtimeNotification,
  sendSystemAlert,
  sendUserMessage,
  sendEmailNotification,
  sendSMSNotification,
  sendPushNotification
} from './notificationsService'

// Employee Services
export {
  getEmployees,
  getEmployeeById,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getEmployeesByBranch
} from './employeeService'

export type {
  CreateEmployeeData,
  UpdateEmployeeData,
  EmployeeFilters
} from './employeeService'

// Inventory Services
export {
  getInventory,
  getInventoryById,
  createInventory,
  updateInventory,
  updateStock,
  getLowStockItems
} from './inventoryService'

export type {
  CreateInventoryData,
  UpdateInventoryData,
  InventoryFilters,
  StockMovement
} from './inventoryService'

// User Management Services
export {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  changeUserPassword
} from './userService'

export type {
  CreateUserData,
  UpdateUserData,
  UserFilters
} from './userService'

// Common Types
export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface PaginationOptions {
  page: number
  limit: number
}

// Service Utilities
export const serviceUtils = {
  /**
   * Validate pagination parameters
   */
  validatePagination: (page?: number, limit?: number): PaginationOptions => {
    const validPage = Math.max(1, page || 1)
    const validLimit = Math.min(100, Math.max(1, limit || 10))
    
    return { page: validPage, limit: validLimit }
  },

  /**
   * Build date range filter
   */
  buildDateFilter: (startDate?: string, endDate?: string) => {
    const filter: any = {}
    
    if (startDate || endDate) {
      filter.createdAt = {}
      if (startDate) filter.createdAt.$gte = new Date(startDate)
      if (endDate) filter.createdAt.$lte = new Date(endDate)
    }
    
    return filter
  },

  /**
   * Apply role-based branch filtering
   */
  applyBranchFilter: (
    filter: any,
    userRole: string,
    userBranchId?: string,
    requestedBranchId?: string
  ) => {
    if (userRole === 'branch_manager' && userBranchId) {
      filter.branchId = userBranchId
    } else if (requestedBranchId) {
      filter.branchId = requestedBranchId
    }
    
    return filter
  },

  /**
   * Check if user can access branch
   */
  canAccessBranch: (
    userRole: string,
    userBranchId?: string,
    targetBranchId?: string
  ): boolean => {
    if (userRole === 'overall_admin') return true
    if (userRole === 'branch_manager') {
      return !targetBranchId || userBranchId === targetBranchId
    }
    return false
  },

  /**
   * Format error response
   */
  errorResponse: <T>(error: string): ServiceResponse<T> => ({
    success: false,
    error
  }),

  /**
   * Format success response
   */
  successResponse: <T>(data: T, pagination?: any): ServiceResponse<T> => ({
    success: true,
    data,
    pagination
  }),

  /**
   * Calculate percentage
   */
  calculatePercentage: (value: number, total: number): number => {
    return total > 0 ? Math.round((value / total) * 100) : 0
  },

  /**
   * Format currency
   */
  formatCurrency: (amount: number, currency: string = 'MWK'): string => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount)
  },

  /**
   * Generate unique identifier
   */
  generateId: (): string => {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15)
  },

  /**
   * Sanitize search query
   */
  sanitizeSearchQuery: (query?: string): string | undefined => {
    if (!query) return undefined
    return query.trim().replace(/[<>]/g, '')
  },

  /**
   * Validate email format
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/
    return emailRegex.test(email)
  },

  /**
   * Validate phone format
   */
  isValidPhone: (phone: string): boolean => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
    return phoneRegex.test(phone)
  },

  /**
   * Calculate growth rate
   */
  calculateGrowthRate: (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0
    return Math.round(((current - previous) / previous) * 100 * 100) / 100
  },

  /**
   * Get date range for period
   */
  getDateRangeForPeriod: (period: number): { startDate: Date; endDate: Date } => {
    const endDate = new Date()
    const startDate = new Date(Date.now() - period * 24 * 60 * 60 * 1000)
    
    return { startDate, endDate }
  }
}

// Service Constants
export const SERVICE_CONSTANTS = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  DEFAULT_PERIOD_DAYS: 30,
  MAX_SEARCH_LENGTH: 100,
  
  SORT_ORDERS: {
    ASC: 'asc' as const,
    DESC: 'desc' as const
  },
  
  DATE_FORMATS: {
    ISO: 'YYYY-MM-DDTHH:mm:ss.sssZ',
    DATE_ONLY: 'YYYY-MM-DD',
    DISPLAY: 'MMM DD, YYYY'
  },
  
  CACHE_DURATIONS: {
    SHORT: 5 * 60, // 5 minutes
    MEDIUM: 30 * 60, // 30 minutes
    LONG: 60 * 60 // 1 hour
  }
} as const
