import { Shop, User, connectDB, logActivity } from '@/lib/database'
import type { IShop } from '@/lib/database'

export interface CreateShopData {
  name: string
  location: string
  country: string
  region: string
  managerId?: string
  description: string
  address: string
  phone: string
  email: string
  coordinates?: {
    lat: number
    lng: number
  }
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  image?: string
}

export interface UpdateShopData {
  name?: string
  location?: string
  country?: string
  region?: string
  description?: string
  address?: string
  phone?: string
  email?: string
  coordinates?: {
    lat: number
    lng: number
  }
  operatingHours?: {
    open: string
    close: string
    timezone: string
  }
  image?: string
  status?: 'Active' | 'Inactive' | 'Opening Soon' | 'Maintenance'
}

export interface ShopFilters {
  search?: string
  country?: string
  region?: string
  status?: string
  branchId?: string // For branch manager filtering
}

export interface PaginationOptions {
  page: number
  limit: number
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

/**
 * Get all shops with filtering and pagination
 */
export async function getShops(
  filters: ShopFilters,
  pagination: PaginationOptions,
  userRole: string,
  userBranchId?: string
): Promise<ServiceResponse<IShop[]>> {
  try {
    await connectDB()

    const { page, limit } = pagination
    const { search, country, region, status, branchId } = filters

    // Build filter query
    const filter: any = {}
    
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { location: { $regex: search, $options: 'i' } },
        { manager: { $regex: search, $options: 'i' } }
      ]
    }

    if (country) filter.country = country
    if (region) filter.region = region
    if (status) filter.status = status

    // Apply role-based filtering
    if (userRole === 'branch_manager' && userBranchId) {
      filter._id = userBranchId
    } else if (branchId) {
      filter._id = branchId
    }

    const skip = (page - 1) * limit

    const [shops, total] = await Promise.all([
      Shop.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Shop.countDocuments(filter)
    ])

    return {
      success: true,
      data: shops.map((shop: any) => ({
        _id: shop._id.toString(),
        name: shop.name,
        location: shop.location,
        country: shop.country,
        region: shop.region,
        managerId: shop.managerId,
        manager: shop.manager,
        description: shop.description,
        address: shop.address,
        phone: shop.phone,
        email: shop.email,
        coordinates: shop.coordinates,
        operatingHours: shop.operatingHours,
        image: shop.image,
        totalProducts: shop.totalProducts,
        totalSales: shop.totalSales,
        status: shop.status,
        createdAt: shop.createdAt,
        updatedAt: shop.updatedAt
      })) as IShop[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Get shops service error:', error)
    return {
      success: false,
      error: 'Failed to fetch shops'
    }
  }
}

/**
 * Get shop by ID
 */
export async function getShopById(shopId: string): Promise<ServiceResponse<IShop>> {
  try {
    await connectDB()

    const shop = await Shop.findById(shopId).lean()

    if (!shop) {
      return {
        success: false,
        error: 'Shop not found'
      }
    }

    return {
      success: true,
      data: {
        _id: (shop as any)._id.toString(),
        name: (shop as any).name,
        location: (shop as any).location,
        country: (shop as any).country,
        region: (shop as any).region,
        managerId: (shop as any).managerId,
        manager: (shop as any).manager,
        description: (shop as any).description,
        address: (shop as any).address,
        phone: (shop as any).phone,
        email: (shop as any).email,
        coordinates: (shop as any).coordinates,
        operatingHours: (shop as any).operatingHours,
        image: (shop as any).image,
        totalProducts: (shop as any).totalProducts,
        totalSales: (shop as any).totalSales,
        status: (shop as any).status,
        createdAt: (shop as any).createdAt,
        updatedAt: (shop as any).updatedAt
      } as IShop
    }
  } catch (error) {
    console.error('Get shop by ID service error:', error)
    return {
      success: false,
      error: 'Failed to fetch shop'
    }
  }
}

/**
 * Create new shop
 */
export async function createShop(
  data: CreateShopData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IShop>> {
  try {
    await connectDB()

    // Handle manager assignment (optional)
    let manager = null
    let managerName = 'To be assigned'

    if (data.managerId) {
      // Check if manager is already assigned to another shop
      const existingShop = await Shop.findOne({ managerId: data.managerId })
      if (existingShop) {
        return {
          success: false,
          error: 'Manager is already assigned to another shop'
        }
      }

      // Get manager details
      manager = await User.findById(data.managerId)
      if (!manager) {
        return {
          success: false,
          error: 'Manager not found'
        }
      }
      managerName = manager.name
    }

    // Create shop
    const shop = await Shop.create({
      ...data,
      manager: managerName,
      totalProducts: 0,
      totalSales: 0,
      status: 'Active',
      image: data.image || 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop'
    })

    // Update user's branchId
    await User.findByIdAndUpdate(data.managerId, { branchId: shop._id.toString() })

    // Log activity
    await logActivity({
      type: 'Product',
      description: `New shop "${shop.name}" created`,
      userId,
      userName,
      metadata: { shopId: shop._id.toString(), shopName: shop.name }
    })

    return {
      success: true,
      data: shop as IShop
    }
  } catch (error) {
    console.error('Create shop service error:', error)
    return {
      success: false,
      error: 'Failed to create shop'
    }
  }
}

/**
 * Update shop
 */
export async function updateShop(
  shopId: string,
  updates: UpdateShopData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IShop>> {
  try {
    await connectDB()

    const shop = await Shop.findByIdAndUpdate(
      shopId,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    )

    if (!shop) {
      return {
        success: false,
        error: 'Shop not found'
      }
    }

    // Log activity
    await logActivity({
      type: 'Product',
      description: `Shop "${shop.name}" updated`,
      userId,
      userName,
      branchId: shopId,
      branchName: shop.name,
      metadata: { updates }
    })

    return {
      success: true,
      data: shop as IShop
    }
  } catch (error) {
    console.error('Update shop service error:', error)
    return {
      success: false,
      error: 'Failed to update shop'
    }
  }
}

/**
 * Delete shop
 */
export async function deleteShop(
  shopId: string,
  userId: string,
  userName: string
): Promise<ServiceResponse<void>> {
  try {
    await connectDB()

    const shop = await Shop.findById(shopId)
    if (!shop) {
      return {
        success: false,
        error: 'Shop not found'
      }
    }

    // Check if shop has products or orders (business logic decision)
    // You might want to prevent deletion if there are dependencies

    // Remove branchId from manager
    if (shop.managerId) {
      await User.findByIdAndUpdate(shop.managerId, { $unset: { branchId: 1 } })
    }

    await Shop.findByIdAndDelete(shopId)

    // Log activity
    await logActivity({
      type: 'Product',
      description: `Shop "${shop.name}" deleted`,
      userId,
      userName,
      metadata: { shopId, shopName: shop.name }
    })

    return {
      success: true
    }
  } catch (error) {
    console.error('Delete shop service error:', error)
    return {
      success: false,
      error: 'Failed to delete shop'
    }
  }
}

/**
 * Get shop statistics
 */
export async function getShopStats(shopId: string): Promise<ServiceResponse<any>> {
  try {
    await connectDB()

    const shop = await Shop.findById(shopId)
    if (!shop) {
      return {
        success: false,
        error: 'Shop not found'
      }
    }

    // You can add more complex statistics here
    const stats = {
      totalProducts: shop.totalProducts,
      totalSales: shop.totalSales,
      status: shop.status,
      manager: shop.manager,
      location: shop.location
    }

    return {
      success: true,
      data: stats
    }
  } catch (error) {
    console.error('Get shop stats service error:', error)
    return {
      success: false,
      error: 'Failed to fetch shop statistics'
    }
  }
}
