import { Product, Inventory, Shop, connectDB, logActivity, generateSKU } from '@/lib/database'
import type { IProduct, ProductStatus } from '@/lib/database'
import type { CreateProductData, UpdateProductData, ProductVariant } from '@/types'

export interface ProductFilters {
  search?: string
  category?: string
  brand?: string
  status?: ProductStatus
  branchId?: string
  country?: string
  region?: string
  minPrice?: number
  maxPrice?: number
  featured?: boolean
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

/**
 * Get all products with filtering and pagination
 */
export async function getProducts(
  filters: ProductFilters,
  pagination: { page: number; limit: number },
  userRole: string,
  userBranchId?: string
): Promise<ServiceResponse<IProduct[]>> {
  try {
    await connectDB()

    const { page, limit } = pagination
    const { search, category, brand, status, branchId, country, region, minPrice, maxPrice, featured } = filters

    console.log('🔍 ProductService.getProducts called with:')
    console.log('  Filters:', JSON.stringify(filters, null, 2))
    console.log('  UserRole:', userRole)
    console.log('  UserBranchId:', userBranchId)

    // Build filter query
    const filter: any = { isActive: true }

    if (search) {
      filter.$text = { $search: search }
    }

    // Handle category filtering - check both category and categoryId/categoryName fields
    if (category) {
      filter.$or = [
        { category: category },
        { categoryId: category },
        { categoryName: category }
      ]
    }

    if (brand) filter.brand = brand
    if (status) filter.status = status
    if (featured) filter.isFeatured = true

    if (minPrice || maxPrice) {
      filter.price = {}
      if (minPrice) filter.price.$gte = minPrice
      if (maxPrice) filter.price.$lte = maxPrice
    }

    // Apply role-based filtering
    console.log('🔐 Applying role-based filtering:')
    console.log('  UserRole:', userRole)
    console.log('  UserBranchId:', userBranchId)
    console.log('  Requested branchId filter:', branchId)

    if (userRole === 'branch_manager' && userBranchId) {
      filter.branchId = userBranchId
      console.log('  ✅ Branch manager: Filtering to branch', userBranchId)
    } else if (branchId) {
      filter.branchId = branchId
      console.log('  ✅ Overall admin: Filtering to specific branch', branchId)
    } else if (userRole === 'overall_admin') {
      console.log('  ✅ Overall admin: No branch filter - showing ALL products')
    }

    // Handle country and region filtering by looking up branches
    if (country || region) {
      console.log('🌍 Applying geographic filters:')
      console.log('  Country:', country)
      console.log('  Region:', region)

      try {
        const branchFilter: any = { status: 'Active' }
        if (country) branchFilter.country = country
        if (region) branchFilter.region = region

        const matchingBranches = await Shop.find(branchFilter).select('_id').lean()
        const branchIds = matchingBranches.map(branch => branch._id.toString())

        console.log('  Found matching branches:', branchIds.length)

        if (branchIds.length > 0) {
          // If we already have a branchId filter, make sure it's in the matching branches
          if (filter.branchId) {
            if (branchIds.includes(filter.branchId)) {
              console.log('  ✅ Existing branch filter matches geographic criteria')
            } else {
              console.log('  ❌ Existing branch filter conflicts with geographic criteria')
              filter.branchId = 'no-match' // This will return no results
            }
          } else {
            filter.branchId = { $in: branchIds }
            console.log('  ✅ Applied geographic branch filter')
          }
        } else {
          console.log('  ❌ No branches found matching geographic criteria')
          filter.branchId = 'no-match' // This will return no results
        }
      } catch (error) {
        console.error('Error applying geographic filters:', error)
      }
    }

    console.log('📋 Final MongoDB filter:', JSON.stringify(filter, null, 2))

    const skip = (page - 1) * limit

    const [products, total] = await Promise.all([
      Product.find(filter)
        .sort(search ? { score: { $meta: 'textScore' } } : { createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Product.countDocuments(filter)
    ])

    console.log('📊 Query results:')
    console.log('  Products found:', products.length)
    console.log('  Total count:', total)
    console.log('  Sample product:', products[0] ? {
      _id: products[0]._id,
      name: products[0].name,
      isFeatured: products[0].isFeatured,
      isActive: products[0].isActive,
      categoryId: products[0].categoryId,
      categoryName: products[0].categoryName
    } : 'No products found')

    return {
      success: true,
      data: products.map((product: any) => ({
        _id: product._id.toString(),
        id: product._id.toString(), // Add id field for compatibility
        name: product.name,
        sku: product.sku,
        category: product.category,
        categoryId: product.categoryId,
        categoryName: product.categoryName,
        price: product.price,
        originalPrice: product.originalPrice,
        currency: product.currency,
        stock: product.stock,
        minStockLevel: product.minStockLevel,
        images: product.images || [],
        featuredImage: product.featuredImage,
        description: product.description,
        specifications: product.specifications || [],
        branchId: product.branchId,
        brand: product.brand,
        model: product.model,
        warranty: product.warranty,
        weight: product.weight,
        dimensions: product.dimensions,
        tags: product.tags || [],
        variants: product.variants || [],
        hasVariants: product.hasVariants || false,
        isFeatured: product.isFeatured,
        isActive: product.isActive,
        isPromoted: product.isPromoted,
        isOnSale: product.isOnSale,
        salePrice: product.salePrice,
        saleStartDate: product.saleStartDate,
        saleEndDate: product.saleEndDate,
        promotionDescription: product.promotionDescription,
        status: product.status,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        createdBy: product.createdBy,
        updatedBy: product.updatedBy
      })) as IProduct[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Get products service error:', error)
    return {
      success: false,
      error: 'Failed to fetch products'
    }
  }
}

/**
 * Get product by ID
 */
export async function getProductById(productId: string): Promise<ServiceResponse<IProduct>> {
  try {
    await connectDB()

    const product = await Product.findById(productId).lean()

    if (!product) {
      return {
        success: false,
        error: 'Product not found'
      }
    }

    return {
      success: true,
      data: {
        _id: (product as any)._id.toString(),
        name: (product as any).name,
        sku: (product as any).sku,
        category: (product as any).category,
        categoryId: (product as any).categoryId,
        categoryName: (product as any).categoryName,
        price: (product as any).price,
        originalPrice: (product as any).originalPrice,
        currency: (product as any).currency,
        stock: (product as any).stock,
        minStockLevel: (product as any).minStockLevel,
        images: (product as any).images,
        featuredImage: (product as any).featuredImage,
        description: (product as any).description,
        specifications: (product as any).specifications,
        branchId: (product as any).branchId,
        brand: (product as any).brand,
        model: (product as any).model,
        warranty: (product as any).warranty,
        weight: (product as any).weight,
        dimensions: (product as any).dimensions,
        tags: (product as any).tags,
        variants: (product as any).variants,
        hasVariants: (product as any).hasVariants,
        isFeatured: (product as any).isFeatured,
        isActive: (product as any).isActive,
        isPromoted: (product as any).isPromoted,
        isOnSale: (product as any).isOnSale,
        salePrice: (product as any).salePrice,
        saleStartDate: (product as any).saleStartDate,
        saleEndDate: (product as any).saleEndDate,
        promotionDescription: (product as any).promotionDescription,
        status: (product as any).status,
        createdAt: (product as any).createdAt,
        updatedAt: (product as any).updatedAt,
        createdBy: (product as any).createdBy,
        updatedBy: (product as any).updatedBy
      } as IProduct
    }
  } catch (error) {
    console.error('Get product by ID service error:', error)
    return {
      success: false,
      error: 'Failed to fetch product'
    }
  }
}

/**
 * Create new product
 */
export async function createProduct(
  data: CreateProductData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IProduct>> {
  try {
    console.log('🏭 CREATE PRODUCT SERVICE STARTED')
    console.log('📋 Service received data:', JSON.stringify(data, null, 2))
    console.log('👤 User info:', { userId, userName })

    await connectDB()
    console.log('🔗 Database connected')

    // Get branch details
    console.log('🏢 Looking for branch with ID:', data.branchId)
    const branch = await Shop.findById(data.branchId)
    console.log('🏢 Branch found:', branch ? { id: branch._id, name: branch.name } : 'null')

    if (!branch) {
      console.log('❌ Branch not found')
      return {
        success: false,
        error: 'Branch not found'
      }
    }

    // Generate SKU if not provided
    const sku = data.sku || generateSKU(data.categoryName, data.brand)
    console.log('🏷️ SKU:', sku)

    // Process variants if any
    const processedVariants = data.variants?.map(variant => ({
      ...variant,
      id: variant.id || new Date().getTime().toString(),
      sku: variant.sku || `${sku}-${variant.name.replace(/\s+/g, '').toUpperCase()}`
    })) || []
    console.log('🔄 Processed variants:', processedVariants)

    // Create product
    console.log('💾 Creating product in database...')
    const productToCreate = {
      ...data,
      sku,
      variants: processedVariants,
      hasVariants: processedVariants.length > 0,
      isActive: true
    }
    console.log('📦 Product to create:', JSON.stringify(productToCreate, null, 2))

    const product = await Product.create(productToCreate)
    console.log('✅ Product created with ID:', product._id)

    // Create corresponding inventory record
    console.log('📦 Creating inventory record...')
    const inventoryData = {
      productId: product._id.toString(),
      productName: product.name,
      sku: product.sku,
      branchId: product.branchId,
      branchName: branch.name,
      stock: product.stock,
      minStockLevel: product.minStockLevel,
      maxStockLevel: product.minStockLevel * 10, // Default max stock
      cost: product.price * 0.7, // Assuming 30% markup
      location: 'Main Warehouse',
      lastRestocked: new Date()
    }
    console.log('📦 Inventory data:', JSON.stringify(inventoryData, null, 2))

    await Inventory.create(inventoryData)
    console.log('✅ Inventory record created')

    // Update shop's total products count
    console.log('🏢 Updating shop product count...')
    await Shop.findByIdAndUpdate(data.branchId, {
      $inc: { totalProducts: 1 }
    })
    console.log('✅ Shop product count updated')

    // Log activity
    console.log('📝 Logging activity...')
    await logActivity({
      type: 'Product',
      description: `New product "${product.name}" created`,
      userId,
      userName,
      branchId: product.branchId,
      branchName: branch.name,
      metadata: {
        productId: product._id.toString(),
        productName: product.name,
        sku: product.sku
      }
    })
    console.log('✅ Activity logged')

    console.log('🎉 Product creation completed successfully!')
    return {
      success: true,
      data: product as IProduct
    }
  } catch (error) {
    console.error('❌ Create product service error:', error)
    return {
      success: false,
      error: 'Failed to create product'
    }
  }
}

/**
 * Update product
 */
export async function updateProduct(
  productId: string,
  updates: UpdateProductData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IProduct>> {
  try {
    await connectDB()

    const existingProduct = await Product.findById(productId)
    if (!existingProduct) {
      return {
        success: false,
        error: 'Product not found'
      }
    }

    const product = await Product.findByIdAndUpdate(
      productId,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: false }
    )

    // Update inventory if stock changed
    if (updates.stock !== undefined) {
      await Inventory.findOneAndUpdate(
        { productId, branchId: product!.branchId },
        { 
          stock: updates.stock,
          lastRestocked: new Date()
        }
      )
    }

    // Get branch name for logging
    const branch = await Shop.findById(product!.branchId)

    // Log activity
    await logActivity({
      type: 'Product',
      description: `Product "${product!.name}" updated`,
      userId,
      userName,
      branchId: product!.branchId,
      branchName: branch?.name,
      metadata: { 
        productId: product!._id.toString(),
        productName: product!.name,
        updates 
      }
    })

    return {
      success: true,
      data: product as IProduct
    }
  } catch (error) {
    console.error('Update product service error:', error)
    return {
      success: false,
      error: 'Failed to update product'
    }
  }
}

/**
 * Delete product (soft delete)
 */
export async function deleteProduct(
  productId: string,
  userId: string,
  userName: string
): Promise<ServiceResponse<void>> {
  try {
    await connectDB()

    const product = await Product.findById(productId)
    if (!product) {
      return {
        success: false,
        error: 'Product not found'
      }
    }

    // Soft delete - set isActive to false
    await Product.findByIdAndUpdate(productId, { isActive: false })

    // Also update inventory
    await Inventory.findOneAndUpdate(
      { productId, branchId: product.branchId },
      { stock: 0 }
    )

    // Update shop's total products count
    await Shop.findByIdAndUpdate(product.branchId, {
      $inc: { totalProducts: -1 }
    })

    // Get branch name for logging
    const branch = await Shop.findById(product.branchId)

    // Log activity
    await logActivity({
      type: 'Product',
      description: `Product "${product.name}" deleted`,
      userId,
      userName,
      branchId: product.branchId,
      branchName: branch?.name,
      metadata: { 
        productId: product._id.toString(),
        productName: product.name
      }
    })

    return {
      success: true
    }
  } catch (error) {
    console.error('Delete product service error:', error)
    return {
      success: false,
      error: 'Failed to delete product'
    }
  }
}

/**
 * Get low stock products
 */
export async function getLowStockProducts(branchId?: string): Promise<ServiceResponse<IProduct[]>> {
  try {
    await connectDB()

    const filter: any = {
      isActive: true,
      $expr: { $lte: ['$stock', '$minStockLevel'] }
    }

    if (branchId) {
      filter.branchId = branchId
    }

    const products = await Product.find(filter)
      .select('name sku stock minStockLevel status branchId')
      .limit(50)
      .lean()

    return {
      success: true,
      data: products.map((product: any) => ({
        _id: product._id.toString(),
        name: product.name,
        sku: product.sku,
        category: product.category,
        price: product.price,
        originalPrice: product.originalPrice,
        stock: product.stock,
        minStockLevel: product.minStockLevel,
        images: product.images,
        description: product.description,
        specifications: product.specifications,
        branchId: product.branchId,
        brand: product.brand,
        model: product.model,
        warranty: product.warranty,
        weight: product.weight,
        dimensions: product.dimensions,
        tags: product.tags,
        isFeatured: product.isFeatured,
        isActive: product.isActive,
        status: product.status,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      })) as IProduct[]
    }
  } catch (error) {
    console.error('Get low stock products service error:', error)
    return {
      success: false,
      error: 'Failed to fetch low stock products'
    }
  }
}
