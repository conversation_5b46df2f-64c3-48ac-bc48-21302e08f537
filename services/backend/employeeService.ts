// services/backend/employeeService.ts
import mongoose from 'mongoose'
import { Employee, User, Shop, connectDB, logActivity } from '@/lib/database'
import type { IEmployee } from '@/lib/database'

export interface CreateEmployeeData {
  employeeId?: string
  firstName: string
  lastName: string
  email: string
  phone: string
  position: string
  department: string
  branchId: string
  salary: number
  hireDate: Date
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  emergencyContact: {
    name: string
    relationship: string
    phone: string
  }
  userId?: string
  isActive?: boolean
}

export interface UpdateEmployeeData {
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  position?: string
  department?: string
  branchId?: string
  salary?: number
  address?: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  emergencyContact?: {
    name: string
    relationship: string
    phone: string
  }
  isActive?: boolean
}

export interface EmployeeFilters {
  search?: string
  branchId?: string
  department?: string
  position?: string
  isActive?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

/**
 * Get all employees with filtering and pagination
 */
export async function getEmployees(
  filters: EmployeeFilters,
  pagination: { page: number; limit: number },
  userRole: string,
  userBranchId?: string
): Promise<ServiceResponse<IEmployee[]>> {
  try {
    await connectDB()

    const { page, limit } = pagination
    const { search, branchId, department, position, isActive, sortBy = 'createdAt', sortOrder = 'desc' } = filters

    // Build filter query
    const filter: any = {}
    
    if (search) {
      filter.$text = { $search: search }
    }

    if (department) filter.department = department
    if (position) filter.position = position
    if (isActive !== undefined) filter.isActive = isActive

    // Apply role-based filtering
    if (userRole === 'branch_manager' && userBranchId) {
      filter.branchId = userBranchId
    } else if (branchId) {
      filter.branchId = branchId
    }

    const skip = (page - 1) * limit
    const sort: any = {}
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1

    const [employees, total] = await Promise.all([
      Employee.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Employee.countDocuments(filter)
    ])

    return {
      success: true,
      data: employees.map((employee: any) => ({
        _id: employee._id.toString(),
        employeeId: employee.employeeId,
        firstName: employee.firstName,
        lastName: employee.lastName,
        email: employee.email,
        phone: employee.phone,
        position: employee.position,
        department: employee.department,
        branchId: employee.branchId,
        branchName: employee.branchName,
        salary: employee.salary,
        hireDate: employee.hireDate,
        address: employee.address,
        emergencyContact: employee.emergencyContact,
        userId: employee.userId,
        isActive: employee.isActive,
        createdAt: employee.createdAt,
        updatedAt: employee.updatedAt
      })) as IEmployee[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Get employees service error:', error)
    return {
      success: false,
      error: 'Failed to fetch employees'
    }
  }
}

/**
 * Get employee by ID
 */
export async function getEmployeeById(employeeId: string): Promise<ServiceResponse<IEmployee>> {
  try {
    await connectDB()

    const employee = await Employee.findById(employeeId).lean()

    if (!employee) {
      return {
        success: false,
        error: 'Employee not found'
      }
    }

    return {
      success: true,
      data: {
        _id: (employee as any)._id.toString(),
        employeeId: (employee as any).employeeId,
        firstName: (employee as any).firstName,
        lastName: (employee as any).lastName,
        email: (employee as any).email,
        phone: (employee as any).phone,
        position: (employee as any).position,
        department: (employee as any).department,
        branchId: (employee as any).branchId,
        branchName: (employee as any).branchName,
        salary: (employee as any).salary,
        hireDate: (employee as any).hireDate,
        address: (employee as any).address,
        emergencyContact: (employee as any).emergencyContact,
        userId: (employee as any).userId,
        isActive: (employee as any).isActive,
        createdAt: (employee as any).createdAt,
        updatedAt: (employee as any).updatedAt
      } as IEmployee
    }
  } catch (error) {
    console.error('Get employee by ID service error:', error)
    return {
      success: false,
      error: 'Failed to fetch employee'
    }
  }
}

/**
 * Create new employee
 */
export async function createEmployee(
  data: CreateEmployeeData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IEmployee>> {
  try {
    console.log('=== BACKEND SERVICE CREATE EMPLOYEE START ===')
    console.log('Backend Service: Received data:', JSON.stringify(data, null, 2))
    console.log('Backend Service: User ID:', userId)
    console.log('Backend Service: User Name:', userName)

    console.log('Backend Service: Connecting to database...')
    await connectDB()
    console.log('Backend Service: Database connected successfully')

    // Check if employee already exists
    console.log('Backend Service: Checking for existing employee...')
    const existingEmployee = await Employee.findOne({
      $or: [
        { email: data.email },
        { phone: data.phone }
      ]
    })

    if (existingEmployee) {
      console.log('Backend Service: Employee already exists:', existingEmployee.email)
      return {
        success: false,
        error: existingEmployee.email === data.email
          ? 'Email already registered'
          : 'Phone number already registered'
      }
    }
    console.log('Backend Service: No existing employee found, proceeding...')

    // Get branch details - handle both ObjectId and string IDs
    console.log('Backend Service: Getting branch details for ID:', data.branchId)
    let branch

    // Try to find by _id first (if it's a valid ObjectId)
    if (mongoose.Types.ObjectId.isValid(data.branchId)) {
      console.log('Backend Service: Searching by ObjectId')
      branch = await Shop.findById(data.branchId)
    }

    // If not found or not a valid ObjectId, try to find by a custom field
    if (!branch) {
      console.log('Backend Service: Searching by custom ID or name')
      // Try to find by name or location as fallback
      branch = await Shop.findOne({
        $or: [
          { name: data.branchId },
          { location: data.branchId }
        ]
      })
    }

    if (!branch) {
      console.log('Backend Service: Branch not found for ID:', data.branchId)
      console.log('Backend Service: Available shops:')
      const allShops = await Shop.find({}, 'name location _id').limit(10)
      console.log('Backend Service: Shops in database:', allShops.map(s => ({ id: s._id, name: s.name, location: s.location })))
      return {
        success: false,
        error: `Branch not found for ID: ${data.branchId}. Available branches: ${allShops.map(s => s.name).join(', ')}`
      }
    }
    console.log('Backend Service: Branch found:', branch.name)

    // Generate employee ID
    console.log('Backend Service: Generating employee ID...')
    const employeeCount = await Employee.countDocuments()
    const employeeId = `EMP${String(employeeCount + 1).padStart(4, '0')}`
    console.log('Backend Service: Generated employee ID:', employeeId, 'from count:', employeeCount)

    // Create employee
    console.log('Backend Service: Creating employee document...')
    const employeeDoc = {
      ...data,
      employeeId,
      branchName: branch.name,
      isActive: true
    }
    console.log('Backend Service: Employee document to create:', JSON.stringify(employeeDoc, null, 2))

    const employee = await Employee.create(employeeDoc)
    console.log('Backend Service: Employee created successfully!')
    console.log('Backend Service: Created employee ID:', employee._id)
    console.log('Backend Service: Created employee data:', JSON.stringify(employee.toObject(), null, 2))

    // Log activity
    await logActivity({
      type: 'User',
      description: `New employee "${employee.firstName} ${employee.lastName}" created`,
      userId,
      userName,
      branchId: data.branchId,
      branchName: branch.name,
      metadata: { 
        employeeId: employee._id.toString(),
        employeeName: `${employee.firstName} ${employee.lastName}`,
        position: employee.position,
        department: employee.department
      }
    })

    console.log('Backend Service: Preparing return data...')
    const returnData = {
      _id: employee._id.toString(),
      employeeId: employee.employeeId,
      firstName: employee.firstName,
      lastName: employee.lastName,
      email: employee.email,
      phone: employee.phone,
      position: employee.position,
      department: employee.department,
      branchId: employee.branchId,
      branchName: employee.branchName,
      salary: employee.salary,
      hireDate: employee.hireDate,
      address: employee.address,
      emergencyContact: employee.emergencyContact,
      userId: employee.userId,
      isActive: employee.isActive,
      createdAt: employee.createdAt,
      updatedAt: employee.updatedAt
    } as IEmployee

    const result = {
      success: true,
      data: returnData
    }

    console.log('Backend Service: Final result:', JSON.stringify(result, null, 2))
    console.log('=== BACKEND SERVICE CREATE EMPLOYEE SUCCESS ===')

    return result
  } catch (error) {
    console.error('=== BACKEND SERVICE CREATE EMPLOYEE ERROR ===')
    console.error('Create employee service error:', error)
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined
    })

    const errorResult = {
      success: false,
      error: 'Failed to create employee'
    }
    console.log('Backend Service: Error result:', errorResult)
    return errorResult
  }
}

/**
 * Update employee
 */
export async function updateEmployee(
  employeeId: string,
  data: UpdateEmployeeData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IEmployee>> {
  try {
    await connectDB()

    // Check if employee exists
    const existingEmployee = await Employee.findById(employeeId)
    if (!existingEmployee) {
      return {
        success: false,
        error: 'Employee not found'
      }
    }

    // Check for email/phone conflicts if being updated
    if (data.email || data.phone) {
      const conflictQuery: any = {
        _id: { $ne: employeeId }
      }

      if (data.email || data.phone) {
        conflictQuery.$or = []
        if (data.email) conflictQuery.$or.push({ email: data.email })
        if (data.phone) conflictQuery.$or.push({ phone: data.phone })
      }

      const conflictEmployee = await Employee.findOne(conflictQuery)
      if (conflictEmployee) {
        return {
          success: false,
          error: conflictEmployee.email === data.email
            ? 'Email already registered'
            : 'Phone number already registered'
        }
      }
    }

    // Get branch name if branchId is being updated
    let branchName = existingEmployee.branchName
    if (data.branchId && data.branchId !== existingEmployee.branchId) {
      const branch = await Shop.findById(data.branchId)
      if (!branch) {
        return {
          success: false,
          error: 'Branch not found'
        }
      }
      branchName = branch.name
    }

    // Update employee
    const employee = await Employee.findByIdAndUpdate(
      employeeId,
      {
        ...data,
        ...(data.branchId && { branchName }),
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    )

    // Log activity
    await logActivity({
      type: 'User',
      description: `Employee "${employee!.firstName} ${employee!.lastName}" updated`,
      userId,
      userName,
      branchId: employee!.branchId,
      branchName: employee!.branchName,
      metadata: {
        employeeId: employee!._id.toString(),
        employeeName: `${employee!.firstName} ${employee!.lastName}`,
        updatedFields: Object.keys(data)
      }
    })

    return {
      success: true,
      data: {
        _id: employee!._id.toString(),
        employeeId: employee!.employeeId,
        firstName: employee!.firstName,
        lastName: employee!.lastName,
        email: employee!.email,
        phone: employee!.phone,
        position: employee!.position,
        department: employee!.department,
        branchId: employee!.branchId,
        branchName: employee!.branchName,
        salary: employee!.salary,
        hireDate: employee!.hireDate,
        address: employee!.address,
        emergencyContact: employee!.emergencyContact,
        userId: employee!.userId,
        isActive: employee!.isActive,
        createdAt: employee!.createdAt,
        updatedAt: employee!.updatedAt
      } as IEmployee
    }
  } catch (error) {
    console.error('Update employee service error:', error)
    return {
      success: false,
      error: 'Failed to update employee'
    }
  }
}

/**
 * Delete employee (soft delete)
 */
export async function deleteEmployee(
  employeeId: string,
  userId: string,
  userName: string
): Promise<ServiceResponse<boolean>> {
  try {
    await connectDB()

    const employee = await Employee.findById(employeeId)
    if (!employee) {
      return {
        success: false,
        error: 'Employee not found'
      }
    }

    // Soft delete by setting isActive to false
    await Employee.findByIdAndUpdate(employeeId, {
      isActive: false,
      updatedAt: new Date()
    })

    // Log activity
    await logActivity({
      type: 'User',
      description: `Employee "${employee.firstName} ${employee.lastName}" deactivated`,
      userId,
      userName,
      branchId: employee.branchId,
      branchName: employee.branchName,
      metadata: {
        employeeId: employee._id.toString(),
        employeeName: `${employee.firstName} ${employee.lastName}`
      }
    })

    return {
      success: true,
      data: true
    }
  } catch (error) {
    console.error('Delete employee service error:', error)
    return {
      success: false,
      error: 'Failed to delete employee'
    }
  }
}

/**
 * Get employees by branch
 */
export async function getEmployeesByBranch(branchId: string): Promise<ServiceResponse<IEmployee[]>> {
  try {
    await connectDB()

    const employees = await Employee.find({
      branchId,
      isActive: true
    })
    .sort({ firstName: 1 })
    .lean()

    return {
      success: true,
      data: employees.map((employee: any) => ({
        _id: employee._id.toString(),
        employeeId: employee.employeeId,
        firstName: employee.firstName,
        lastName: employee.lastName,
        email: employee.email,
        phone: employee.phone,
        position: employee.position,
        department: employee.department,
        branchId: employee.branchId,
        branchName: employee.branchName,
        salary: employee.salary,
        hireDate: employee.hireDate,
        address: employee.address,
        emergencyContact: employee.emergencyContact,
        userId: employee.userId,
        isActive: employee.isActive,
        createdAt: employee.createdAt,
        updatedAt: employee.updatedAt
      })) as IEmployee[]
    }
  } catch (error) {
    console.error('Get employees by branch service error:', error)
    return {
      success: false,
      error: 'Failed to fetch employees'
    }
  }
}
