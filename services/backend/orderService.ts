// services/backend/orderService.ts
import { Order, Product, Customer, Shop, connectDB, logActivity, generateOrderNumber, calculateOrderTotal, updateProductStock } from '@/lib/database'
import type { IOrder, IOrderItem, OrderStatus, PaymentStatus, PaymentMethod } from '@/lib/database'

export interface CreateOrderData {
  customerId: string
  items: Array<{
    productId: string
    quantity: number
  }>
  branchId: string
  paymentMethod: PaymentMethod
  shippingAddress: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  billingAddress: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  notes?: string
  discount?: number
}

export interface UpdateOrderData {
  status?: OrderStatus
  paymentStatus?: PaymentStatus
  trackingNumber?: string
  estimatedDelivery?: Date
  notes?: string
}

export interface OrderFilters {
  status?: OrderStatus
  paymentStatus?: PaymentStatus
  branchId?: string
  customerId?: string
  startDate?: Date
  endDate?: Date
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

/**
 * Get all orders with filtering and pagination
 */
export async function getOrders(
  filters: OrderFilters,
  pagination: { page: number; limit: number },
  userRole: string,
  userBranchId?: string
): Promise<ServiceResponse<IOrder[]>> {
  try {
    await connectDB()

    const { page, limit } = pagination
    const { status, paymentStatus, branchId, customerId, startDate, endDate } = filters

    // Build filter query
    const filter: any = {}
    
    if (status) filter.status = status
    if (paymentStatus) filter.paymentStatus = paymentStatus
    if (customerId) filter.customerId = customerId

    if (startDate || endDate) {
      filter.createdAt = {}
      if (startDate) filter.createdAt.$gte = startDate
      if (endDate) filter.createdAt.$lte = endDate
    }

    // Apply role-based filtering
    if (userRole === 'branch_manager' && userBranchId) {
      filter.branchId = userBranchId
    } else if (branchId) {
      filter.branchId = branchId
    }

    const skip = (page - 1) * limit

    const [orders, total] = await Promise.all([
      Order.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Order.countDocuments(filter)
    ])

    return {
      success: true,
      data: orders.map((order: any) => ({
        _id: order._id.toString(),
        orderNumber: order.orderNumber,
        customerId: order.customerId,
        customerName: order.customerName,
        customerEmail: order.customerEmail,
        customerPhone: order.customerPhone,
        items: order.items,
        subtotal: order.subtotal,
        tax: order.tax,
        shipping: order.shipping,
        discount: order.discount,
        total: order.total,
        status: order.status,
        paymentStatus: order.paymentStatus,
        paymentMethod: order.paymentMethod,
        branchId: order.branchId,
        branchName: order.branchName,
        shippingAddress: order.shippingAddress,
        billingAddress: order.billingAddress,
        notes: order.notes,
        trackingNumber: order.trackingNumber,
        estimatedDelivery: order.estimatedDelivery,
        createdAt: order.createdAt,
        updatedAt: order.updatedAt
      })) as IOrder[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Get orders service error:', error)
    return {
      success: false,
      error: 'Failed to fetch orders'
    }
  }
}

/**
 * Get order by ID
 */
export async function getOrderById(orderId: string): Promise<ServiceResponse<IOrder>> {
  try {
    await connectDB()

    const order = await Order.findById(orderId).lean()

    if (!order) {
      return {
        success: false,
        error: 'Order not found'
      }
    }

    return {
      success: true,
      data: {
        _id: (order as any)._id.toString(),
        orderNumber: (order as any).orderNumber,
        customerId: (order as any).customerId,
        customerName: (order as any).customerName,
        customerEmail: (order as any).customerEmail,
        customerPhone: (order as any).customerPhone,
        items: (order as any).items,
        subtotal: (order as any).subtotal,
        tax: (order as any).tax,
        shipping: (order as any).shipping,
        discount: (order as any).discount,
        total: (order as any).total,
        status: (order as any).status,
        paymentStatus: (order as any).paymentStatus,
        paymentMethod: (order as any).paymentMethod,
        branchId: (order as any).branchId,
        branchName: (order as any).branchName,
        shippingAddress: (order as any).shippingAddress,
        billingAddress: (order as any).billingAddress,
        notes: (order as any).notes,
        trackingNumber: (order as any).trackingNumber,
        estimatedDelivery: (order as any).estimatedDelivery,
        createdAt: (order as any).createdAt,
        updatedAt: (order as any).updatedAt
      } as IOrder
    }
  } catch (error) {
    console.error('Get order by ID service error:', error)
    return {
      success: false,
      error: 'Failed to fetch order'
    }
  }
}

/**
 * Create new order
 */
export async function createOrder(
  data: CreateOrderData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IOrder>> {
  try {
    await connectDB()

    // Get customer details
    const customer = await Customer.findById(data.customerId)
    if (!customer) {
      return {
        success: false,
        error: 'Customer not found'
      }
    }

    // Get branch details
    const branch = await Shop.findById(data.branchId)
    if (!branch) {
      return {
        success: false,
        error: 'Branch not found'
      }
    }

    // Process order items and validate stock
    const orderItems: IOrderItem[] = []
    let subtotal = 0

    for (const item of data.items) {
      const product = await Product.findById(item.productId)
      if (!product) {
        return {
          success: false,
          error: `Product ${item.productId} not found`
        }
      }

      if (!product.isActive) {
        return {
          success: false,
          error: `Product ${product.name} is not available`
        }
      }

      if (product.stock < item.quantity) {
        return {
          success: false,
          error: `Insufficient stock for ${product.name}. Available: ${product.stock}, Requested: ${item.quantity}`
        }
      }

      const itemSubtotal = product.price * item.quantity
      subtotal += itemSubtotal

      orderItems.push({
        id: item.productId,
        productId: item.productId,
        productName: product.name,
        sku: product.sku,
        price: product.price,
        quantity: item.quantity,
        subtotal: itemSubtotal,
        image: product.images[0]
      })
    }

    // Calculate totals (18% VAT for Malawi)
    const totals = calculateOrderTotal(orderItems, 0.18, 0, data.discount || 0)

    // Generate order number
    const orderNumber = generateOrderNumber()

    // Create order
    const order = await Order.create({
      orderNumber,
      customerId: customer._id.toString(),
      customerName: `${customer.firstName} ${customer.lastName}`,
      customerEmail: customer.email,
      customerPhone: customer.phone,
      items: orderItems,
      subtotal: totals.subtotal,
      tax: totals.tax,
      shipping: totals.shipping,
      discount: totals.discount,
      total: totals.total,
      status: 'Pending',
      paymentStatus: 'Pending',
      paymentMethod: data.paymentMethod,
      branchId: data.branchId,
      branchName: branch.name,
      shippingAddress: data.shippingAddress,
      billingAddress: data.billingAddress,
      notes: data.notes
    })

    // Update product stock
    for (const item of data.items) {
      await updateProductStock(
        item.productId,
        data.branchId,
        -item.quantity, // Negative for sale
        userId,
        userName
      )
    }

    // Update customer stats
    await Customer.findByIdAndUpdate(customer._id, {
      $inc: { 
        totalOrders: 1, 
        totalSpent: totals.total,
        loyaltyPoints: Math.floor(totals.total / 100) // 1 point per $100
      },
      lastOrderDate: new Date()
    })

    // Update branch sales
    await Shop.findByIdAndUpdate(data.branchId, {
      $inc: { totalSales: totals.total }
    })

    // Log activity
    await logActivity({
      type: 'Order',
      description: `New order ${order.orderNumber} created for ${customer.firstName} ${customer.lastName}`,
      userId,
      userName,
      branchId: data.branchId,
      branchName: branch.name,
      metadata: { 
        orderId: order._id.toString(),
        orderNumber: order.orderNumber,
        total: totals.total,
        itemCount: orderItems.length,
        customerId: customer._id.toString()
      }
    })

    return {
      success: true,
      data: order as IOrder
    }
  } catch (error) {
    console.error('Create order service error:', error)
    return {
      success: false,
      error: 'Failed to create order'
    }
  }
}

/**
 * Update order
 */
export async function updateOrder(
  orderId: string,
  updates: UpdateOrderData,
  userId: string,
  userName: string
): Promise<ServiceResponse<IOrder>> {
  try {
    await connectDB()

    const existingOrder = await Order.findById(orderId)
    if (!existingOrder) {
      return {
        success: false,
        error: 'Order not found'
      }
    }

    const order = await Order.findByIdAndUpdate(
      orderId,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    )

    // Generate activity description based on what was updated
    let activityDescription = `Order ${order!.orderNumber} updated`
    if (updates.status) {
      activityDescription = `Order ${order!.orderNumber} status changed to ${updates.status}`
    } else if (updates.paymentStatus) {
      activityDescription = `Order ${order!.orderNumber} payment status changed to ${updates.paymentStatus}`
    } else if (updates.trackingNumber) {
      activityDescription = `Tracking number ${updates.trackingNumber} added to order ${order!.orderNumber}`
    }

    // Log activity
    await logActivity({
      type: 'Order',
      description: activityDescription,
      userId,
      userName,
      branchId: order!.branchId,
      branchName: order!.branchName,
      metadata: { 
        orderId: order!._id.toString(),
        orderNumber: order!.orderNumber,
        updates 
      }
    })

    return {
      success: true,
      data: order as IOrder
    }
  } catch (error) {
    console.error('Update order service error:', error)
    return {
      success: false,
      error: 'Failed to update order'
    }
  }
}

/**
 * Cancel order
 */
export async function cancelOrder(
  orderId: string,
  userId: string,
  userName: string
): Promise<ServiceResponse<void>> {
  try {
    await connectDB()

    const order = await Order.findById(orderId)
    if (!order) {
      return {
        success: false,
        error: 'Order not found'
      }
    }

    // Check if order can be cancelled
    if (['Shipped', 'Delivered'].includes(order.status)) {
      return {
        success: false,
        error: 'Cannot cancel shipped or delivered orders'
      }
    }

    // Update order status to cancelled
    await Order.findByIdAndUpdate(orderId, { 
      status: 'Cancelled',
      updatedAt: new Date()
    })

    // Restore product stock if order was confirmed/processing
    if (['Confirmed', 'Processing'].includes(order.status)) {
      for (const item of order.items) {
        await updateProductStock(
          item.productId,
          order.branchId,
          item.quantity, // Positive to restore stock
          userId,
          userName
        )
      }

      // Update customer stats (subtract the order)
      await Customer.findByIdAndUpdate(order.customerId, {
        $inc: { 
          totalOrders: -1, 
          totalSpent: -order.total,
          loyaltyPoints: -Math.floor(order.total / 100)
        }
      })

      // Update branch sales
      await Shop.findByIdAndUpdate(order.branchId, {
        $inc: { totalSales: -order.total }
      })
    }

    // Log activity
    await logActivity({
      type: 'Order',
      description: `Order ${order.orderNumber} cancelled`,
      userId,
      userName,
      branchId: order.branchId,
      branchName: order.branchName,
      metadata: { 
        orderId: order._id.toString(),
        orderNumber: order.orderNumber,
        originalStatus: order.status
      }
    })

    return {
      success: true
    }
  } catch (error) {
    console.error('Cancel order service error:', error)
    return {
      success: false,
      error: 'Failed to cancel order'
    }
  }
}
