// services/backend/userService.ts
import { User, Shop, connectDB, logActivity } from '@/lib/database'
import type { IUser } from '@/lib/database'

export interface CreateUserData {
  username: string
  email: string
  password: string
  role: 'branch_manager' | 'overall_admin'
  branchId?: string
  name: string
  phone?: string
}

export interface UpdateUserData {
  username?: string
  email?: string
  role?: 'overall_admin' | 'branch_manager'
  branchId?: string
  firstName?: string
  lastName?: string
  phone?: string
  isActive?: boolean
}

export interface UserFilters {
  search?: string
  role?: string
  branchId?: string
  isActive?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

/**
 * Get all users with filtering and pagination
 */
export async function getUsers(
  filters: UserFilters,
  pagination: { page: number; limit: number },
  userRole: string,
  userBranchId?: string
): Promise<ServiceResponse<IUser[]>> {
  try {
    await connectDB()

    const { page, limit } = pagination
    const { search, role, branchId, isActive, sortBy = 'createdAt', sortOrder = 'desc' } = filters

    // Build filter query
    const filter: any = {}
    
    if (search) {
      filter.$or = [
        { username: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } }
      ]
    }

    if (role) filter.role = role
    if (isActive !== undefined) filter.isActive = isActive

    // Apply role-based filtering
    if (userRole === 'branch_manager' && userBranchId) {
      filter.branchId = userBranchId
    } else if (branchId) {
      filter.branchId = branchId
    }

    const skip = (page - 1) * limit
    const sort: any = {}
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1

    const [users, total] = await Promise.all([
      User.find(filter)
        .select('-password') // Exclude password from results
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      User.countDocuments(filter)
    ])

    return {
      success: true,
      data: users.map((user: any) => ({
        _id: user._id.toString(),
        username: user.username,
        email: user.email,
        role: user.role,
        branchId: user.branchId,
        branchName: user.branchName,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      })) as IUser[],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  } catch (error) {
    console.error('Get users service error:', error)
    return {
      success: false,
      error: 'Failed to fetch users'
    }
  }
}

/**
 * Get user by ID
 */
export async function getUserById(userId: string): Promise<ServiceResponse<IUser>> {
  try {
    await connectDB()

    const user = await User.findById(userId).select('-password').lean()

    if (!user) {
      return {
        success: false,
        error: 'User not found'
      }
    }

    return {
      success: true,
      data: {
        _id: (user as any)._id.toString(),
        username: (user as any).username,
        email: (user as any).email,
        role: (user as any).role,
        branchId: (user as any).branchId,
        branchName: (user as any).branchName,
        firstName: (user as any).firstName,
        lastName: (user as any).lastName,
        phone: (user as any).phone,
        isActive: (user as any).isActive,
        lastLogin: (user as any).lastLogin,
        createdAt: (user as any).createdAt,
        updatedAt: (user as any).updatedAt
      } as IUser
    }
  } catch (error) {
    console.error('Get user by ID service error:', error)
    return {
      success: false,
      error: 'Failed to fetch user'
    }
  }
}

/**
 * Create new user
 */
export async function createUser(
  data: CreateUserData,
  adminUserId: string,
  adminUserName: string
): Promise<ServiceResponse<IUser>> {
  try {
    await connectDB()

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { username: data.username },
        { email: data.email }
      ]
    })

    if (existingUser) {
      return {
        success: false,
        error: existingUser.username === data.username 
          ? 'Username already exists' 
          : 'Email already registered'
      }
    }

    // Validate branch if provided
    let branchName = undefined
    if (data.branchId) {
      const branch = await Shop.findById(data.branchId)
      if (!branch) {
        return {
          success: false,
          error: 'Branch not found'
        }
      }
      branchName = branch.name
    }

    // Create user (password will be hashed by the pre-save middleware)
    const user = await User.create({
      ...data,
      branchName,
      isActive: true
    })

    // Log activity
    await logActivity({
      type: 'User',
      description: `New user "${user.username}" created with role ${user.role}`,
      userId: adminUserId,
      userName: adminUserName,
      branchId: data.branchId,
      branchName,
      metadata: { 
        newUserId: user._id.toString(),
        newUsername: user.username,
        newUserRole: user.role
      }
    })

    return {
      success: true,
      data: {
        _id: user._id.toString(),
        username: user.username,
        email: user.email,
        role: user.role,
        branchId: user.branchId,
        branchName: user.branchName,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        isActive: user.isActive,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      } as IUser
    }
  } catch (error) {
    console.error('Create user service error:', error)
    return {
      success: false,
      error: 'Failed to create user'
    }
  }
}

/**
 * Update user
 */
export async function updateUser(
  userId: string,
  data: UpdateUserData,
  adminUserId: string,
  adminUserName: string
): Promise<ServiceResponse<IUser>> {
  try {
    await connectDB()

    // Check if user exists
    const existingUser = await User.findById(userId)
    if (!existingUser) {
      return {
        success: false,
        error: 'User not found'
      }
    }

    // Check for username/email conflicts if being updated
    if (data.username || data.email) {
      const conflictQuery: any = {
        _id: { $ne: userId }
      }

      if (data.username || data.email) {
        conflictQuery.$or = []
        if (data.username) conflictQuery.$or.push({ username: data.username })
        if (data.email) conflictQuery.$or.push({ email: data.email })
      }

      const conflictUser = await User.findOne(conflictQuery)
      if (conflictUser) {
        return {
          success: false,
          error: conflictUser.username === data.username
            ? 'Username already exists'
            : 'Email already registered'
        }
      }
    }

    // Get branch name if branchId is being updated
    let branchName = existingUser.branchName
    if (data.branchId && data.branchId !== existingUser.branchId) {
      const branch = await Shop.findById(data.branchId)
      if (!branch) {
        return {
          success: false,
          error: 'Branch not found'
        }
      }
      branchName = branch.name
    }

    // Update user
    const user = await User.findByIdAndUpdate(
      userId,
      {
        ...data,
        ...(data.branchId && { branchName }),
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    ).select('-password')

    // Log activity
    await logActivity({
      type: 'User',
      description: `User "${user!.username}" updated`,
      userId: adminUserId,
      userName: adminUserName,
      branchId: user!.branchId,
      branchName: user!.branchName,
      metadata: {
        updatedUserId: user!._id.toString(),
        updatedUsername: user!.username,
        updatedFields: Object.keys(data)
      }
    })

    return {
      success: true,
      data: {
        _id: user!._id.toString(),
        username: user!.username,
        email: user!.email,
        role: user!.role,
        branchId: user!.branchId,
        branchName: user!.branchName,
        firstName: user!.firstName,
        lastName: user!.lastName,
        phone: user!.phone,
        isActive: user!.isActive,
        lastLogin: user!.lastLogin,
        createdAt: user!.createdAt,
        updatedAt: user!.updatedAt
      } as IUser
    }
  } catch (error) {
    console.error('Update user service error:', error)
    return {
      success: false,
      error: 'Failed to update user'
    }
  }
}

/**
 * Delete user (soft delete)
 */
export async function deleteUser(
  userId: string,
  adminUserId: string,
  adminUserName: string
): Promise<ServiceResponse<boolean>> {
  try {
    await connectDB()

    const user = await User.findById(userId)
    if (!user) {
      return {
        success: false,
        error: 'User not found'
      }
    }

    // Prevent self-deletion
    if (userId === adminUserId) {
      return {
        success: false,
        error: 'Cannot delete your own account'
      }
    }

    // Soft delete by setting isActive to false
    await User.findByIdAndUpdate(userId, {
      isActive: false,
      updatedAt: new Date()
    })

    // Log activity
    await logActivity({
      type: 'User',
      description: `User "${user.username}" deactivated`,
      userId: adminUserId,
      userName: adminUserName,
      branchId: user.branchId,
      branchName: user.branchName,
      metadata: {
        deactivatedUserId: user._id.toString(),
        deactivatedUsername: user.username
      }
    })

    return {
      success: true,
      data: true
    }
  } catch (error) {
    console.error('Delete user service error:', error)
    return {
      success: false,
      error: 'Failed to delete user'
    }
  }
}

/**
 * Change user password
 */
export async function changeUserPassword(
  userId: string,
  newPassword: string,
  adminUserId: string,
  adminUserName: string
): Promise<ServiceResponse<boolean>> {
  try {
    await connectDB()

    const user = await User.findById(userId)
    if (!user) {
      return {
        success: false,
        error: 'User not found'
      }
    }

    // Update password (will be hashed by the pre-save middleware)
    user.password = newPassword
    await user.save()

    // Log activity
    await logActivity({
      type: 'User',
      description: `Password changed for user "${user.username}"`,
      userId: adminUserId,
      userName: adminUserName,
      branchId: user.branchId,
      branchName: user.branchName,
      metadata: {
        targetUserId: user._id.toString(),
        targetUsername: user.username
      }
    })

    return {
      success: true,
      data: true
    }
  } catch (error) {
    console.error('Change user password service error:', error)
    return {
      success: false,
      error: 'Failed to change password'
    }
  }
}


