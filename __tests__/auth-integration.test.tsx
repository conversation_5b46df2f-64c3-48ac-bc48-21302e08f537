/**
 * Integration test for authentication system
 * Tests the complete authentication flow with UI components
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import LoginForm from '@/components/auth/login-form'
import RegisterForm from '@/components/auth/register-form'
import { useAuthStore } from '@/stores/authStore'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}))

// Mock the auth store
jest.mock('@/stores/authStore')

// Mock API client
jest.mock('@/services/frontend', () => ({
  apiClient: {
    post: jest.fn(),
    setAuthToken: jest.fn(),
  },
  handleServiceError: jest.fn(),
}))

const mockPush = jest.fn()
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>

describe('Authentication Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockUseRouter.mockReturnValue({
      push: mockPush,
      replace: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      prefetch: jest.fn(),
    } as any)
  })

  describe('LoginForm Component', () => {
    it('should render login form with all required fields', () => {
      // Mock auth store state
      const mockAuthStore = {
        login: jest.fn(),
        isLoading: false,
        error: null,
        clearError: jest.fn(),
        isAuthenticated: false,
      }
      ;(useAuthStore as jest.Mock).mockReturnValue(mockAuthStore)

      render(<LoginForm />)

      expect(screen.getByLabelText(/username/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument()
      expect(screen.getByText(/don't have an account/i)).toBeInTheDocument()
    })

    it('should handle form submission correctly', async () => {
      const mockLogin = jest.fn().mockResolvedValue({ success: true })
      const mockAuthStore = {
        login: mockLogin,
        isLoading: false,
        error: null,
        clearError: jest.fn(),
        isAuthenticated: false,
      }
      ;(useAuthStore as jest.Mock).mockReturnValue(mockAuthStore)

      render(<LoginForm />)

      const usernameInput = screen.getByLabelText(/username/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /login/i })

      fireEvent.change(usernameInput, { target: { value: 'testuser' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith({
          username: 'testuser',
          password: 'password123',
          rememberMe: false,
        })
      })
    })

    it('should redirect to dashboard on successful login', async () => {
      const mockLogin = jest.fn().mockResolvedValue({ success: true })
      const mockAuthStore = {
        login: mockLogin,
        isLoading: false,
        error: null,
        clearError: jest.fn(),
        isAuthenticated: false,
      }
      ;(useAuthStore as jest.Mock).mockReturnValue(mockAuthStore)

      render(<LoginForm />)

      const usernameInput = screen.getByLabelText(/username/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /login/i })

      fireEvent.change(usernameInput, { target: { value: 'testuser' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockPush).toHaveBeenCalledWith('/dashboard')
      })
    })
  })

  describe('RegisterForm Component', () => {
    it('should render registration form with all required fields', () => {
      const mockAuthStore = {
        register: jest.fn(),
        isLoading: false,
        error: null,
        clearError: jest.fn(),
        isAuthenticated: false,
      }
      ;(useAuthStore as jest.Mock).mockReturnValue(mockAuthStore)

      render(<RegisterForm />)

      expect(screen.getByLabelText(/first name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/last name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/username/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
      expect(screen.getByText(/role/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument()
    })

    it('should show branch selection for branch managers', async () => {
      const mockAuthStore = {
        register: jest.fn(),
        isLoading: false,
        error: null,
        clearError: jest.fn(),
        isAuthenticated: false,
      }
      ;(useAuthStore as jest.Mock).mockReturnValue(mockAuthStore)

      render(<RegisterForm />)

      // Role should default to branch_manager, so branch field should be visible
      expect(screen.getByText('Branch')).toBeInTheDocument()
    })

    it('should validate password confirmation', async () => {
      const mockAuthStore = {
        register: jest.fn(),
        isLoading: false,
        error: null,
        clearError: jest.fn(),
        isAuthenticated: false,
      }
      ;(useAuthStore as jest.Mock).mockReturnValue(mockAuthStore)

      render(<RegisterForm />)

      const passwordInput = screen.getByLabelText(/^password$/i)
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i)

      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.change(confirmPasswordInput, { target: { value: 'different' } })

      await waitFor(() => {
        expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument()
      })
    })
  })

  describe('Authentication Flow', () => {
    it('should redirect authenticated users away from login page', () => {
      const mockAuthStore = {
        login: jest.fn(),
        isLoading: false,
        error: null,
        clearError: jest.fn(),
        isAuthenticated: true, // User is already authenticated
      }
      ;(useAuthStore as jest.Mock).mockReturnValue(mockAuthStore)

      render(<LoginForm />)

      expect(mockPush).toHaveBeenCalledWith('/dashboard')
    })

    it('should redirect authenticated users away from register page', () => {
      const mockAuthStore = {
        register: jest.fn(),
        isLoading: false,
        error: null,
        clearError: jest.fn(),
        isAuthenticated: true, // User is already authenticated
      }
      ;(useAuthStore as jest.Mock).mockReturnValue(mockAuthStore)

      render(<RegisterForm />)

      expect(mockPush).toHaveBeenCalledWith('/dashboard')
    })
  })
})
