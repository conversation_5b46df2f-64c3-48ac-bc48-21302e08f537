// __tests__/websocket-fix.test.ts - Test WebSocket fix for Vercel environment

import { isVercelEnvironment, isWebSocketSupported, getConnectionUrl, getConnectionStrategy } from '@/lib/utils/environment'

// Mock environment variables
const originalEnv = process.env

beforeEach(() => {
  jest.resetModules()
  process.env = { ...originalEnv }
})

afterAll(() => {
  process.env = originalEnv
})

describe('WebSocket Fix for Vercel Environment', () => {
  describe('isVercelEnvironment', () => {
    it('should detect Vercel environment from VERCEL env var', () => {
      process.env.VERCEL = '1'
      expect(isVercelEnvironment()).toBe(true)
    })

    it('should detect Vercel environment from socket URL', () => {
      process.env.NEXT_PUBLIC_SOCKET_URL = 'https://myapp.vercel.app'
      expect(isVercelEnvironment()).toBe(true)
    })

    it('should return false for non-Vercel environment', () => {
      process.env.VERCEL = undefined
      process.env.NEXT_PUBLIC_SOCKET_URL = 'http://localhost:3001'
      expect(isVercelEnvironment()).toBe(false)
    })
  })

  describe('isWebSocketSupported', () => {
    it('should return false for Vercel environment', () => {
      process.env.VERCEL = '1'
      expect(isWebSocketSupported()).toBe(false)
    })

    it('should return true for non-Vercel environment', () => {
      process.env.VERCEL = undefined
      expect(isWebSocketSupported()).toBe(true)
    })
  })

  describe('getConnectionUrl', () => {
    it('should return localhost for development', () => {
      process.env.NODE_ENV = 'development'
      process.env.NEXT_PUBLIC_SOCKET_URL = 'http://localhost:3001'
      expect(getConnectionUrl()).toBe('http://localhost:3001')
    })

    it('should return environment URL for production', () => {
      process.env.NODE_ENV = 'production'
      process.env.NEXT_PUBLIC_SOCKET_URL = 'https://myapp.vercel.app'
      expect(getConnectionUrl()).toBe('https://myapp.vercel.app')
    })
  })

  describe('getConnectionStrategy', () => {
    it('should return http for Vercel environment', () => {
      process.env.VERCEL = '1'
      expect(getConnectionStrategy()).toBe('http')
    })

    it('should return websocket for development', () => {
      process.env.NODE_ENV = 'development'
      process.env.VERCEL = undefined
      expect(getConnectionStrategy()).toBe('websocket')
    })

    it('should return hybrid for production non-Vercel', () => {
      process.env.NODE_ENV = 'production'
      process.env.VERCEL = undefined
      expect(getConnectionStrategy()).toBe('hybrid')
    })
  })
})

describe('WebSocket Error Prevention', () => {
  it('should not attempt WebSocket connection in Vercel environment', () => {
    // Mock Vercel environment
    process.env.VERCEL = '1'
    
    // This should not throw an error
    expect(() => {
      const supported = isWebSocketSupported()
      expect(supported).toBe(false)
    }).not.toThrow()
  })

  it('should gracefully handle connection failures', () => {
    // Mock connection failure scenario
    process.env.NODE_ENV = 'production'
    process.env.VERCEL = undefined
    
    // This should not throw an error
    expect(() => {
      const strategy = getConnectionStrategy()
      expect(strategy).toBe('hybrid')
    }).not.toThrow()
  })
})
