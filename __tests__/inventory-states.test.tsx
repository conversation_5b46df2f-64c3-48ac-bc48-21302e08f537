// __tests__/inventory-states.test.tsx - Test inventory states handling

import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { useInventoryStore } from '@/stores/inventoryStore'
import InventoryDashboard from '@/components/dashboard/inventory-dashboard'
import type { User } from '@/lib/auth-types'

// Mock the inventory store
jest.mock('@/stores/inventoryStore')
const mockUseInventoryStore = useInventoryStore as jest.MockedFunction<typeof useInventoryStore>

// Mock the services
jest.mock('@/services/frontend', () => ({
  inventoryService: {
    getStockStatus: jest.fn(() => ({ status: 'in_stock', label: 'In Stock' })),
    formatCost: jest.fn((value) => `$${value.toFixed(2)}`)
  }
}))

// Mock user
const mockUser: User = {
  _id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  role: 'overall_admin',
  firstName: 'Test',
  lastName: 'User',
  isActive: true,
  createdAt: '2024-01-01',
  updatedAt: '2024-01-01'
}

describe('Inventory States', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Loading State', () => {
    it('should display loading spinner when isLoading is true', () => {
      mockUseInventoryStore.mockReturnValue({
        inventoryItems: [],
        isLoading: true,
        error: null,
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        pageSize: 20,
        searchQuery: '',
        filters: {},
        fetchInventoryItems: jest.fn(),
        fetchInventoryByBranch: jest.fn(),
        searchInventory: jest.fn(),
        setCurrentPage: jest.fn(),
        setSearchQuery: jest.fn(),
        clearError: jest.fn(),
        getLowStockItems: jest.fn(() => []),
        getOutOfStockItems: jest.fn(() => [])
      } as any)

      render(<InventoryDashboard currentUser={mockUser} />)

      expect(screen.getByText('Loading inventory...')).toBeInTheDocument()
      expect(screen.getByRole('status')).toBeInTheDocument() // Loading spinner
    })
  })

  describe('Error State', () => {
    it('should display error message when error exists', () => {
      mockUseInventoryStore.mockReturnValue({
        inventoryItems: [],
        isLoading: false,
        error: 'Failed to fetch inventory items',
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        pageSize: 20,
        searchQuery: '',
        filters: {},
        fetchInventoryItems: jest.fn(),
        fetchInventoryByBranch: jest.fn(),
        searchInventory: jest.fn(),
        setCurrentPage: jest.fn(),
        setSearchQuery: jest.fn(),
        clearError: jest.fn(),
        getLowStockItems: jest.fn(() => []),
        getOutOfStockItems: jest.fn(() => [])
      } as any)

      render(<InventoryDashboard currentUser={mockUser} />)

      expect(screen.getByText('Failed to fetch inventory items')).toBeInTheDocument()
      expect(screen.getByText('Try Again')).toBeInTheDocument()
    })
  })

  describe('Empty State', () => {
    it('should display empty state when no inventory items and not loading', () => {
      mockUseInventoryStore.mockReturnValue({
        inventoryItems: [],
        isLoading: false,
        error: null,
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        pageSize: 20,
        searchQuery: '',
        filters: {},
        fetchInventoryItems: jest.fn(),
        fetchInventoryByBranch: jest.fn(),
        searchInventory: jest.fn(),
        setCurrentPage: jest.fn(),
        setSearchQuery: jest.fn(),
        clearError: jest.fn(),
        getLowStockItems: jest.fn(() => []),
        getOutOfStockItems: jest.fn(() => [])
      } as any)

      render(<InventoryDashboard currentUser={mockUser} />)

      expect(screen.getByText('No inventory items')).toBeInTheDocument()
      expect(screen.getByText('Add Your First Item')).toBeInTheDocument()
    })

    it('should display search empty state when searching with no results', () => {
      mockUseInventoryStore.mockReturnValue({
        inventoryItems: [],
        isLoading: false,
        error: null,
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        pageSize: 20,
        searchQuery: 'test search',
        filters: {},
        fetchInventoryItems: jest.fn(),
        fetchInventoryByBranch: jest.fn(),
        searchInventory: jest.fn(),
        setCurrentPage: jest.fn(),
        setSearchQuery: jest.fn(),
        clearError: jest.fn(),
        getLowStockItems: jest.fn(() => []),
        getOutOfStockItems: jest.fn(() => [])
      } as any)

      render(<InventoryDashboard currentUser={mockUser} />)

      expect(screen.getByText('No search results')).toBeInTheDocument()
      expect(screen.getByText('No inventory items found for "test search"')).toBeInTheDocument()
      expect(screen.getByText('Clear Search')).toBeInTheDocument()
    })
  })

  describe('Populated State', () => {
    it('should display inventory items when data is available', () => {
      const mockInventoryItems = [
        {
          _id: '1',
          productName: 'Test Product',
          sku: 'TEST-001',
          branchName: 'Main Branch',
          stock: 10,
          minStockLevel: 5,
          maxStockLevel: 50,
          cost: 100,
          location: 'A1',
          supplier: 'Test Supplier',
          branchId: 'branch1',
          productId: 'product1',
          batchNumber: 'BATCH001',
          expiryDate: '2024-12-31',
          lastRestocked: '2024-01-01',
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ]

      mockUseInventoryStore.mockReturnValue({
        inventoryItems: mockInventoryItems,
        isLoading: false,
        error: null,
        currentPage: 1,
        totalPages: 1,
        totalItems: 1,
        pageSize: 20,
        searchQuery: '',
        filters: {},
        fetchInventoryItems: jest.fn(),
        fetchInventoryByBranch: jest.fn(),
        searchInventory: jest.fn(),
        setCurrentPage: jest.fn(),
        setSearchQuery: jest.fn(),
        clearError: jest.fn(),
        getLowStockItems: jest.fn(() => []),
        getOutOfStockItems: jest.fn(() => [])
      } as any)

      render(<InventoryDashboard currentUser={mockUser} />)

      expect(screen.getByText('Test Product')).toBeInTheDocument()
      expect(screen.getByText('TEST-001')).toBeInTheDocument()
      expect(screen.getByText('Main Branch')).toBeInTheDocument()
      expect(screen.getByText('10')).toBeInTheDocument()
    })
  })

  describe('State Transitions', () => {
    it('should not show empty state when loading', () => {
      mockUseInventoryStore.mockReturnValue({
        inventoryItems: [],
        isLoading: true,
        error: null,
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        pageSize: 20,
        searchQuery: '',
        filters: {},
        fetchInventoryItems: jest.fn(),
        fetchInventoryByBranch: jest.fn(),
        searchInventory: jest.fn(),
        setCurrentPage: jest.fn(),
        setSearchQuery: jest.fn(),
        clearError: jest.fn(),
        getLowStockItems: jest.fn(() => []),
        getOutOfStockItems: jest.fn(() => [])
      } as any)

      render(<InventoryDashboard currentUser={mockUser} />)

      expect(screen.getByText('Loading inventory...')).toBeInTheDocument()
      expect(screen.queryByText('No inventory items')).not.toBeInTheDocument()
    })

    it('should prioritize error state over empty state', () => {
      mockUseInventoryStore.mockReturnValue({
        inventoryItems: [],
        isLoading: false,
        error: 'Network error',
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        pageSize: 20,
        searchQuery: '',
        filters: {},
        fetchInventoryItems: jest.fn(),
        fetchInventoryByBranch: jest.fn(),
        searchInventory: jest.fn(),
        setCurrentPage: jest.fn(),
        setSearchQuery: jest.fn(),
        clearError: jest.fn(),
        getLowStockItems: jest.fn(() => []),
        getOutOfStockItems: jest.fn(() => [])
      } as any)

      render(<InventoryDashboard currentUser={mockUser} />)

      expect(screen.getByText('Network error')).toBeInTheDocument()
      expect(screen.queryByText('No inventory items')).not.toBeInTheDocument()
    })
  })
})
