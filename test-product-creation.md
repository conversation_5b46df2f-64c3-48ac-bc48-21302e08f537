# Product Creation Test

## Test Data for Product Creation

Here's a sample product that should work with our new simplified form:

```json
{
  "name": "Test Laptop",
  "sku": "TEST-LAP-001",
  "category": "Laptops",
  "price": 999.99,
  "stock": 10,
  "branchId": "68921f391e974cb33a458aa9",
  "description": "This is a test laptop for verifying the product creation functionality."
}
```

## Expected API Call

When the form is submitted, it should make a POST request to `/api/products` with this data structure:

```json
{
  "name": "Test Laptop",
  "sku": "TEST-LAP-001",
  "categoryId": "category-id-from-lookup",
  "categoryName": "Laptops",
  "price": 999.99,
  "originalPrice": undefined,
  "stock": 10,
  "minStockLevel": 5,
  "description": "This is a test laptop for verifying the product creation functionality.",
  "specifications": [],
  "branchId": "68921f391e974cb33a458aa9",
  "brand": "Generic",
  "model": "Standard",
  "warranty": "1 year manufacturer warranty",
  "weight": undefined,
  "dimensions": undefined,
  "tags": [],
  "variants": [],
  "hasVariants": false,
  "featuredImage": "/images/placeholder-product.png",
  "images": [],
  "isActive": true,
  "isFeatured": false
}
```

## Steps to Test

1. Navigate to `/admin-products`
2. Click "Add Product" button
3. Fill in the form with the test data above
4. Optionally add a featured image (should show preview, no auto-upload)
5. Click "Create Product"
6. Should see success toast and product should appear in the list
7. Check server logs for the API call

## Expected Behavior

- ✅ Form should not auto-upload images
- ✅ Image preview should work
- ✅ Drag and drop should work
- ✅ Form should submit successfully
- ✅ Product should be created in database
- ✅ Success toast should appear
- ✅ Product list should refresh
- ✅ Modal should close

## Troubleshooting

If the form doesn't submit:
1. Check browser console for errors
2. Check server logs for API calls
3. Verify category exists and is active
4. Verify branch ID is correct
5. Check authentication token
