# Notification Authorization Fix

## Issue Description

The notification system was throwing an authorization error:

```
ApiError: Missing or invalid authorization header
    at ApiClient.handleResponse (webpack-internal:///(app-pages-browser)/./services/frontend/api-client.ts:160:19)
    at async ApiClient.makeRequest (webpack-internal:///(app-pages-browser)/./services/frontend/api-client.ts:206:20)
    at async fetchNotifications (webpack-internal:///(app-pages-browser)/./stores/notificationStore.ts:50:34)
```

## Root Cause

The notification system was trying to fetch notifications before the authentication token was properly set in the API client. This happened because:

1. **Timing Issue**: The notification system initialized immediately when `isAuthenticated` became true
2. **Missing Token Check**: The notification store didn't verify if an auth token was available before making API calls
3. **Race Condition**: The auth store rehydration and API client token setting happened asynchronously

## Solution Implemented

### 1. **Added Auth Token Validation** (`stores/notificationStore.ts`)

Added checks in all API-calling methods to ensure auth token is available:

```typescript
// Before making any API call
const authToken = apiClient.getAuthToken()
if (!authToken) {
  console.warn('No auth token available for notifications')
  return
}
```

**Applied to methods:**
- `fetchNotifications`
- `markAsRead`
- `markMultipleAsRead`
- `fetchPreferences`
- `updatePreferences`
- `resetPreferences`

### 2. **Enhanced Header Initialization** (`components/layout/header.tsx`)

Added a delay to ensure auth token is set before initializing notifications:

```typescript
// Before
useEffect(() => {
  if (isAuthenticated && user) {
    fetchNotifications()
    connectSocket()
  }
}, [isAuthenticated, user])

// After
useEffect(() => {
  if (isAuthenticated && user) {
    const initializeNotifications = async () => {
      try {
        await fetchNotifications()
        connectSocket()
      } catch (error) {
        console.error('Failed to initialize notifications:', error)
      }
    }
    
    // Use setTimeout to ensure auth token is set
    const timeoutId = setTimeout(initializeNotifications, 100)
    
    return () => {
      clearTimeout(timeoutId)
      disconnectSocket()
    }
  }
}, [isAuthenticated, user])
```

### 3. **Improved Error Handling**

Enhanced error handling to distinguish between auth errors and other issues:

```typescript
try {
  const response = await apiClient.get(`/api/notifications?${params.toString()}`)
  // ... handle response
} catch (error) {
  if (error.message.includes('authorization')) {
    console.warn('Authorization error in notifications - user may need to re-login')
    set({ error: 'Authentication required', isLoading: false })
  } else {
    console.error('Fetch notifications error:', error)
    set({ error: 'Failed to fetch notifications', isLoading: false })
  }
}
```

## How the Fix Works

### **Authentication Flow:**
1. User logs in → Auth store sets token in API client
2. Auth store rehydrates from localStorage → Token set in API client
3. Header component detects `isAuthenticated` → Waits 100ms
4. Notification store checks for auth token → Proceeds if available
5. API calls include proper Authorization header

### **Fallback Behavior:**
- If no auth token: Log warning and return early (no error thrown)
- If auth error during API call: Show "Authentication required" message
- If other error: Show generic "Failed to fetch" message

## Testing the Fix

### **Before Fix:**
```
❌ ApiError: Missing or invalid authorization header
❌ Notification system fails to initialize
❌ Console shows authorization errors
```

### **After Fix:**
```
✅ No authorization errors
✅ Notifications load properly when authenticated
✅ Graceful handling when not authenticated
✅ Console shows helpful warnings instead of errors
```

### **Test Scenarios:**

1. **Fresh Login:**
   - Login → Notifications should load after ~100ms delay
   - No authorization errors in console

2. **Page Refresh:**
   - Refresh page → Auth rehydrates → Notifications load
   - No authorization errors

3. **Not Authenticated:**
   - Visit page without login → No notification API calls
   - Console shows "No auth token available" warning

4. **Token Expiry:**
   - Token expires → API client handles refresh
   - Notifications continue working after refresh

## Prevention Measures

### **For Future Development:**

1. **Always Check Auth State:**
   ```typescript
   const authToken = apiClient.getAuthToken()
   if (!authToken) {
     console.warn('No auth token available')
     return
   }
   ```

2. **Use Proper Error Handling:**
   ```typescript
   try {
     const response = await apiClient.get('/api/endpoint')
   } catch (error) {
     if (error.message.includes('authorization')) {
       // Handle auth error
     } else {
       // Handle other errors
     }
   }
   ```

3. **Initialize Services After Auth:**
   ```typescript
   useEffect(() => {
     if (isAuthenticated && user) {
       // Add delay for auth token to be set
       setTimeout(() => {
         initializeService()
       }, 100)
     }
   }, [isAuthenticated, user])
   ```

## Additional Benefits

### **Improved User Experience:**
- No more confusing authorization error messages
- Graceful degradation when not authenticated
- Clear distinction between auth and other errors

### **Better Debugging:**
- Helpful console warnings instead of errors
- Clear indication of auth state issues
- Easier to identify timing problems

### **Robust Error Handling:**
- Prevents notification system from breaking the app
- Allows other features to work even if notifications fail
- Provides fallback behavior for edge cases

## Rollback Plan

If issues persist, the changes can be easily reverted:

1. Remove auth token checks from notification store methods
2. Revert header initialization to immediate execution
3. Remove enhanced error handling

The core notification functionality remains unchanged, only the initialization and error handling were improved.

## Related Files Modified

- `stores/notificationStore.ts` - Added auth token validation
- `components/layout/header.tsx` - Enhanced initialization timing
- `docs/notification-auth-fix.md` - This documentation

## Future Improvements

1. **Centralized Auth Check:** Create a higher-order function for auth-required API calls
2. **Retry Logic:** Automatically retry failed requests after token refresh
3. **Auth State Subscription:** Subscribe to auth state changes for real-time updates
4. **Service Worker:** Handle notifications even when app is not active
