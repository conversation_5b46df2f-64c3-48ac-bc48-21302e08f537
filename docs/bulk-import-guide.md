# Bulk Import System for Inventory Management

## Overview

The bulk import system allows shop management to quickly upload multiple inventory items using CSV or JSON files. This feature is designed to save time when setting up new stores or updating large inventories.

## Features

- **Multiple File Formats**: Support for CSV and JSON files
- **Template Downloads**: Pre-formatted templates with sample data
- **Data Validation**: Comprehensive validation with detailed error reporting
- **Progress Tracking**: Real-time upload progress with visual feedback
- **Error Handling**: Detailed error messages for failed imports
- **Role-Based Access**: Different permissions for admin and branch managers
- **Duplicate Detection**: Automatic SKU duplicate checking

## Supported File Formats

### CSV Format
- Standard comma-separated values
- First row must contain headers
- Quoted values supported for fields containing commas
- Maximum file size: 10MB

### JSON Format
- Array of objects or single object
- Structured data format
- Maximum file size: 10MB

## Required Fields

The following fields are **required** for each inventory item:

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `productName` | String | Name of the product | "Wireless Bluetooth Headphones" |
| `sku` | String | Stock Keeping Unit (unique) | "WBH-001" |
| `category` | String | Product category | "Electronics" |
| `stock` | Number | Current stock quantity | 50 |
| `cost` | Number | Cost price per unit | 25.99 |
| `price` | Number | Selling price per unit | 49.99 |

## Optional Fields

These fields have default values if not provided:

| Field | Type | Default | Description |
|-------|------|---------|-------------|
| `minStockLevel` | Number | 10 | Minimum stock level for alerts |
| `supplier` | String | "Unknown" | Supplier name |
| `location` | String | "Main Warehouse" | Storage location |
| `description` | String | "" | Product description |
| `barcode` | String | "" | Product barcode |
| `unit` | String | "pcs" | Unit of measurement |
| `status` | String | "active" | Product status (active/inactive) |

## Data Validation Rules

### SKU Validation
- Must be unique across all inventory items
- Can only contain letters, numbers, hyphens, and underscores
- Example: `PROD-001`, `item_123`, `SKU-ABC-001`

### Numeric Fields
- `stock`: Must be a non-negative integer
- `cost`: Must be a positive number
- `price`: Must be a positive number
- `minStockLevel`: Must be a non-negative integer

### Text Fields
- `productName`: Required, cannot be empty
- `category`: Required, cannot be empty
- `status`: Must be either "active" or "inactive"

## CSV Template Example

```csv
productName,sku,category,stock,cost,price,minStockLevel,supplier,location,description,barcode,unit,status
Wireless Bluetooth Headphones,WBH-001,Electronics,50,25.99,49.99,10,TechSupplier Inc,Warehouse A,High-quality wireless headphones with noise cancellation,1234567890123,pcs,active
Organic Coffee Beans,OCB-002,Food & Beverages,100,8.50,15.99,20,Coffee Roasters Ltd,Storage Room B,Premium organic coffee beans from Ethiopia,2345678901234,kg,active
Cotton T-Shirt,CTS-003,Clothing,75,5.00,19.99,15,Fashion Wholesale,Display Area,100% cotton comfortable t-shirt available in multiple sizes,3456789012345,pcs,active
```

## JSON Template Example

```json
[
  {
    "productName": "Wireless Bluetooth Headphones",
    "sku": "WBH-001",
    "category": "Electronics",
    "stock": 50,
    "cost": 25.99,
    "price": 49.99,
    "minStockLevel": 10,
    "supplier": "TechSupplier Inc",
    "location": "Warehouse A",
    "description": "High-quality wireless headphones with noise cancellation",
    "barcode": "1234567890123",
    "unit": "pcs",
    "status": "active"
  },
  {
    "productName": "Organic Coffee Beans",
    "sku": "OCB-002",
    "category": "Food & Beverages",
    "stock": 100,
    "cost": 8.50,
    "price": 15.99,
    "minStockLevel": 20,
    "supplier": "Coffee Roasters Ltd",
    "location": "Storage Room B",
    "description": "Premium organic coffee beans from Ethiopia",
    "barcode": "2345678901234",
    "unit": "kg",
    "status": "active"
  }
]
```

## How to Use

### Step 1: Access Bulk Import
1. Navigate to the Inventory Management page
2. Click the "Bulk Import" button in the top-right corner
3. The bulk import dialog will open

### Step 2: Download Template (Optional)
1. Click on the "Download Templates" tab
2. Choose either CSV or JSON format
3. Download the template file with sample data
4. Use the template as a guide for formatting your data

### Step 3: Prepare Your Data
1. Ensure all required fields are included
2. Validate that SKUs are unique
3. Check that numeric values are properly formatted
4. Save your file in CSV or JSON format

### Step 4: Upload File
1. Click "Choose File" or drag and drop your file
2. The system will validate the file format and size
3. Click "Import File" to start the upload process
4. Monitor the progress bar during upload

### Step 5: Review Results
1. After upload, review the import results
2. Check the number of successful imports
3. Review any error messages for failed items
4. Failed items will show specific error details

## Error Handling

### Common Errors

1. **Missing Required Fields**
   - Error: "Required field 'productName' is missing or empty"
   - Solution: Ensure all required fields are present and not empty

2. **Duplicate SKU**
   - Error: "SKU 'WBH-001' already exists"
   - Solution: Use unique SKUs or update existing items separately

3. **Invalid Data Types**
   - Error: "Stock must be a valid number"
   - Solution: Ensure numeric fields contain valid numbers

4. **Invalid SKU Format**
   - Error: "SKU can only contain letters, numbers, hyphens, and underscores"
   - Solution: Use only allowed characters in SKU field

### Error Report Format

Failed imports will show:
- Row number where the error occurred
- Field name that caused the error
- Detailed error message
- Original data for reference

## Permissions

### Overall Admin
- Can import inventory for any branch
- Can access all import features
- Can view all import results

### Branch Manager
- Can only import inventory for their assigned branch
- Items are automatically assigned to their branch
- Cannot import for other branches

## Best Practices

1. **Start Small**: Test with a few items before importing large datasets
2. **Validate Data**: Use templates and check data format before upload
3. **Backup First**: Export existing inventory before large imports
4. **Check SKUs**: Ensure SKUs are unique and follow your naming convention
5. **Review Results**: Always check import results and fix any errors
6. **Use Templates**: Download and use provided templates for consistency

## Troubleshooting

### File Upload Issues
- **File too large**: Maximum size is 10MB
- **Invalid format**: Only CSV and JSON files are supported
- **Empty file**: File must contain data rows, not just headers

### Import Failures
- **All items failed**: Check file format and required fields
- **Some items failed**: Review error messages for specific issues
- **Slow import**: Large files may take time to process

### Performance Tips
- Split large files into smaller batches (recommended: 500-1000 items per file)
- Remove unnecessary columns to reduce file size
- Use CSV format for better performance with large datasets

## API Endpoints

For developers integrating with the bulk import system:

- `POST /api/inventory/bulk-import` - Upload and process import file
- `GET /api/inventory/bulk-import/template?format=csv` - Download CSV template
- `GET /api/inventory/bulk-import/template?format=json` - Download JSON template

## Support

If you encounter issues with the bulk import system:
1. Check this documentation for common solutions
2. Verify your data format matches the templates
3. Contact system administrator for technical support
4. Provide error messages and sample data for faster resolution
