# Shopping Cart System Implementation

## Overview

The FathahiTech application now features a comprehensive shopping cart system that provides users with a seamless shopping experience. The cart system is built with Zustand for state management, includes persistent storage, and integrates with the notification system.

## Key Features

### ✅ **Real-time Cart Management**
- Add, remove, and update items in real-time
- Persistent cart storage using localStorage
- Real-time cart count and total updates
- Stock validation and quantity limits

### ✅ **Cart Sidebar**
- Slide-out cart sidebar accessible from header
- Real-time item management with quantity controls
- Price calculations with tax and shipping
- Quick checkout and continue shopping options

### ✅ **Smart Pricing**
- Automatic tax calculation (18% VAT)
- Free shipping for orders over 100,000 MWK
- Real-time total updates
- Currency formatting for Malawi Kwacha (MWK)

### ✅ **Product Integration**
- Easy "Add to Cart" buttons on product cards
- Variant support (color, size, storage)
- Stock management and availability checking
- Branch-specific product tracking

### ✅ **Checkout Integration**
- Seamless checkout process
- Order summary with cart items
- Shipping and billing address management
- Multiple payment method support

## Components

### 1. Cart Store (`stores/cartStore.ts`)

**State Management with Zustand:**
- **Cart Items**: Array of products with quantities and variants
- **UI State**: Cart open/closed, loading states, error handling
- **Checkout Data**: Shipping address, payment method
- **Computed Properties**: Summary calculations, item counts

**Key Actions:**
```typescript
// Adding items
addItem(item: Omit<CartItem, 'quantity'> & { quantity?: number })

// Managing quantities
updateQuantity(itemId: string, quantity: number)
removeItem(itemId: string)
clearCart()

// UI controls
openCart()
closeCart()
toggleCart()

// Checkout data
setShippingAddress(address: ShippingAddress)
setPaymentMethod(method: 'cash' | 'card' | 'mobile_money')
```

### 2. Cart Sidebar (`components/cart/cart-sidebar.tsx`)

**Features:**
- Slide-out sheet component using shadcn/ui
- Real-time cart item display
- Quantity controls with stock validation
- Price summary with tax and shipping
- Quick actions (clear cart, continue shopping, checkout)

**Usage:**
```typescript
import { CartSidebar, CartButton } from '@/components/cart/cart-sidebar'

// In header or anywhere
<CartButton />

// Custom trigger
<CartSidebar>
  <Button>Open Cart</Button>
</CartSidebar>
```

### 3. Cart Items (`components/cart/cart-item.tsx`)

**Multiple Variants:**
- **Default**: Full cart item with controls
- **Compact**: Minimal display for lists
- **Checkout**: Read-only for checkout pages

**Add to Cart Button:**
```typescript
import { AddToCartButton } from '@/components/cart/cart-item'

<AddToCartButton
  product={{
    id: "product-123",
    name: "iPhone 15 Pro",
    price: 1200000,
    image: "/images/iphone.jpg",
    stock: 50
  }}
  variant={{ color: "Blue", storage: "256GB" }}
  quantity={1}
>
  Add to Cart
</AddToCartButton>
```

### 4. Header Integration (`components/layout/header.tsx`)

**Enhanced Cart Icon:**
- Real-time item count badge
- Dynamic cart button with current total
- Integrated with cart sidebar
- Responsive design for mobile and desktop

## Usage Examples

### Basic Cart Operations

```typescript
import { useCartStore } from '@/stores/cartStore'

function ProductPage() {
  const { addItem, items, summary } = useCartStore()

  const handleAddToCart = () => {
    addItem({
      productId: 'iphone-15-pro',
      name: 'iPhone 15 Pro',
      price: 1200000,
      image: '/images/iphone.jpg',
      category: 'Electronics',
      branchId: 'branch-001',
      branchName: 'Fathahitech Blantyre',
      maxStock: 50,
      variant: { color: 'Blue', storage: '256GB' }
    })
  }

  return (
    <div>
      <h1>iPhone 15 Pro</h1>
      <p>Price: {summary.total} MWK</p>
      <button onClick={handleAddToCart}>Add to Cart</button>
      <p>Items in cart: {items.length}</p>
    </div>
  )
}
```

### Cart State Management

```typescript
import { useCartStore } from '@/stores/cartStore'

function CartManagement() {
  const {
    items,
    summary,
    isEmpty,
    itemCount,
    updateQuantity,
    removeItem,
    clearCart
  } = useCartStore()

  return (
    <div>
      {isEmpty ? (
        <p>Your cart is empty</p>
      ) : (
        <div>
          <h2>Cart ({itemCount} items)</h2>
          {items.map(item => (
            <div key={item.id}>
              <h3>{item.name}</h3>
              <p>Quantity: {item.quantity}</p>
              <button onClick={() => updateQuantity(item.id, item.quantity + 1)}>
                +
              </button>
              <button onClick={() => updateQuantity(item.id, item.quantity - 1)}>
                -
              </button>
              <button onClick={() => removeItem(item.id)}>
                Remove
              </button>
            </div>
          ))}
          <p>Total: {summary.total} MWK</p>
          <button onClick={clearCart}>Clear Cart</button>
        </div>
      )}
    </div>
  )
}
```

### Checkout Integration

```typescript
import { useCartStore } from '@/stores/cartStore'

function CheckoutPage() {
  const {
    items,
    summary,
    shippingAddress,
    paymentMethod,
    setShippingAddress,
    setPaymentMethod,
    clearCart
  } = useCartStore()

  const handleCheckout = async () => {
    // Process order
    const order = {
      items,
      total: summary.total,
      shippingAddress,
      paymentMethod
    }

    // Submit to backend
    await submitOrder(order)

    // Clear cart after successful order
    clearCart()
  }

  return (
    <div>
      <h1>Checkout</h1>
      {/* Order summary */}
      {/* Shipping form */}
      {/* Payment selection */}
      <button onClick={handleCheckout}>Place Order</button>
    </div>
  )
}
```

## Cart Data Structure

### CartItem Interface
```typescript
interface CartItem {
  id: string              // Unique cart item ID
  productId: string       // Product identifier
  name: string           // Product name
  price: number          // Unit price in MWK
  quantity: number       // Quantity in cart
  image?: string         // Product image URL
  category?: string      // Product category
  branchId?: string      // Source branch ID
  branchName?: string    // Source branch name
  maxStock?: number      // Available stock limit
  sku?: string          // Product SKU
  variant?: {           // Product variants
    color?: string
    size?: string
    storage?: string
  }
}
```

### Cart Summary
```typescript
interface CartSummary {
  subtotal: number      // Items total before tax/shipping
  tax: number          // 18% VAT
  shipping: number     // Shipping cost (free over 100,000 MWK)
  discount: number     // Applied discounts
  total: number        // Final total
  itemCount: number    // Total items in cart
}
```

## Pricing Logic

### Tax Calculation
- **VAT Rate**: 18% (standard rate in Malawi)
- **Applied to**: Subtotal of all items

### Shipping Calculation
- **Free Shipping**: Orders over 100,000 MWK
- **Standard Shipping**: 5,000 MWK for orders under threshold

### Currency Formatting
```typescript
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: 'MWK',
    minimumFractionDigits: 0
  }).format(price)
}
```

## Persistence

### Local Storage
- Cart items are automatically saved to localStorage
- Data persists across browser sessions
- Automatic hydration on app load

### Excluded from Persistence
- UI state (isOpen, isLoading)
- Error messages
- Temporary states

## Integration with Notifications

### Cart Actions Trigger Notifications
```typescript
import { useToast } from '@/components/providers/notification-provider'

function CartWithNotifications() {
  const { showSuccess, showError } = useToast()
  const { addItem } = useCartStore()

  const handleAddToCart = (product) => {
    try {
      addItem(product)
      showSuccess('Added to Cart', `${product.name} has been added to your cart`)
    } catch (error) {
      showError('Failed to Add', 'Could not add item to cart')
    }
  }
}
```

## Mobile Responsiveness

### Header Cart Button
- Responsive design for all screen sizes
- Touch-friendly cart sidebar
- Optimized for mobile shopping experience

### Cart Sidebar
- Full-width on mobile devices
- Scrollable item list
- Touch-friendly quantity controls

## Performance Optimizations

### State Management
- Zustand for minimal re-renders
- Computed properties for derived state
- Efficient item updates

### Storage
- JSON serialization for localStorage
- Partial state persistence
- Automatic cleanup of invalid data

## Future Enhancements

### Planned Features
- **Wishlist Integration**: Save items for later
- **Cart Sharing**: Share cart with others
- **Bulk Operations**: Add multiple items at once
- **Cart Analytics**: Track cart abandonment
- **Promotional Codes**: Discount code support
- **Guest Checkout**: Checkout without account

### Backend Integration
- **Real-time Stock**: Live inventory updates
- **Price Updates**: Dynamic pricing
- **Cart Sync**: Multi-device cart synchronization
- **Order History**: Previous cart recovery

## Testing

### Cart Store Tests
```typescript
import { useCartStore } from '@/stores/cartStore'

describe('Cart Store', () => {
  test('adds item to cart', () => {
    const { addItem, items } = useCartStore.getState()
    
    addItem({
      productId: 'test-product',
      name: 'Test Product',
      price: 100
    })
    
    expect(items).toHaveLength(1)
    expect(items[0].name).toBe('Test Product')
  })
})
```

### Component Tests
- Cart sidebar functionality
- Add to cart button behavior
- Quantity controls
- Price calculations

## Troubleshooting

### Common Issues
1. **Cart not persisting**: Check localStorage permissions
2. **Price calculations wrong**: Verify tax/shipping logic
3. **Items not updating**: Check Zustand store updates
4. **Mobile cart issues**: Test responsive design

### Debug Tools
- Browser localStorage inspector
- Zustand devtools
- React Developer Tools
- Network tab for API calls
