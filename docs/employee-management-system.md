# Employee Management System Documentation

## Overview
The Employee Management System provides comprehensive role-based employee administration for the Fathahitech application. It implements proper access control where Overall System Admins can manage branch managers, while both System Admins and Branch Managers can manage regular employees.

## Access Control Matrix

| User Role | Can Add Branch Managers | Can Add Employees | Can View Employees |
|-----------|------------------------|-------------------|-------------------|
| Overall Admin | ✅ Yes | ✅ Yes | ✅ All employees |
| Branch Manager | ❌ No | ✅ Yes | ✅ Own branch only |
| Employee | ❌ No | ❌ No | ❌ No |

## System Components

### 1. Employee Types & Roles

#### Branch Manager
- **Role**: `branch_manager`
- **Department**: Management
- **Added By**: Overall Admin only
- **Permissions**: Full branch management capabilities
- **Features**: Enhanced permissions, qualifications tracking, management experience

#### Regular Employees
- **Roles**: `employee`, `cashier`, `sales_rep`, `inventory_manager`
- **Added By**: Overall Admin or Branch Manager
- **Permissions**: Role-specific permissions
- **Features**: Standard employee management

### 2. Modal Components

#### AddBranchManagerModal
- **Location**: `components/modals/add-branch-manager-modal.tsx`
- **Access**: Overall Admin only
- **Features**:
  - Personal information (name, email, phone)
  - Branch assignment
  - Management experience tracking
  - Qualifications & certifications
  - Enhanced permissions set
  - Emergency contact information
  - Salary management

#### AddEmployeeModal
- **Location**: `components/modals/add-employee-modal.tsx`
- **Access**: Overall Admin and Branch Manager
- **Features**:
  - Personal information
  - Role selection (employee, cashier, sales_rep, inventory_manager)
  - Department assignment
  - Branch assignment (filtered by user role)
  - Manager assignment
  - Role-specific permissions
  - Emergency contact information
  - Salary management

### 3. Employee Management Dashboard

#### Location
- **Page**: `app/(dashboard)/employees/page.tsx`
- **Component**: `components/dashboard/employee-management-dashboard.tsx`
- **URL**: `/employees`

#### Features
- **Employee Directory**: Comprehensive employee listing with search and filters
- **Statistics Cards**: Total employees, managers, active staff, departments
- **Role-Based Filtering**: Filter by managers, employees, status
- **Search Functionality**: Search by name, email, or role
- **Pagination**: Handle large employee lists efficiently
- **Action Menu**: Edit and remove employee options

#### Access Control
- **Overall Admin**: Can see all employees across all branches
- **Branch Manager**: Can only see employees in their assigned branch
- **Automatic Filtering**: System automatically filters data based on user role

## Data Structure

### Employee Interface
```typescript
interface Employee {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  role: "branch_manager" | "employee" | "cashier" | "sales_rep" | "inventory_manager"
  department: string
  branchId: string
  branchName: string
  managerId?: string
  managerName?: string
  hireDate: string
  salary: number
  status: "Active" | "Inactive" | "On Leave" | "Terminated"
  address: string
  emergencyContact: {
    name: string
    phone: string
    relationship: string
  }
  permissions: string[]
  avatar?: string
}
```

### Form Data Types
```typescript
interface CreateEmployeeData {
  firstName: string
  lastName: string
  email: string
  phone: string
  role: "employee" | "cashier" | "sales_rep" | "inventory_manager"
  department: string
  branchId: string
  managerId?: string
  hireDate: string
  salary: number
  address: string
  emergencyContact: {
    name: string
    phone: string
    relationship: string
  }
  permissions: string[]
}

interface CreateBranchManagerData {
  firstName: string
  lastName: string
  email: string
  phone: string
  branchId: string
  hireDate: string
  salary: number
  address: string
  emergencyContact: {
    name: string
    phone: string
    relationship: string
  }
  permissions: string[]
  managerialExperience: number
  qualifications: string[]
}
```

## Permission System

### Employee Permissions
- `view_products` - View product catalog
- `edit_products` - Modify product information
- `delete_products` - Remove products
- `view_orders` - View customer orders
- `edit_orders` - Modify order details
- `process_orders` - Process and fulfill orders
- `view_customers` - View customer information
- `edit_customers` - Modify customer data
- `view_reports` - Access basic reports
- `manage_inventory` - Inventory management
- `process_payments` - Handle payment processing
- `handle_returns` - Process returns and refunds

### Manager Permissions (Additional)
- `manage_employees` - Add, edit, remove employees
- `view_all_reports` - Access comprehensive reports
- `edit_branch_settings` - Modify branch configuration
- `approve_orders` - Approve large orders
- `handle_customer_complaints` - Manage customer issues
- `process_refunds` - Authorize refunds
- `view_financial_reports` - Access financial data
- `manage_schedules` - Employee scheduling
- `approve_discounts` - Authorize discounts
- `manage_suppliers` - Supplier management
- `conduct_performance_reviews` - Employee evaluations

## Usage Examples

### Adding a Branch Manager (Overall Admin Only)
```typescript
const handleBranchManagerSubmit = async (data: CreateBranchManagerData) => {
  // Only accessible to overall_admin
  const newManager: Employee = {
    id: `mgr-${Date.now()}`,
    role: "branch_manager",
    department: "Management",
    permissions: managerPermissions, // Full manager permissions
    // ... other fields
  }
  // Add to system
}
```

### Adding an Employee (Admin or Branch Manager)
```typescript
const handleEmployeeSubmit = async (data: CreateEmployeeData) => {
  // Accessible to overall_admin and branch_manager
  const newEmployee: Employee = {
    id: `emp-${Date.now()}`,
    role: data.role, // employee, cashier, sales_rep, inventory_manager
    permissions: data.permissions, // Role-specific permissions
    branchId: data.branchId, // Filtered by user access
    // ... other fields
  }
  // Add to system
}
```

### Role-Based Branch Filtering
```typescript
// In AddEmployeeModal
const availableBranches = currentUserRole === "overall_admin" 
  ? allBranches 
  : branches.filter(branch => branch.id === currentUserBranchId)
```

## Integration Points

### Dashboard Navigation
- Employee management accessible via `/employees`
- Integrated into main dashboard navigation
- Role-based access control at route level

### Modal Integration
- AddEmployeeModal integrated into employee dashboard
- AddBranchManagerModal available to overall admin only
- Consistent with other modal patterns in the application

### Data Flow
1. User clicks "Add Employee" or "Add Manager" button
2. Appropriate modal opens based on user role
3. Form validation with Zod schemas
4. Data submission with loading states
5. Real-time UI updates
6. Success feedback to user

## Security Features

### Route Protection
```typescript
// In app/(dashboard)/employees/page.tsx
if (!["overall_admin", "branch_manager"].includes(user.role)) {
  redirect("/dashboard")
}
```

### Data Filtering
- Branch managers can only see their branch employees
- Branch selection filtered by user access level
- Manager assignment limited to available managers

### Permission Validation
- Role-specific permission sets
- Manager permissions include all employee permissions
- Granular access control for different operations

## Testing

### Demo Integration
- Employee and Branch Manager modals added to `/modal-demo`
- Test with different user roles
- Verify access control and data filtering

### Live Testing
- Access `/employees` as different user roles
- Test employee creation with various roles
- Verify branch filtering and permission assignment

## Future Enhancements

### Planned Features
1. Employee performance tracking
2. Attendance management
3. Payroll integration
4. Training and certification tracking
5. Employee self-service portal
6. Advanced reporting and analytics
7. Bulk employee operations
8. Employee document management

### API Integration
- Replace mock data with actual API calls
- Implement proper authentication
- Add real-time notifications
- Include audit logging for employee changes

## Maintenance

### Adding New Employee Roles
1. Update role enum in types
2. Add role to permission mapping
3. Update form validation
4. Test with new role permissions

### Modifying Permissions
1. Update permission arrays
2. Modify permission mapping logic
3. Update UI permission displays
4. Test access control changes

This employee management system provides a robust, secure, and scalable foundation for managing organizational hierarchy and access control in the Fathahitech application.
