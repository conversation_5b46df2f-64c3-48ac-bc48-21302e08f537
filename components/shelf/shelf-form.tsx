// components/shelf/shelf-form.tsx - Shelf creation/editing form

'use client'

import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Package, Ruler, Weight } from 'lucide-react'
import type { Shelf, CreateShelfData, UpdateShelfData, Warehouse } from '@/types/frontend'

// Form validation schema
const shelfFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name cannot exceed 100 characters'),
  warehouseId: z.string().min(1, 'Warehouse is required'),
  section: z.string().min(1, 'Section is required').max(10, 'Section cannot exceed 10 characters')
    .regex(/^[A-Z0-9]+$/, 'Section must contain only uppercase letters and numbers'),
  row: z.string().min(1, 'Row is required').max(10, 'Row cannot exceed 10 characters'),
  position: z.string().min(1, 'Position is required').max(10, 'Position cannot exceed 10 characters')
    .regex(/^[A-Z0-9]+$/, 'Position must contain only uppercase letters and numbers'),
  level: z.number().min(1, 'Level must be at least 1').max(10, 'Level cannot exceed 10'),
  description: z.string().max(300, 'Description cannot exceed 300 characters').optional(),
  capacity: z.number().min(1, 'Capacity must be greater than 0'),
  width: z.number().min(1, 'Width must be greater than 0'),
  height: z.number().min(1, 'Height must be greater than 0'),
  depth: z.number().min(1, 'Depth must be greater than 0'),
  weightLimit: z.number().min(1, 'Weight limit must be greater than 0'),
  shelfType: z.enum(['standard', 'refrigerated', 'hazmat', 'fragile', 'bulk']),
  accessLevel: z.enum(['ground', 'ladder', 'forklift', 'crane']),
  notes: z.string().max(500, 'Notes cannot exceed 500 characters').optional(),
  barcode: z.string().max(50, 'Barcode cannot exceed 50 characters').optional(),
  qrCode: z.string().max(100, 'QR code cannot exceed 100 characters').optional()
})

type ShelfFormData = z.infer<typeof shelfFormSchema>

interface ShelfFormProps {
  shelf?: Shelf
  warehouses: Warehouse[]
  onSubmit: (data: CreateShelfData | UpdateShelfData) => Promise<{ success: boolean; error?: string }>
  onCancel: () => void
  isLoading?: boolean
}

export function ShelfForm({ shelf, warehouses, onSubmit, onCancel, isLoading = false }: ShelfFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<ShelfFormData>({
    resolver: zodResolver(shelfFormSchema),
    defaultValues: shelf ? {
      name: shelf.name,
      warehouseId: shelf.warehouseId,
      section: shelf.section,
      row: shelf.row,
      position: shelf.position,
      level: shelf.level,
      description: shelf.description || '',
      capacity: shelf.capacity,
      width: shelf.dimensions.width,
      height: shelf.dimensions.height,
      depth: shelf.dimensions.depth,
      weightLimit: shelf.weightLimit,
      shelfType: shelf.shelfType,
      accessLevel: shelf.accessLevel,
      notes: shelf.notes || '',
      barcode: shelf.barcode || '',
      qrCode: shelf.qrCode || ''
    } : {
      shelfType: 'standard',
      accessLevel: 'ground',
      level: 1
    }
  })

  const onFormSubmit = async (data: ShelfFormData) => {
    try {
      setSubmitError(null)

      // Transform form data to API format
      const submitData: CreateShelfData | UpdateShelfData = {
        name: data.name,
        description: data.description,
        capacity: data.capacity,
        dimensions: {
          width: data.width,
          height: data.height,
          depth: data.depth
        },
        weightLimit: data.weightLimit,
        shelfType: data.shelfType,
        accessLevel: data.accessLevel,
        notes: data.notes,
        barcode: data.barcode,
        qrCode: data.qrCode
      }

      // Add fields only for creation
      if (!shelf) {
        (submitData as CreateShelfData).warehouseId = data.warehouseId
        ;(submitData as CreateShelfData).section = data.section
        ;(submitData as CreateShelfData).row = data.row
        ;(submitData as CreateShelfData).position = data.position
        ;(submitData as CreateShelfData).level = data.level
      }

      const result = await onSubmit(submitData)
      
      if (!result.success) {
        setSubmitError(result.error || 'Failed to save shelf')
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'An unexpected error occurred')
    }
  }

  const isFormLoading = isLoading || isSubmitting

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {submitError && (
        <Alert variant="destructive">
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Shelf Name *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Enter shelf name"
                disabled={isFormLoading}
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="warehouseId">Warehouse *</Label>
              <Select
                value={watch('warehouseId')}
                onValueChange={(value) => setValue('warehouseId', value)}
                disabled={isFormLoading || !!shelf}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select warehouse" />
                </SelectTrigger>
                <SelectContent>
                  {warehouses.map((warehouse) => (
                    <SelectItem key={warehouse._id} value={warehouse._id}>
                      {warehouse.name} ({warehouse.code})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.warehouseId && (
                <p className="text-sm text-red-600">{errors.warehouseId.message}</p>
              )}
              {shelf && (
                <p className="text-sm text-muted-foreground">Warehouse cannot be changed after creation</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Enter shelf description"
              disabled={isFormLoading}
              rows={2}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Position Information */}
      <Card>
        <CardHeader>
          <CardTitle>Position Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="section">Section *</Label>
              <Input
                id="section"
                {...register('section')}
                placeholder="e.g., A"
                disabled={isFormLoading || !!shelf}
                style={{ textTransform: 'uppercase' }}
              />
              {errors.section && (
                <p className="text-sm text-red-600">{errors.section.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="row">Row *</Label>
              <Input
                id="row"
                {...register('row')}
                placeholder="e.g., 1"
                disabled={isFormLoading || !!shelf}
              />
              {errors.row && (
                <p className="text-sm text-red-600">{errors.row.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="position">Position *</Label>
              <Input
                id="position"
                {...register('position')}
                placeholder="e.g., A"
                disabled={isFormLoading || !!shelf}
                style={{ textTransform: 'uppercase' }}
              />
              {errors.position && (
                <p className="text-sm text-red-600">{errors.position.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="level">Level *</Label>
              <Input
                id="level"
                type="number"
                {...register('level', { valueAsNumber: true })}
                placeholder="1"
                disabled={isFormLoading || !!shelf}
                min="1"
                max="10"
              />
              {errors.level && (
                <p className="text-sm text-red-600">{errors.level.message}</p>
              )}
            </div>
          </div>

          {shelf && (
            <p className="text-sm text-muted-foreground">Position cannot be changed after creation</p>
          )}
        </CardContent>
      </Card>

      {/* Capacity & Dimensions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Ruler className="h-5 w-5" />
            Capacity & Dimensions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="capacity">Capacity (items) *</Label>
              <Input
                id="capacity"
                type="number"
                {...register('capacity', { valueAsNumber: true })}
                placeholder="Enter item capacity"
                disabled={isFormLoading}
                min="1"
              />
              {errors.capacity && (
                <p className="text-sm text-red-600">{errors.capacity.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="weightLimit" className="flex items-center gap-2">
                <Weight className="h-4 w-4" />
                Weight Limit (kg) *
              </Label>
              <Input
                id="weightLimit"
                type="number"
                {...register('weightLimit', { valueAsNumber: true })}
                placeholder="Enter weight limit"
                disabled={isFormLoading}
                min="1"
              />
              {errors.weightLimit && (
                <p className="text-sm text-red-600">{errors.weightLimit.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="width">Width (cm) *</Label>
              <Input
                id="width"
                type="number"
                {...register('width', { valueAsNumber: true })}
                placeholder="Width"
                disabled={isFormLoading}
                min="1"
              />
              {errors.width && (
                <p className="text-sm text-red-600">{errors.width.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="height">Height (cm) *</Label>
              <Input
                id="height"
                type="number"
                {...register('height', { valueAsNumber: true })}
                placeholder="Height"
                disabled={isFormLoading}
                min="1"
              />
              {errors.height && (
                <p className="text-sm text-red-600">{errors.height.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="depth">Depth (cm) *</Label>
              <Input
                id="depth"
                type="number"
                {...register('depth', { valueAsNumber: true })}
                placeholder="Depth"
                disabled={isFormLoading}
                min="1"
              />
              {errors.depth && (
                <p className="text-sm text-red-600">{errors.depth.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Shelf Properties */}
      <Card>
        <CardHeader>
          <CardTitle>Shelf Properties</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="shelfType">Shelf Type *</Label>
              <Select
                value={watch('shelfType')}
                onValueChange={(value) => setValue('shelfType', value as any)}
                disabled={isFormLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select shelf type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">Standard</SelectItem>
                  <SelectItem value="refrigerated">Refrigerated</SelectItem>
                  <SelectItem value="hazmat">Hazmat</SelectItem>
                  <SelectItem value="fragile">Fragile Items</SelectItem>
                  <SelectItem value="bulk">Bulk Storage</SelectItem>
                </SelectContent>
              </Select>
              {errors.shelfType && (
                <p className="text-sm text-red-600">{errors.shelfType.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="accessLevel">Access Level *</Label>
              <Select
                value={watch('accessLevel')}
                onValueChange={(value) => setValue('accessLevel', value as any)}
                disabled={isFormLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select access level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ground">Ground Level</SelectItem>
                  <SelectItem value="ladder">Ladder Required</SelectItem>
                  <SelectItem value="forklift">Forklift Required</SelectItem>
                  <SelectItem value="crane">Crane Required</SelectItem>
                </SelectContent>
              </Select>
              {errors.accessLevel && (
                <p className="text-sm text-red-600">{errors.accessLevel.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="barcode">Barcode</Label>
              <Input
                id="barcode"
                {...register('barcode')}
                placeholder="Enter barcode"
                disabled={isFormLoading}
              />
              {errors.barcode && (
                <p className="text-sm text-red-600">{errors.barcode.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="qrCode">QR Code</Label>
              <Input
                id="qrCode"
                {...register('qrCode')}
                placeholder="Enter QR code"
                disabled={isFormLoading}
              />
              {errors.qrCode && (
                <p className="text-sm text-red-600">{errors.qrCode.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="Enter any additional notes"
              disabled={isFormLoading}
              rows={3}
            />
            {errors.notes && (
              <p className="text-sm text-red-600">{errors.notes.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isFormLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isFormLoading}
        >
          {isFormLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {shelf ? 'Update Shelf' : 'Create Shelf'}
        </Button>
      </div>
    </form>
  )
}
