// components/customer-landing.tsx
"use client"

import { useEffect, useState } from "react"
import { ArrowUp } from "lucide-react"

import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"
import HeroSlider from "@/components/landing/hero-slider"
import SimpleProductFilters from "@/components/landing/simple-product-filters"
import ProductGrid from "@/components/landing/product-grid"
import FeaturedCategories from "@/components/landing/featured-categories"
import ValueProposition from "@/components/landing/value-proposition"
import EmptyState from "@/components/landing/empty-state"
import { Button } from "@/components/ui/button"
import { usePublicProducts } from "@/hooks/use-public-products"

export default function CustomerLanding() {
  const [showScrollToTop, setShowScrollToTop] = useState(false)
  const [productFilters, setProductFilters] = useState<{
    search?: string
    category?: string
    branchId?: string
    country?: string
    region?: string
  }>({})
  const [filteredProducts, setFilteredProducts] = useState<any[]>([])
  const [isFiltering, setIsFiltering] = useState(false)

  // Fetch ALL products initially (no filters applied)
  const { products: allProducts, isLoading, error } = usePublicProducts({
    filters: {}, // No initial filters - load all products
    limit: 12,
    autoFetch: true
  })

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollToTop(true)
      } else {
        setShowScrollToTop(false)
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  // Apply filters on-demand (client-side filtering for now, can be moved to API later)
  const applyFilters = async (filters: typeof productFilters) => {
    setIsFiltering(true)
    setProductFilters(filters)

    try {
      // If no filters, show all products
      if (Object.keys(filters).length === 0 || Object.values(filters).every(v => !v)) {
        setFilteredProducts(allProducts)
        setIsFiltering(false)
        return
      }

      // Apply filters to products
      let filtered = [...allProducts]

      // Category filter
      if (filters.category) {
        filtered = filtered.filter(product =>
          product.categoryName?.toLowerCase().includes(filters.category!.toLowerCase())
        )
      }

      // Branch filter
      if (filters.branchId) {
        filtered = filtered.filter(product => product.branchId === filters.branchId)
      }

      // Country filter (filter by branch country)
      if (filters.country) {
        // We'll need to get branch data to filter by country
        // For now, we can make an API call for filtered results
        const response = await fetch(`/api/products/public?${new URLSearchParams(filters as any)}`)
        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            filtered = result.data || []
          }
        }
      } else if (filters.region) {
        // Similar for region filtering
        const response = await fetch(`/api/products/public?${new URLSearchParams(filters as any)}`)
        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            filtered = result.data || []
          }
        }
      }

      setFilteredProducts(filtered)
    } catch (error) {
      console.error('Error applying filters:', error)
      setFilteredProducts(allProducts) // Fallback to all products
    } finally {
      setIsFiltering(false)
    }
  }

  const handleFiltersChange = (filters: typeof productFilters) => {
    applyFilters(filters)
  }

  const clearAllFilters = () => {
    const emptyFilters = {}
    applyFilters(emptyFilters)
  }

  const hasActiveFilters = Object.keys(productFilters).length > 0 && Object.values(productFilters).some(v => v)

  // Determine which products to display
  const displayProducts = hasActiveFilters ? filteredProducts : allProducts
  const displayLoading = isLoading || isFiltering

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        <HeroSlider />
        <SimpleProductFilters onFiltersChange={handleFiltersChange} />
        <section className="py-8 md:py-12 lg:py-16">
          <div className="container mx-auto px-4 md:px-6">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-8 text-center">
              {hasActiveFilters ? "Filtered Products" : "Our Latest Products"}
            </h2>

            {!displayLoading && !error && displayProducts.length === 0 ? (
              <div className="flex justify-center">
                <EmptyState
                  type={hasActiveFilters ? "no-filter-results" : "no-products"}
                  onClearFilters={hasActiveFilters ? clearAllFilters : undefined}
                  onBrowseAll={hasActiveFilters ? clearAllFilters : undefined}
                />
              </div>
            ) : (
              <div className="flex justify-center">
                <ProductGrid products={displayProducts} isLoading={displayLoading} error={error} />
              </div>
            )}
          </div>
        </section>
        <FeaturedCategories />
        <ValueProposition />
      </main>
      <Footer />

      {showScrollToTop && (
        <Button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 rounded-full shadow-lg transition-all duration-300 ease-in-out hover:scale-110 z-50"
          size="icon"
        >
          <ArrowUp className="h-5 w-5" />
          <span className="sr-only">Scroll to top</span>
        </Button>
      )}
    </div>
  )
}
