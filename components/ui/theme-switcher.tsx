"use client"

import * as React from "react"
import { Moon, Sun, Palette, Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useThemeStore } from "@/lib/stores/theme-store"
import type { ThemeMode, ThemeColor } from "@/types"

const themeColors = [
  { value: "sky", label: "Sky Blue", color: "bg-sky-500" },
  { value: "green", label: "Green", color: "bg-green-500" },
  { value: "forest", label: "Forest Green", color: "bg-green-700" },
  { value: "red", label: "Red", color: "bg-red-500" },
] as const

const themeModes = [
  { value: "light", label: "Light", icon: Sun },
  { value: "dark", label: "Dark", icon: Moon },
] as const

export function ThemeSwitcher() {
  const { mode, color, setMode, setColor, getEffectiveTheme } = useThemeStore()
  const effectiveTheme = getEffectiveTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="h-9 w-9">
          <Palette className="h-4 w-4" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Theme Settings</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* Theme Mode Section */}
        <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">
          Mode
        </DropdownMenuLabel>
        {themeModes.map((themeMode) => {
          const Icon = themeMode.icon
          return (
            <DropdownMenuItem
              key={themeMode.value}
              onClick={() => setMode(themeMode.value as ThemeMode)}
              className="flex items-center gap-2"
            >
              <Icon className="h-4 w-4" />
              <span className="flex-1">{themeMode.label}</span>
              {mode === themeMode.value && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
          )
        })}
        
        <DropdownMenuSeparator />
        
        {/* Color Scheme Section */}
        <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">
          Color Scheme
        </DropdownMenuLabel>
        {themeColors.map((themeColor) => (
          <DropdownMenuItem
            key={themeColor.value}
            onClick={() => setColor(themeColor.value as ThemeColor)}
            className="flex items-center gap-2"
          >
            <div className={`h-4 w-4 rounded-full ${themeColor.color}`} />
            <span className="flex-1">{themeColor.label}</span>
            {color === themeColor.value && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
        ))}
        
        <DropdownMenuSeparator />
        
        {/* Current Theme Info */}
        <div className="px-2 py-1.5 text-xs text-muted-foreground">
          Current: {effectiveTheme === "light" ? "Light" : "Dark"} • {" "}
          {themeColors.find(c => c.value === color)?.label}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export function ThemeToggle() {
  const { mode, toggleMode, getEffectiveTheme } = useThemeStore()
  const effectiveTheme = getEffectiveTheme()

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleMode}
      className="h-9 w-9"
    >
      {effectiveTheme === "dark" ? (
        <Sun className="h-4 w-4" />
      ) : (
        <Moon className="h-4 w-4" />
      )}
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}
