'use client'

import React, { useState, useEffect } from 'react'
import { MapPin, Clock, Phone, Mail, Store, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Image from 'next/image'

export interface PublicBranch {
  id: string
  name: string
  location: string
  country: string
  region: string
  address: string
  phone: string
  email: string
  description: string
  image?: string
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  coordinates?: {
    lat: number
    lng: number
  }
}

interface BranchSelectionModalProps {
  isOpen: boolean
  onClose: () => void
  onSelectBranch: (branch: PublicBranch) => void
  currentBranch?: PublicBranch | null
  mode: 'select' | 'conflict'
  conflictMessage?: string
}

export function BranchSelectionModal({
  isOpen,
  onClose,
  onSelectBranch,
  currentBranch,
  mode,
  conflictMessage
}: BranchSelectionModalProps) {
  const [branches, setBranches] = useState<PublicBranch[]>([])
  const [filteredBranches, setFilteredBranches] = useState<PublicBranch[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCountry, setSelectedCountry] = useState<string>('')
  const [selectedRegion, setSelectedRegion] = useState<string>('')

  // Fetch branches
  useEffect(() => {
    if (isOpen) {
      fetchBranches()
    }
  }, [isOpen])

  // Filter branches based on search and filters
  useEffect(() => {
    let filtered = branches

    if (searchTerm) {
      filtered = filtered.filter(branch =>
        branch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        branch.address.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    if (selectedCountry) {
      filtered = filtered.filter(branch => branch.country === selectedCountry)
    }

    if (selectedRegion) {
      filtered = filtered.filter(branch => branch.region === selectedRegion)
    }

    setFilteredBranches(filtered)
  }, [branches, searchTerm, selectedCountry, selectedRegion])

  const fetchBranches = async () => {
    setLoading(true)
    setError(null)
    try {
      const response = await fetch('/api/branches/public')
      const result = await response.json()

      if (result.success) {
        setBranches(result.data)
        setFilteredBranches(result.data)
      } else {
        setError(result.error || 'Failed to load branches')
      }
    } catch (err) {
      setError('Failed to load branches')
      console.error('Error fetching branches:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSelectBranch = (branch: PublicBranch) => {
    onSelectBranch(branch)
    onClose()
  }

  const countries = [...new Set(branches.map(b => b.country).filter(country => country && country.trim()))].sort()
  const regions = [...new Set(branches.filter(b => !selectedCountry || b.country === selectedCountry).map(b => b.region).filter(region => region && region.trim()))].sort()

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-4 border-b">
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Store className="h-6 w-6" />
            {mode === 'select' ? 'Choose Your Preferred Branch' : 'Branch Conflict'}
          </DialogTitle>
          {mode === 'conflict' && conflictMessage && (
            <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
              <p className="text-sm text-orange-800">{conflictMessage}</p>
            </div>
          )}
        </DialogHeader>

        <div className="p-6 space-y-4">
          {/* Search and Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              placeholder="Search branches..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
            <Select value={selectedCountry || "all-countries"} onValueChange={(value) => setSelectedCountry(value === "all-countries" ? "" : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select Country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-countries">All Countries</SelectItem>
                {countries.filter(country => country && country.trim()).map(country => (
                  <SelectItem key={country} value={country}>{country}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedRegion || "all-regions"} onValueChange={(value) => setSelectedRegion(value === "all-regions" ? "" : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select Region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-regions">All Regions</SelectItem>
                {regions.filter(region => region && region.trim()).map(region => (
                  <SelectItem key={region} value={region}>{region}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Current Branch Info */}
          {currentBranch && mode === 'conflict' && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Current Cart Branch:</h4>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{currentBranch.country}</Badge>
                <span className="font-medium">{currentBranch.name}</span>
                <span className="text-sm text-muted-foreground">- {currentBranch.location}</span>
              </div>
            </div>
          )}

          {/* Branches List */}
          <ScrollArea className="h-96">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                  <p className="text-sm text-muted-foreground">Loading branches...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center text-red-600">
                  <X className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">{error}</p>
                  <Button variant="outline" size="sm" onClick={fetchBranches} className="mt-2">
                    Try Again
                  </Button>
                </div>
              </div>
            ) : filteredBranches.length === 0 ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center text-muted-foreground">
                  <Store className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">No branches found matching your criteria</p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredBranches.map((branch) => (
                  <BranchCard
                    key={branch.id}
                    branch={branch}
                    onSelect={() => handleSelectBranch(branch)}
                    isSelected={currentBranch?.id === branch.id}
                  />
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  )
}

interface BranchCardProps {
  branch: PublicBranch
  onSelect: () => void
  isSelected: boolean
}

function BranchCard({ branch, onSelect, isSelected }: BranchCardProps) {
  return (
    <div className={`p-4 border rounded-xl transition-all hover:border-primary ${isSelected ? 'border-primary bg-primary/5' : 'border-muted'}`}>
      <div className="flex gap-4">
        {/* Branch Image */}
        <div className="relative w-20 h-20 bg-background rounded-lg overflow-hidden flex-shrink-0 border-2 border-muted">
          <Image
            src={branch.image || "/logo.png"}
            alt={branch.name}
            fill
            className="object-contain p-2"
            onError={(e) => {
              // Fallback to logo.png if branch image fails to load
              const target = e.target as HTMLImageElement
              if (target.src !== "/logo.png") {
                target.src = "/logo.png"
              }
            }}
          />
        </div>

        {/* Branch Details */}
        <div className="flex-1 space-y-2">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="font-bold text-lg">{branch.name}</h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">{branch.country}</Badge>
                <Badge variant="secondary">{branch.region}</Badge>
              </div>
            </div>
            <Button
              onClick={onSelect}
              variant={isSelected ? "default" : "outline"}
              size="sm"
            >
              {isSelected ? 'Selected' : 'Select Branch'}
            </Button>
          </div>

          <div className="space-y-1 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              <span>{branch.address}, {branch.location}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>Open {branch.operatingHours.open} - {branch.operatingHours.close}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4" />
              <span>{branch.phone}</span>
            </div>
          </div>

          {branch.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">{branch.description}</p>
          )}
        </div>
      </div>
    </div>
  )
}
