'use client'

import React, { useState } from 'react'
import { ShoppingBag, AlertTriangle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useCartStore } from '@/stores/cartStore'
import { useNotifications } from '@/components/providers/notification-provider'
import { BranchSelectionModal, type PublicBranch } from './branch-selection-modal'

interface BranchAwareAddToCartProps {
  product: {
    id: string
    name: string
    price: number
    image?: string
    category?: string
    branchId?: string
    branchName?: string
    stock?: number
    sku?: string
  }
  variant?: {
    color?: string
    size?: string
    storage?: string
  }
  quantity?: number
  disabled?: boolean
  className?: string
  children?: React.ReactNode
}

export function BranchAwareAddToCart({
  product,
  variant,
  quantity = 1,
  disabled = false,
  className,
  children
}: BranchAwareAddToCartProps) {
  const {
    addItem,
    hasItem,
    canAddItemFromBranch,
    isEmpty,
    getCartBranchId,
    selectedBranch,
    setSelectedBranch,
    clearCart
  } = useCartStore()

  const [showBranchModal, setShowBranchModal] = useState(false)
  const [modalMode, setModalMode] = useState<'select' | 'conflict'>('select')
  const [conflictMessage, setConflictMessage] = useState('')

  // Safely get notifications with fallback
  let showSuccess: (title: string, message: string) => void = () => {}
  let showError: (title: string, message: string) => void = () => {}
  let showWarning: (title: string, message: string) => void = () => {}

  try {
    const notifications = useNotifications()
    showSuccess = notifications.showSuccess
    showError = notifications.showError
    showWarning = notifications.showWarning
  } catch (error) {
    console.warn('Notifications provider not available, using fallback')
  }

  const handleAddToCart = () => {
    console.log('🛒 BranchAwareAddToCart clicked for:', product.name)
    console.log('🛒 Product branch:', product.branchId, product.branchName)
    console.log('🛒 Cart is empty:', isEmpty)
    console.log('🛒 Selected branch:', selectedBranch)

    // If cart is empty and no branch is selected, show branch selection
    if (isEmpty && !selectedBranch) {
      console.log('🛒 Cart is empty, showing branch selection modal')
      setModalMode('select')
      setShowBranchModal(true)
      return
    }

    // If cart has items, check branch compatibility
    if (!isEmpty && product.branchId) {
      const canAdd = canAddItemFromBranch(product.branchId)
      if (!canAdd) {
        const cartBranchId = getCartBranchId()
        console.log('🛒 Branch conflict detected. Cart branch:', cartBranchId, 'Product branch:', product.branchId)
        
        setConflictMessage(
          `Your cart contains items from a different branch. You can either clear your cart and shop from ${product.branchName}, or continue shopping from your current branch.`
        )
        setModalMode('conflict')
        setShowBranchModal(true)
        return
      }
    }

    // If we reach here, we can add the item
    addItemToCart()
  }

  const addItemToCart = () => {
    try {
      const itemToAdd = {
        productId: product.id,
        name: product.name,
        price: product.price,
        image: product.image || '/placeholder.svg',
        category: product.category || 'General',
        branchId: product.branchId || selectedBranch?.id || 'default-branch',
        branchName: product.branchName || selectedBranch?.name || 'Default Store',
        maxStock: product.stock || 50,
        sku: product.sku || `SKU-${product.id}`,
        variant: variant ? JSON.stringify(variant) : undefined,
        quantity
      }

      console.log('🛒 Adding item to cart:', itemToAdd)
      addItem(itemToAdd)

      showSuccess(
        'Added to Cart',
        `${product.name} has been added to your cart`
      )

    } catch (error) {
      console.error('❌ Error adding item to cart:', error)
      showError(
        'Failed to Add Item',
        'Could not add item to cart. Please try again.'
      )
    }
  }

  const handleBranchSelection = (branch: PublicBranch) => {
    console.log('🛒 Branch selected:', branch.name)
    
    // Convert PublicBranch to SelectedBranch
    const selectedBranchData = {
      id: branch.id,
      name: branch.name,
      location: branch.location,
      country: branch.country,
      region: branch.region
    }

    setSelectedBranch(selectedBranchData)

    if (modalMode === 'conflict') {
      // Clear cart and set new branch
      clearCart()
      setSelectedBranch(selectedBranchData)
      showWarning(
        'Cart Cleared',
        `Your cart has been cleared. You can now shop from ${branch.name}.`
      )
    }

    // Add the item after branch selection
    setTimeout(() => {
      addItemToCart()
    }, 100)
  }

  const isInCart = hasItem(product.id, variant ? JSON.stringify(variant) : undefined)

  return (
    <>
      <Button
        onClick={handleAddToCart}
        disabled={disabled}
        className={className}
        variant={isInCart ? "outline" : "default"}
      >
        {children || (
          <>
            <ShoppingBag className="h-4 w-4 mr-2" />
            {isInCart ? 'Add More' : 'Add to Cart'}
          </>
        )}
      </Button>

      <BranchSelectionModal
        isOpen={showBranchModal}
        onClose={() => setShowBranchModal(false)}
        onSelectBranch={handleBranchSelection}
        currentBranch={selectedBranch ? {
          id: selectedBranch.id,
          name: selectedBranch.name,
          location: selectedBranch.location,
          country: selectedBranch.country,
          region: selectedBranch.region,
          address: '',
          phone: '',
          email: '',
          description: '',
          operatingHours: { open: '08:00', close: '18:00', timezone: 'Africa/Blantyre' }
        } : null}
        mode={modalMode}
        conflictMessage={conflictMessage}
      />
    </>
  )
}

// Export the original AddToCartButton for backward compatibility
export { AddToCartButton } from './cart-item'
