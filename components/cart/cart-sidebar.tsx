'use client'

import React, { useState } from 'react'
import { ArrowRight, MessageCircle, ShoppingBag } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>eader, Sheet<PERSON>itle, SheetTrigger } from '@/components/ui/sheet'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { useCartStore } from '@/stores/cartStore'
import { CartItemComponent } from './cart-item'
import WhatsAppCheckout from '@/components/checkout/whatsapp-checkout'
import Link from 'next/link'
import { formatPrice } from '@/lib/currency'

interface CartSidebarProps {
  children?: React.ReactNode
}

export function CartSidebar({ children }: CartSidebarProps) {
  const {
    items,
    isOpen,
    summary,
    isEmpty,
    itemCount,
    selectedBranch,
    clearCart,
    closeCart,
    openCart
  } = useCartStore()

  const [showCheckout, setShowCheckout] = useState(false)

  if (!children) {
    return null
  }

  return (
    <Sheet open={isOpen} onOpenChange={(open) => open ? openCart() : closeCart()}>
      <SheetTrigger asChild>
        {children}
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-lg flex flex-col h-full p-0">
        <SheetHeader className="flex-shrink-0 space-y-2.5 p-6 pb-4 border-b">
          <SheetTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Shopping Cart
            {itemCount > 0 && (
              <Badge variant="secondary" className="ml-auto">
                {itemCount} {itemCount === 1 ? 'item' : 'items'}
              </Badge>
            )}
          </SheetTitle>
        </SheetHeader>

        {isEmpty ? (
          <div className="flex flex-col items-center justify-center flex-1 space-y-4 p-6">
            <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center">
              <ShoppingBag className="h-12 w-12 text-muted-foreground" />
            </div>
            <div className="text-center space-y-2">
              <h3 className="font-semibold text-lg">Your cart is empty</h3>
              <p className="text-muted-foreground text-sm">
                Add some products to get started
              </p>
            </div>
            <Button asChild className="mt-4">
              <Link href="/products" onClick={closeCart}>
                Browse Products
              </Link>
            </Button>
          </div>
        ) : (
          <div className="flex flex-col h-full">
            {/* Cart Items - Scrollable Area */}
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full">
                <div className="px-6 py-4">
                  {/* Branch Information */}
                  {selectedBranch && (
                    <div className="mb-4 p-3 bg-blue-50 rounded-xl border-l-4 border-l-blue-500">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-semibold text-blue-900">Shopping from:</p>
                          <p className="text-sm text-blue-700">{selectedBranch.name}</p>
                          <p className="text-xs text-blue-600">{selectedBranch.location}, {selectedBranch.country}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Quick Summary */}
                  <div className="mb-6 p-4 bg-muted/30 rounded-xl border-l-4 border-l-primary">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">
                        {itemCount} {itemCount === 1 ? 'item' : 'items'} in cart
                      </span>
                      <span className="text-lg font-bold text-primary">
                        {formatPrice(summary.total)}
                      </span>
                    </div>
                  </div>

                  {/* Cart Items */}
                  <div className="space-y-4">
                    {items.map((item) => (
                      <div 
                        key={item.id} 
                        className="bg-muted/20 rounded-xl p-4 border-l-4 border-l-muted hover:border-l-primary transition-colors"
                      >
                        <CartItemComponent
                          item={item}
                          variant="enhanced"
                          showActions={true}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </ScrollArea>
            </div>

            {/* Cart Summary - Fixed at Bottom */}
            <div className="flex-shrink-0 border-t-2 border-muted bg-background">
              <div className="p-6 space-y-5">
                {/* Summary Details */}
                <div className="space-y-3 bg-muted/20 rounded-xl p-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground font-medium">Subtotal</span>
                    <span className="font-semibold">{formatPrice(summary.subtotal)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground font-medium">Tax</span>
                    <span className="font-semibold">{formatPrice(summary.tax)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground font-medium">Delivery</span>
                    <span className="font-semibold text-blue-600">
                      To be discussed
                    </span>
                  </div>
                  {summary.discount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-green-600 font-medium">Discount</span>
                      <span className="text-green-600 font-bold">-{formatPrice(summary.discount)}</span>
                    </div>
                  )}
                  <div className="border-t-2 border-muted pt-3 mt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold">Total</span>
                      <span className="text-2xl font-bold text-primary">{formatPrice(summary.total)}</span>
                    </div>
                  </div>
                </div>

                {/* Delivery Information */}
                <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border-l-4 border-l-blue-500">
                  <p className="text-sm font-medium text-blue-700">
                    📦 Delivery charges will be discussed based on your location
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button asChild className="w-full h-14 text-lg font-bold rounded-xl bg-primary hover:bg-primary/90" size="lg">
                    <Link href="/checkout" onClick={closeCart}>
                      Proceed to Checkout
                      <ArrowRight className="h-6 w-6 ml-2" />
                    </Link>
                  </Button>

                  <Dialog open={showCheckout} onOpenChange={setShowCheckout}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full h-12 border-2 border-green-200 text-green-700 hover:bg-green-50 hover:text-green-800 font-bold rounded-xl"
                        size="lg"
                      >
                        <MessageCircle className="h-5 w-5 mr-2" />
                        Quick WhatsApp Order
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                      <WhatsAppCheckout
                        onSuccess={() => {
                          setShowCheckout(false)
                          closeCart()
                        }}
                      />
                    </DialogContent>
                  </Dialog>

                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      className="flex-1 h-11 border-2 border-muted hover:bg-muted/50 rounded-xl font-semibold"
                      asChild
                    >
                      <Link href="/products" onClick={closeCart}>
                        Continue Shopping
                      </Link>
                    </Button>

                    <Button
                      variant="ghost"
                      onClick={clearCart}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10 h-11 px-6 rounded-xl font-semibold"
                    >
                      Clear Cart
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  )
}

// Compact cart button for header
export function CartButton() {
  const { itemCount, summary } = useCartStore()

  return (
    <CartSidebar>
      <Button variant="ghost" size="sm" className="relative">
        <ShoppingBag className="h-5 w-5" />
        {itemCount > 0 && (
          <>
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {itemCount}
            </Badge>
            <span className="ml-2 hidden sm:inline">
              {formatPrice(summary.total)}
            </span>
          </>
        )}
      </Button>
    </CartSidebar>
  )
}
