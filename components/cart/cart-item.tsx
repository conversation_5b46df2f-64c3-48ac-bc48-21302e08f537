'use client'

import React from 'react'
import { Plus, Minus, Trash2, ShoppingBag } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useCartStore, type CartItem } from '@/stores/cartStore'
import { useNotifications } from '@/components/providers/notification-provider'
import Image from 'next/image'

interface CartItemProps {
  item: CartItem
  variant?: 'default' | 'compact' | 'checkout' | 'enhanced'
  showActions?: boolean
}

export function CartItemComponent({ 
  item, 
  variant = 'default', 
  showActions = true 
}: CartItemProps) {
  const { updateQuantity, removeItem } = useCartStore()

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0
    }).format(price)
  }

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(item.id)
    } else {
      updateQuantity(item.id, newQuantity)
    }
  }

  if (variant === 'compact') {
    return (
      <div className="flex items-center gap-3 py-2">
        {/* Product Image */}
        <div className="relative w-12 h-12 bg-muted rounded-md overflow-hidden flex-shrink-0">
          {item.image ? (
            <Image
              src={item.image}
              alt={item.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <ShoppingBag className="h-4 w-4 text-muted-foreground" />
            </div>
          )}
        </div>

        {/* Product Details */}
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-sm truncate">{item.name}</h4>
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">
              Qty: {item.quantity}
            </span>
            <span className="font-medium text-sm">
              {formatPrice(item.price * item.quantity)}
            </span>
          </div>
        </div>

        {showActions && (
          <Button
            variant="ghost"
            size="sm"
            className="h-auto p-1 text-destructive hover:text-destructive"
            onClick={() => removeItem(item.id)}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  if (variant === 'checkout') {
    return (
      <div className="flex gap-4 py-4 border-b">
        {/* Product Image */}
        <div className="relative w-20 h-20 bg-muted rounded-md overflow-hidden flex-shrink-0">
          {item.image ? (
            <Image
              src={item.image}
              alt={item.name}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <ShoppingBag className="h-8 w-8 text-muted-foreground" />
            </div>
          )}
        </div>

        {/* Product Details */}
        <div className="flex-1 space-y-2">
          <div>
            <h4 className="font-medium">{item.name}</h4>
            {item.variant && (
              <p className="text-sm text-muted-foreground">
                {Object.entries(item.variant)
                  .filter(([_, value]) => value)
                  .map(([key, value]) => `${key}: ${value}`)
                  .join(', ')}
              </p>
            )}
            {item.branchName && (
              <p className="text-sm text-muted-foreground">
                From: {item.branchName}
              </p>
            )}
            {item.sku && (
              <p className="text-xs text-muted-foreground">
                SKU: {item.sku}
              </p>
            )}
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm">
              <span className="text-muted-foreground">Quantity: </span>
              <span className="font-medium">{item.quantity}</span>
            </div>
            <div className="text-right">
              <p className="font-semibold">
                {formatPrice(item.price * item.quantity)}
              </p>
              <p className="text-sm text-muted-foreground">
                {formatPrice(item.price)} each
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (variant === 'enhanced') {
    return (
      <div className="flex gap-4">
        {/* Product Image */}
        <div className="relative w-24 h-24 bg-background rounded-2xl overflow-hidden flex-shrink-0 border-2 border-muted">
          {item.image ? (
            <Image
              src={item.image}
              alt={item.name}
              fill
              className="object-contain p-3"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-muted/50">
              <ShoppingBag className="h-10 w-10 text-muted-foreground" />
            </div>
          )}
        </div>

        {/* Product Details */}
        <div className="flex-1 space-y-3">
          {/* Product Info */}
          <div className="space-y-2">
            <h4 className="font-bold text-base leading-tight line-clamp-2 text-foreground">{item.name}</h4>

            <div className="flex flex-wrap gap-2">
              {item.variant && (
                <span className="inline-flex items-center px-2 py-1 rounded-lg bg-primary/10 text-primary text-xs font-medium">
                  {Object.entries(item.variant)
                    .filter(([_, value]) => value)
                    .map(([key, value]) => `${key}: ${value}`)
                    .join(', ')}
                </span>
              )}
              {item.branchName && (
                <span className="inline-flex items-center gap-1 px-2 py-1 rounded-lg bg-green-50 text-green-700 text-xs font-medium">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full"></span>
                  {item.branchName}
                </span>
              )}
            </div>
          </div>

          {/* Price Section */}
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xl font-bold text-primary">
                {formatPrice(item.price * item.quantity)}
              </p>
              <p className="text-sm text-muted-foreground">
                {formatPrice(item.price)} × {item.quantity}
              </p>
            </div>
          </div>

          {/* Quantity Controls */}
          {showActions && (
            <div className="flex items-center justify-between pt-2 border-t border-muted/50">
              <div className="flex items-center gap-3 bg-muted/30 rounded-xl p-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 rounded-lg hover:bg-background"
                  onClick={() => handleQuantityChange(item.quantity - 1)}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <span className="w-8 text-center text-sm font-bold bg-background rounded-lg py-1">
                  {item.quantity}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 rounded-lg hover:bg-background"
                  onClick={() => handleQuantityChange(item.quantity + 1)}
                  disabled={item.maxStock ? item.quantity >= item.maxStock : false}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-3 text-destructive hover:text-destructive hover:bg-destructive/10 rounded-lg"
                onClick={() => removeItem(item.id)}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Remove
              </Button>
            </div>
          )}
        </div>
      </div>
    )
  }

  // Default variant
  return (
    <div className="flex gap-4 py-4 border-b last:border-b-0">
      {/* Product Image */}
      <div className="relative w-16 h-16 bg-muted rounded-md overflow-hidden flex-shrink-0">
        {item.image ? (
          <Image
            src={item.image}
            alt={item.name}
            fill
            className="object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <ShoppingBag className="h-6 w-6 text-muted-foreground" />
          </div>
        )}
      </div>

      {/* Product Details */}
      <div className="flex-1 space-y-2">
        <div>
          <h4 className="font-medium text-sm leading-tight">{item.name}</h4>
          {item.variant && (
            <p className="text-xs text-muted-foreground">
              {Object.entries(item.variant)
                .filter(([_, value]) => value)
                .map(([key, value]) => `${key}: ${value}`)
                .join(', ')}
            </p>
          )}
          {item.branchName && (
            <p className="text-xs text-muted-foreground">
              From: {item.branchName}
            </p>
          )}
        </div>

        <div className="flex items-center justify-between">
          {showActions ? (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => handleQuantityChange(item.quantity - 1)}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <span className="w-8 text-center text-sm font-medium">
                {item.quantity}
              </span>
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => handleQuantityChange(item.quantity + 1)}
                disabled={item.maxStock ? item.quantity >= item.maxStock : false}
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          ) : (
            <div className="text-sm">
              <span className="text-muted-foreground">Qty: </span>
              <span className="font-medium">{item.quantity}</span>
            </div>
          )}

          <div className="text-right">
            <p className="font-medium text-sm">
              {formatPrice(item.price * item.quantity)}
            </p>
            <p className="text-xs text-muted-foreground">
              {formatPrice(item.price)} each
            </p>
          </div>
        </div>

        {showActions && (
          <Button
            variant="ghost"
            size="sm"
            className="h-auto p-0 text-destructive hover:text-destructive"
            onClick={() => removeItem(item.id)}
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Remove
          </Button>
        )}
      </div>
    </div>
  )
}

// Quick add to cart button for product cards
interface AddToCartButtonProps {
  product: {
    id: string
    name: string
    price: number
    image?: string
    category?: string
    branchId?: string
    branchName?: string
    stock?: number
    sku?: string
  }
  variant?: {
    color?: string
    size?: string
    storage?: string
  }
  quantity?: number
  disabled?: boolean
  className?: string
  children?: React.ReactNode
}

export function AddToCartButton({
  product,
  variant,
  quantity = 1,
  disabled = false,
  className,
  children
}: AddToCartButtonProps) {
  const { addItem, hasItem, canAddItemFromBranch, isEmpty, getCartBranchId } = useCartStore()

  // Safely get notifications with fallback
  let showSuccess: (title: string, message: string) => void = () => {}
  let showError: (title: string, message: string) => void = () => {}

  try {
    const notifications = useNotifications()
    showSuccess = notifications.showSuccess
    showError = notifications.showError
  } catch (error) {
    console.warn('Notifications provider not available, using fallback')
  }

  const handleAddToCart = () => {
    console.log('🛒 AddToCartButton clicked for:', product.name)
    console.log('🛒 Product data:', product)
    console.log('🛒 Variant:', variant)
    console.log('🛒 Quantity:', quantity)

    try {
      const itemToAdd = {
        productId: product.id,
        name: product.name,
        price: product.price,
        image: product.image || '/placeholder.svg',
        category: product.category || 'General',
        branchId: product.branchId || 'default-branch',
        branchName: product.branchName || 'Default Store',
        maxStock: product.stock || 50,
        sku: product.sku || `SKU-${product.id}`,
        variant,
        quantity
      }

      console.log('🛒 Item to add:', itemToAdd)

      addItem(itemToAdd)

      console.log('✅ Item added to cart successfully')

      // Show success notification
      try {
        showSuccess(
          'Added to Cart',
          `${product.name} has been added to your cart`
        )
      } catch (notifError) {
        console.log('📢 Notification fallback: Item added to cart -', product.name)
      }

    } catch (error) {
      console.error('❌ Error adding item to cart:', error)
      console.error('❌ Error details:', error.message, error.stack)

      try {
        showError(
          'Failed to Add Item',
          'Could not add item to cart. Please try again.'
        )
      } catch (notifError) {
        console.error('📢 Notification fallback: Failed to add item to cart')
      }
    }
  }

  const isInCart = hasItem(product.id)

  return (
    <Button
      onClick={handleAddToCart}
      disabled={disabled}
      className={className}
      variant={isInCart ? "outline" : "default"}
    >
      {children || (
        <>
          <ShoppingBag className="h-4 w-4 mr-2" />
          {isInCart ? 'Add More' : 'Add to Cart'}
        </>
      )}
    </Button>
  )
}
