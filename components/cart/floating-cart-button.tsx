"use client"

import { useState, useEffect } from 'react'
import { ShoppingBag, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useCartStore } from '@/stores/cartStore'
import { CartSidebar } from './cart-sidebar'
import { cn } from '@/lib/utils'

export function FloatingCartButton() {
  const { itemCount, summary, isOpen, toggleCart } = useCartStore()
  const [mounted, setMounted] = useState(false)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setMounted(true)
    
    // Show floating cart when there are items
    if (itemCount > 0) {
      setIsVisible(true)
    } else {
      setIsVisible(false)
    }
  }, [itemCount])

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0
    }).format(price)
  }

  if (!mounted) {
    return null
  }

  return (
    <>
      {/* Floating Cart <PERSON>ton */}
      <div
        className={cn(
          "fixed bottom-6 right-6 z-50 transition-all duration-300 transform",
          isVisible ? "translate-y-0 opacity-100 scale-100" : "translate-y-16 opacity-0 scale-95 pointer-events-none"
        )}
      >
        <CartSidebar>
          <div className="relative">
            {/* Main Cart Button */}
            <Button
              size="lg"
              className={cn(
                "h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300",
                "bg-primary hover:bg-primary/90 border-2 border-background",
                itemCount > 0 && "animate-pulse"
              )}
            >
              <ShoppingBag className="h-6 w-6" />
            </Button>

            {/* Item Count Badge */}
            {itemCount > 0 && (
              <Badge
                className={cn(
                  "absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 text-xs font-bold",
                  "bg-red-500 hover:bg-red-600 border-2 border-background",
                  "flex items-center justify-center min-w-[24px]",
                  "animate-bounce"
                )}
              >
                {itemCount > 99 ? '99+' : itemCount}
              </Badge>
            )}

            {/* Total Price Badge */}
            {itemCount > 0 && (
              <div className="absolute -bottom-2 -left-2 bg-background border-2 border-primary rounded-full px-2 py-1 shadow-md">
                <span className="text-xs font-semibold text-primary">
                  {formatPrice(summary.total)}
                </span>
              </div>
            )}
          </div>
        </CartSidebar>
      </div>

      {/* Cart Status Indicator */}
      {itemCount > 0 && (
        <div
          className={cn(
            "fixed bottom-24 right-6 z-40 transition-all duration-300",
            isVisible ? "translate-y-0 opacity-100" : "translate-y-8 opacity-0"
          )}
        >
          <div className="bg-background/95 backdrop-blur-sm border rounded-lg px-3 py-2 shadow-lg">
            <div className="text-xs text-muted-foreground">
              {itemCount} {itemCount === 1 ? 'item' : 'items'} in cart
            </div>
          </div>
        </div>
      )}
    </>
  )
}

// Enhanced Cart Button for Header (Mall97 Style)
export function EnhancedCartButton() {
  const { itemCount, summary, isOpen } = useCartStore()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0
    }).format(price)
  }

  if (!mounted) {
    return (
      <Button variant="ghost" size="icon" className="relative">
        <ShoppingBag className="h-5 w-5" />
      </Button>
    )
  }

  return (
    <CartSidebar>
      <Button 
        variant="ghost" 
        size="icon" 
        className={cn(
          "relative transition-all duration-200",
          itemCount > 0 && "bg-primary/10 hover:bg-primary/20"
        )}
      >
        <ShoppingBag className={cn(
          "h-5 w-5 transition-colors",
          itemCount > 0 ? "text-primary" : "text-muted-foreground"
        )} />
        
        {/* Item Count Badge */}
        {itemCount > 0 && (
          <Badge
            className={cn(
              "absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs",
              "bg-red-500 hover:bg-red-600 border-2 border-background",
              "flex items-center justify-center min-w-[20px]"
            )}
          >
            {itemCount > 99 ? '99+' : itemCount}
          </Badge>
        )}

        {/* Screen Reader Text */}
        <span className="sr-only">
          Shopping cart ({itemCount} items, {formatPrice(summary.total)})
        </span>
      </Button>
    </CartSidebar>
  )
}

// Cart Preview Tooltip (Mall97 Style)
export function CartPreviewTooltip() {
  const { items, summary, itemCount } = useCartStore()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0
    }).format(price)
  }

  if (!mounted || itemCount === 0) {
    return null
  }

  return (
    <div className="absolute top-full right-0 mt-2 w-80 bg-background border rounded-lg shadow-lg p-4 z-50">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold">Cart Preview</h3>
          <Badge variant="secondary">{itemCount} items</Badge>
        </div>
        
        <div className="space-y-2 max-h-40 overflow-y-auto">
          {items.slice(0, 3).map((item) => (
            <div key={item.id} className="flex items-center gap-3 text-sm">
              <div className="w-8 h-8 bg-muted rounded flex items-center justify-center">
                <ShoppingBag className="h-4 w-4" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium truncate">{item.name}</div>
                <div className="text-muted-foreground">Qty: {item.quantity}</div>
              </div>
              <div className="font-medium">
                {formatPrice(item.price * item.quantity)}
              </div>
            </div>
          ))}
          
          {items.length > 3 && (
            <div className="text-center text-sm text-muted-foreground">
              +{items.length - 3} more items
            </div>
          )}
        </div>
        
        <div className="border-t pt-3">
          <div className="flex items-center justify-between font-semibold">
            <span>Total:</span>
            <span className="text-primary">{formatPrice(summary.total)}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
