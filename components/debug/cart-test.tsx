"use client"

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useCartStore } from '@/stores/cartStore'
import { Badge } from '@/components/ui/badge'
import { ShoppingBag, Trash2 } from 'lucide-react'

export function CartTest() {
  const { 
    items, 
    itemCount, 
    summary, 
    addItem, 
    clearCart,
    isEmpty,
    toggleCart,
    isOpen
  } = useCartStore()
  
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const testProducts = [
    {
      id: 'test-1',
      name: 'iPhone 15 Pro',
      price: 1200000,
      image: '/placeholder.svg',
      category: 'Smartphones',
      branchId: 'branch-1',
      branchName: 'TechStore Lilongwe',
      stock: 10,
      sku: 'IP15P-001'
    },
    {
      id: 'test-2',
      name: 'Samsung Galaxy S24',
      price: 950000,
      image: '/placeholder.svg',
      category: 'Smartphones',
      branchId: 'branch-2',
      branchName: 'Mobile World Blantyre',
      stock: 5,
      sku: 'SGS24-001'
    },
    {
      id: 'test-3',
      name: 'MacBook Air M3',
      price: 2500000,
      image: '/placeholder.svg',
      category: 'Laptops',
      branchId: 'branch-1',
      branchName: 'TechStore Lilongwe',
      stock: 3,
      sku: 'MBA-M3-001'
    }
  ]

  const handleAddTestItem = (product: typeof testProducts[0]) => {
    console.log('🧪 Adding test product:', product.name)
    addItem({
      productId: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      category: product.category,
      branchId: product.branchId,
      branchName: product.branchName,
      maxStock: product.stock,
      sku: product.sku
    })
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-MW', {
      style: 'currency',
      currency: 'MWK',
      minimumFractionDigits: 0
    }).format(price)
  }

  if (!mounted) {
    return <div>Loading cart test...</div>
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🛒 Cart Functionality Test
          <Badge variant="outline">{itemCount} items</Badge>
          <Badge variant={isOpen ? "default" : "secondary"}>
            Cart {isOpen ? "Open" : "Closed"}
          </Badge>
        </CardTitle>
        <CardDescription>
          Test the cart functionality with sample products
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Cart State Info */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold">{items.length}</div>
            <div className="text-sm text-muted-foreground">Unique Items</div>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold">{itemCount}</div>
            <div className="text-sm text-muted-foreground">Total Quantity</div>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold">{formatPrice(summary.subtotal)}</div>
            <div className="text-sm text-muted-foreground">Subtotal</div>
          </div>
          <div className="text-center p-4 bg-muted rounded-lg">
            <div className="text-2xl font-bold">{formatPrice(summary.total)}</div>
            <div className="text-sm text-muted-foreground">Total</div>
          </div>
        </div>

        {/* Test Products */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Test Products</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {testProducts.map((product) => (
              <Card key={product.id} className="p-4">
                <div className="space-y-2">
                  <h4 className="font-medium">{product.name}</h4>
                  <p className="text-sm text-muted-foreground">{product.category}</p>
                  <p className="font-bold text-primary">{formatPrice(product.price)}</p>
                  <Button 
                    onClick={() => handleAddTestItem(product)}
                    className="w-full"
                    size="sm"
                  >
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    Add to Cart
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Cart Actions */}
        <div className="flex gap-4">
          <Button onClick={toggleCart} variant="outline">
            {isOpen ? "Close Cart" : "Open Cart"}
          </Button>
          <Button onClick={clearCart} variant="destructive" disabled={isEmpty}>
            <Trash2 className="h-4 w-4 mr-2" />
            Clear Cart
          </Button>
        </div>

        {/* Current Cart Items */}
        {items.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold mb-4">Current Cart Items</h3>
            <div className="space-y-2">
              {items.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 border rounded">
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {item.branchName} • Qty: {item.quantity}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{formatPrice(item.price * item.quantity)}</div>
                    <div className="text-sm text-muted-foreground">{formatPrice(item.price)} each</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
