"use client"

import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { useCartStore } from '@/stores/cartStore'

export function SimpleCartTest() {
  const [mounted, setMounted] = useState(false)
  const { items, itemCount, addItem, clearCart } = useCartStore()

  useEffect(() => {
    setMounted(true)
    console.log('🧪 SimpleCartTest mounted')
  }, [])

  const testAddItem = () => {
    console.log('🧪 Test button clicked - adding item')
    try {
      addItem({
        productId: 'test-123',
        name: 'Test Product',
        price: 50000,
        image: '/placeholder.svg',
        category: 'Test',
        branchId: 'test-branch',
        branchName: 'Test Branch',
        maxStock: 10,
        sku: 'TEST-001'
      })
      console.log('🧪 addItem called successfully')
    } catch (error) {
      console.error('🧪 Error in testAddItem:', error)
    }
  }

  if (!mounted) {
    return <div>Loading...</div>
  }

  console.log('🧪 SimpleCartTest render - itemCount:', itemCount, 'items:', items)

  return (
    <div className="fixed top-4 left-4 z-50 bg-white border rounded p-4 shadow-lg">
      <h3 className="font-bold mb-2">Cart Debug</h3>
      <p>Items: {itemCount}</p>
      <p>Array length: {items.length}</p>
      <div className="space-y-2 mt-2">
        <Button onClick={testAddItem} size="sm">
          Add Test Item
        </Button>
        <Button onClick={clearCart} variant="outline" size="sm">
          Clear Cart
        </Button>
      </div>
      {items.length > 0 && (
        <div className="mt-2 text-xs">
          <p>Items:</p>
          {items.map(item => (
            <div key={item.id}>{item.name} (x{item.quantity})</div>
          ))}
        </div>
      )}
    </div>
  )
}
