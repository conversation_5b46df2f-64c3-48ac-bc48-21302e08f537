'use client'

import React, { useState, useEffect } from 'react'
import { X, Bell, Mail, MessageSquare, Smartphone, Volume2, VolumeX, RotateCcw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { useNotificationStore } from '@/stores/notificationStore'
import { toast } from 'sonner'
import type { NotificationCategory, NotificationChannel, NotificationPriority } from '@/types/notifications'

interface NotificationPreferencesProps {
  onClose?: () => void
  className?: string
}

const categories: { value: NotificationCategory; label: string; description: string }[] = [
  { value: 'system', label: 'System', description: 'System updates and maintenance notifications' },
  { value: 'security', label: 'Security', description: 'Security alerts and login notifications' },
  { value: 'inventory', label: 'Inventory', description: 'Stock levels and product updates' },
  { value: 'orders', label: 'Orders', description: 'Order status and payment notifications' },
  { value: 'delivery', label: 'Delivery', description: 'Delivery tracking and status updates' },
  { value: 'campaigns', label: 'Campaigns', description: 'Sales, discounts, and promotional offers' },
  { value: 'users', label: 'Users', description: 'User management and account notifications' },
  { value: 'financial', label: 'Financial', description: 'Payment and financial notifications' },
  { value: 'reports', label: 'Reports', description: 'Report generation and analytics' },
  { value: 'maintenance', label: 'Maintenance', description: 'Scheduled maintenance and updates' }
]

const channels: { value: NotificationChannel; label: string; icon: React.ComponentType<{ className?: string }> }[] = [
  { value: 'in_app', label: 'In-App', icon: Bell },
  { value: 'email', label: 'Email', icon: Mail },
  { value: 'sms', label: 'SMS', icon: MessageSquare },
  { value: 'push', label: 'Push', icon: Smartphone }
]

const priorities: { value: NotificationPriority; label: string }[] = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'critical', label: 'Critical' },
  { value: 'urgent', label: 'Urgent' }
]

export function NotificationPreferences({ onClose, className }: NotificationPreferencesProps) {
  const {
    preferences,
    isLoadingPreferences,
    fetchPreferences,
    updatePreferences,
    resetPreferences
  } = useNotificationStore()

  const [localPreferences, setLocalPreferences] = useState(preferences)
  const [hasChanges, setHasChanges] = useState(false)

  // Load preferences on mount
  useEffect(() => {
    if (!preferences) {
      fetchPreferences()
    }
  }, [preferences, fetchPreferences])

  // Update local state when preferences change
  useEffect(() => {
    if (preferences) {
      setLocalPreferences(preferences)
      setHasChanges(false)
    }
  }, [preferences])

  const handleChannelToggle = (channel: NotificationChannel, enabled: boolean) => {
    if (!localPreferences) return
    
    const updated = {
      ...localPreferences,
      channels: {
        ...localPreferences.channels,
        [channel]: enabled
      }
    }
    setLocalPreferences(updated)
    setHasChanges(true)
  }

  const handleCategoryToggle = (category: NotificationCategory, enabled: boolean) => {
    if (!localPreferences) return
    
    const updated = {
      ...localPreferences,
      categories: {
        ...localPreferences.categories,
        [category]: {
          ...localPreferences.categories[category],
          enabled
        }
      }
    }
    setLocalPreferences(updated)
    setHasChanges(true)
  }

  const handleCategoryChannelToggle = (category: NotificationCategory, channel: NotificationChannel, enabled: boolean) => {
    if (!localPreferences) return
    
    const currentChannels = localPreferences.categories[category]?.channels || []
    const updatedChannels = enabled
      ? [...currentChannels, channel]
      : currentChannels.filter(c => c !== channel)
    
    const updated = {
      ...localPreferences,
      categories: {
        ...localPreferences.categories,
        [category]: {
          ...localPreferences.categories[category],
          channels: updatedChannels
        }
      }
    }
    setLocalPreferences(updated)
    setHasChanges(true)
  }

  const handlePriorityToggle = (category: NotificationCategory, priority: NotificationPriority, enabled: boolean) => {
    if (!localPreferences) return
    
    const currentPriorities = localPreferences.categories[category]?.priority || []
    const updatedPriorities = enabled
      ? [...currentPriorities, priority]
      : currentPriorities.filter(p => p !== priority)
    
    const updated = {
      ...localPreferences,
      categories: {
        ...localPreferences.categories,
        [category]: {
          ...localPreferences.categories[category],
          priority: updatedPriorities
        }
      }
    }
    setLocalPreferences(updated)
    setHasChanges(true)
  }

  const handleSettingChange = (key: string, value: any) => {
    if (!localPreferences) return
    
    const updated = {
      ...localPreferences,
      settings: {
        ...localPreferences.settings,
        [key]: value
      }
    }
    setLocalPreferences(updated)
    setHasChanges(true)
  }

  const handleSave = async () => {
    if (!localPreferences || !hasChanges) return
    
    try {
      await updatePreferences(localPreferences)
      setHasChanges(false)
      toast.success('Notification preferences updated')
    } catch (error) {
      toast.error('Failed to update preferences')
    }
  }

  const handleReset = async () => {
    try {
      await resetPreferences()
      setHasChanges(false)
      toast.success('Notification preferences reset to defaults')
    } catch (error) {
      toast.error('Failed to reset preferences')
    }
  }

  if (isLoadingPreferences || !localPreferences) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium">Notification Preferences</CardTitle>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose} className="h-6 w-6 p-0">
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        <Tabs defaultValue="channels" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="channels" className="text-xs">Channels</TabsTrigger>
            <TabsTrigger value="categories" className="text-xs">Categories</TabsTrigger>
            <TabsTrigger value="settings" className="text-xs">Settings</TabsTrigger>
          </TabsList>

          {/* Channels Tab */}
          <TabsContent value="channels" className="space-y-4">
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Notification Channels</h4>
              <p className="text-xs text-muted-foreground">
                Choose how you want to receive notifications
              </p>
              
              {channels.map((channel) => {
                const Icon = channel.icon
                return (
                  <div key={channel.value} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <Label className="text-sm">{channel.label}</Label>
                      </div>
                    </div>
                    <Switch
                      checked={localPreferences.channels[channel.value]}
                      onCheckedChange={(checked) => handleChannelToggle(channel.value, checked)}
                    />
                  </div>
                )
              })}
            </div>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories" className="space-y-4">
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium">Notification Categories</h4>
                <p className="text-xs text-muted-foreground">
                  Customize notifications for each category
                </p>
              </div>

              {categories.map((category) => {
                const categoryPrefs = localPreferences.categories[category.value]
                if (!categoryPrefs) return null

                return (
                  <Card key={category.value} className="p-4">
                    <div className="space-y-3">
                      {/* Category header */}
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <Label className="text-sm font-medium">{category.label}</Label>
                            <Switch
                              checked={categoryPrefs.enabled}
                              onCheckedChange={(checked) => handleCategoryToggle(category.value, checked)}
                            />
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            {category.description}
                          </p>
                        </div>
                      </div>

                      {categoryPrefs.enabled && (
                        <>
                          {/* Channels for this category */}
                          <div>
                            <Label className="text-xs text-muted-foreground">Channels</Label>
                            <div className="flex gap-2 mt-1">
                              {channels.map((channel) => (
                                <Badge
                                  key={channel.value}
                                  variant={categoryPrefs.channels.includes(channel.value) ? 'default' : 'outline'}
                                  className="cursor-pointer text-xs"
                                  onClick={() => handleCategoryChannelToggle(
                                    category.value,
                                    channel.value,
                                    !categoryPrefs.channels.includes(channel.value)
                                  )}
                                >
                                  {channel.label}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          {/* Priorities for this category */}
                          <div>
                            <Label className="text-xs text-muted-foreground">Priority Levels</Label>
                            <div className="flex gap-2 mt-1">
                              {priorities.map((priority) => (
                                <Badge
                                  key={priority.value}
                                  variant={categoryPrefs.priority.includes(priority.value) ? 'default' : 'outline'}
                                  className="cursor-pointer text-xs"
                                  onClick={() => handlePriorityToggle(
                                    category.value,
                                    priority.value,
                                    !categoryPrefs.priority.includes(priority.value)
                                  )}
                                >
                                  {priority.label}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </Card>
                )
              })}
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-4">
            <div className="space-y-4">
              <h4 className="text-sm font-medium">General Settings</h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm">Group Similar Notifications</Label>
                    <p className="text-xs text-muted-foreground">
                      Combine similar notifications to reduce clutter
                    </p>
                  </div>
                  <Switch
                    checked={localPreferences.settings.groupSimilar}
                    onCheckedChange={(checked) => handleSettingChange('groupSimilar', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm">Auto Archive</Label>
                    <p className="text-xs text-muted-foreground">
                      Automatically archive old notifications
                    </p>
                  </div>
                  <Switch
                    checked={localPreferences.settings.autoArchive}
                    onCheckedChange={(checked) => handleSettingChange('autoArchive', checked)}
                  />
                </div>

                {localPreferences.settings.autoArchive && (
                  <div className="ml-4">
                    <Label className="text-xs text-muted-foreground">Archive after (days)</Label>
                    <Input
                      type="number"
                      min="1"
                      max="365"
                      value={localPreferences.settings.autoArchiveDays}
                      onChange={(e) => handleSettingChange('autoArchiveDays', parseInt(e.target.value))}
                      className="h-8 w-20 mt-1"
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm">Sound Notifications</Label>
                    <p className="text-xs text-muted-foreground">
                      Play sound for new notifications
                    </p>
                  </div>
                  <Switch
                    checked={localPreferences.settings.soundEnabled}
                    onCheckedChange={(checked) => handleSettingChange('soundEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm">Vibration</Label>
                    <p className="text-xs text-muted-foreground">
                      Vibrate for push notifications
                    </p>
                  </div>
                  <Switch
                    checked={localPreferences.settings.vibrationEnabled}
                    onCheckedChange={(checked) => handleSettingChange('vibrationEnabled', checked)}
                  />
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Action buttons */}
        <Separator />
        <div className="flex justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            disabled={isLoadingPreferences}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
          
          <div className="flex gap-2">
            {onClose && (
              <Button variant="outline" size="sm" onClick={onClose}>
                Cancel
              </Button>
            )}
            <Button
              size="sm"
              onClick={handleSave}
              disabled={!hasChanges || isLoadingPreferences}
            >
              Save Changes
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
