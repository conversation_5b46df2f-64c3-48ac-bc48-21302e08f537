'use client'

import { useState, useEffect } from 'react'
import { Bell, X, Shield, AlertTriangle, Info, CheckCircle, Clock } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useNotifications } from '@/lib/socket/client'
import { format } from 'date-fns'

interface Notification {
  id: string
  type: 'security' | 'session' | 'admin' | 'info'
  title: string
  message: string
  severity?: 'low' | 'medium' | 'high' | 'critical'
  timestamp: Date
  read?: boolean
  data?: any
}

export function RealTimeNotifications() {
  const socket = useNotifications()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  // Sync with socket notifications
  useEffect(() => {
    if (socket.notifications) {
      const formattedNotifications = socket.notifications.map((notif, index) => ({
        id: `${Date.now()}-${index}`,
        type: notif.type || 'info',
        title: notif.title || 'Notification',
        message: notif.message,
        severity: notif.severity,
        timestamp: new Date(notif.timestamp || Date.now()),
        read: false,
        data: notif.data
      }))
      
      setNotifications(formattedNotifications)
      setUnreadCount(formattedNotifications.filter(n => !n.read).length)
    }
  }, [socket.notifications])

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    )
    setUnreadCount(prev => Math.max(0, prev - 1))
  }

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(notif => ({ ...notif, read: true })))
    setUnreadCount(0)
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id))
    setUnreadCount(prev => {
      const notification = notifications.find(n => n.id === id)
      return notification && !notification.read ? Math.max(0, prev - 1) : prev
    })
  }

  const clearAll = () => {
    setNotifications([])
    setUnreadCount(0)
    socket.clearAll()
  }

  const getNotificationIcon = (type: string, severity?: string) => {
    switch (type) {
      case 'security':
        return severity === 'critical' || severity === 'high' 
          ? <AlertTriangle className="h-4 w-4 text-red-500" />
          : <Shield className="h-4 w-4 text-yellow-500" />
      case 'session':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'admin':
        return <Info className="h-4 w-4 text-purple-500" />
      default:
        return <CheckCircle className="h-4 w-4 text-green-500" />
    }
  }

  const getSeverityColor = (severity?: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 border-red-200 dark:bg-red-900/20 dark:border-red-800'
      case 'high':
        return 'bg-orange-100 border-orange-200 dark:bg-orange-900/20 dark:border-orange-800'
      case 'medium':
        return 'bg-yellow-100 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
      case 'low':
        return 'bg-blue-100 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
      default:
        return 'bg-gray-100 border-gray-200 dark:bg-gray-900/20 dark:border-gray-800'
    }
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">
                Notifications
                {socket.connected && (
                  <Badge variant="outline" className="ml-2 text-xs">
                    Live
                  </Badge>
                )}
              </CardTitle>
              {notifications.length > 0 && (
                <div className="flex gap-1">
                  {unreadCount > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={markAllAsRead}
                      className="text-xs h-6 px-2"
                    >
                      Mark all read
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearAll}
                    className="text-xs h-6 px-2"
                  >
                    Clear all
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {notifications.length === 0 ? (
              <div className="p-4 text-center text-sm text-muted-foreground">
                No notifications
              </div>
            ) : (
              <ScrollArea className="h-80">
                <div className="space-y-1 p-2">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors hover:bg-muted/50 ${
                        getSeverityColor(notification.severity)
                      } ${!notification.read ? 'ring-1 ring-primary/20' : ''}`}
                      onClick={() => markAsRead(notification.id)}
                    >
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex items-start gap-2 flex-1 min-w-0">
                          {getNotificationIcon(notification.type, notification.severity)}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <p className="text-sm font-medium truncate">
                                {notification.title}
                              </p>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                              )}
                            </div>
                            <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                              {notification.message}
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {format(notification.timestamp, 'MMM d, HH:mm')}
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            removeNotification(notification.id)
                          }}
                          className="h-6 w-6 p-0 flex-shrink-0"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  )
}

// Connection status indicator
export function ConnectionStatus() {
  const socket = useNotifications()

  if (!socket.connected && !socket.connecting) {
    return null
  }

  return (
    <div className="flex items-center gap-2 text-xs">
      {socket.connected ? (
        <>
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-green-600 dark:text-green-400">Live</span>
        </>
      ) : socket.connecting ? (
        <>
          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
          <span className="text-yellow-600 dark:text-yellow-400">Connecting...</span>
        </>
      ) : (
        <>
          <div className="w-2 h-2 bg-red-500 rounded-full" />
          <span className="text-red-600 dark:text-red-400">Offline</span>
        </>
      )}
    </div>
  )
}
