'use client'

import React, { useState, useEffect } from 'react'
import { Bell, Mail, MessageSquare, Smartphone, Globe, Save, RefreshCw } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useNotificationStore } from '@/stores/notificationStore'
import { useToast } from '@/components/providers/notification-provider'
import { cn } from '@/lib/utils'
import type { NotificationCategory, NotificationPriority, NotificationChannel } from '@/types/notifications'

interface NotificationSettingsProps {
  className?: string
  variant?: 'full' | 'compact'
}

// Channel configurations
const channels = [
  {
    id: 'in_app' as NotificationChannel,
    name: 'In-App',
    description: 'Notifications within the application',
    icon: Bell,
    available: true
  },
  {
    id: 'email' as NotificationChannel,
    name: 'Email',
    description: 'Email notifications to your registered email',
    icon: Mail,
    available: true
  },
  {
    id: 'sms' as NotificationChannel,
    name: 'SMS',
    description: 'Text messages to your phone',
    icon: MessageSquare,
    available: false // Not implemented yet
  },
  {
    id: 'push' as NotificationChannel,
    name: 'Push',
    description: 'Browser push notifications',
    icon: Smartphone,
    available: true
  }
]

// Category configurations
const categories = [
  {
    id: 'system' as NotificationCategory,
    name: 'System',
    description: 'System updates and maintenance',
    defaultEnabled: true
  },
  {
    id: 'security' as NotificationCategory,
    name: 'Security',
    description: 'Security alerts and warnings',
    defaultEnabled: true
  },
  {
    id: 'inventory' as NotificationCategory,
    name: 'Inventory',
    description: 'Stock and inventory updates',
    defaultEnabled: true
  },
  {
    id: 'orders' as NotificationCategory,
    name: 'Orders',
    description: 'Order status and updates',
    defaultEnabled: true
  },
  {
    id: 'delivery' as NotificationCategory,
    name: 'Delivery',
    description: 'Delivery status updates',
    defaultEnabled: true
  },
  {
    id: 'campaigns' as NotificationCategory,
    name: 'Campaigns',
    description: 'Sales and marketing campaigns',
    defaultEnabled: false
  },
  {
    id: 'users' as NotificationCategory,
    name: 'Users',
    description: 'User management notifications',
    defaultEnabled: true
  },
  {
    id: 'financial' as NotificationCategory,
    name: 'Financial',
    description: 'Payment and financial alerts',
    defaultEnabled: true
  },
  {
    id: 'reports' as NotificationCategory,
    name: 'Reports',
    description: 'Report generation and analytics',
    defaultEnabled: false
  },
  {
    id: 'maintenance' as NotificationCategory,
    name: 'Maintenance',
    description: 'System maintenance notifications',
    defaultEnabled: true
  }
]

// Priority configurations
const priorities = [
  {
    id: 'low' as NotificationPriority,
    name: 'Low',
    description: 'Non-urgent information',
    defaultEnabled: true
  },
  {
    id: 'medium' as NotificationPriority,
    name: 'Medium',
    description: 'Standard notifications',
    defaultEnabled: true
  },
  {
    id: 'high' as NotificationPriority,
    name: 'High',
    description: 'Important notifications',
    defaultEnabled: true
  },
  {
    id: 'critical' as NotificationPriority,
    name: 'Critical',
    description: 'Critical system alerts',
    defaultEnabled: true
  },
  {
    id: 'urgent' as NotificationPriority,
    name: 'Urgent',
    description: 'Urgent action required',
    defaultEnabled: true
  }
]

export function NotificationSettings({ className, variant = 'full' }: NotificationSettingsProps) {
  const { 
    preferences, 
    isLoadingPreferences, 
    fetchPreferences, 
    updatePreferences 
  } = useNotificationStore()
  const { showSuccess, showError } = useToast()

  const [localPreferences, setLocalPreferences] = useState<any>({
    channels: {},
    categories: {},
    priorities: {},
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00'
    },
    frequency: {
      digest: false,
      immediate: true
    }
  })

  const [hasChanges, setHasChanges] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  useEffect(() => {
    fetchPreferences()
  }, [fetchPreferences])

  useEffect(() => {
    if (preferences) {
      setLocalPreferences(preferences)
    }
  }, [preferences])

  const handleChannelToggle = (channelId: NotificationChannel, enabled: boolean) => {
    setLocalPreferences((prev: any) => ({
      ...prev,
      channels: {
        ...prev.channels,
        [channelId]: enabled
      }
    }))
    setHasChanges(true)
  }

  const handleCategoryToggle = (categoryId: NotificationCategory, enabled: boolean) => {
    setLocalPreferences((prev: any) => ({
      ...prev,
      categories: {
        ...prev.categories,
        [categoryId]: enabled
      }
    }))
    setHasChanges(true)
  }

  const handlePriorityToggle = (priorityId: NotificationPriority, enabled: boolean) => {
    setLocalPreferences((prev: any) => ({
      ...prev,
      priorities: {
        ...prev.priorities,
        [priorityId]: enabled
      }
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    setIsSaving(true)
    try {
      await updatePreferences(localPreferences)
      setHasChanges(false)
      showSuccess('Settings Saved', 'Your notification preferences have been updated.')
    } catch (error) {
      showError('Save Failed', 'Failed to save notification preferences.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleReset = () => {
    if (preferences) {
      setLocalPreferences(preferences)
      setHasChanges(false)
    }
  }

  if (isLoadingPreferences) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Notification Settings</CardTitle>
            <CardDescription>
              Manage how and when you receive notifications
            </CardDescription>
          </div>
          
          {hasChanges && (
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleReset}>
                Reset
              </Button>
              <Button size="sm" onClick={handleSave} disabled={isSaving}>
                {isSaving ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save
              </Button>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="channels" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="channels">Channels</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="priorities">Priorities</TabsTrigger>
          </TabsList>

          <TabsContent value="channels" className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-3">Notification Channels</h3>
              <div className="space-y-3">
                {channels.map((channel) => {
                  const ChannelIcon = channel.icon
                  const isEnabled = localPreferences.channels?.[channel.id] ?? true
                  
                  return (
                    <div key={channel.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <ChannelIcon className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="flex items-center gap-2">
                            <Label className="font-medium">{channel.name}</Label>
                            {!channel.available && (
                              <Badge variant="secondary" className="text-xs">
                                Coming Soon
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {channel.description}
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={isEnabled}
                        onCheckedChange={(checked) => handleChannelToggle(channel.id, checked)}
                        disabled={!channel.available}
                      />
                    </div>
                  )
                })}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="categories" className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-3">Notification Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => {
                  const isEnabled = localPreferences.categories?.[category.id] ?? category.defaultEnabled
                  
                  return (
                    <div key={category.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <Label className="font-medium">{category.name}</Label>
                        <p className="text-sm text-muted-foreground">
                          {category.description}
                        </p>
                      </div>
                      <Switch
                        checked={isEnabled}
                        onCheckedChange={(checked) => handleCategoryToggle(category.id, checked)}
                      />
                    </div>
                  )
                })}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="priorities" className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-3">Priority Levels</h3>
              <div className="space-y-2">
                {priorities.map((priority) => {
                  const isEnabled = localPreferences.priorities?.[priority.id] ?? priority.defaultEnabled
                  
                  return (
                    <div key={priority.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <Label className="font-medium">{priority.name}</Label>
                        <p className="text-sm text-muted-foreground">
                          {priority.description}
                        </p>
                      </div>
                      <Switch
                        checked={isEnabled}
                        onCheckedChange={(checked) => handlePriorityToggle(priority.id, checked)}
                      />
                    </div>
                  )
                })}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
