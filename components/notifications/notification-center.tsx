'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { <PERSON>, <PERSON>ting<PERSON>, Filter, Search, MoreVertical, Check, Archive, Trash2, Eye, EyeOff } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'
import { Checkbox } from '@/components/ui/checkbox'
import { useNotificationStore } from '@/stores/notificationStore'
import { NotificationItem } from './notification-item'
import { NotificationFilters } from './notification-filters'
import { NotificationPreferences } from './notification-preferences'
import { cn } from '@/lib/utils'
import type { NotificationCategory, NotificationPriority, NotificationStatus } from '@/types/notifications'

interface NotificationCenterProps {
  className?: string
  variant?: 'popover' | 'sidebar' | 'page'
  maxHeight?: string
  showHeader?: boolean
  showFilters?: boolean
  showPreferences?: boolean
}

export function NotificationCenter({
  className,
  variant = 'popover',
  maxHeight = '600px',
  showHeader = true,
  showFilters = true,
  showPreferences = true
}: NotificationCenterProps) {
  const {
    notifications,
    unreadCount,
    totalCount,
    isLoading,
    error,
    filters,
    pagination,
    isConnected,
    fetchNotifications,
    markAsRead,
    markMultipleAsRead,
    markAllAsRead,
    setFilters,
    clearFilters,
    loadMore
  } = useNotificationStore()

  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState('all')
  const [showFiltersPanel, setShowFiltersPanel] = useState(false)
  const [showPreferencesPanel, setShowPreferencesPanel] = useState(false)

  // Load notifications on mount
  useEffect(() => {
    fetchNotifications(true)
  }, [fetchNotifications])

  // Filter notifications based on search and active tab
  const filteredNotifications = useMemo(() => {
    let filtered = notifications

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(n => 
        n.title.toLowerCase().includes(query) ||
        n.message.toLowerCase().includes(query) ||
        n.description?.toLowerCase().includes(query)
      )
    }

    // Tab filter
    switch (activeTab) {
      case 'unread':
        filtered = filtered.filter(n => n.status === 'unread')
        break
      case 'important':
        filtered = filtered.filter(n => n.priority === 'high' || n.priority === 'critical' || n.priority === 'urgent')
        break
      case 'read':
        filtered = filtered.filter(n => n.status === 'read')
        break
      default:
        // 'all' - no additional filtering
        break
    }

    return filtered
  }, [notifications, searchQuery, activeTab])

  // Handle notification selection
  const handleSelectNotification = (notificationId: string, selected: boolean) => {
    setSelectedNotifications(prev => 
      selected 
        ? [...prev, notificationId]
        : prev.filter(id => id !== notificationId)
    )
  }

  const handleSelectAll = (selected: boolean) => {
    setSelectedNotifications(selected ? filteredNotifications.map(n => n.id) : [])
  }

  // Bulk actions
  const handleBulkMarkAsRead = async () => {
    if (selectedNotifications.length > 0) {
      await markMultipleAsRead(selectedNotifications)
      setSelectedNotifications([])
    }
  }

  const handleMarkAllAsRead = async () => {
    await markAllAsRead()
    setSelectedNotifications([])
  }

  // Get tab counts
  const tabCounts = useMemo(() => {
    const unread = notifications.filter(n => n.status === 'unread').length
    const important = notifications.filter(n => n.priority === 'high' || n.priority === 'critical' || n.priority === 'urgent').length
    const read = notifications.filter(n => n.status === 'read').length
    
    return { unread, important, read, all: notifications.length }
  }, [notifications])

  const containerClasses = cn(
    'flex flex-col',
    variant === 'popover' && 'w-96',
    variant === 'sidebar' && 'w-80 h-full',
    variant === 'page' && 'w-full max-w-4xl mx-auto',
    className
  )

  const contentClasses = cn(
    'flex flex-col',
    variant === 'popover' && 'max-h-[600px]',
    variant === 'sidebar' && 'h-full',
    variant === 'page' && 'min-h-[600px]'
  )

  return (
    <Card className={containerClasses}>
      {showHeader && (
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notifications
              {unreadCount > 0 && (
                <Badge variant="destructive" className="h-5 px-2 text-xs">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </Badge>
              )}
              {isConnected && (
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              )}
            </CardTitle>
            
            <div className="flex items-center gap-1">
              {showFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFiltersPanel(!showFiltersPanel)}
                  className={cn(showFiltersPanel && 'bg-muted')}
                >
                  <Filter className="h-4 w-4" />
                </Button>
              )}
              
              {showPreferences && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPreferencesPanel(!showPreferencesPanel)}
                  className={cn(showPreferencesPanel && 'bg-muted')}
                >
                  <Settings className="h-4 w-4" />
                </Button>
              )}
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleMarkAllAsRead} disabled={unreadCount === 0}>
                    <Check className="h-4 w-4 mr-2" />
                    Mark all as read
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={clearFilters}>
                    <Eye className="h-4 w-4 mr-2" />
                    Clear filters
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => fetchNotifications(true)}>
                    <Bell className="h-4 w-4 mr-2" />
                    Refresh
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search notifications..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>

          {/* Bulk actions */}
          {selectedNotifications.length > 0 && (
            <div className="flex items-center justify-between p-2 bg-muted rounded-md">
              <span className="text-sm text-muted-foreground">
                {selectedNotifications.length} selected
              </span>
              <div className="flex gap-1">
                <Button size="sm" variant="outline" onClick={handleBulkMarkAsRead}>
                  <Check className="h-3 w-3 mr-1" />
                  Mark read
                </Button>
                <Button size="sm" variant="outline" onClick={() => setSelectedNotifications([])}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </CardHeader>
      )}

      <CardContent className={contentClasses}>
        {/* Filters Panel */}
        {showFiltersPanel && (
          <div className="mb-4">
            <NotificationFilters
              filters={filters}
              onFiltersChange={setFilters}
              onClearFilters={clearFilters}
            />
          </div>
        )}

        {/* Preferences Panel */}
        {showPreferencesPanel && (
          <div className="mb-4">
            <NotificationPreferences onClose={() => setShowPreferencesPanel(false)} />
          </div>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="all" className="text-xs">
              All {tabCounts.all > 0 && `(${tabCounts.all})`}
            </TabsTrigger>
            <TabsTrigger value="unread" className="text-xs">
              Unread {tabCounts.unread > 0 && `(${tabCounts.unread})`}
            </TabsTrigger>
            <TabsTrigger value="important" className="text-xs">
              Important {tabCounts.important > 0 && `(${tabCounts.important})`}
            </TabsTrigger>
            <TabsTrigger value="read" className="text-xs">
              Read {tabCounts.read > 0 && `(${tabCounts.read})`}
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="flex-1 mt-4">
            {/* Select all checkbox */}
            {filteredNotifications.length > 0 && (
              <div className="flex items-center gap-2 mb-3 px-1">
                <Checkbox
                  checked={selectedNotifications.length === filteredNotifications.length}
                  onCheckedChange={handleSelectAll}
                />
                <span className="text-sm text-muted-foreground">
                  Select all ({filteredNotifications.length})
                </span>
              </div>
            )}

            {/* Notifications List */}
            <ScrollArea className="flex-1" style={{ maxHeight }}>
              {isLoading && notifications.length === 0 ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : error ? (
                <div className="text-center py-8 text-destructive">
                  <p>{error}</p>
                  <Button variant="outline" size="sm" onClick={() => fetchNotifications(true)} className="mt-2">
                    Try again
                  </Button>
                </div>
              ) : filteredNotifications.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No notifications found</p>
                  {searchQuery && (
                    <Button variant="outline" size="sm" onClick={() => setSearchQuery('')} className="mt-2">
                      Clear search
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-2">
                  {filteredNotifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      selected={selectedNotifications.includes(notification.id)}
                      onSelect={(selected) => handleSelectNotification(notification.id, selected)}
                      onMarkAsRead={() => markAsRead(notification.id)}
                      variant={variant}
                    />
                  ))}
                  
                  {/* Load more button */}
                  {pagination.hasMore && (
                    <div className="text-center py-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={loadMore}
                        disabled={isLoading}
                      >
                        {isLoading ? 'Loading...' : 'Load more'}
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
