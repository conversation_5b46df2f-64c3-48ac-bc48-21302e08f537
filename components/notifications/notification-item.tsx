'use client'

import React, { useState } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { 
  Bell, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  XCircle, 
  Package, 
  ShoppingCart, 
  Truck, 
  Megaphone, 
  Users, 
  DollarSign, 
  BarChart3, 
  Settings,
  Eye,
  EyeOff,
  MoreHorizontal,
  ExternalLink,
  Archive,
  Trash2
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent } from '@/components/ui/card'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'
import type { PlatformNotification, NotificationCategory, NotificationPriority } from '@/types/notifications'

interface NotificationItemProps {
  notification: PlatformNotification
  selected?: boolean
  onSelect?: (selected: boolean) => void
  onMarkAsRead?: () => void
  onArchive?: () => void
  onDelete?: () => void
  variant?: 'compact' | 'default' | 'detailed'
  showActions?: boolean
  showSelection?: boolean
}

// Category icons mapping
const categoryIcons: Record<NotificationCategory, React.ComponentType<{ className?: string }>> = {
  system: Settings,
  security: AlertTriangle,
  inventory: Package,
  orders: ShoppingCart,
  delivery: Truck,
  campaigns: Megaphone,
  users: Users,
  financial: DollarSign,
  reports: BarChart3,
  maintenance: Settings
}

// Priority colors mapping
const priorityColors: Record<NotificationPriority, string> = {
  low: 'text-blue-600 bg-blue-50 border-blue-200 dark:text-blue-400 dark:bg-blue-950 dark:border-blue-800',
  medium: 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-950 dark:border-yellow-800',
  high: 'text-orange-600 bg-orange-50 border-orange-200 dark:text-orange-400 dark:bg-orange-950 dark:border-orange-800',
  critical: 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-950 dark:border-red-800',
  urgent: 'text-purple-600 bg-purple-50 border-purple-200 dark:text-purple-400 dark:bg-purple-950 dark:border-purple-800'
}

// Category colors mapping
const categoryColors: Record<NotificationCategory, string> = {
  system: 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-950',
  security: 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-950',
  inventory: 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-950',
  orders: 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-950',
  delivery: 'text-indigo-600 bg-indigo-50 dark:text-indigo-400 dark:bg-indigo-950',
  campaigns: 'text-pink-600 bg-pink-50 dark:text-pink-400 dark:bg-pink-950',
  users: 'text-cyan-600 bg-cyan-50 dark:text-cyan-400 dark:bg-cyan-950',
  financial: 'text-emerald-600 bg-emerald-50 dark:text-emerald-400 dark:bg-emerald-950',
  reports: 'text-violet-600 bg-violet-50 dark:text-violet-400 dark:bg-violet-950',
  maintenance: 'text-amber-600 bg-amber-50 dark:text-amber-400 dark:bg-amber-950'
}

export function NotificationItem({
  notification,
  selected = false,
  onSelect,
  onMarkAsRead,
  onArchive,
  onDelete,
  variant = 'default',
  showActions = true,
  showSelection = true
}: NotificationItemProps) {
  const [isHovered, setIsHovered] = useState(false)
  
  const CategoryIcon = categoryIcons[notification.category]
  const isUnread = notification.status === 'unread'
  const isExpired = notification.expiresAt && new Date(notification.expiresAt) < new Date()
  
  // Get custom color from notification or use category default
  const customColor = notification.color
  const priorityColorClass = priorityColors[notification.priority]
  const categoryColorClass = categoryColors[notification.category]
  
  const handleClick = () => {
    if (isUnread && onMarkAsRead) {
      onMarkAsRead()
    }
  }
  
  const handleActionClick = (action: any) => {
    // Handle notification action clicks
    if (action.type === 'link' && action.url) {
      window.open(action.url, '_blank')
    } else if (action.action) {
      // Handle custom actions
      console.log('Custom action:', action.action, action.data)
    }
  }

  const cardClasses = cn(
    'group relative transition-all duration-200 cursor-pointer',
    'hover:shadow-md hover:scale-[1.01]',
    isUnread && 'ring-2 ring-primary/20 bg-primary/5',
    isExpired && 'opacity-60',
    selected && 'ring-2 ring-primary bg-primary/10',
    variant === 'compact' && 'p-3',
    variant === 'detailed' && 'p-4'
  )

  const contentClasses = cn(
    'flex gap-3',
    variant === 'compact' && 'items-center'
  )

  return (
    <Card 
      className={cardClasses}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
    >
      <CardContent className="p-3">
        <div className={contentClasses}>
          {/* Selection checkbox */}
          {showSelection && onSelect && (
            <div className="flex-shrink-0 pt-1">
              <Checkbox
                checked={selected}
                onCheckedChange={onSelect}
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          )}

          {/* Icon */}
          <div className="flex-shrink-0">
            <div className={cn(
              'w-10 h-10 rounded-full flex items-center justify-center',
              customColor ? `bg-${customColor}-50 text-${customColor}-600` : categoryColorClass
            )}>
              {notification.icon ? (
                <img src={notification.icon} alt="" className="w-6 h-6" />
              ) : (
                <CategoryIcon className="w-5 h-5" />
              )}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <div className="flex-1 min-w-0">
                {/* Title and badges */}
                <div className="flex items-center gap-2 mb-1">
                  <h4 className={cn(
                    'font-medium text-sm truncate',
                    isUnread ? 'text-foreground' : 'text-muted-foreground'
                  )}>
                    {notification.title}
                  </h4>
                  
                  {isUnread && (
                    <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0" />
                  )}
                  
                  {notification.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {notification.badge}
                    </Badge>
                  )}
                </div>

                {/* Message */}
                <p className={cn(
                  'text-sm text-muted-foreground',
                  variant === 'compact' ? 'line-clamp-1' : 'line-clamp-2'
                )}>
                  {notification.message}
                </p>

                {/* Description (detailed variant only) */}
                {variant === 'detailed' && notification.description && (
                  <p className="text-xs text-muted-foreground mt-1 line-clamp-3">
                    {notification.description}
                  </p>
                )}

                {/* Metadata */}
                <div className="flex items-center gap-2 mt-2">
                  {/* Category badge */}
                  <Badge variant="outline" className={cn('text-xs', categoryColorClass)}>
                    {notification.category}
                  </Badge>
                  
                  {/* Priority badge */}
                  {(notification.priority === 'high' || notification.priority === 'critical' || notification.priority === 'urgent') && (
                    <Badge className={cn('text-xs', priorityColorClass)}>
                      {notification.priority}
                    </Badge>
                  )}
                  
                  {/* Timestamp */}
                  <span className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                  </span>
                  
                  {/* Expiry warning */}
                  {isExpired && (
                    <Badge variant="destructive" className="text-xs">
                      Expired
                    </Badge>
                  )}
                </div>

                {/* Actions */}
                {notification.actions && notification.actions.length > 0 && (
                  <div className="flex gap-2 mt-3">
                    {notification.actions.slice(0, 2).map((action) => (
                      <Button
                        key={action.id}
                        size="sm"
                        variant={action.variant || 'outline'}
                        onClick={(e) => {
                          e.stopPropagation()
                          handleActionClick(action)
                        }}
                        className="text-xs h-7"
                      >
                        {action.type === 'link' && <ExternalLink className="w-3 h-3 mr-1" />}
                        {action.label}
                      </Button>
                    ))}
                    
                    {notification.actions.length > 2 && (
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button size="sm" variant="outline" className="text-xs h-7 px-2">
                            <MoreHorizontal className="w-3 h-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          {notification.actions.slice(2).map((action) => (
                            <DropdownMenuItem
                              key={action.id}
                              onClick={() => handleActionClick(action)}
                            >
                              {action.label}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    )}
                  </div>
                )}
              </div>

              {/* Quick actions */}
              {showActions && (isHovered || selected) && (
                <div className="flex-shrink-0">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {isUnread && onMarkAsRead && (
                        <DropdownMenuItem onClick={onMarkAsRead}>
                          <Eye className="h-4 w-4 mr-2" />
                          Mark as read
                        </DropdownMenuItem>
                      )}
                      
                      {!isUnread && (
                        <DropdownMenuItem onClick={() => {}}>
                          <EyeOff className="h-4 w-4 mr-2" />
                          Mark as unread
                        </DropdownMenuItem>
                      )}
                      
                      {onArchive && (
                        <DropdownMenuItem onClick={onArchive}>
                          <Archive className="h-4 w-4 mr-2" />
                          Archive
                        </DropdownMenuItem>
                      )}
                      
                      <DropdownMenuSeparator />
                      
                      {onDelete && (
                        <DropdownMenuItem onClick={onDelete} className="text-destructive">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </div>
          </div>

          {/* Image (if provided) */}
          {notification.image && variant !== 'compact' && (
            <div className="flex-shrink-0">
              <img 
                src={notification.image} 
                alt="" 
                className="w-16 h-16 rounded-lg object-cover"
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
