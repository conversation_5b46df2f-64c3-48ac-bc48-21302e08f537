'use client'

import React, { useState, useEffect } from 'react'
import { Bell, Settings } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { useNotificationStore } from '@/stores/notificationStore'
import { NotificationCenter } from './notification-center'
import { cn } from '@/lib/utils'

interface NotificationBellProps {
  className?: string
  variant?: 'default' | 'minimal'
  showBadge?: boolean
  showSettings?: boolean
  maxNotifications?: number
}

export function NotificationBell({
  className,
  variant = 'default',
  showBadge = true,
  showSettings = true,
  maxNotifications = 50
}: NotificationBellProps) {
  const {
    unreadCount,
    isConnected,
    fetchNotifications,
    addNotification,
    updateNotification,
    connectSocket,
    disconnectSocket
  } = useNotificationStore()

  const [isOpen, setIsOpen] = useState(false)
  const [hasNewNotification, setHasNewNotification] = useState(false)

  // Load notifications and connect socket on mount
  useEffect(() => {
    fetchNotifications(true)
    connectSocket()

    // Request notification permission
    if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }

    return () => {
      disconnectSocket()
    }
  }, [fetchNotifications, connectSocket, disconnectSocket])

  // Handle real-time notifications via Socket.IO
  useEffect(() => {
    // This would be connected to your Socket.IO client
    // For now, we'll simulate with a listener pattern
    
    const handleNewNotification = (notification: any) => {
      addNotification(notification)
      setHasNewNotification(true)
      
      // Auto-clear the "new" indicator after 3 seconds
      setTimeout(() => setHasNewNotification(false), 3000)
    }

    const handleNotificationUpdate = (data: any) => {
      updateNotification(data.id, data.updates)
    }

    // In a real implementation, you would connect to Socket.IO here
    // socket.on('notification', handleNewNotification)
    // socket.on('notificationUpdate', handleNotificationUpdate)

    // Cleanup
    return () => {
      // socket.off('notification', handleNewNotification)
      // socket.off('notificationUpdate', handleNotificationUpdate)
    }
  }, [addNotification, updateNotification])

  // Reset new notification indicator when popover opens
  useEffect(() => {
    if (isOpen) {
      setHasNewNotification(false)
    }
  }, [isOpen])

  const buttonClasses = cn(
    'relative',
    variant === 'minimal' && 'h-8 w-8 p-0',
    variant === 'default' && 'h-9 w-9',
    className
  )

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size={variant === 'minimal' ? 'sm' : 'icon'}
          className={buttonClasses}
        >
          <div className="relative">
            <Bell className={cn(
              'transition-all duration-200',
              variant === 'minimal' ? 'h-4 w-4' : 'h-5 w-5',
              hasNewNotification && 'animate-bounce',
              isConnected ? 'text-foreground' : 'text-muted-foreground'
            )} />
            
            {/* Unread count badge */}
            {showBadge && unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className={cn(
                  'absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs font-medium',
                  'animate-in zoom-in-50 duration-200',
                  hasNewNotification && 'animate-pulse'
                )}
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
            
            {/* Connection indicator */}
            {isConnected && (
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background animate-pulse" />
            )}
            
            {/* New notification indicator */}
            {hasNewNotification && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-primary rounded-full animate-ping" />
            )}
          </div>
          <span className="sr-only">
            Notifications {unreadCount > 0 && `(${unreadCount} unread)`}
          </span>
        </Button>
      </PopoverTrigger>
      
      <PopoverContent 
        className="w-96 p-0" 
        align="end"
        sideOffset={8}
      >
        <NotificationCenter
          variant="popover"
          maxHeight="500px"
          showHeader={true}
          showFilters={true}
          showPreferences={showSettings}
        />
      </PopoverContent>
    </Popover>
  )
}

// Compact version for mobile or space-constrained areas
export function NotificationBellCompact(props: Omit<NotificationBellProps, 'variant'>) {
  return <NotificationBell {...props} variant="minimal" />
}

// Notification bell with dropdown for desktop
export function NotificationBellDropdown({
  className,
  children,
  ...props
}: NotificationBellProps & { children?: React.ReactNode }) {
  const { unreadCount, isConnected } = useNotificationStore()
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className={cn('relative', className)}>
      <Button
        variant="ghost"
        onClick={() => setIsOpen(!isOpen)}
        className="relative h-9 w-9"
        {...props}
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
        {isConnected && (
          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
        )}
      </Button>
      
      {isOpen && (
        <div className="absolute top-full right-0 mt-2 z-50">
          <div className="bg-background border rounded-lg shadow-lg">
            <NotificationCenter
              variant="popover"
              maxHeight="400px"
              showHeader={true}
              showFilters={false}
              showPreferences={false}
            />
          </div>
        </div>
      )}
      
      {children}
    </div>
  )
}

// Notification summary for dashboard widgets
export function NotificationSummary({ className }: { className?: string }) {
  const { 
    unreadCount, 
    totalCount, 
    getNotificationsByPriority,
    getNotificationsByCategory 
  } = useNotificationStore()

  const criticalCount = getNotificationsByPriority('critical').length
  const urgentCount = getNotificationsByPriority('urgent').length
  const securityCount = getNotificationsByCategory('security').filter(n => n.status === 'unread').length

  return (
    <div className={cn('grid grid-cols-2 md:grid-cols-4 gap-4', className)}>
      <div className="text-center">
        <div className="text-2xl font-bold text-primary">{unreadCount}</div>
        <div className="text-xs text-muted-foreground">Unread</div>
      </div>
      
      <div className="text-center">
        <div className="text-2xl font-bold text-red-600">{criticalCount}</div>
        <div className="text-xs text-muted-foreground">Critical</div>
      </div>
      
      <div className="text-center">
        <div className="text-2xl font-bold text-purple-600">{urgentCount}</div>
        <div className="text-xs text-muted-foreground">Urgent</div>
      </div>
      
      <div className="text-center">
        <div className="text-2xl font-bold text-orange-600">{securityCount}</div>
        <div className="text-xs text-muted-foreground">Security</div>
      </div>
    </div>
  )
}

// Floating notification bell for mobile
export function FloatingNotificationBell({ className }: { className?: string }) {
  const { unreadCount } = useNotificationStore()
  const [isOpen, setIsOpen] = useState(false)

  if (unreadCount === 0) return null

  return (
    <div className={cn('fixed bottom-4 right-4 z-50', className)}>
      <Button
        size="lg"
        className="rounded-full h-14 w-14 shadow-lg"
        onClick={() => setIsOpen(true)}
      >
        <Bell className="h-6 w-6" />
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 flex items-center justify-center text-xs"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>
      
      {isOpen && (
        <div className="fixed inset-0 bg-background z-50 p-4">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold">Notifications</h2>
            <Button variant="ghost" onClick={() => setIsOpen(false)}>
              ×
            </Button>
          </div>
          <NotificationCenter
            variant="page"
            showHeader={false}
            showFilters={true}
            showPreferences={true}
          />
        </div>
      )}
    </div>
  )
}
