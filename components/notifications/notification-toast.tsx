'use client'

import React, { useEffect, useState } from 'react'
import { X, Bell, AlertTriangle, CheckCircle, Info, AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { useNotificationStore } from '@/stores/notificationStore'
import type { PlatformNotification, NotificationPriority, NotificationCategory } from '@/types/notifications'

interface NotificationToastProps {
  notification: PlatformNotification
  onDismiss?: () => void
  onAction?: (actionId: string) => void
  autoHide?: boolean
  hideDelay?: number
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
}

// Priority icons
const priorityIcons = {
  low: Info,
  medium: Bell,
  high: AlertCircle,
  critical: AlertTriangle,
  urgent: AlertTriangle
}

// Priority colors
const priorityColors = {
  low: 'bg-blue-50 border-blue-200 text-blue-800',
  medium: 'bg-gray-50 border-gray-200 text-gray-800',
  high: 'bg-orange-50 border-orange-200 text-orange-800',
  critical: 'bg-red-50 border-red-200 text-red-800',
  urgent: 'bg-red-50 border-red-200 text-red-800'
}

// Category colors
const categoryColors = {
  system: 'bg-gray-50 border-gray-200',
  security: 'bg-red-50 border-red-200',
  inventory: 'bg-blue-50 border-blue-200',
  orders: 'bg-green-50 border-green-200',
  delivery: 'bg-purple-50 border-purple-200',
  campaigns: 'bg-yellow-50 border-yellow-200',
  users: 'bg-indigo-50 border-indigo-200',
  financial: 'bg-emerald-50 border-emerald-200',
  reports: 'bg-cyan-50 border-cyan-200',
  maintenance: 'bg-orange-50 border-orange-200'
}

export function NotificationToast({
  notification,
  onDismiss,
  onAction,
  autoHide = true,
  hideDelay = 5000,
  position = 'top-right'
}: NotificationToastProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [isAnimating, setIsAnimating] = useState(false)

  const PriorityIcon = priorityIcons[notification.priority]
  const priorityColorClass = priorityColors[notification.priority]
  const categoryColorClass = categoryColors[notification.category]

  useEffect(() => {
    if (autoHide && hideDelay > 0) {
      const timer = setTimeout(() => {
        handleDismiss()
      }, hideDelay)

      return () => clearTimeout(timer)
    }
  }, [autoHide, hideDelay])

  const handleDismiss = () => {
    setIsAnimating(true)
    setTimeout(() => {
      setIsVisible(false)
      onDismiss?.()
    }, 300)
  }

  const handleActionClick = (actionId: string) => {
    onAction?.(actionId)
    handleDismiss()
  }

  if (!isVisible) return null

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
  }

  return (
    <div
      className={cn(
        'fixed z-50 w-96 max-w-sm transition-all duration-300',
        positionClasses[position],
        isAnimating ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
      )}
    >
      <Card className={cn(
        'shadow-lg border-l-4',
        priorityColorClass,
        categoryColorClass
      )}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            {/* Icon */}
            <div className="flex-shrink-0 mt-0.5">
              {notification.icon ? (
                <img src={notification.icon} alt="" className="w-5 h-5" />
              ) : (
                <PriorityIcon className="w-5 h-5" />
              )}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1">
                  <h4 className="text-sm font-semibold text-foreground">
                    {notification.title}
                  </h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    {notification.message}
                  </p>
                </div>

                {/* Priority badge */}
                <Badge 
                  variant={notification.priority === 'critical' || notification.priority === 'urgent' ? 'destructive' : 'secondary'}
                  className="text-xs"
                >
                  {notification.priority}
                </Badge>
              </div>

              {/* Actions */}
              {notification.actions && notification.actions.length > 0 && (
                <div className="flex gap-2 mt-3">
                  {notification.actions.map((action) => (
                    <Button
                      key={action.id}
                      variant={action.variant || 'outline'}
                      size="sm"
                      onClick={() => handleActionClick(action.id)}
                      className="text-xs"
                    >
                      {action.label}
                    </Button>
                  ))}
                </div>
              )}
            </div>

            {/* Dismiss button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="flex-shrink-0 h-6 w-6 p-0 hover:bg-background/80"
            >
              <X className="w-4 h-4" />
              <span className="sr-only">Dismiss</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Toast container for managing multiple toasts
export function NotificationToastContainer() {
  const { notifications, markAsRead, updateNotification } = useNotificationStore()
  const [toastNotifications, setToastNotifications] = useState<PlatformNotification[]>([])

  useEffect(() => {
    // Show only unread notifications as toasts
    const unreadNotifications = notifications
      .filter(n => n.status === 'unread' && n.priority !== 'low')
      .slice(0, 3) // Limit to 3 toasts at a time

    setToastNotifications(unreadNotifications)
  }, [notifications])

  const handleDismiss = (notificationId: string) => {
    markAsRead(notificationId)
    setToastNotifications(prev => prev.filter(n => n.id !== notificationId))
  }

  const handleAction = (notificationId: string, actionId: string) => {
    // Handle notification actions
    const notification = toastNotifications.find(n => n.id === notificationId)
    if (notification) {
      const action = notification.actions?.find(a => a.id === actionId)
      if (action) {
        if (action.url) {
          window.open(action.url, '_blank')
        }
        if (action.action) {
          // Handle custom actions
          console.log('Custom action:', action.action, action.data)
        }
      }
    }
    handleDismiss(notificationId)
  }

  return (
    <div className="fixed inset-0 pointer-events-none z-50">
      {toastNotifications.map((notification, index) => (
        <div
          key={notification.id}
          className="pointer-events-auto"
          style={{
            transform: `translateY(${index * 110}px)`
          }}
        >
          <NotificationToast
            notification={notification}
            onDismiss={() => handleDismiss(notification.id)}
            onAction={(actionId) => handleAction(notification.id, actionId)}
            position="top-right"
            autoHide={notification.priority !== 'critical' && notification.priority !== 'urgent'}
            hideDelay={notification.priority === 'high' ? 8000 : 5000}
          />
        </div>
      ))}
    </div>
  )
}

// Hook for showing custom toasts
export function useNotificationToast() {
  const { addNotification } = useNotificationStore()

  const showToast = (
    title: string,
    message: string,
    options?: {
      category?: NotificationCategory
      priority?: NotificationPriority
      actions?: Array<{
        id: string
        label: string
        variant?: 'primary' | 'secondary' | 'destructive' | 'outline'
        url?: string
        action?: string
      }>
      autoHide?: boolean
      hideDelay?: number
    }
  ) => {
    const notification: PlatformNotification = {
      id: `toast-${Date.now()}`,
      category: options?.category || 'system',
      type: 'toast',
      priority: options?.priority || 'medium',
      status: 'unread',
      title,
      message,
      target: { userIds: [] },
      data: {},
      actions: options?.actions?.map(action => ({
        ...action,
        type: action.url ? 'link' : 'button'
      })),
      createdAt: new Date(),
      createdBy: 'system',
      source: 'toast',
      channels: ['in_app'],
      deliveryStatus: { in_app: 'delivered' }
    }

    addNotification(notification)
  }

  return { showToast }
}
