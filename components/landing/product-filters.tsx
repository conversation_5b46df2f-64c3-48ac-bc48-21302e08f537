"use client"

import { useState, useEffect } from "react"
import {
  Search,
  DollarSign,
  X,
  ChevronDown,
  MapPin,
  Store,
  Tag,
  Package,
  RotateCcw,
  Filter,
  Star
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Slider } from "@/components/ui/slider"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from "@/components/ui/collapsible"
import { usePublicCategories } from "@/hooks/use-public-products"
import { useBranchStore } from "@/stores/branchStore"

// Filter Section Component
const FilterSection = ({
  title,
  icon: Icon,
  isOpen,
  onToggle,
  children
}: {
  title: string
  icon: any
  isOpen: boolean
  onToggle: () => void
  children: React.ReactNode
}) => (
  <Collapsible open={isOpen} onOpenChange={onToggle}>
    <CollapsibleTrigger className="flex w-full items-center justify-between py-3 hover:bg-muted/50 rounded-lg px-3 transition-colors">
      <div className="flex items-center gap-2">
        <Icon className="h-4 w-4" />
        <span className="font-medium">{title}</span>
      </div>
      <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
    </CollapsibleTrigger>
    <CollapsibleContent className="px-3 pb-3">
      {children}
    </CollapsibleContent>
  </Collapsible>
)

interface FilterState {
  searchQuery: string
  priceRange: [number, number]
  categories: string[]
  availability: string[]
  locations: string[]
  shops: string[]
  ratings: string[]
}

interface ProductFiltersProps {
  onFiltersChange?: (filters: {
    search?: string
    category?: string
    branchId?: string
    minPrice?: number
    maxPrice?: number
  }) => void
}

export default function ProductFilters({ onFiltersChange }: ProductFiltersProps = {}) {
  const { categories: dynamicCategories, isLoading: categoriesLoading } = usePublicCategories()
  const { branches, fetchBranches, isLoading: branchesLoading } = useBranchStore()

  // Fetch branches if not already loaded
  useEffect(() => {
    if (branches.length === 0) {
      fetchBranches()
    }
  }, [branches.length, fetchBranches])

  const [filters, setFilters] = useState<FilterState>({
    searchQuery: "",
    priceRange: [0, 5000],
    categories: [],
    availability: [],
    locations: [],
    shops: [],
    ratings: []
  })

  const [isOpen, setIsOpen] = useState({
    categories: true,
    price: true,
    availability: true,
    location: true,
    shops: false,
    ratings: true
  })

  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false)

  // Map dynamic categories to the expected format
  const categories = dynamicCategories.map(cat => ({
    id: cat.slug || cat._id,
    name: cat.name,
    count: cat.productCount || 0
  }))

  const availabilityOptions = [
    { id: "in-stock", name: "In Stock", count: 156 },
    { id: "low-stock", name: "Low Stock", count: 23 },
    { id: "out-of-stock", name: "Out of Stock", count: 8 }
  ]

  // Generate locations from real branch data
  const activeBranches = branches.filter(branch => branch.status === 'Active')

  // Get unique countries and regions
  const countries = Array.from(new Set(activeBranches.map(branch => branch.country)))
    .map(country => ({
      id: country.toLowerCase(),
      name: country,
      count: activeBranches.filter(branch => branch.country === country).reduce((sum, branch) => sum + (branch.totalProducts || 0), 0)
    }))

  const regions = Array.from(new Set(activeBranches.map(branch => branch.region)))
    .map(region => ({
      id: region.toLowerCase().replace(/\s+/g, '-'),
      name: region,
      count: activeBranches.filter(branch => branch.region === region).reduce((sum, branch) => sum + (branch.totalProducts || 0), 0)
    }))

  const locations = [...countries, ...regions]

  // Generate shops from real branch data
  const shops = activeBranches.map(branch => ({
    id: branch._id,
    name: branch.name,
    count: branch.totalProducts || 0
  }))

  const ratingOptions = [
    { id: "5-stars", name: "5 Stars", stars: 5, count: 89 },
    { id: "4-stars", name: "4 Stars & Up", stars: 4, count: 156 },
    { id: "3-stars", name: "3 Stars & Up", stars: 3, count: 178 },
    { id: "2-stars", name: "2 Stars & Up", stars: 2, count: 185 },
    { id: "1-star", name: "1 Star & Up", stars: 1, count: 187 }
  ]

  // Helper functions
  const updateFilter = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const toggleArrayFilter = (key: 'categories' | 'availability' | 'locations' | 'shops' | 'ratings', value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: prev[key].includes(value)
        ? prev[key].filter(item => item !== value)
        : [...prev[key], value]
    }))
  }

  const clearAllFilters = () => {
    setFilters({
      searchQuery: "",
      priceRange: [0, 5000],
      categories: [],
      availability: [],
      locations: [],
      shops: [],
      ratings: []
    })
  }

  const getActiveFilterCount = () => {
    return filters.categories.length +
           filters.availability.length +
           filters.locations.length +
           filters.shops.length +
           filters.ratings.length +
           (filters.searchQuery ? 1 : 0) +
           (filters.priceRange[0] > 0 || filters.priceRange[1] < 5000 ? 1 : 0)
  }

  const toggleSection = (section: keyof typeof isOpen) => {
    setIsOpen(prev => ({ ...prev, [section]: !prev[section] }))
  }

  const applyFilters = () => {
    if (onFiltersChange) {
      // Convert internal filter state to API filter format
      const apiFilters: any = {}

      if (filters.searchQuery) {
        apiFilters.search = filters.searchQuery
      }

      if (filters.categories.length > 0) {
        // Use the first selected category (API typically handles one category at a time)
        const selectedCategory = categories.find(cat => cat.id === filters.categories[0])
        if (selectedCategory) {
          apiFilters.category = selectedCategory.name
        }
      }

      if (filters.shops.length > 0) {
        // Use the first selected shop
        apiFilters.branchId = filters.shops[0]
      }

      if (filters.priceRange[0] > 0) {
        apiFilters.minPrice = filters.priceRange[0]
      }

      if (filters.priceRange[1] < 5000) {
        apiFilters.maxPrice = filters.priceRange[1]
      }

      onFiltersChange(apiFilters)
    }
  }

  return (
    <div className="space-y-6">
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products..."
            value={filters.searchQuery}
            onChange={(e) => updateFilter('searchQuery', e.target.value)}
            className="pl-10 h-11"
          />
        </div>

        {/* Active Filters */}
        {getActiveFilterCount() > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Active Filters ({getActiveFilterCount()})</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="h-8 px-2 text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Clear All
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {filters.searchQuery && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Search: {filters.searchQuery}
                  <X
                    className="h-3 w-3 cursor-pointer hover:text-destructive"
                    onClick={() => updateFilter('searchQuery', '')}
                  />
                </Badge>
              )}
              {(filters.priceRange[0] > 0 || filters.priceRange[1] < 5000) && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  ${filters.priceRange[0]} - ${filters.priceRange[1]}
                  <X
                    className="h-3 w-3 cursor-pointer hover:text-destructive"
                    onClick={() => updateFilter('priceRange', [0, 5000])}
                  />
                </Badge>
              )}
              {/* Category filters */}
              {filters.categories.map(categoryId => {
                const category = categories.find(c => c.id === categoryId)
                return category ? (
                  <Badge key={categoryId} variant="secondary" className="flex items-center gap-1">
                    {category.name}
                    <X
                      className="h-3 w-3 cursor-pointer hover:text-destructive"
                      onClick={() => toggleArrayFilter('categories', categoryId)}
                    />
                  </Badge>
                ) : null
              })}
              {/* Rating filters */}
              {filters.ratings.map(ratingId => {
                const rating = ratingOptions.find(r => r.id === ratingId)
                return rating ? (
                  <Badge key={ratingId} variant="secondary" className="flex items-center gap-1">
                    <div className="flex items-center">
                      {[...Array(rating.stars)].map((_, i) => (
                        <Star key={i} className="h-2 w-2 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <span className="ml-1">{rating.stars}+ Stars</span>
                    <X
                      className="h-3 w-3 cursor-pointer hover:text-destructive"
                      onClick={() => toggleArrayFilter('ratings', ratingId)}
                    />
                  </Badge>
                ) : null
              })}
            </div>
          </div>
        )}
        {/* Filter Sections */}
        <div className="space-y-1 border rounded-lg p-1">
          {/* Categories */}
          <FilterSection
            title="Categories"
            icon={Tag}
            isOpen={isOpen.categories}
            onToggle={() => toggleSection('categories')}
          >
            <div className="space-y-2 mt-2">
              {categoriesLoading ? (
                <div className="space-y-2">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center space-x-2">
                      <div className="w-4 h-4 bg-muted rounded animate-pulse"></div>
                      <div className="h-4 bg-muted rounded flex-1 animate-pulse"></div>
                    </div>
                  ))}
                </div>
              ) : categories.length > 0 ? (
                categories.map((category) => (
                  <div key={category.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category.id}`}
                        checked={filters.categories.includes(category.id)}
                        onCheckedChange={() => toggleArrayFilter('categories', category.id)}
                      />
                      <Label
                        htmlFor={`category-${category.id}`}
                        className="text-sm font-normal cursor-pointer flex-1"
                      >
                        {category.name}
                      </Label>
                    </div>
                    <span className="text-xs text-muted-foreground">({category.count})</span>
                  </div>
                ))
              ) : (
                <div className="text-sm text-muted-foreground">No categories available</div>
              )}
            </div>
          </FilterSection>

          <Separator />

          {/* Price Range */}
          <FilterSection
            title="Price Range"
            icon={DollarSign}
            isOpen={isOpen.price}
            onToggle={() => toggleSection('price')}
          >
            <div className="space-y-4 mt-4">
              <div className="px-2">
                <Slider
                  value={filters.priceRange}
                  onValueChange={(value) => updateFilter('priceRange', value as [number, number])}
                  max={100000}
                  min={0}
                  step={100}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-muted-foreground mt-2">
                  <span>${filters.priceRange[0]}</span>
                  <span>${filters.priceRange[1]}</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Min Price</Label>
                  <Input
                    type="number"
                    value={filters.priceRange[0]}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 0
                      updateFilter('priceRange', [value, filters.priceRange[1]])
                    }}
                    className="h-8"
                  />
                </div>
                <div>
                  <Label className="text-xs">Max Price</Label>
                  <Input
                    type="number"
                    value={filters.priceRange[1]}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 5000
                      updateFilter('priceRange', [filters.priceRange[0], value])
                    }}
                    className="h-8"
                  />
                </div>
              </div>
            </div>
          </FilterSection>

          <Separator />

          {/* Availability */}
          <FilterSection
            title="Availability"
            icon={Package}
            isOpen={isOpen.availability}
            onToggle={() => toggleSection('availability')}
          >
            <div className="space-y-2 mt-2">
              {availabilityOptions.map((option) => (
                <div key={option.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`availability-${option.id}`}
                      checked={filters.availability.includes(option.id)}
                      onCheckedChange={() => toggleArrayFilter('availability', option.id)}
                    />
                    <Label
                      htmlFor={`availability-${option.id}`}
                      className="text-sm font-normal cursor-pointer flex-1"
                    >
                      {option.name}
                    </Label>
                  </div>
                  <span className="text-xs text-muted-foreground">({option.count})</span>
                </div>
              ))}
            </div>
          </FilterSection>

          <Separator />

          {/* Customer Ratings */}
          <FilterSection
            title="Customer Ratings"
            icon={Star}
            isOpen={isOpen.ratings}
            onToggle={() => toggleSection('ratings')}
          >
            <div className="space-y-2 mt-2">
              {ratingOptions.map((rating) => (
                <div key={rating.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`rating-${rating.id}`}
                      checked={filters.ratings.includes(rating.id)}
                      onCheckedChange={() => toggleArrayFilter('ratings', rating.id)}
                    />
                    <Label
                      htmlFor={`rating-${rating.id}`}
                      className="text-sm font-normal cursor-pointer flex-1 flex items-center gap-1"
                    >
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${
                              i < rating.stars
                                ? 'fill-yellow-400 text-yellow-400'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="ml-1">{rating.name}</span>
                    </Label>
                  </div>
                  <span className="text-xs text-muted-foreground">({rating.count})</span>
                </div>
              ))}
            </div>
          </FilterSection>

          <Separator />

          {/* Location */}
          <FilterSection
            title="Location"
            icon={MapPin}
            isOpen={isOpen.location}
            onToggle={() => toggleSection('location')}
          >
            <div className="space-y-2 mt-2">
              {locations.map((location) => (
                <div key={location.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`location-${location.id}`}
                      checked={filters.locations.includes(location.id)}
                      onCheckedChange={() => toggleArrayFilter('locations', location.id)}
                    />
                    <Label
                      htmlFor={`location-${location.id}`}
                      className="text-sm font-normal cursor-pointer flex-1"
                    >
                      {location.name}
                    </Label>
                  </div>
                  <span className="text-xs text-muted-foreground">({location.count})</span>
                </div>
              ))}
            </div>
          </FilterSection>

          <Separator />

          {/* Shops */}
          <FilterSection
            title="Shops"
            icon={Store}
            isOpen={isOpen.shops}
            onToggle={() => toggleSection('shops')}
          >
            <div className="space-y-2 mt-2">
              {shops.map((shop) => (
                <div key={shop.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`shop-${shop.id}`}
                      checked={filters.shops.includes(shop.id)}
                      onCheckedChange={() => toggleArrayFilter('shops', shop.id)}
                    />
                    <Label
                      htmlFor={`shop-${shop.id}`}
                      className="text-sm font-normal cursor-pointer flex-1"
                    >
                      {shop.name}
                    </Label>
                  </div>
                  <span className="text-xs text-muted-foreground">({shop.count})</span>
                </div>
              ))}
            </div>
          </FilterSection>
        </div>

      {/* Quick Actions */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          className="flex-1"
          onClick={clearAllFilters}
          disabled={getActiveFilterCount() === 0}
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset Filters
        </Button>
        <Button className="flex-1" onClick={applyFilters}>
          <Filter className="h-4 w-4 mr-2" />
          Apply Filters
        </Button>
      </div>
    </div>
  )
}
