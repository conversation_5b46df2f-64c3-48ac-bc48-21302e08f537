"use client"

import { useState, useEffect } from "react"
import ProductCard from "./product-card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Grid3X3, List, SlidersHorizontal, Package } from "lucide-react"
import type { Product } from "@/types/frontend"
import { useBranchStore } from "@/stores/branchStore"

interface ProductGridProps {
  products: Product[]
  isLoading?: boolean
  error?: string | null
}

// Helper function to map backend product to display format
function mapProductForDisplay(product: Product, branches: any[] = []) {
  // Find the branch for this product
  const branch = branches.find(b => b._id === product.branchId)

  return {
    id: product._id || product.id,
    name: product.name,
    image: product.featuredImage || product.images?.[0] || "/placeholder.svg",
    price: product.price,
    originalPrice: product.originalPrice,
    salePrice: product.salePrice,
    currency: product.currency,
    availability: product.status as "In Stock" | "Low Stock" | "Out of Stock",
    shop: branch ? `${branch.name}` : `Fathahitech - ${product.branchId}`,
    region: branch ? branch.region : "Region",
    country: branch ? branch.country : "Malawi",
    category: product.categoryName || product.categoryId,
    isFeatured: product.isFeatured,
    isPromoted: product.isPromoted,
    isOnSale: product.isOnSale,
    promotionDescription: product.promotionDescription,
    branchId: product.branchId,
    branchName: branch ? branch.name : undefined,
    sku: product.sku,
    stock: product.stock,
  }
}

export default function ProductGrid({ products, isLoading = false, error = null }: ProductGridProps) {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortBy, setSortBy] = useState("name")
  const [itemsPerPage, setItemsPerPage] = useState(9)
  const [currentPage, setCurrentPage] = useState(1)

  // Get branches for proper display names
  const { branches, fetchBranches } = useBranchStore()


  // Fetch branches if not already loaded
  useEffect(() => {
    if (branches.length === 0) {
      fetchBranches()
    }
  }, [branches.length, fetchBranches])

  // Map backend products to display format
  const displayProducts = products.map(product => mapProductForDisplay(product, branches))

  const sortedProducts = [...displayProducts].sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.price - b.price
      case "price-high":
        return b.price - a.price
      case "name":
        return a.name.localeCompare(b.name)
      case "availability":
        return a.availability.localeCompare(b.availability)
      default:
        return 0
    }
  })

  const totalPages = Math.ceil(sortedProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedProducts = sortedProducts.slice(startIndex, startIndex + itemsPerPage)

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-background to-muted/30 rounded-2xl border shadow-sm p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-muted rounded w-48 mb-4"></div>
            <div className="h-4 bg-muted rounded w-32"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-muted rounded-2xl h-96"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-background to-muted/30 rounded-2xl border shadow-sm p-6">
          <div className="text-center">
            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Unable to load products</h3>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  // Empty state
  if (displayProducts.length === 0) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-background to-muted/30 rounded-2xl border shadow-sm p-6">
          <div className="text-center">
            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No products found</h3>
            <p className="text-muted-foreground">Try adjusting your filters or check back later.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Enhanced Toolbar */}
      <div className="bg-gradient-to-r from-background to-muted/30 rounded-2xl border shadow-sm">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 p-6">
          {/* Results Info */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-primary" />
              <span className="font-semibold text-lg">
                {sortedProducts.length} Products Found
              </span>
            </div>
            <span className="text-sm text-muted-foreground">
              Showing {startIndex + 1}-{Math.min(startIndex + itemsPerPage, sortedProducts.length)} of {sortedProducts.length}
            </span>
          </div>

          {/* Controls */}
          <div className="flex items-center gap-3">
            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px] border-2 hover:border-primary/50 transition-colors">
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name A-Z</SelectItem>
                <SelectItem value="price-low">Price: Low to High</SelectItem>
                <SelectItem value="price-high">Price: High to Low</SelectItem>
                <SelectItem value="availability">Availability</SelectItem>
              </SelectContent>
            </Select>

            {/* Items per page */}
            <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
              <SelectTrigger className="w-[70px] border-2 hover:border-primary/50 transition-colors">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="9">9</SelectItem>
                <SelectItem value="18">18</SelectItem>
                <SelectItem value="36">36</SelectItem>
              </SelectContent>
            </Select>

            {/* View Mode */}
            <div className="flex border-2 rounded-lg overflow-hidden">
              <Button
                variant={viewMode === "grid" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-none border-0"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "ghost"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-none border-0"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Products Grid/List */}
      <div className={
        viewMode === "grid"
          ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 justify-items-center"
          : "space-y-6"
      }>
        {paginatedProducts.map((product) => (
          <ProductCard key={product.id} product={product} viewMode={viewMode} />
        ))}
      </div>

      {/* Empty State */}
      {paginatedProducts.length === 0 && (
        <div className="text-center py-16">
          <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
            <Package className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="text-xl font-semibold mb-2">No products found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your filters or search terms to find what you're looking for.
          </p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Reset Filters
          </Button>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-8">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={currentPage === page ? "default" : "outline"}
              size="sm"
              onClick={() => setCurrentPage(page)}
              className="w-10"
            >
              {page}
            </Button>
          ))}

          <Button
            variant="outline"
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
