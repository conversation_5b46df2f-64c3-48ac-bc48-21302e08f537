"use client"

import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { usePromotionalProducts } from "@/hooks/use-promotional-products"
import { BranchAwareAddToCart } from "@/components/cart/branch-aware-add-to-cart"
import type { Product } from "@/types/frontend"
import Link from "next/link"

// Helper function to format currency
const formatCurrency = (price: number, currency: string = 'MWK') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency === 'MWK' ? 'USD' : currency, // Fallback for MWK
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price)
}

// Helper function to create slide data from product
const createSlideFromProduct = (product: Product, index: number) => {
  const isOnSale = product.isOnSale && product.salePrice
  const currentPrice = isOnSale ? product.salePrice! : product.price
  const originalPrice = isOnSale ? product.price : product.originalPrice

  // Create compelling title based on product promotion
  let title = product.name
  if (product.isPromoted && product.promotionDescription) {
    title = product.promotionDescription
  } else if (product.isOnSale) {
    const discount = originalPrice ? Math.round(((originalPrice - currentPrice) / originalPrice) * 100) : 0
    title = `${discount}% OFF - ${product.name}`
  }

  // Create description with pricing info
  let description = product.description
  if (isOnSale && originalPrice) {
    const savings = originalPrice - currentPrice
    description = `Save ${formatCurrency(savings)}! ${product.description.substring(0, 100)}...`
  } else {
    description = product.description.length > 150
      ? `${product.description.substring(0, 150)}...`
      : product.description
  }

  return {
    id: product.id,
    title,
    description,
    image: product.featuredImage || "/placeholder.svg",
    alt: `${product.name} - ${product.brand} ${product.model}`,
    cta: isOnSale ? "Shop Sale Now" : product.isPromoted ? "View Promotion" : "Shop Now",
    product,
    currentPrice,
    originalPrice,
    isOnSale,
    isPromoted: product.isPromoted
  }
}

// Fallback slides for when no promotional products are available
const fallbackSlides = [
  {
    id: "fallback-1",
    title: "Discover Amazing Products",
    description: "Explore our wide range of quality electronics and technology products.",
    image: "/images/laptop-hero.png",
    alt: "Featured products showcase",
    cta: "Browse Products",
    product: null,
    currentPrice: null,
    originalPrice: null,
    isOnSale: false,
    isPromoted: false
  },
  {
    id: "fallback-2",
    title: "Quality Electronics Store",
    description: "Find the latest technology and electronics at competitive prices.",
    image: "/images/tv-hero.png",
    alt: "Electronics store showcase",
    cta: "Shop Now",
    product: null,
    currentPrice: null,
    originalPrice: null,
    isOnSale: false,
    isPromoted: false
  }
]

export default function HeroSlider() {
  // Fetch promotional products
  const { products: promotionalProducts, isLoading, error } = usePromotionalProducts({
    limit: 6,
    autoFetch: true
  })

  // Create slides from promotional products or use fallback
  const slides = promotionalProducts.length > 0
    ? promotionalProducts.map(createSlideFromProduct)
    : fallbackSlides

  console.log('🎯 HeroSlider: Using slides:', slides.length, 'promotional products:', promotionalProducts.length)

  // Show loading state
  if (isLoading) {
    return (
      <section className="relative w-full py-0 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 overflow-hidden">
        <div className="flex items-center justify-center min-h-[500px] lg:min-h-[600px]">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground">Loading featured products...</p>
          </div>
        </div>
      </section>
    )
  }

  // Show error state with fallback
  if (error) {
    console.warn('🚨 HeroSlider error:', error, '- Using fallback slides')
  }

  return (
    <section className="relative w-full py-0 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 overflow-hidden">
      <Carousel className="w-full max-w-full mx-auto">
        <CarouselContent>
          {slides.map((slide, index) => (
            <CarouselItem key={slide.id}>
              <Card className="border-none shadow-none bg-transparent">
                <CardContent className="flex flex-col md:flex-row items-center justify-center p-0 md:min-h-[500px] lg:min-h-[600px] relative">
                  {/* Background pattern */}
                  <div className="absolute inset-0 z-0 opacity-10 pointer-events-none">
                    <svg className="w-full h-full" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <pattern
                        id={`pattern-circles-${slide.id}`}
                        x="0"
                        y="0"
                        width="20"
                        height="20"
                        patternUnits="userSpaceOnUse"
                      >
                        <circle
                          cx="10"
                          cy="10"
                          r="1"
                          fill="currentColor"
                          className="text-gray-300 dark:text-gray-700"
                        />
                      </pattern>
                      <rect x="0" y="0" width="100%" height="100%" fill={`url(#pattern-circles-${slide.id})`} />
                    </svg>
                  </div>

                  {/* Text content */}
                  <div className="relative z-10 w-full md:w-1/2 p-8 md:p-12 lg:p-16 text-center md:text-left space-y-4">
                    <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">{slide.title}</h1>
                    <p className="text-lg text-muted-foreground md:text-xl">{slide.description}</p>

                    {/* Pricing information for promotional products */}
                    {slide.product && (
                      <div className="flex flex-col sm:flex-row items-center md:items-start gap-2 text-center md:text-left">
                        <div className="text-3xl font-bold text-primary">
                          {formatCurrency(slide.currentPrice!, slide.product.currency)}
                        </div>
                        {slide.isOnSale && slide.originalPrice && (
                          <div className="text-xl text-muted-foreground line-through">
                            {formatCurrency(slide.originalPrice, slide.product.currency)}
                          </div>
                        )}
                        {slide.isOnSale && (
                          <div className="bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold">
                            SALE
                          </div>
                        )}
                        {slide.isPromoted && !slide.isOnSale && (
                          <div className="bg-blue-500 text-white px-2 py-1 rounded text-sm font-semibold">
                            FEATURED
                          </div>
                        )}
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row gap-3 mt-6">
                      {slide.product ? (
                        <>
                          <BranchAwareAddToCart
                            product={{
                              id: slide.product.id,
                              name: slide.product.name,
                              price: slide.currentPrice!,
                              image: slide.product.featuredImage,
                              category: slide.product.categoryName,
                              branchId: slide.product.branchId,
                              branchName: undefined, // Will be resolved
                              stock: slide.product.stock,
                              sku: slide.product.sku
                            }}
                            disabled={slide.product.status === "Out of Stock"}
                            className="flex-1 bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg transition-all"
                          >
                            {slide.cta}
                          </BranchAwareAddToCart>
                          <Button
                            asChild
                            variant="outline"
                            size="lg"
                            className="flex-1 shadow-md hover:shadow-lg transition-shadow"
                          >
                            <Link href={`/products/${slide.product.id}`}>
                              View Details
                            </Link>
                          </Button>
                        </>
                      ) : (
                        <Button
                          asChild
                          size="lg"
                          className="w-full shadow-md hover:shadow-lg transition-shadow"
                        >
                          <Link href="/products">
                            {slide.cta}
                          </Link>
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Image with overlay - now on the right */}
                  <div className="relative z-10 w-full md:w-1/2 h-64 md:h-auto flex items-center justify-center overflow-hidden">
                    <Image
                      src={slide.image || "/placeholder.svg"}
                      alt={slide.alt}
                      width={600}
                      height={400}
                      className="object-cover w-full h-full md:object-contain md:w-auto md:h-auto max-h-full max-w-full transition-transform duration-500 ease-in-out hover:scale-105"
                      priority={index === 0} // Prioritize loading for the first slide
                      loading={index === 0 ? "eager" : "lazy"} // Lazy load subsequent slides
                    />
                    {/* Adjusted gradient for image on the right */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent md:bg-gradient-to-l md:from-black/20 md:to-transparent"></div>
                  </div>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 z-20 shadow-md hover:shadow-lg transition-shadow" />
        <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 z-20 shadow-md hover:shadow-lg transition-shadow" />
      </Carousel>
    </section>
  )
}
