"use client"

import { Globe, CheckCircle, Award } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"

export default function ValueProposition() {
  const propositions = [
    {
      icon: <Globe className="h-10 w-10 text-primary" />,
      title: "Multi-Branch Availability",
      description: "Find products across our branches in Malawi and Zambia, ensuring local availability.",
    },
    {
      icon: <CheckCircle className="h-10 w-10 text-primary" />,
      title: "Verified Stock Status",
      description: "Real-time inventory updates to confirm product availability before you order.",
    },
    {
      icon: <Award className="h-10 w-10 text-primary" />,
      title: "Premium Grade Electronics",
      description: "We offer only the highest quality electronic systems and components.",
    },
  ]

  return (
    <section className="py-12 md:py-20 bg-white dark:bg-gray-950">
      <div className="container px-4 md:px-6 text-center">
        <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-12">Why Choose Fathahitech?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {propositions.map((prop, index) => (
            <Card
              key={index}
              className="p-6 flex flex-col items-center text-center shadow-lg hover:shadow-xl transition-shadow duration-300 rounded-xl border border-gray-200 dark:border-gray-800 bg-card dark:bg-card-dark"
            >
              <CardHeader className="p-0 mb-6">
                <div className="bg-primary/10 dark:bg-primary/20 p-4 rounded-full inline-flex items-center justify-center">
                  {prop.icon}
                </div>
              </CardHeader>
              <CardTitle className="text-2xl font-semibold mb-3">{prop.title}</CardTitle>
              <CardContent className="p-0 text-muted-foreground">
                <p className="text-base">{prop.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
