"use client"

import { Package, Search, Filter } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

interface EmptyStateProps {
  type?: "no-products" | "no-search-results" | "no-filter-results"
  title?: string
  description?: string
  onClearFilters?: () => void
  onBrowseAll?: () => void
}

export default function EmptyState({
  type = "no-products",
  title,
  description,
  onClearFilters,
  onBrowseAll
}: EmptyStateProps) {
  const getDefaultContent = () => {
    switch (type) {
      case "no-search-results":
        return {
          icon: Search,
          title: "No search results found",
          description: "We couldn't find any products matching your search. Try different keywords or browse our categories."
        }
      case "no-filter-results":
        return {
          icon: Filter,
          title: "No products match your filters",
          description: "Try adjusting your filters or clear them to see more products."
        }
      default:
        return {
          icon: Package,
          title: "No products available",
          description: "We're currently updating our inventory. Please check back soon for new products."
        }
    }
  }

  const defaultContent = getDefaultContent()
  const Icon = defaultContent.icon

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardContent className="flex flex-col items-center justify-center py-12 px-6 text-center">
        <div className="rounded-full bg-muted p-4 mb-4">
          <Icon className="h-8 w-8 text-muted-foreground" />
        </div>
        
        <h3 className="text-lg font-semibold mb-2">
          {title || defaultContent.title}
        </h3>
        
        <p className="text-muted-foreground mb-6 text-sm leading-relaxed">
          {description || defaultContent.description}
        </p>
        
        <div className="flex flex-col sm:flex-row gap-3 w-full">
          {type === "no-filter-results" && onClearFilters && (
            <Button 
              variant="outline" 
              onClick={onClearFilters}
              className="flex-1"
            >
              Clear Filters
            </Button>
          )}
          
          {onBrowseAll && (
            <Button 
              onClick={onBrowseAll}
              className="flex-1"
            >
              Browse All Products
            </Button>
          )}
          
          {!onClearFilters && !onBrowseAll && (
            <Button 
              onClick={() => window.location.reload()}
              className="flex-1"
            >
              Refresh Page
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
