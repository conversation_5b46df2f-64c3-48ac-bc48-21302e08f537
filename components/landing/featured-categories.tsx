// components/landing/featured-categories.tsx
"use client"

import Link from "next/link"
import { <PERSON>, CardContent, CardTitle } from "@/components/ui/card"
import Image from "next/image"
import { usePublicCategories } from "@/hooks/use-public-products"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"
import * as LucideIcons from "lucide-react"

// Helper function to get Lucide icon component
const getLucideIcon = (iconName: string | null | undefined) => {
  if (!iconName) return null

  // Convert kebab-case to PascalCase for Lucide icons
  const pascalCase = iconName
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('')

  // Get the icon component from Lucide
  const IconComponent = (LucideIcons as any)[pascalCase]
  return IconComponent || null
}

// Generate theme-based gradient backgrounds
const getThemeGradient = (index: number) => {
  const gradients = [
    'from-blue-500 to-blue-700',
    'from-purple-500 to-purple-700',
    'from-green-500 to-green-700',
    'from-orange-500 to-orange-700',
    'from-red-500 to-red-700',
    'from-indigo-500 to-indigo-700',
    'from-pink-500 to-pink-700',
    'from-teal-500 to-teal-700'
  ]
  return gradients[index % gradients.length]
}

export default function FeaturedCategories() {
  const { categories: backendCategories, isLoading } = usePublicCategories()

  // Category image mapping for consistent design (fallback)
  const categoryImages: Record<string, { image: string; description: string }> = {
    "Laptops": {
      image: "/images/category-laptop.png",
      description: "Powerful machines for work and play.",
    },
    "TV Sets": {
      image: "/images/category-tv.png",
      description: "Immersive visuals for your entertainment.",
    },
    "Server Systems": {
      image: "/images/category-server.png",
      description: "Robust solutions for business infrastructure.",
    },
    "Amplifiers": {
      image: "/images/category-amplifier.png",
      description: "Enhance your audio experience.",
    },
    "Network Devices": {
      image: "/images/category-router.png",
      description: "Stay connected with high-speed networking.",
    },
    "Security Software": {
      image: "/images/category-security.png",
      description: "Protect your digital world.",
    },
    "Application Software": {
      image: "/images/category-software.png",
      description: "Tools for productivity and creativity.",
    },
    "Desktops": {
      image: "/images/category-desktop.png",
      description: "Reliable performance for home and office.",
    },
  }

  // Map backend categories to display format with enhanced image support
  const categories = backendCategories.map((category, index) => ({
    id: category._id,
    name: category.name,
    slug: category.slug,
    productCount: category.productCount,
    description: category.description,
    // Enhanced image handling - prioritize database featuredImage over static fallbacks
    featuredImage: category.featuredImage,
    hasImage: !!(category.featuredImage || categoryImages[category.name]?.image),
    fallbackImage: categoryImages[category.name]?.image,
    themeGradient: getThemeGradient(index),
    // Icon handling - prioritize database icon over static fallbacks
    icon: category.icon,
    iconType: category.iconType || 'lucide',
    iconName: category.iconName,
    lucideIcon: getLucideIcon(category.iconName),
  })).slice(0, 8) // Show only first 8 categories to maintain design

  return (
    <section className="py-12 md:py-20 bg-gray-50 dark:bg-gray-900">
      <div className="container px-4 md:px-6 text-center">
        <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-12">Explore Our Top Categories</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {isLoading ? (
            // Loading skeleton maintaining the same design
            Array.from({ length: 8 }).map((_, index) => (
              <Card key={index} className="relative flex flex-col items-center justify-end h-64 overflow-hidden rounded-lg shadow-md">
                <Skeleton className="absolute inset-0 w-full h-full" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                <CardContent className="relative z-10 p-4 text-white text-left w-full">
                  <Skeleton className="h-6 w-3/4 mb-2 bg-white/20" />
                  <Skeleton className="h-4 w-full bg-white/10" />
                </CardContent>
              </Card>
            ))
          ) : categories.length > 0 ? (
            categories.map((category) => {
              const IconComponent = category.lucideIcon
              // Prioritize database featuredImage, then fallback to static images, then theme colors
              const backgroundImageSrc = category.featuredImage || category.fallbackImage
              const hasBackgroundImage = !!backgroundImageSrc

              return (
                <Link href={`/products?category=${encodeURIComponent(category.name)}`} key={category.id}>
                  <Card className="relative flex flex-col items-center justify-center h-64 overflow-hidden rounded-lg shadow-md transition-all hover:shadow-xl hover:scale-[1.02] group">
                    {/* Background Layer */}
                    {hasBackgroundImage ? (
                      <>
                        <Image
                          src={backgroundImageSrc}
                          alt={`${category.name} background`}
                          width={300}
                          height={200}
                          className="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                          loading="lazy"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                      </>
                    ) : (
                      <div className={cn(
                        "absolute inset-0 bg-gradient-to-br",
                        category.themeGradient
                      )}></div>
                    )}

                    {/* Content Layer */}
                    <CardContent className="relative z-10 p-6 text-white text-center w-full h-full flex flex-col items-center justify-center">
                      {/* Icon Section */}
                      {category.iconType === 'image' && category.icon ? (
                        <div className="mb-4">
                          <Image
                            src={category.icon}
                            alt={`${category.name} icon`}
                            width={48}
                            height={48}
                            className="w-12 h-12 object-contain filter brightness-0 invert"
                          />
                        </div>
                      ) : IconComponent ? (
                        <div className="mb-4">
                          <IconComponent className="w-12 h-12 text-white" />
                        </div>
                      ) : null}

                      {/* Category Info */}
                      <div className="text-center">
                        <CardTitle className="text-xl font-bold mb-2 leading-tight">
                          {category.name}
                          {category.productCount > 0 && (
                            <span className="block text-sm font-normal opacity-75 mt-1">
                              {category.productCount} {category.productCount === 1 ? 'Product' : 'Products'}
                            </span>
                          )}
                        </CardTitle>
                        <p className="text-sm opacity-90 line-clamp-2">{category.description}</p>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              )
            })
          ) : (
            <div className="col-span-full text-center text-muted-foreground">
              <p>No categories available at the moment.</p>
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
