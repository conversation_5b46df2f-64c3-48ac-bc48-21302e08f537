"use client"

import Image from "next/image"
import { Card, CardContent, CardDescription, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Heart,
  Eye,
  Star,
  MapPin,
  Package,
  Truck,
  Shield,
  Zap,
  Megaphone,
  Tag,
  Crown
} from "lucide-react"
import { BranchAwareAddToCart } from "@/components/cart/branch-aware-add-to-cart"
import Link from "next/link"
import { formatPrice } from "@/lib/currency"

interface ProductCardProps {
  product: {
    id: string
    name: string
    image: string
    price: number
    originalPrice?: number
    salePrice?: number
    currency?: string
    availability: "In Stock" | "Low Stock" | "Out of Stock"
    shop: string
    region: string
    country: string
    category: string
    isFeatured?: boolean
    isPromoted?: boolean
    isOnSale?: boolean
    promotionDescription?: string
    branchId?: string
    branchName?: string
    sku?: string
    stock?: number
  }
  viewMode?: "grid" | "list"
}

export default function ProductCard({ product, viewMode = "grid" }: ProductCardProps) {
  const getBadgeVariant = (availability: string) => {
    switch (availability) {
      case "In Stock":
        return "default"
      case "Low Stock":
        return "destructive"
      case "Out of Stock":
        return "secondary"
      default:
        return "outline"
    }
  }

  const getBadgeColors = (availability: string) => {
    switch (availability) {
      case "In Stock":
        return "bg-green-100 text-green-700 border-green-200"
      case "Low Stock":
        return "bg-yellow-100 text-yellow-700 border-yellow-200"
      case "Out of Stock":
        return "bg-red-100 text-red-700 border-red-200"
      default:
        return ""
    }
  }

  // Render promotional badges
  const renderPromotionalBadges = () => {
    const badges = []

    if (product.isFeatured) {
      badges.push(
        <Badge key="featured" className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white border-0 shadow-lg">
          <Crown className="h-3 w-3 mr-1" />
          Featured
        </Badge>
      )
    }

    if (product.isPromoted) {
      badges.push(
        <Badge key="promoted" className="bg-gradient-to-r from-blue-500 to-blue-700 text-white border-0 shadow-lg">
          <Megaphone className="h-3 w-3 mr-1" />
          Promoted
        </Badge>
      )
    }

    if (product.isOnSale) {
      badges.push(
        <Badge key="sale" className="bg-gradient-to-r from-red-500 to-red-700 text-white border-0 shadow-lg animate-pulse">
          <Tag className="h-3 w-3 mr-1" />
          On Sale
        </Badge>
      )
    }

    return badges
  }

  // Calculate price display
  const getPriceDisplay = () => {
    if (product.isOnSale && product.salePrice) {
      return {
        currentPrice: product.salePrice,
        originalPrice: product.price,
        savings: product.price - product.salePrice,
        savingsPercent: Math.round(((product.price - product.salePrice) / product.price) * 100)
      }
    } else if (product.originalPrice && product.originalPrice > product.price) {
      return {
        currentPrice: product.price,
        originalPrice: product.originalPrice,
        savings: product.originalPrice - product.price,
        savingsPercent: Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
      }
    }
    return {
      currentPrice: product.price,
      originalPrice: null,
      savings: 0,
      savingsPercent: 0
    }
  }

  const priceInfo = getPriceDisplay()

  if (viewMode === "list") {
    return (
      <Card className="group flex overflow-hidden transition-all duration-300 hover:shadow-2xl shadow-md rounded-2xl border-0 bg-gradient-to-r from-background to-muted/10">
        {/* Featured Image Section */}
        <div className="relative w-80 h-64 flex-shrink-0 overflow-hidden bg-gradient-to-br from-muted/50 to-muted">
          <Link href={`/products/${product.id}`} className="block w-full h-full">
            <Image
              src={product.image || "/placeholder.svg"}
              alt={product.name}
              fill
              className="object-contain p-6 transition-all duration-500 ease-out group-hover:scale-110"
              loading="lazy"
            />

            {/* Image Overlay */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 transition-all duration-300" />

            {/* Quick Action Buttons */}
            <div className="absolute top-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
              <Button size="icon" variant="secondary" className="h-8 w-8 rounded-full shadow-lg">
                <Heart className="h-4 w-4" />
              </Button>
              <Button size="icon" variant="secondary" className="h-8 w-8 rounded-full shadow-lg">
                <Eye className="h-4 w-4" />
              </Button>
            </div>

            {/* Promotional Badges */}
            <div className="absolute top-4 left-4 flex flex-col gap-2">
              {renderPromotionalBadges()}
            </div>

            {/* Availability Badge */}
            <div className="absolute bottom-4 left-4">
              <Badge
                variant={getBadgeVariant(product.availability)}
                className={`${getBadgeColors(product.availability)} shadow-lg border-0 font-medium`}
              >
                <Package className="h-3 w-3 mr-1" />
                {product.availability}
              </Badge>
            </div>
          </Link>
        </div>

        {/* Detailed Content Section */}
        <CardContent className="flex-1 p-8 flex flex-col justify-between">
          <div className="space-y-6">
            {/* Header Section */}
            <div className="space-y-3">
              <div className="flex items-start justify-between">
                <CardTitle className="text-2xl font-bold line-clamp-2 group-hover:text-primary transition-colors pr-4">
                  {product.name}
                </CardTitle>
                <div className="flex flex-col items-end space-y-1">
                  <span className="text-3xl font-bold text-primary">
                    {formatPrice(priceInfo.currentPrice, product.currency || 'MWK')}
                  </span>
                  {priceInfo.originalPrice && (
                    <span className="text-lg text-muted-foreground line-through">
                      {formatPrice(priceInfo.originalPrice, product.currency || 'MWK')}
                    </span>
                  )}
                </div>
              </div>

              {/* Rating & Reviews */}
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className={`h-4 w-4 ${i < 4 ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} />
                    ))}
                  </div>
                  <span className="text-sm font-medium">4.2</span>
                  <span className="text-sm text-muted-foreground">(156 reviews)</span>
                </div>
                {priceInfo.savings > 0 && (
                  <div className="flex items-center gap-1 text-sm text-green-600">
                    <Zap className="h-3 w-3" />
                    <span className="font-medium">
                      Save {formatPrice(priceInfo.savings, product.currency || 'MWK')} ({priceInfo.savingsPercent}% off)
                    </span>
                  </div>
                )}
                {product.promotionDescription && (
                  <div className="text-sm text-orange-600 font-medium">
                    🎉 {product.promotionDescription}
                  </div>
                )}
              </div>
            </div>

            {/* Product Details */}
            <div className="space-y-4">
              <div className="flex items-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{product.shop}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-muted-foreground" />
                  <span className="text-primary font-medium">{product.category}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{product.region}, {product.country}</span>
                </div>
              </div>

              {/* Product Description */}
              <CardDescription className="text-base leading-relaxed">
                High-quality {product.category.toLowerCase()} featuring advanced technology and premium build quality.
                Perfect for professional and personal use with excellent performance and reliability.
              </CardDescription>

              {/* Features */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
                  <Truck className="h-3 w-3 mr-1" />
                  Free Delivery
                </Badge>
                <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200">
                  <Shield className="h-3 w-3 mr-1" />
                  2 Year Warranty
                </Badge>
                <Badge variant="secondary" className="bg-purple-50 text-purple-700 border-purple-200">
                  <Zap className="h-3 w-3 mr-1" />
                  Fast Setup
                </Badge>
              </div>
            </div>
          </div>

          {/* Action Section */}
          <div className="flex items-center gap-4 pt-6 border-t">
            <Button
              variant="outline"
              size="lg"
              className="flex-1 border-2 hover:bg-primary hover:text-primary-foreground transition-all duration-300"
              asChild
            >
              <Link href={`/products/${product.id}`}>
                <Eye className="h-4 w-4 mr-2" />
                View Full Details
              </Link>
            </Button>
            <BranchAwareAddToCart
              product={{
                id: product.id,
                name: product.name,
                price: priceInfo.currentPrice, // Use current price (sale price if on sale)
                image: product.image,
                category: product.category,
                branchId: product.branchId,
                branchName: product.branchName || product.shop,
                stock: product.stock || (product.availability === "Out of Stock" ? 0 : 50),
                sku: product.sku
              }}
              disabled={product.availability === "Out of Stock"}
              className="flex-1 bg-primary hover:bg-primary/90 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              Add to Cart - {formatPrice(priceInfo.currentPrice, product.currency || 'MWK')}
            </BranchAwareAddToCart>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="group flex flex-col overflow-hidden transition-all duration-300 hover:shadow-2xl shadow-md rounded-2xl border-0 bg-gradient-to-b from-background to-muted/20 w-full max-w-sm mx-auto">
      {/* Enhanced Image Section */}
      <div className="relative overflow-hidden">
        <Link
          href={`/products/${product.id}`}
          className="relative block w-full h-72 bg-gradient-to-br from-muted/50 to-muted overflow-hidden"
        >
          <Image
            src={product.image || "/placeholder.svg"}
            alt={product.name}
            fill
            className="object-contain p-6 transition-all duration-500 ease-out group-hover:scale-110 group-hover:rotate-1"
            loading="lazy"
          />

          {/* Overlay Actions */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300" />
          <div className="absolute top-4 right-4 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-4 group-hover:translate-x-0">
            <Button size="icon" variant="secondary" className="h-8 w-8 rounded-full shadow-lg">
              <Heart className="h-4 w-4" />
            </Button>
            <Button size="icon" variant="secondary" className="h-8 w-8 rounded-full shadow-lg">
              <Eye className="h-4 w-4" />
            </Button>
          </div>

          {/* Promotional Badges */}
          <div className="absolute top-4 left-4 flex flex-col gap-2">
            {renderPromotionalBadges()}
          </div>

          {/* Availability Badge */}
          <div className="absolute bottom-4 left-4">
            <Badge
              variant={getBadgeVariant(product.availability)}
              className={`${getBadgeColors(product.availability)} shadow-lg border-0 font-medium`}
            >
              <Package className="h-3 w-3 mr-1" />
              {product.availability}
            </Badge>
          </div>

          {/* Quick Features */}
          <div className="absolute bottom-4 left-4 flex gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0">
            <Badge variant="secondary" className="text-xs bg-white/90 text-gray-700">
              <Truck className="h-3 w-3 mr-1" />
              Fast Delivery
            </Badge>
            <Badge variant="secondary" className="text-xs bg-white/90 text-gray-700">
              <Shield className="h-3 w-3 mr-1" />
              Warranty
            </Badge>
          </div>
        </Link>
      </div>

      {/* Enhanced Content Section */}
      <CardContent className="p-5 flex-1 flex flex-col space-y-3">
        {/* Product Title & Rating */}
        <div className="space-y-2">
          <CardTitle className="text-lg font-bold line-clamp-2 group-hover:text-primary transition-colors leading-tight">
            {product.name}
          </CardTitle>
          <div className="flex items-center gap-2 flex-wrap">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className={`h-3 w-3 ${i < 4 ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} />
              ))}
            </div>
            <span className="text-xs text-muted-foreground">(4.2)</span>
            <span className="text-xs text-muted-foreground">•</span>
            <span className="text-xs text-muted-foreground">156 reviews</span>
          </div>
        </div>

        {/* Location & Category */}
        <div className="flex items-center gap-2 text-xs text-muted-foreground flex-wrap">
          <div className="flex items-center gap-1">
            <MapPin className="h-3 w-3" />
            <span className="truncate">{product.shop}</span>
          </div>
          <span>•</span>
          <span className="font-medium text-primary truncate">{product.category}</span>
        </div>

        {/* Price Section */}
        <div className="space-y-1">
          <div className="flex items-center gap-2 flex-wrap">
            <span className="text-2xl font-bold text-primary">
              {formatPrice(priceInfo.currentPrice, product.currency || 'MWK')}
            </span>
            {priceInfo.originalPrice && (
              <span className="text-sm text-muted-foreground line-through">
                {formatPrice(priceInfo.originalPrice, product.currency || 'MWK')}
              </span>
            )}
          </div>
          {priceInfo.savings > 0 && (
            <div className="flex items-center gap-1 text-xs text-green-600">
              <Zap className="h-3 w-3" />
              <span>Save {formatPrice(priceInfo.savings, product.currency || 'MWK')} ({priceInfo.savingsPercent}% off)</span>
            </div>
          )}
          {product.promotionDescription && (
            <div className="text-xs text-orange-600 font-medium">
              🎉 {product.promotionDescription}
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-2 pt-2">
          <Button
            variant="outline"
            size="sm"
            className="w-full border-2 hover:bg-primary hover:text-primary-foreground transition-all duration-300"
            asChild
          >
            <Link href={`/products/${product.id}`}>
              <Eye className="h-3 w-3 mr-2" />
              View Details
            </Link>
          </Button>
          <BranchAwareAddToCart
            product={{
              id: product.id,
              name: product.name,
              price: priceInfo.currentPrice, // Use current price (sale price if on sale)
              image: product.image,
              category: product.category,
              branchId: product.branchId,
              branchName: product.branchName || product.shop,
              stock: product.stock || (product.availability === "Out of Stock" ? 0 : 50),
              sku: product.sku
            }}
            disabled={product.availability === "Out of Stock"}
            className="w-full bg-primary hover:bg-primary/90 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            Add to Cart
          </BranchAwareAddToCart>
        </div>
      </CardContent>
    </Card>
  )
}
