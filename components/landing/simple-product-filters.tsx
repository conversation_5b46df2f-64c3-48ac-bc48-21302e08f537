"use client"

import { useEffect, useState } from "react"
import { MapPin, Store, Tag, ChevronDown, X, Filter } from "lucide-react"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useBranchStore } from "@/stores/branchStore"
import { usePublicCategories } from "@/hooks/use-public-products"

interface FilterState {
  country: string | null
  region: string | null
  branchId: string | null
  categoryId: string | null
}

interface SimpleProductFiltersProps {
  onFiltersChange?: (filters: {
    search?: string
    category?: string
    branchId?: string
    country?: string
    region?: string
  }) => void
}

export default function SimpleProductFilters({ onFiltersChange }: SimpleProductFiltersProps = {}) {
  const { branches, fetchBranches } = useBranchStore()
  const { categories, isLoading: categoriesLoading } = usePublicCategories()

  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    country: null,
    region: null,
    branchId: null,
    categoryId: null
  })

  // Fetch branches on component mount
  useEffect(() => {
    if (branches.length === 0) {
      fetchBranches()
    }
  }, [branches.length, fetchBranches])

  // Apply filters only when explicitly called (not automatically)
  const applyCurrentFilters = () => {
    if (onFiltersChange) {
      const apiFilters: any = {}

      if (filters.categoryId) {
        const selectedCategory = categories.find(cat => cat._id === filters.categoryId)
        if (selectedCategory) {
          apiFilters.category = selectedCategory.name
        }
      }

      if (filters.branchId) {
        apiFilters.branchId = filters.branchId
      }

      if (filters.country) {
        apiFilters.country = filters.country
      }

      if (filters.region) {
        apiFilters.region = filters.region
      }

      onFiltersChange(apiFilters)
    }
  }

  // Helper functions
  const clearAllFilters = () => {
    setFilters({
      country: null,
      region: null,
      branchId: null,
      categoryId: null
    })
    // Apply the cleared filters immediately
    if (onFiltersChange) {
      onFiltersChange({})
    }
  }

  const updateFilter = (key: keyof FilterState, value: string | null) => {
    setFilters(prev => {
      const newFilters = { ...prev, [key]: value }

      // Clear dependent filters when parent changes
      if (key === 'country') {
        newFilters.region = null
        newFilters.branchId = null
      } else if (key === 'region') {
        newFilters.branchId = null
      }

      return newFilters
    })
  }

  const getActiveFilterCount = () => {
    return Object.values(filters).filter(value => value !== null).length
  }

  // Process branch data
  const activeBranches = branches.filter(branch => branch.status === 'Active')
  const countries = Array.from(new Set(activeBranches.map(branch => branch.country)))

  // Get regions based on selected country
  const availableRegions = filters.country
    ? Array.from(new Set(activeBranches
        .filter(branch => branch.country === filters.country)
        .map(branch => branch.region)))
    : []

  // Get branches based on selected country/region
  const availableBranches = activeBranches.filter(branch => {
    if (filters.country && branch.country !== filters.country) return false
    if (filters.region && branch.region !== filters.region) return false
    return true
  })

  // Get selected display names
  const getSelectedCountryName = () => filters.country || "Country"
  const getSelectedRegionName = () => filters.region || "Region/City"
  const getSelectedBranchName = () => {
    if (!filters.branchId) return "Shop"
    const branch = branches.find(b => b._id === filters.branchId)
    return branch ? branch.name : "Shop"
  }
  const getSelectedCategoryName = () => {
    if (!filters.categoryId) return "Category"
    const category = categories.find(c => c._id === filters.categoryId)
    return category ? category.name : "Category"
  }

  return (
    <Card className="w-full sticky top-16 z-30 rounded-none border-x-0 border-t-0 shadow-md">
      <CardContent className="py-4 px-6">
        {/* Main filter row - single line layout */}
        <div className="flex items-center justify-center gap-2 md:gap-3 lg:gap-4 flex-nowrap overflow-x-auto pb-1">
          <span className="font-semibold text-sm text-muted-foreground hidden lg:block whitespace-nowrap">Filter by:</span>

          {/* Country Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant={filters.country ? "default" : "outline"}
                size="sm"
                className="flex items-center gap-1 bg-transparent shadow-sm hover:shadow-md transition-shadow whitespace-nowrap"
              >
                <MapPin className="h-3 w-3" />
                {getSelectedCountryName()} <ChevronDown className="ml-1 h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {countries.map((country) => (
                <DropdownMenuItem
                  key={country}
                  onClick={() => updateFilter('country', country)}
                  className={filters.country === country ? "bg-accent" : ""}
                >
                  {country}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Region/City Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant={filters.region ? "default" : "outline"}
                size="sm"
                className="flex items-center gap-1 bg-transparent shadow-sm hover:shadow-md transition-shadow whitespace-nowrap"
                disabled={!filters.country}
              >
                <MapPin className="h-3 w-3" />
                {getSelectedRegionName()} <ChevronDown className="ml-1 h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {availableRegions.length > 0 ? (
                availableRegions.map((region) => (
                  <DropdownMenuItem
                    key={region}
                    onClick={() => updateFilter('region', region)}
                    className={filters.region === region ? "bg-accent" : ""}
                  >
                    {region}
                  </DropdownMenuItem>
                ))
              ) : (
                <DropdownMenuItem disabled>
                  {filters.country ? "No regions available" : "Select a country first"}
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Shop/Branch Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant={filters.branchId ? "default" : "outline"}
                size="sm"
                className="flex items-center gap-1 bg-transparent shadow-sm hover:shadow-md transition-shadow whitespace-nowrap"
              >
                <Store className="h-3 w-3" />
                {getSelectedBranchName()} <ChevronDown className="ml-1 h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {availableBranches.length > 0 ? (
                availableBranches.map((branch) => (
                  <DropdownMenuItem
                    key={branch._id}
                    onClick={() => updateFilter('branchId', branch._id)}
                    className={filters.branchId === branch._id ? "bg-accent" : ""}
                  >
                    <div className="flex flex-col">
                      <span>{branch.name}</span>
                      <span className="text-xs text-muted-foreground">{branch.location}</span>
                    </div>
                  </DropdownMenuItem>
                ))
              ) : (
                <DropdownMenuItem disabled>
                  No shops available
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Category Filter */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant={filters.categoryId ? "default" : "outline"}
                size="sm"
                className="flex items-center gap-1 bg-transparent shadow-sm hover:shadow-md transition-shadow whitespace-nowrap"
              >
                <Tag className="h-3 w-3" />
                {getSelectedCategoryName()} <ChevronDown className="ml-1 h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {categoriesLoading ? (
                <DropdownMenuItem disabled>Loading categories...</DropdownMenuItem>
              ) : categories.length > 0 ? (
                categories.map((category) => (
                  <DropdownMenuItem
                    key={category._id}
                    onClick={() => updateFilter('categoryId', category._id)}
                    className={filters.categoryId === category._id ? "bg-accent" : ""}
                  >
                    {category.name}
                    {category.productCount > 0 && (
                      <span className="ml-auto text-xs text-muted-foreground">
                        ({category.productCount})
                      </span>
                    )}
                  </DropdownMenuItem>
                ))
              ) : (
                <DropdownMenuItem disabled>No categories available</DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Separator */}
          <div className="hidden lg:block w-px h-6 bg-border"></div>

          {/* Apply and Clear Buttons */}
          <Button
            onClick={applyCurrentFilters}
            disabled={getActiveFilterCount() === 0}
            size="sm"
            className="flex items-center gap-1 whitespace-nowrap"
          >
            <Filter className="h-3 w-3" />
            Apply
          </Button>
          <Button
            variant="ghost"
            onClick={clearAllFilters}
            disabled={getActiveFilterCount() === 0}
            size="sm"
            className="whitespace-nowrap"
          >
            Clear
          </Button>
        </div>

        {/* Active Filters Display - Compact row below */}
        {getActiveFilterCount() > 0 && (
          <div className="flex flex-wrap items-center justify-center gap-2 mt-3 pt-3 border-t">
            <span className="text-xs text-muted-foreground">Active:</span>

            {filters.country && (
              <Badge variant="secondary" className="flex items-center gap-1 text-xs">
                {filters.country}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={() => updateFilter('country', null)}
                />
              </Badge>
            )}

            {filters.region && (
              <Badge variant="secondary" className="flex items-center gap-1 text-xs">
                {filters.region}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={() => updateFilter('region', null)}
                />
              </Badge>
            )}

            {filters.branchId && (
              <Badge variant="secondary" className="flex items-center gap-1 text-xs">
                {getSelectedBranchName()}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={() => updateFilter('branchId', null)}
                />
              </Badge>
            )}

            {filters.categoryId && (
              <Badge variant="secondary" className="flex items-center gap-1 text-xs">
                {getSelectedCategoryName()}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-destructive"
                  onClick={() => updateFilter('categoryId', null)}
                />
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
