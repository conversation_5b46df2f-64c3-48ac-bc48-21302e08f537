'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Search,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Warehouse,
  MapPin,
  Clock,
  Package,
  Layers
} from 'lucide-react'
import { AddWarehouseModal } from '@/components/modals/add-warehouse-modal'
import { EditWarehouseModal } from '@/components/modals/edit-warehouse-modal'
import { DeleteConfirmationModal } from '@/components/modals/delete-confirmation-modal'
import { useToast } from '@/hooks/use-toast'

interface Warehouse {
  _id: string
  name: string
  code: string
  description?: string
  type: 'main' | 'branch' | 'storage' | 'distribution'
  branchId?: string
  branchName?: string
  address: string
  capacity: number
  currentUtilization: number
  managerId?: string
  managerName?: string
  isActive: boolean
  operatingHours: {
    open: string
    close: string
  }
  createdAt: string
  updatedAt: string
}

export default function WarehouseManagement() {
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [filteredWarehouses, setFilteredWarehouses] = useState<Warehouse[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(null)
  const { toast } = useToast()

  // Fetch warehouses
  const fetchWarehouses = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/warehouses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setWarehouses(result.data)
          setFilteredWarehouses(result.data)
        }
      }
    } catch (error) {
      console.error('Error fetching warehouses:', error)
      toast({
        title: "Error",
        description: "Failed to fetch warehouses",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Filter warehouses based on search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredWarehouses(warehouses)
    } else {
      const filtered = warehouses.filter(warehouse =>
        warehouse.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        warehouse.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        warehouse.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
        warehouse.type.toLowerCase().includes(searchQuery.toLowerCase())
      )
      setFilteredWarehouses(filtered)
    }
  }, [searchQuery, warehouses])

  useEffect(() => {
    fetchWarehouses()
  }, [])

  const handleEdit = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse)
    setIsEditModalOpen(true)
  }

  const handleDelete = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse)
    setIsDeleteModalOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!selectedWarehouse) return

    try {
      const response = await fetch(`/api/warehouses/${selectedWarehouse._id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Warehouse deleted successfully",
        })
        fetchWarehouses()
      } else {
        throw new Error('Failed to delete warehouse')
      }
    } catch (error) {
      console.error('Error deleting warehouse:', error)
      toast({
        title: "Error",
        description: "Failed to delete warehouse",
        variant: "destructive",
      })
    } finally {
      setIsDeleteModalOpen(false)
      setSelectedWarehouse(null)
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'main': return 'bg-blue-100 text-blue-800'
      case 'branch': return 'bg-green-100 text-green-800'
      case 'storage': return 'bg-yellow-100 text-yellow-800'
      case 'distribution': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return 'text-red-600'
    if (utilization >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Warehouse Management</h1>
          <p className="text-muted-foreground">
            Manage warehouses, storage facilities, and distribution centers
          </p>
        </div>
        <Button onClick={() => setIsAddModalOpen(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Warehouse
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Warehouse className="h-5 w-5" />
            Warehouses
          </CardTitle>
          <CardDescription>
            Manage your warehouse locations and storage facilities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search warehouses..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredWarehouses.length === 0 ? (
            <div className="text-center py-8">
              <Warehouse className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No warehouses found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ? 'No warehouses match your search criteria.' : 'No warehouses have been created yet.'}
              </p>
              <Button onClick={() => setIsAddModalOpen(true)} className="gap-2">
                <Plus className="h-4 w-4" />
                Add First Warehouse
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Warehouse</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Capacity</TableHead>
                    <TableHead>Utilization</TableHead>
                    <TableHead>Hours</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredWarehouses.map((warehouse) => (
                    <TableRow key={warehouse._id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{warehouse.name}</div>
                          <div className="text-sm text-muted-foreground">{warehouse.code}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getTypeColor(warehouse.type)}>
                          {warehouse.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{warehouse.address}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Package className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{warehouse.capacity.toLocaleString()} units</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={`font-medium ${getUtilizationColor(warehouse.currentUtilization)}`}>
                          {warehouse.currentUtilization}%
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">
                            {warehouse.operatingHours.open} - {warehouse.operatingHours.close}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={warehouse.isActive ? "default" : "secondary"}>
                          {warehouse.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEdit(warehouse)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDelete(warehouse)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      <AddWarehouseModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onWarehouseCreated={fetchWarehouses}
      />

      <EditWarehouseModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setSelectedWarehouse(null)
        }}
        warehouse={selectedWarehouse}
        onWarehouseUpdated={fetchWarehouses}
      />

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false)
          setSelectedWarehouse(null)
        }}
        onConfirm={handleDeleteConfirm}
        title="Delete Warehouse"
        description={`Are you sure you want to delete "${selectedWarehouse?.name}"? This action cannot be undone.`}
      />
    </div>
  )
}
