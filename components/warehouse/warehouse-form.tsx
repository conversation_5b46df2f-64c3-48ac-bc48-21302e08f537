// components/warehouse/warehouse-form.tsx - Warehouse creation/editing form

'use client'

import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, MapPin, Clock, Phone, Mail, User } from 'lucide-react'
import type { Warehouse, CreateWarehouseData, UpdateWarehouseData } from '@/types/frontend'

// Form validation schema
const warehouseFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name cannot exceed 100 characters'),
  code: z.string().min(1, 'Code is required').max(20, 'Code cannot exceed 20 characters')
    .regex(/^[A-Z0-9]+$/, 'Code must contain only uppercase letters and numbers'),
  description: z.string().max(500, 'Description cannot exceed 500 characters').optional(),
  type: z.enum(['main', 'branch', 'storage', 'distribution']),
  branchId: z.string().optional(),
  address: z.string().min(1, 'Address is required').max(200, 'Address cannot exceed 200 characters'),
  capacity: z.number().min(1, 'Capacity must be greater than 0'),
  manager: z.string().max(100, 'Manager name cannot exceed 100 characters').optional(),
  managerId: z.string().optional(),
  contactPhone: z.string().max(20, 'Phone cannot exceed 20 characters').optional(),
  contactEmail: z.string().email('Invalid email format').max(100, 'Email cannot exceed 100 characters').optional(),
  openTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  closeTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  timezone: z.string().default('Africa/Blantyre'),
  latitude: z.number().min(-90).max(90).optional(),
  longitude: z.number().min(-180).max(180).optional(),
  features: z.array(z.string()).optional()
})

type WarehouseFormData = z.infer<typeof warehouseFormSchema>

interface WarehouseFormProps {
  warehouse?: Warehouse
  onSubmit: (data: CreateWarehouseData | UpdateWarehouseData) => Promise<{ success: boolean; error?: string }>
  onCancel: () => void
  isLoading?: boolean
}

const warehouseFeatures = [
  { id: 'climate_controlled', label: 'Climate Controlled' },
  { id: 'security_cameras', label: 'Security Cameras' },
  { id: 'loading_dock', label: 'Loading Dock' },
  { id: 'fire_suppression', label: 'Fire Suppression' },
  { id: 'backup_power', label: 'Backup Power' },
  { id: 'refrigerated', label: 'Refrigerated' },
  { id: 'hazmat_certified', label: 'Hazmat Certified' },
  { id: 'automated_systems', label: 'Automated Systems' }
]

export function WarehouseForm({ warehouse, onSubmit, onCancel, isLoading = false }: WarehouseFormProps) {
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([])
  const [submitError, setSubmitError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting }
  } = useForm<WarehouseFormData>({
    resolver: zodResolver(warehouseFormSchema),
    defaultValues: warehouse ? {
      name: warehouse.name,
      code: warehouse.code,
      description: warehouse.description || '',
      type: warehouse.type,
      branchId: warehouse.branchId || '',
      address: warehouse.address,
      capacity: warehouse.capacity,
      manager: warehouse.manager || '',
      managerId: warehouse.managerId || '',
      contactPhone: warehouse.contactInfo?.phone || '',
      contactEmail: warehouse.contactInfo?.email || '',
      openTime: warehouse.operatingHours.open,
      closeTime: warehouse.operatingHours.close,
      timezone: warehouse.operatingHours.timezone,
      latitude: warehouse.coordinates?.lat,
      longitude: warehouse.coordinates?.lng,
      features: warehouse.features || []
    } : {
      type: 'storage',
      openTime: '08:00',
      closeTime: '17:00',
      timezone: 'Africa/Blantyre',
      features: []
    }
  })

  const watchedType = watch('type')

  // Initialize features
  useEffect(() => {
    if (warehouse?.features) {
      setSelectedFeatures(warehouse.features)
    }
  }, [warehouse])

  const handleFeatureChange = (featureId: string, checked: boolean) => {
    const newFeatures = checked
      ? [...selectedFeatures, featureId]
      : selectedFeatures.filter(f => f !== featureId)
    
    setSelectedFeatures(newFeatures)
    setValue('features', newFeatures)
  }

  const onFormSubmit = async (data: WarehouseFormData) => {
    try {
      setSubmitError(null)

      // Transform form data to API format
      const submitData: CreateWarehouseData | UpdateWarehouseData = {
        name: data.name,
        description: data.description,
        address: data.address,
        capacity: data.capacity,
        manager: data.manager,
        managerId: data.managerId,
        contactInfo: {
          phone: data.contactPhone,
          email: data.contactEmail
        },
        operatingHours: {
          open: data.openTime,
          close: data.closeTime,
          timezone: data.timezone
        },
        coordinates: data.latitude && data.longitude ? {
          lat: data.latitude,
          lng: data.longitude
        } : undefined,
        features: selectedFeatures
      }

      // Add fields only for creation
      if (!warehouse) {
        (submitData as CreateWarehouseData).code = data.code
        ;(submitData as CreateWarehouseData).type = data.type
        if (data.branchId) {
          (submitData as CreateWarehouseData).branchId = data.branchId
        }
      }

      const result = await onSubmit(submitData)
      
      if (!result.success) {
        setSubmitError(result.error || 'Failed to save warehouse')
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'An unexpected error occurred')
    }
  }

  const isFormLoading = isLoading || isSubmitting

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {submitError && (
        <Alert variant="destructive">
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Warehouse Name *</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="Enter warehouse name"
                disabled={isFormLoading}
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="code">Warehouse Code *</Label>
              <Input
                id="code"
                {...register('code')}
                placeholder="e.g., WH001"
                disabled={isFormLoading || !!warehouse}
                style={{ textTransform: 'uppercase' }}
              />
              {errors.code && (
                <p className="text-sm text-red-600">{errors.code.message}</p>
              )}
              {warehouse && (
                <p className="text-sm text-muted-foreground">Code cannot be changed after creation</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Enter warehouse description"
              disabled={isFormLoading}
              rows={3}
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">Warehouse Type *</Label>
              <Select
                value={watchedType}
                onValueChange={(value) => setValue('type', value as any)}
                disabled={isFormLoading || !!warehouse}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select warehouse type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="main">Main Warehouse</SelectItem>
                  <SelectItem value="branch">Branch Warehouse</SelectItem>
                  <SelectItem value="storage">Storage Facility</SelectItem>
                  <SelectItem value="distribution">Distribution Center</SelectItem>
                </SelectContent>
              </Select>
              {errors.type && (
                <p className="text-sm text-red-600">{errors.type.message}</p>
              )}
              {warehouse && (
                <p className="text-sm text-muted-foreground">Type cannot be changed after creation</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="capacity">Capacity *</Label>
              <Input
                id="capacity"
                type="number"
                {...register('capacity', { valueAsNumber: true })}
                placeholder="Enter storage capacity"
                disabled={isFormLoading}
                min="1"
              />
              {errors.capacity && (
                <p className="text-sm text-red-600">{errors.capacity.message}</p>
              )}
            </div>
          </div>

          {watchedType === 'branch' && (
            <div className="space-y-2">
              <Label htmlFor="branchId">Branch ID</Label>
              <Input
                id="branchId"
                {...register('branchId')}
                placeholder="Enter branch ID"
                disabled={isFormLoading || !!warehouse}
              />
              {errors.branchId && (
                <p className="text-sm text-red-600">{errors.branchId.message}</p>
              )}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="address">Address *</Label>
            <Textarea
              id="address"
              {...register('address')}
              placeholder="Enter warehouse address"
              disabled={isFormLoading}
              rows={2}
            />
            {errors.address && (
              <p className="text-sm text-red-600">{errors.address.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Management & Contact */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Management & Contact
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="manager">Manager Name</Label>
              <Input
                id="manager"
                {...register('manager')}
                placeholder="Enter manager name"
                disabled={isFormLoading}
              />
              {errors.manager && (
                <p className="text-sm text-red-600">{errors.manager.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="managerId">Manager ID</Label>
              <Input
                id="managerId"
                {...register('managerId')}
                placeholder="Enter manager ID"
                disabled={isFormLoading}
              />
              {errors.managerId && (
                <p className="text-sm text-red-600">{errors.managerId.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contactPhone" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Contact Phone
              </Label>
              <Input
                id="contactPhone"
                {...register('contactPhone')}
                placeholder="Enter phone number"
                disabled={isFormLoading}
              />
              {errors.contactPhone && (
                <p className="text-sm text-red-600">{errors.contactPhone.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="contactEmail" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Contact Email
              </Label>
              <Input
                id="contactEmail"
                type="email"
                {...register('contactEmail')}
                placeholder="Enter email address"
                disabled={isFormLoading}
              />
              {errors.contactEmail && (
                <p className="text-sm text-red-600">{errors.contactEmail.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Operating Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Operating Hours
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="openTime">Opening Time *</Label>
              <Input
                id="openTime"
                type="time"
                {...register('openTime')}
                disabled={isFormLoading}
              />
              {errors.openTime && (
                <p className="text-sm text-red-600">{errors.openTime.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="closeTime">Closing Time *</Label>
              <Input
                id="closeTime"
                type="time"
                {...register('closeTime')}
                disabled={isFormLoading}
              />
              {errors.closeTime && (
                <p className="text-sm text-red-600">{errors.closeTime.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Select
                value={watch('timezone')}
                onValueChange={(value) => setValue('timezone', value)}
                disabled={isFormLoading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Africa/Blantyre">Africa/Blantyre</SelectItem>
                  <SelectItem value="Africa/Johannesburg">Africa/Johannesburg</SelectItem>
                  <SelectItem value="UTC">UTC</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Location Coordinates */}
      <Card>
        <CardHeader>
          <CardTitle>Location Coordinates (Optional)</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="latitude">Latitude</Label>
              <Input
                id="latitude"
                type="number"
                step="any"
                {...register('latitude', { valueAsNumber: true })}
                placeholder="e.g., -15.7861"
                disabled={isFormLoading}
              />
              {errors.latitude && (
                <p className="text-sm text-red-600">{errors.latitude.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="longitude">Longitude</Label>
              <Input
                id="longitude"
                type="number"
                step="any"
                {...register('longitude', { valueAsNumber: true })}
                placeholder="e.g., 35.0058"
                disabled={isFormLoading}
              />
              {errors.longitude && (
                <p className="text-sm text-red-600">{errors.longitude.message}</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features */}
      <Card>
        <CardHeader>
          <CardTitle>Warehouse Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {warehouseFeatures.map((feature) => (
              <div key={feature.id} className="flex items-center space-x-2">
                <Checkbox
                  id={feature.id}
                  checked={selectedFeatures.includes(feature.id)}
                  onCheckedChange={(checked) => handleFeatureChange(feature.id, !!checked)}
                  disabled={isFormLoading}
                />
                <Label htmlFor={feature.id} className="text-sm">
                  {feature.label}
                </Label>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isFormLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isFormLoading}
        >
          {isFormLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {warehouse ? 'Update Warehouse' : 'Create Warehouse'}
        </Button>
      </div>
    </form>
  )
}
