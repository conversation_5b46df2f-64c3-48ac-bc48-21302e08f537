'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Search, 
  Plus, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Layers,
  Package,
  MapPin,
  Weight,
  AlertTriangle
} from 'lucide-react'
import { AddShelfModal } from '@/components/modals/add-shelf-modal'
import { EditShelfModal } from '@/components/modals/edit-shelf-modal'
import { DeleteConfirmationModal } from '@/components/modals/delete-confirmation-modal'
import { useToast } from '@/hooks/use-toast'

interface Shelf {
  _id: string
  code: string
  name: string
  warehouseId: string
  warehouseName: string
  warehouseCode: string
  section: string
  row: number
  position: number
  level: number
  description?: string
  dimensions: {
    length: number
    width: number
    height: number
  }
  weightLimit: number
  currentWeight: number
  shelfType: 'standard' | 'refrigerated' | 'hazmat' | 'fragile' | 'bulk'
  accessLevel: 'ground' | 'ladder' | 'forklift' | 'crane'
  isActive: boolean
  isReserved: boolean
  reservedFor?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

interface Warehouse {
  _id: string
  name: string
  code: string
}

export default function ShelfManagement() {
  const [shelves, setShelves] = useState<Shelf[]>([])
  const [warehouses, setWarehouses] = useState<Warehouse[]>([])
  const [filteredShelves, setFilteredShelves] = useState<Shelf[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedWarehouse, setSelectedWarehouse] = useState<string>('all')
  const [selectedShelfType, setSelectedShelfType] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [selectedShelf, setSelectedShelf] = useState<Shelf | null>(null)
  const { toast } = useToast()

  // Fetch shelves
  const fetchShelves = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/shelves', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setShelves(result.data)
          setFilteredShelves(result.data)
        }
      }
    } catch (error) {
      console.error('Error fetching shelves:', error)
      toast({
        title: "Error",
        description: "Failed to fetch shelves",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch warehouses
  const fetchWarehouses = async () => {
    try {
      const response = await fetch('/api/warehouses', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setWarehouses(result.data)
        }
      }
    } catch (error) {
      console.error('Error fetching warehouses:', error)
    }
  }

  // Filter shelves based on search and filters
  useEffect(() => {
    let filtered = shelves

    // Search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(shelf =>
        shelf.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shelf.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shelf.warehouseName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        shelf.section.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Warehouse filter
    if (selectedWarehouse && selectedWarehouse !== 'all') {
      filtered = filtered.filter(shelf => shelf.warehouseId === selectedWarehouse)
    }

    // Shelf type filter
    if (selectedShelfType && selectedShelfType !== 'all') {
      filtered = filtered.filter(shelf => shelf.shelfType === selectedShelfType)
    }

    setFilteredShelves(filtered)
  }, [searchQuery, selectedWarehouse, selectedShelfType, shelves])

  useEffect(() => {
    fetchShelves()
    fetchWarehouses()
  }, [])

  const handleEdit = (shelf: Shelf) => {
    setSelectedShelf(shelf)
    setIsEditModalOpen(true)
  }

  const handleDelete = (shelf: Shelf) => {
    setSelectedShelf(shelf)
    setIsDeleteModalOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (!selectedShelf) return

    try {
      const response = await fetch(`/api/shelves/${selectedShelf._id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: "Shelf deleted successfully",
        })
        fetchShelves()
      } else {
        throw new Error('Failed to delete shelf')
      }
    } catch (error) {
      console.error('Error deleting shelf:', error)
      toast({
        title: "Error",
        description: "Failed to delete shelf",
        variant: "destructive",
      })
    } finally {
      setIsDeleteModalOpen(false)
      setSelectedShelf(null)
    }
  }

  const getShelfTypeColor = (type: string) => {
    switch (type) {
      case 'standard': return 'bg-blue-100 text-blue-800'
      case 'refrigerated': return 'bg-cyan-100 text-cyan-800'
      case 'hazmat': return 'bg-red-100 text-red-800'
      case 'fragile': return 'bg-yellow-100 text-yellow-800'
      case 'bulk': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'ground': return 'bg-green-100 text-green-800'
      case 'ladder': return 'bg-yellow-100 text-yellow-800'
      case 'forklift': return 'bg-orange-100 text-orange-800'
      case 'crane': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getWeightUtilization = (current: number, limit: number) => {
    return limit > 0 ? Math.round((current / limit) * 100) : 0
  }

  const getWeightUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return 'text-red-600'
    if (utilization >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Shelf Management</h1>
          <p className="text-muted-foreground">
            Manage warehouse shelves, storage locations, and organization
          </p>
        </div>
        <Button onClick={() => setIsAddModalOpen(true)} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Shelf
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            Shelves
          </CardTitle>
          <CardDescription>
            Manage shelf locations and storage organization within warehouses
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search shelves..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={selectedWarehouse} onValueChange={setSelectedWarehouse}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="All warehouses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All warehouses</SelectItem>
                {warehouses.map((warehouse) => (
                  <SelectItem key={warehouse._id} value={warehouse._id}>
                    {warehouse.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedShelfType} onValueChange={setSelectedShelfType}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                <SelectItem value="standard">Standard</SelectItem>
                <SelectItem value="refrigerated">Refrigerated</SelectItem>
                <SelectItem value="hazmat">Hazmat</SelectItem>
                <SelectItem value="fragile">Fragile</SelectItem>
                <SelectItem value="bulk">Bulk</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : filteredShelves.length === 0 ? (
            <div className="text-center py-8">
              <Layers className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No shelves found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || selectedWarehouse || selectedShelfType 
                  ? 'No shelves match your search criteria.' 
                  : 'No shelves have been created yet.'}
              </p>
              <Button onClick={() => setIsAddModalOpen(true)} className="gap-2">
                <Plus className="h-4 w-4" />
                Add First Shelf
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Shelf</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Access</TableHead>
                    <TableHead>Weight</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredShelves.map((shelf) => {
                    const weightUtilization = getWeightUtilization(shelf.currentWeight, shelf.weightLimit)
                    return (
                      <TableRow key={shelf._id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{shelf.code}</div>
                            <div className="text-sm text-muted-foreground">{shelf.name}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3 text-muted-foreground" />
                              <span className="text-sm">{shelf.warehouseName}</span>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {shelf.section}-{shelf.row}-{shelf.position}-{shelf.level}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getShelfTypeColor(shelf.shelfType)}>
                            {shelf.shelfType}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getAccessLevelColor(shelf.accessLevel)}>
                            {shelf.accessLevel}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Weight className="h-3 w-3 text-muted-foreground" />
                            <span className={`text-sm font-medium ${getWeightUtilizationColor(weightUtilization)}`}>
                              {shelf.currentWeight}/{shelf.weightLimit} kg ({weightUtilization}%)
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <Badge variant={shelf.isActive ? "default" : "secondary"}>
                              {shelf.isActive ? "Active" : "Inactive"}
                            </Badge>
                            {shelf.isReserved && (
                              <Badge variant="outline" className="text-xs">
                                Reserved
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEdit(shelf)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleDelete(shelf)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      <AddShelfModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onShelfCreated={fetchShelves}
        warehouses={warehouses}
      />

      <EditShelfModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setSelectedShelf(null)
        }}
        shelf={selectedShelf}
        onShelfUpdated={fetchShelves}
        warehouses={warehouses}
      />

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false)
          setSelectedShelf(null)
        }}
        onConfirm={handleDeleteConfirm}
        title="Delete Shelf"
        description={`Are you sure you want to delete shelf "${selectedShelf?.code}"? This action cannot be undone.`}
      />
    </div>
  )
}
