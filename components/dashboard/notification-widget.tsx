'use client'

import React, { useState } from 'react'
import { Bell, Settings, Eye, Archive, Trash2, <PERSON><PERSON>, MoreHorizontal } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { NotificationItem } from '@/components/notifications/notification-item'
import { NotificationFilters } from '@/components/notifications/notification-filters'
import { useNotificationStore } from '@/stores/notificationStore'
import { cn } from '@/lib/utils'
import Link from 'next/link'

interface NotificationWidgetProps {
  className?: string
  variant?: 'compact' | 'full' | 'summary'
  maxItems?: number
  showFilters?: boolean
  showActions?: boolean
  title?: string
}

export function NotificationWidget({
  className,
  variant = 'compact',
  maxItems = 5,
  showFilters = false,
  showActions = true,
  title = 'Recent Notifications'
}: NotificationWidgetProps) {
  const {
    notifications,
    unreadCount,
    totalCount,
    isLoading,
    filters,
    markAsRead,
    markAllAsRead,
    setFilters,
    clearFilters
  } = useNotificationStore()

  const [selectedTab, setSelectedTab] = useState<'all' | 'unread' | 'important'>('all')

  // Filter notifications based on selected tab
  const filteredNotifications = notifications
    .filter(notification => {
      switch (selectedTab) {
        case 'unread':
          return notification.status === 'unread'
        case 'important':
          return notification.priority === 'high' || notification.priority === 'critical' || notification.priority === 'urgent'
        default:
          return true
      }
    })
    .slice(0, maxItems)

  const handleMarkAllAsRead = () => {
    markAllAsRead()
  }

  const handleClearFilters = () => {
    clearFilters()
    setSelectedTab('all')
  }

  if (variant === 'summary') {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Notifications</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold">{unreadCount}</span>
              <Badge variant="secondary" className="text-xs">
                {totalCount} total
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              {unreadCount === 0 ? 'All caught up!' : `${unreadCount} unread notification${unreadCount === 1 ? '' : 's'}`}
            </p>
            <Link href="/dashboard/notifications">
              <Button variant="outline" size="sm" className="w-full text-xs">
                View All
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            <CardDescription>
              {unreadCount > 0 ? `${unreadCount} unread` : 'All caught up'}
            </CardDescription>
          </div>
          
          {showActions && (
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleMarkAllAsRead}
                  className="text-xs"
                >
                  <Eye className="h-3 w-3 mr-1" />
                  Mark all read
                </Button>
              )}
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard/notifications">
                      <Bell className="h-4 w-4 mr-2" />
                      View All
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard/notifications/settings">
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleClearFilters}>
                    <Filter className="h-4 w-4 mr-2" />
                    Clear Filters
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {variant === 'full' && (
          <div className="px-6 pb-4">
            <Tabs value={selectedTab} onValueChange={(value) => setSelectedTab(value as any)}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="all" className="text-xs">
                  All ({totalCount})
                </TabsTrigger>
                <TabsTrigger value="unread" className="text-xs">
                  Unread ({unreadCount})
                </TabsTrigger>
                <TabsTrigger value="important" className="text-xs">
                  Important
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        )}

        {showFilters && variant === 'full' && (
          <div className="px-6 pb-4">
            <NotificationFilters
              filters={filters}
              onFiltersChange={setFilters}
              compact={true}
            />
          </div>
        )}

        <ScrollArea className={cn(
          "w-full",
          variant === 'compact' ? "h-64" : "h-96"
        )}>
          <div className="space-y-1 p-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              </div>
            ) : filteredNotifications.length === 0 ? (
              <div className="text-center py-8">
                <Bell className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  {selectedTab === 'unread' ? 'No unread notifications' : 'No notifications'}
                </p>
              </div>
            ) : (
              filteredNotifications.map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  variant="compact"
                  showActions={false}
                  showSelection={false}
                  onMarkAsRead={() => markAsRead(notification.id)}
                />
              ))
            )}
          </div>
        </ScrollArea>

        {filteredNotifications.length > 0 && totalCount > maxItems && (
          <div className="p-4 border-t">
            <Link href="/dashboard/notifications">
              <Button variant="outline" className="w-full text-sm">
                View All {totalCount} Notifications
              </Button>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Compact notification summary for dashboard cards
export function NotificationSummaryCard({ className }: { className?: string }) {
  return (
    <NotificationWidget
      className={className}
      variant="summary"
      title="Notifications"
    />
  )
}

// Full notification widget for dashboard pages
export function NotificationDashboardWidget({ className }: { className?: string }) {
  return (
    <NotificationWidget
      className={className}
      variant="full"
      maxItems={10}
      showFilters={true}
      showActions={true}
      title="Notifications"
    />
  )
}

// Compact notification widget for sidebars
export function NotificationSidebarWidget({ className }: { className?: string }) {
  return (
    <NotificationWidget
      className={className}
      variant="compact"
      maxItems={5}
      showFilters={false}
      showActions={true}
      title="Recent Notifications"
    />
  )
}
