"use client"

import { useState, useEffect, useMemo } from "react"
import Image from "next/image"
import {
  MoreHorizontal,
  PlusCircle,
  Edit,
  Trash2,
  Package,
  DollarSign,
  List,
  Search,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  AlertCircle,
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import SimpleProductModal from "@/components/modals/simple-product-modal"
import { DeleteConfirmationModal } from "@/components/modals/delete-confirmation-modal"
import { useProductsStore } from "@/stores/productsStore"
import { useCategoryStore } from "@/stores/categoryStore"
import type { User } from "@/lib/auth-types"
import type { CreateProductData } from "@/types"
import { formatPrice } from "@/lib/currency"

interface ProductManagementDashboardProps {
  currentUser: User | null
}

export default function ProductManagementDashboard({ currentUser }: ProductManagementDashboardProps) {
  const { toast } = useToast()

  // Zustand stores
  const {
    products,
    isLoading: productsLoading,
    error: productsError,
    currentPage,
    totalPages,
    totalProducts,
    pageSize,
    filters: productsFilters,
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    setFilters: _setProductsFilters,
    clearError: clearProductsError
  } = useProductsStore()

  const {
    activeCategories,
    fetchActiveCategories,
  } = useCategoryStore()

  // Local state
  const [currentTab, setCurrentTab] = useState("all")
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [isProductModalOpen, setIsProductModalOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<any>(null)
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{
    isOpen: boolean
    item: any
  }>({ isOpen: false, item: null })

  // Load data on component mount
  useEffect(() => {
    loadInitialData()
  }, [])

  // Load data when user changes (for branch filtering)
  useEffect(() => {
    if (currentUser) {
      loadProducts()
    }
  }, [currentUser])

  const loadInitialData = async () => {
    try {
      await Promise.all([
        loadProducts(),
        fetchActiveCategories()
      ])
    } catch (error) {
      console.error('Failed to load initial data:', error)
    }
  }

  const loadProducts = async () => {
    try {
      const filters: any = {}

      console.log('🏪 ProductDashboard.loadProducts called:')
      console.log('  Current user role:', currentUser?.role)
      console.log('  Current user branchId:', currentUser?.branchId)

      // Apply branch filtering for branch managers
      if (currentUser?.role === 'branch_manager' && currentUser.branchId) {
        filters.branchId = currentUser.branchId
        console.log('  ✅ Branch manager: Adding branchId filter:', currentUser.branchId)
      } else if (currentUser?.role === 'overall_admin') {
        console.log('  ✅ Overall admin: No branch filter - will see all products')
      }

      console.log('  📋 Final filters to send:', JSON.stringify(filters, null, 2))

      await fetchProducts(
        { page: currentPage, limit: pageSize },
        filters
      )
    } catch (error) {
      console.error('Failed to load products:', error)
    }
  }



  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term)
    // For immediate feedback, we'll filter client-side
    // For server-side search, we could debounce and call loadProducts()
  }

  // Handle category filter
  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category)
    // For immediate feedback, we'll filter client-side
    // For server-side filtering, we could call loadProducts() with category filter
  }

  // Handle pagination
  const handlePageChange = (page: number) => {
    fetchProducts({ page, limit: pageSize }, productsFilters)
  }

  // Handle product deletion
  const handleDeleteProduct = async (productId: string) => {
    try {
      const success = await deleteProduct(productId)
      if (success) {
        toast({
          title: "Success",
          description: "Product deleted successfully",
        })
        setDeleteConfirmDialog({ isOpen: false, item: null })
        loadProducts() // Refresh the list
      } else {
        toast({
          title: "Error",
          description: "Failed to delete product",
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete product",
        variant: "destructive",
      })
    }
  }



  // Handle product creation success
  const handleProductCreated = () => {
    setIsProductModalOpen(false)
    setEditingProduct(null)
    loadProducts()
    toast({
      title: "Success",
      description: "Product created successfully",
    })
  }



  // Filter products based on current tab and search
  const filteredProducts = useMemo(() => {
    let filtered = products

    // Apply branch filtering for branch managers (already handled in API call)
    // No need to filter here as it's done server-side

    // Filter by status tab
    if (currentTab !== "all") {
      filtered = filtered.filter((product) => {
        if (currentTab === "in-stock") return product.stock > 0 && product.stock > (product.minStockLevel || 0)
        if (currentTab === "low-stock") return product.stock > 0 && product.stock <= (product.minStockLevel || 0)
        if (currentTab === "out-of-stock") return product.stock === 0
        return true
      })
    }

    // Filter by category
    if (selectedCategory !== "all") {
      filtered = filtered.filter((product) => product.categoryName === selectedCategory)
    }

    // Search filter (client-side for immediate feedback)
    if (searchTerm) {
      filtered = filtered.filter(
        (product) =>
          product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.categoryName.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    return filtered
  }, [products, currentTab, searchTerm, selectedCategory])

  // Get status badge component
  const getStatusBadge = (product: any) => {
    const stock = product.stock || 0
    const minStock = product.minStockLevel || 0

    if (stock === 0) {
      return (
        <Badge variant="outline" className="bg-red-100 text-red-700 border-red-200">
          Out of Stock
        </Badge>
      )
    } else if (stock <= minStock) {
      return (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-700 border-yellow-200">
          Low Stock
        </Badge>
      )
    } else {
      return (
        <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200">
          In Stock
        </Badge>
      )
    }
  }

  // Get promotional badges
  const getPromotionalBadges = (product: any) => {
    const badges = []

    if (product.isFeatured) {
      badges.push(
        <Badge key="featured" className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white border-0">
          Featured
        </Badge>
      )
    }

    if (product.isPromoted) {
      badges.push(
        <Badge key="promoted" className="bg-gradient-to-r from-blue-500 to-blue-700 text-white border-0">
          Promoted
        </Badge>
      )
    }

    if (product.isOnSale) {
      badges.push(
        <Badge key="sale" className="bg-gradient-to-r from-red-500 to-red-700 text-white border-0">
          On Sale
        </Badge>
      )
    }

    return badges
  }

  // Modal handlers for product creation/editing
  const handleProductSubmit = async (data: any) => {
    try {
      console.log('🚀 Dashboard handleProductSubmit called')
      console.log('📦 Received data:', JSON.stringify(data, null, 2))
      console.log('🔄 Mode:', editingProduct ? 'edit' : 'create')
      console.log('📝 Editing product:', editingProduct?._id)

      // Find the category to get its ID
      const category = activeCategories.find(cat => cat.name === data.category)
      if (!category) {
        toast({
          title: "Error",
          description: "Selected category not found",
          variant: "destructive",
        })
        return
      }

      let featuredImageUrl = "/images/placeholder-product.png"
      let additionalImageUrls: string[] = []

      // For edit mode, use existing images if no new ones uploaded
      if (editingProduct && !data._featuredImageFile) {
        featuredImageUrl = editingProduct.featuredImage || "/images/placeholder-product.png"
      }
      if (editingProduct && (!data._additionalImageFiles || data._additionalImageFiles.length === 0)) {
        additionalImageUrls = editingProduct.images || []
      }

      // Upload images if they exist
      if (data._featuredImageFile || (data._additionalImageFiles && data._additionalImageFiles.length > 0)) {
        console.log('📸 Uploading images...')

        try {
          const formData = new FormData()

          // Add featured image if exists
          if (data._featuredImageFile) {
            console.log('📸 Adding featured image:', data._featuredImageFile.name)
            formData.append('files', data._featuredImageFile)
          }

          // Add additional images if exist
          if (data._additionalImageFiles && data._additionalImageFiles.length > 0) {
            console.log('📸 Adding additional images:', data._additionalImageFiles.length)
            data._additionalImageFiles.forEach((file: File) => {
              formData.append('files', file)
            })
          }

          formData.append('category', 'products')

          console.log('📤 Uploading to /api/images/upload...')
          const uploadResponse = await fetch('/api/images/upload', {
            method: 'POST',
            body: formData,
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            },
          })

          console.log('📥 Upload response status:', uploadResponse.status)

          if (uploadResponse.ok) {
            const uploadResult = await uploadResponse.json()
            console.log('✅ Upload successful:', uploadResult)

            if (uploadResult.success && uploadResult.data) {
              // Set the first uploaded image as featured image
              if (data._featuredImageFile && uploadResult.data.length > 0) {
                featuredImageUrl = uploadResult.data[0].url
                console.log('🖼️ Featured image URL:', featuredImageUrl)
              }

              // Set additional images (skip the first one if it's the featured image)
              const startIndex = data._featuredImageFile ? 1 : 0
              additionalImageUrls = uploadResult.data.slice(startIndex).map((img: any) => img.url)
              console.log('🖼️ Additional image URLs:', additionalImageUrls)
            }
          } else {
            console.error('❌ Image upload failed:', uploadResponse.statusText)
            toast({
              title: "Warning",
              description: "Image upload failed, but product will be created with placeholder image",
              variant: "destructive",
            })
          }
        } catch (uploadError) {
          console.error('❌ Image upload error:', uploadError)
          toast({
            title: "Warning",
            description: "Image upload failed, but product will be created with placeholder image",
            variant: "destructive",
          })
        }
      }

      // Prepare the product data with required fields
      const productData: CreateProductData = {
        name: data.name,
        sku: data.sku,
        categoryId: category.id,
        categoryName: category.name,
        price: data.price,
        originalPrice: data.originalPrice,
        currency: data.currency || "MWK",
        stock: data.stock,
        minStockLevel: data.minStockLevel || 5,
        description: data.description,
        specifications: data.specifications || [],
        branchId: data.branchId,
        brand: data.brand || "Generic", // Default value for required field
        model: data.model || "Standard", // Default value for required field
        warranty: data.warranty || "1 year manufacturer warranty", // Default value
        weight: data.weight,
        dimensions: data.dimensions,
        tags: data.tags || [],
        variants: data.variants || [],
        hasVariants: data.hasVariants || false,
        featuredImage: featuredImageUrl, // Use uploaded image URL or placeholder
        images: additionalImageUrls, // Use uploaded image URLs
        isActive: data.isActive !== undefined ? data.isActive : true,
        isFeatured: data.isFeatured || false,
        isPromoted: data.isPromoted || false,
        isOnSale: data.isOnSale || false,
        salePrice: data.salePrice,
        saleStartDate: data.saleStartDate,
        saleEndDate: data.saleEndDate,
        promotionDescription: data.promotionDescription || "",
      }

      console.log('📦 Final product data:', JSON.stringify(productData, null, 2))

      let result
      if (editingProduct) {
        // Update existing product
        console.log('🔄 Updating product:', editingProduct._id)
        result = await updateProduct(editingProduct._id, productData)

        if (result.success) {
          toast({
            title: "Success",
            description: "Product updated successfully",
          })
          handleProductCreated() // Refresh the products list
        } else {
          toast({
            title: "Error",
            description: result.error || "Failed to update product",
            variant: "destructive",
          })
        }
      } else {
        // Create new product
        console.log('➕ Creating new product')
        result = await createProduct(productData)

        if (result.success) {
          toast({
            title: "Success",
            description: "Product created successfully",
          })
          handleProductCreated() // Refresh the products list
        } else {
          toast({
            title: "Error",
            description: result.error || "Failed to create product",
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      console.error("Error with product operation:", error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      })
    }
  }



  // Open edit dialogs
  const openEditProductDialog = (product: any) => {
    setEditingProduct(product)
    setIsProductModalOpen(true)
  }

  // Open add dialogs
  const openAddProductDialog = () => {
    setEditingProduct(null)
    setIsProductModalOpen(true)
  }

  // Open delete confirmation
  const openDeleteConfirmation = (item: any) => {
    setDeleteConfirmDialog({ isOpen: true, item })
  }

  if (!currentUser) {
    return null // Should not happen if DashboardLayout redirects, but good for safety
  }

  return (
    <>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-semibold text-lg md:text-2xl">Product Management</h1>
          <p className="text-sm text-muted-foreground mt-1">
            {currentUser?.role === 'branch_manager'
              ? 'Managing products for your branch'
              : 'Managing products across all branches'
            }
            {currentUser?.role === 'branch_manager' && (
              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Branch View
              </span>
            )}
            {currentUser?.role === 'overall_admin' && (
              <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                All Branches
              </span>
            )}
          </p>
        </div>
        <div className="flex gap-2">

          <Button
            size="sm"
            className="h-8 gap-1"
            onClick={openAddProductDialog}
            disabled={productsLoading}
          >
            <PlusCircle className="h-3.5 w-3.5" />
            <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Add Product</span>
          </Button>
          <Button
            size="sm"
            variant="outline"
            className="h-8 gap-1"
            onClick={loadInitialData}
            disabled={productsLoading}
          >
            <RefreshCw className={`h-3.5 w-3.5 ${productsLoading ? 'animate-spin' : ''}`} />
            <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Refresh</span>
          </Button>
        </div>
      </div>

      {/* Error Messages */}
      {productsError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {productsError}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={() => {
                clearProductsError()
                loadInitialData()
              }}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">

            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {productsLoading ? (
                  <Skeleton className="h-8 w-16" />
                ) : (
                  <div className="text-2xl font-bold">{totalProducts}</div>
                )}
                <p className="text-xs text-muted-foreground">
                  {currentUser.role === "branch_manager" ? "in this branch" : "across all branches"}
                </p>
              </CardContent>
            </Card>
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Price</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {productsLoading ? (
                  <Skeleton className="h-8 w-20" />
                ) : (
                  <div className="text-2xl font-bold">
                    ${products.length > 0 ? new Intl.NumberFormat('en-US', {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2
                    }).format(products.reduce((sum, p) => sum + (p.price || 0), 0) / products.length) : '0.00'}
                  </div>
                )}
                <p className="text-xs text-muted-foreground">per item</p>
              </CardContent>
            </Card>
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Categories</CardTitle>
                <List className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                {productsLoading ? (
                  <Skeleton className="h-8 w-12" />
                ) : (
                  <div className="text-2xl font-bold">{activeCategories.length}</div>
                )}
                <p className="text-xs text-muted-foreground">available</p>
              </CardContent>
            </Card>
      </div>

      <Tabs value={currentTab} onValueChange={setCurrentTab}>
        <div className="flex items-center justify-between flex-wrap gap-2">
          <TabsList>
            <TabsTrigger value="all">All Products</TabsTrigger>
            <TabsTrigger value="in-stock">In Stock</TabsTrigger>
            <TabsTrigger value="low-stock">Low Stock</TabsTrigger>
            <TabsTrigger value="out-of-stock">Out of Stock</TabsTrigger>

          </TabsList>
          <div className="flex gap-2 flex-1 max-w-md">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search products..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                disabled={productsLoading}
              />
            </div>
            <Select value={selectedCategory} onValueChange={handleCategoryFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {activeCategories.map((category) => (
                  <SelectItem key={category.id} value={category.name}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <TabsContent value={currentTab}>
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle>Product List</CardTitle>
              <CardDescription>
                {currentUser?.role === 'branch_manager'
                  ? `View and manage products for ${currentUser.branchId ? 'your branch' : 'your assigned branch'}.`
                  : 'View and manage all products across all branches.'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">Image</TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead className="hidden md:table-cell">Category</TableHead>
                    <TableHead className="text-right">Price</TableHead>
                    <TableHead className="text-right">Stock</TableHead>
                    <TableHead className="hidden sm:table-cell">Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {productsLoading ? (
                    // Loading skeleton
                    Array.from({ length: 5 }).map((_, index) => (
                      <TableRow key={index}>
                        <TableCell><Skeleton className="h-12 w-12 rounded" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                        <TableCell className="hidden md:table-cell"><Skeleton className="h-4 w-24" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-12" /></TableCell>
                        <TableCell className="hidden sm:table-cell"><Skeleton className="h-6 w-20" /></TableCell>
                        <TableCell><Skeleton className="h-8 w-8" /></TableCell>
                      </TableRow>
                    ))
                  ) : filteredProducts.length > 0 ? (
                    filteredProducts.map((product) => (
                      <TableRow key={product._id || product._id}>
                        <TableCell>
                          <div className="relative w-12 h-12 overflow-hidden rounded-md bg-muted">
                            <Image
                              src={product.featuredImage || product.images?.[0] || "/placeholder.svg"}
                              alt={product.name}
                              fill
                              className="object-cover transition-all duration-300 hover:scale-110"
                              loading="lazy"
                              unoptimized
                              onError={(e) => {
                                const target = e.target as HTMLImageElement
                                console.log('🖼️ Image load error for:', target.src)
                                if (target.src !== "/placeholder.svg") {
                                  console.log('🔄 Falling back to placeholder')
                                  target.src = "/placeholder.svg"
                                }
                              }}
                              onLoad={(e) => {
                                const target = e.target as HTMLImageElement
                                console.log('✅ Image loaded successfully:', target.src)
                              }}
                            />
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">
                          <div className="space-y-1">
                            <div>{product.name}</div>
                            <div className="flex flex-wrap gap-1">
                              {getPromotionalBadges(product)}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{product.sku}</TableCell>
                        <TableCell className="hidden md:table-cell">{product.categoryName}</TableCell>
                        <TableCell className="text-right">
                          {formatPrice(product.price || 0, product.currency || 'MWK')}
                        </TableCell>
                        <TableCell className="text-right">{product.stock || 0}</TableCell>
                        <TableCell className="hidden sm:table-cell">{getStatusBadge(product)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button aria-haspopup="true" size="icon" variant="ghost">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Toggle menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openEditProductDialog(product)}>
                                <Edit className="mr-2 h-4 w-4" /> Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => openDeleteConfirmation(product)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" /> Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                        {productsError ? (
                          <div className="flex flex-col items-center gap-2">
                            <AlertCircle className="h-8 w-8 text-muted-foreground" />
                            <p>Failed to load products</p>
                            <Button variant="outline" size="sm" onClick={loadProducts}>
                              Try Again
                            </Button>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center gap-2">
                            <Package className="h-8 w-8 text-muted-foreground" />
                            <p>No products found matching your criteria.</p>
                            <Button variant="outline" size="sm" onClick={openAddProductDialog}>
                              Add Your First Product
                            </Button>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between space-x-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * pageSize) + 1} to{' '}
                    {Math.min(currentPage * pageSize, totalProducts)} of{' '}
                    {totalProducts} products
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1 || productsLoading}
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" /> Previous
                    </Button>
                    <span className="text-sm text-muted-foreground">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages || productsLoading}
                    >
                      Next <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>


      </Tabs>

      {/* Product Modal */}
      <SimpleProductModal
        isOpen={isProductModalOpen}
        onClose={() => {
          setIsProductModalOpen(false)
          setEditingProduct(null)
        }}
        onSubmit={handleProductSubmit}
        isLoading={productsLoading}
        initialData={editingProduct}
        mode={editingProduct ? "edit" : "create"}
      />



      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteConfirmDialog.isOpen}
        onClose={() => setDeleteConfirmDialog({ isOpen: false, item: null })}
        onConfirm={() => handleDeleteProduct(deleteConfirmDialog.item?._id || deleteConfirmDialog.item?.id)}
        title="Delete Product"
        description={`Are you sure you want to delete "${deleteConfirmDialog.item?.name}"? This action cannot be undone.`}
        confirmText="Delete Product"
        cancelText="Cancel"
      />
    </>
  )
}
