"use client"

import { useState, useMemo } from "react" // Removed useEffect for currentUser
import Image from "next/image"
import {
  MoreHorizontal,
  PlusCircle,
  Edit,
  Trash2,
  MapPin,
  DollarSign,
  Package,
  Search,
  ChevronLeft,
  ChevronRight,
  Building2,
  Store,
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AddShopModal, AddBranchModal } from "@/components/modals"
import type { User } from "@/lib/auth-types" // Import User type
import type { CreateShopData, CreateBranchData } from "@/types"

interface Shop {
  id: string
  name: string
  location: string
  country: string
  manager: string
  totalProducts: number
  totalSales: number
  status: "Active" | "Inactive" | "Opening Soon"
  image: string
  description: string
}

interface ShopManagementDashboardProps {
  currentUser: User | null // Now received as a prop
}

export default function ShopManagementDashboard({ currentUser }: ShopManagementDashboardProps) {
  const [shops, setShops] = useState<Shop[]>([
    {
      id: "shop-001",
      name: "Fathahitech - Blantyre",
      location: "Blantyre, Malawi",
      country: "Malawi",
      manager: "Jane Doe",
      totalProducts: 500,
      totalSales: 120000,
      status: "Active",
      image: "/images/shop-blantyre.png",
      description: "Main branch in Blantyre, offering a wide range of electronics.",
    },
    {
      id: "shop-002",
      name: "Fathahitech - Lilongwe",
      location: "Lilongwe, Malawi",
      country: "Malawi",
      manager: "John Smith",
      totalProducts: 350,
      totalSales: 85000,
      status: "Active",
      image: "/images/shop-lilongwe.png",
      description: "Central branch in Lilongwe, specializing in network devices.",
    },
    {
      id: "shop-003",
      name: "Fathahitech - Mzuzu",
      location: "Mzuzu, Malawi",
      country: "Malawi",
      manager: "Alice Johnson",
      totalProducts: 200,
      totalSales: 45000,
      status: "Active",
      image: "/images/shop-mzuzu.png",
      description: "Northern branch in Mzuzu, focusing on software and accessories.",
    },
    {
      id: "shop-004",
      name: "Fathahitech - Lusaka",
      location: "Lusaka, Zambia",
      country: "Zambia",
      manager: "David Brown",
      totalProducts: 400,
      totalSales: 95000,
      status: "Active",
      image: "/images/shop-lusaka.png",
      description: "Our first international branch in Lusaka, Zambia.",
    },
    {
      id: "shop-005",
      name: "Fathahitech - Ndola",
      location: "Ndola, Zambia",
      country: "Zambia",
      manager: "Sarah Green",
      totalProducts: 150,
      totalSales: 0,
      status: "Opening Soon",
      image: "/placeholder.svg?height=150&width=150",
      description: "New branch opening soon in Ndola, Zambia.",
    },
  ])

  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingShop, setEditingShop] = useState<Shop | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentTab, setCurrentTab] = useState("all")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 5

  // Modal states
  const [isShopModalOpen, setIsShopModalOpen] = useState(false)
  const [isBranchModalOpen, setIsBranchModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const countries = ["Malawi", "Zambia"]

  const filteredShops = useMemo(() => {
    let filtered = shops

    if (currentUser && currentUser.role === "branch_manager" && currentUser.branchId) {
      filtered = filtered.filter((shop) => shop.id === currentUser.branchId)
    }

    if (currentTab !== "all") {
      filtered = filtered.filter((shop) => {
        if (currentTab === "active") return shop.status === "Active"
        if (currentTab === "inactive") return shop.status === "Inactive"
        if (currentTab === "opening-soon") return shop.status === "Opening Soon"
        return true
      })
    }

    if (searchTerm) {
      filtered = filtered.filter(
        (shop) =>
          shop.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          shop.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
          shop.manager.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }
    return filtered
  }, [shops, currentTab, searchTerm, currentUser])

  const totalPages = Math.ceil(filteredShops.length / itemsPerPage)
  const paginatedShops = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return filteredShops.slice(startIndex, endIndex)
  }, [filteredShops, currentPage, itemsPerPage])

  const handleAddShop = (newShop: Shop) => {
    setShops((prev) => [...prev, { ...newShop, id: `shop-${Date.now()}` }])
    setIsDialogOpen(false)
  }

  const handleUpdateShop = (updatedShop: Shop) => {
    setShops((prev) => prev.map((s) => (s.id === updatedShop.id ? updatedShop : s)))
    setIsDialogOpen(false)
    setEditingShop(null)
  }

  const handleDeleteShop = (id: string) => {
    setShops((prev) => prev.filter((s) => s.id !== id))
  }

  const openEditDialog = (shop: Shop) => {
    setEditingShop(shop)
    setIsDialogOpen(true)
  }

  const openAddDialog = () => {
    setEditingShop(null)
    setIsDialogOpen(true)
  }

  // Modal handlers
  const handleShopSubmit = async (data: CreateShopData) => {
    setIsLoading(true)
    try {
      // Convert CreateShopData to Shop format
      const newShop: Shop = {
        id: `shop-${Date.now()}`,
        name: data.name,
        location: `${data.location}, ${data.country}`,
        country: data.country,
        manager: "Manager Name", // You would get this from the managerId
        totalProducts: 0,
        totalSales: 0,
        status: "Active",
        image: "/placeholder.svg?height=150&width=150",
        description: data.description,
      }
      setShops((prev) => [...prev, newShop])
      setIsShopModalOpen(false)
    } catch (error) {
      console.error("Error creating shop:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleBranchSubmit = async (data: CreateBranchData) => {
    setIsLoading(true)
    try {
      // Convert CreateBranchData to Shop format (treating branch as shop)
      const newBranch: Shop = {
        id: `branch-${Date.now()}`,
        name: data.name,
        location: `${data.location}, ${data.country}`,
        country: data.country,
        manager: "Branch Manager", // You would get this from the managerId
        totalProducts: 0,
        totalSales: 0,
        status: "Active",
        image: "/placeholder.svg?height=150&width=150",
        description: data.description,
      }
      setShops((prev) => [...prev, newBranch])
      setIsBranchModalOpen(false)
    } catch (error) {
      console.error("Error creating branch:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusBadge = (status: Shop["status"]) => {
    switch (status) {
      case "Active":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200">
            Active
          </Badge>
        )
      case "Inactive":
        return (
          <Badge variant="destructive" className="bg-red-100 text-red-700 border-red-200">
            Inactive
          </Badge>
        )
      case "Opening Soon":
        return (
          <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-blue-200">
            Opening Soon
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (!currentUser) {
    return null // Should not happen if DashboardLayout redirects, but good for safety
  }

  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="font-semibold text-lg md:text-2xl">Shop Management</h1>
        {currentUser.role === "overall_admin" && ( // Only allow admin to add shops
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              className="h-8 gap-1"
              onClick={() => setIsBranchModalOpen(true)}
            >
              <Building2 className="h-3.5 w-3.5" />
              <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Add Branch</span>
            </Button>
            <Button
              size="sm"
              className="h-8 gap-1"
              onClick={() => setIsShopModalOpen(true)}
            >
              <Store className="h-3.5 w-3.5" />
              <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Add Shop</span>
            </Button>
          </div>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Branches</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredShops.length}</div>
            <p className="text-xs text-muted-foreground">
              {currentUser.role === "branch_manager" ? "this branch" : "across Malawi & Zambia"}
            </p>
          </CardContent>
        </Card>
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products (All Shops)</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredShops.reduce((sum, s) => sum + s.totalProducts, 0)}</div>
            <p className="text-xs text-muted-foreground">combined inventory</p>
          </CardContent>
        </Card>
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales (All Shops)</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${filteredShops.reduce((sum, s) => sum + s.totalSales, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">lifetime revenue</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={currentTab} onValueChange={setCurrentTab}>
        <div className="flex items-center justify-between flex-wrap gap-2">
          <TabsList>
            <TabsTrigger value="all">All Shops</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="inactive">Inactive</TabsTrigger>
            <TabsTrigger value="opening-soon">Opening Soon</TabsTrigger>
          </TabsList>
          <div className="relative flex-1 max-w-xs min-w-[200px]">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search shops..."
              className="pl-8 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        <TabsContent value={currentTab}>
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle>Shop List</CardTitle>
              <CardDescription>View and manage your Fathahitech branches.</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">Image</TableHead>
                    <TableHead>Shop Name</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead className="hidden md:table-cell">Manager</TableHead>
                    <TableHead className="text-right">Products</TableHead>
                    <TableHead className="text-right">Sales</TableHead>
                    <TableHead className="hidden sm:table-cell">Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedShops.length > 0 ? (
                    paginatedShops.map((shop) => (
                      <TableRow key={shop.id}>
                        <TableCell>
                          <Image
                            src={shop.image || "/placeholder.svg"}
                            alt={shop.name}
                            width={48}
                            height={48}
                            className="aspect-square rounded-md object-cover"
                            loading="lazy"
                          />
                        </TableCell>
                        <TableCell className="font-medium">{shop.name}</TableCell>
                        <TableCell>{shop.location}</TableCell>
                        <TableCell className="hidden md:table-cell">{shop.manager}</TableCell>
                        <TableCell className="text-right">{shop.totalProducts}</TableCell>
                        <TableCell className="text-right">${shop.totalSales.toLocaleString()}</TableCell>
                        <TableCell className="hidden sm:table-cell">{getStatusBadge(shop.status)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button aria-haspopup="true" size="icon" variant="ghost">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Toggle menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openEditDialog(shop)}>
                                <Edit className="mr-2 h-4 w-4" /> Edit
                              </DropdownMenuItem>
                              {currentUser.role === "overall_admin" && ( // Only allow admin to delete shops
                                <DropdownMenuItem onClick={() => handleDeleteShop(shop.id)}>
                                  <Trash2 className="mr-2 h-4 w-4" /> Delete
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                        No shops found matching your criteria.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
              <div className="flex items-center justify-end space-x-2 py-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" /> Previous
                </Button>
                <span className="text-sm text-muted-foreground">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage((prev) => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add/Edit Shop Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{editingShop ? "Edit Shop" : "Add New Shop"}</DialogTitle>
            <DialogDescription>
              {editingShop ? "Make changes to this shop here." : "Fill in the details to add a new shop."}
            </DialogDescription>
          </DialogHeader>
          <form
            onSubmit={(e) => {
              e.preventDefault()
              const formData = new FormData(e.currentTarget)
              const shopData: Shop = {
                id: editingShop?.id || `shop-${Date.now()}`,
                name: formData.get("name") as string,
                location: formData.get("location") as string,
                country: formData.get("country") as string,
                manager: formData.get("manager") as string,
                totalProducts: Number.parseInt(formData.get("totalProducts") as string),
                totalSales: Number.parseFloat(formData.get("totalSales") as string),
                status: formData.get("status") as Shop["status"],
                image: (formData.get("image") as string) || "/placeholder.svg?height=150&width=150",
                description: formData.get("description") as string,
              }
              if (editingShop) {
                handleUpdateShop(shopData)
              } else {
                handleAddShop(shopData)
              }
            }}
          >
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input id="name" name="name" defaultValue={editingShop?.name || ""} className="col-span-3" required />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="location" className="text-right">
                  Location
                </Label>
                <Input
                  id="location"
                  name="location"
                  defaultValue={editingShop?.location || ""}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="country" className="text-right">
                  Country
                </Label>
                <Select name="country" defaultValue={editingShop?.country || ""}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select a country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((c) => (
                      <SelectItem key={c} value={c}>
                        {c}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="manager" className="text-right">
                  Manager
                </Label>
                <Input
                  id="manager"
                  name="manager"
                  defaultValue={editingShop?.manager || ""}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="totalProducts" className="text-right">
                  Total Products
                </Label>
                <Input
                  id="totalProducts"
                  name="totalProducts"
                  type="number"
                  defaultValue={editingShop?.totalProducts || ""}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="totalSales" className="text-right">
                  Total Sales
                </Label>
                <Input
                  id="totalSales"
                  name="totalSales"
                  type="number"
                  step="0.01"
                  defaultValue={editingShop?.totalSales || ""}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="text-right">
                  Status
                </Label>
                <Select name="status" defaultValue={editingShop?.status || "Active"}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="Inactive">Inactive</SelectItem>
                    <SelectItem value="Opening Soon">Opening Soon</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="image" className="text-right">
                  Image URL
                </Label>
                <Input
                  id="image"
                  name="image"
                  defaultValue={editingShop?.image || ""}
                  className="col-span-3"
                  placeholder="e.g., /images/shop-blantyre.png"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  name="description"
                  defaultValue={editingShop?.description || ""}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit">{editingShop ? "Save Changes" : "Add Shop"}</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Professional Modal Components */}
      <AddShopModal
        isOpen={isShopModalOpen}
        onClose={() => setIsShopModalOpen(false)}
        onSubmit={handleShopSubmit}
        isLoading={isLoading}
      />

      <AddBranchModal
        isOpen={isBranchModalOpen}
        onClose={() => setIsBranchModalOpen(false)}
        onSubmit={handleBranchSubmit}
        isLoading={isLoading}
      />
    </>
  )
}
