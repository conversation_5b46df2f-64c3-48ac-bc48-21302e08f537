"use client"

import { useState, useEffect, useMemo } from "react"
import { Package, Warehouse, MoreHorizontal, PlusCircle, Search, ChevronLeft, ChevronRight, Edit, TrendingUp, TrendingDown, AlertTriangle, Upload } from "lucide-react"
import Image from "next/image"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { useInventoryStore } from "@/stores/inventoryStore"
import { inventoryService } from "@/services/frontend"
import AddInventoryForm from "@/components/forms/add-inventory-form"
import EditInventoryForm from "@/components/forms/edit-inventory-form"
import StockAdjustmentForm from "@/components/forms/stock-adjustment-form"
import { InventoryEmptyState, NoSearchResultsEmptyState } from "@/components/inventory/inventory-empty-state"
import { InventoryLoadingError } from "@/components/inventory/inventory-error-boundary"
import { BulkImportDialog } from "@/components/inventory/bulk-import-dialog"
import type { User } from "@/lib/auth-types"
import type { Inventory } from "@/types/frontend"

interface InventoryDashboardProps {
  currentUser: User | null
}

export default function InventoryDashboard({ currentUser }: InventoryDashboardProps) {
  // Store state
  const {
    inventoryItems,
    isLoading,
    error,
    currentPage,
    totalPages,
    totalItems,
    pageSize,
    searchQuery,
    filters,
    fetchInventoryItems,
    fetchInventoryByBranch,
    searchInventory,
    setCurrentPage,
    setSearchQuery,
    clearError,
    getLowStockItems,
    getOutOfStockItems,
  } = useInventoryStore()

  // Local state
  const [searchInput, setSearchInput] = useState("")
  const [selectedItem, setSelectedItem] = useState<Inventory | null>(null)
  const [showAddForm, setShowAddForm] = useState(false)
  const [showEditForm, setShowEditForm] = useState(false)
  const [showStockForm, setShowStockForm] = useState(false)
  const [showBulkImportDialog, setShowBulkImportDialog] = useState(false)

  // Load inventory data on mount
  useEffect(() => {
    const loadInventory = async () => {
      if (currentUser?.role === "branch_manager") {
        await fetchInventoryByBranch(currentUser.branchId, { page: 1, limit: 20 })
      } else {
        await fetchInventoryItems({ page: 1, limit: 20 })
      }
    }

    if (currentUser) {
      loadInventory()
    }
  }, [currentUser, fetchInventoryItems, fetchInventoryByBranch])

  // Clear stale errors when we have successful data
  useEffect(() => {
    if (!isLoading && inventoryItems !== undefined && error) {
      // If we're not loading and we have data (even empty array), clear any stale errors
      console.log('Clearing stale error:', error)
      clearError()
    }
  }, [isLoading, inventoryItems, error, clearError])

  // Filter inventory items based on user role
  const filteredInventoryItems = useMemo(() => {
    if (currentUser?.role === "branch_manager") {
      return inventoryItems.filter(item => item.branchId === currentUser.branchId)
    }
    return inventoryItems
  }, [inventoryItems, currentUser])

  // Get stock status
  const getStockStatus = (item: Inventory) => {
    return inventoryService.getStockStatus(item)
  }

  // Calculate stats with fallback values
  const stats = useMemo(() => {
    const totalItems = filteredInventoryItems?.length || 0
    const lowStockItems = getLowStockItems(currentUser?.role === "branch_manager" ? currentUser.branchId : undefined) || []
    const outOfStockItems = getOutOfStockItems(currentUser?.role === "branch_manager" ? currentUser.branchId : undefined) || []
    const totalValue = filteredInventoryItems?.reduce((sum, item) => sum + (item.stock * item.cost), 0) || 0

    return {
      totalItems,
      lowStockCount: lowStockItems.length,
      outOfStockCount: outOfStockItems.length,
      totalValue,
    }
  }, [filteredInventoryItems, getLowStockItems, getOutOfStockItems, currentUser])

  // Handle search
  const handleSearch = async () => {
    if (searchInput.trim()) {
      setSearchQuery(searchInput)
      await searchInventory(searchInput, { page: 1, limit: pageSize })
    } else {
      setSearchQuery("")
      if (currentUser?.role === "branch_manager") {
        await fetchInventoryByBranch(currentUser.branchId, { page: 1, limit: pageSize })
      } else {
        await fetchInventoryItems({ page: 1, limit: pageSize })
      }
    }
  }

  // Handle pagination
  const handlePageChange = async (page: number) => {
    setCurrentPage(page)
    if (searchQuery) {
      await searchInventory(searchQuery, { page, limit: pageSize })
    } else if (currentUser?.role === "branch_manager") {
      await fetchInventoryByBranch(currentUser.branchId, { page, limit: pageSize })
    } else {
      await fetchInventoryItems({ page, limit: pageSize })
    }
  }
  // Local state for tabs
  const [currentTab, setCurrentTab] = useState("all")

  // Filter inventory by tab
  const tabFilteredItems = useMemo(() => {
    switch (currentTab) {
      case "in-stock":
        return filteredInventoryItems.filter(item => {
          const status = getStockStatus(item)
          return status.status === "in_stock"
        })
      case "low-stock":
        return filteredInventoryItems.filter(item => {
          const status = getStockStatus(item)
          return status.status === "low_stock"
        })
      case "out-of-stock":
        return filteredInventoryItems.filter(item => {
          const status = getStockStatus(item)
          return status.status === "out_of_stock"
        })
      default:
        return filteredInventoryItems
    }
  }, [filteredInventoryItems, currentTab, getStockStatus])

  const getStatusBadge = (item: Inventory) => {
    const status = getStockStatus(item)

    switch (status.status) {
      case "in_stock":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200">
            {status.label}
          </Badge>
        )
      case "low_stock":
        return (
          <Badge variant="destructive" className="bg-yellow-100 text-yellow-700 border-yellow-200">
            {status.label}
          </Badge>
        )
      case "out_of_stock":
        return (
          <Badge variant="secondary" className="bg-red-100 text-red-700 border-red-200">
            {status.label}
          </Badge>
        )
      case "overstocked":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-700 border-purple-200">
            {status.label}
          </Badge>
        )
      default:
        return <Badge variant="outline">{status.label}</Badge>
    }
  }

  if (!currentUser) {
    return null // Should not happen if DashboardLayout redirects, but good for safety
  }

  // Note: We don't return early on error anymore - we show stats with fallback values
  // and display the error in the table area only

  // Determine if we should show empty state
  const shouldShowEmptyState = !isLoading && inventoryItems.length === 0
  const isSearching = searchQuery.trim().length > 0
  const hasFilters = Object.keys(filters).length > 0

  return (
    <>
      {/* Dialogs */}
      <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add Inventory Item</DialogTitle>
            <DialogDescription>
              Add a new product to your inventory
            </DialogDescription>
          </DialogHeader>
          <AddInventoryForm
            onSuccess={() => {
              setShowAddForm(false)
              // Refresh inventory
              if (currentUser?.role === "branch_manager") {
                fetchInventoryByBranch(currentUser.branchId, { page: currentPage, limit: pageSize })
              } else {
                fetchInventoryItems({ page: currentPage, limit: pageSize })
              }
            }}
            onCancel={() => setShowAddForm(false)}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={showEditForm} onOpenChange={setShowEditForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Inventory Item</DialogTitle>
            <DialogDescription>
              Update inventory settings
            </DialogDescription>
          </DialogHeader>
          {selectedItem && (
            <EditInventoryForm
              inventoryItem={selectedItem}
              onSuccess={() => {
                setShowEditForm(false)
                setSelectedItem(null)
                // Refresh inventory
                if (currentUser?.role === "branch_manager") {
                  fetchInventoryByBranch(currentUser.branchId, { page: currentPage, limit: pageSize })
                } else {
                  fetchInventoryItems({ page: currentPage, limit: pageSize })
                }
              }}
              onCancel={() => {
                setShowEditForm(false)
                setSelectedItem(null)
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      <Dialog open={showStockForm} onOpenChange={setShowStockForm}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Adjust Stock</DialogTitle>
            <DialogDescription>
              Update stock levels for this item
            </DialogDescription>
          </DialogHeader>
          {selectedItem && (
            <StockAdjustmentForm
              inventoryItem={selectedItem}
              onSuccess={() => {
                setShowStockForm(false)
                setSelectedItem(null)
                // Refresh inventory
                if (currentUser?.role === "branch_manager") {
                  fetchInventoryByBranch(currentUser.branchId, { page: currentPage, limit: pageSize })
                } else {
                  fetchInventoryItems({ page: currentPage, limit: pageSize })
                }
              }}
              onCancel={() => {
                setShowStockForm(false)
                setSelectedItem(null)
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      <div className="flex items-center justify-between">
        <h1 className="font-semibold text-lg md:text-2xl">Inventory Management</h1>
        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            className="h-8 gap-1"
            onClick={() => setShowBulkImportDialog(true)}
          >
            <Upload className="h-3.5 w-3.5" />
            <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Bulk Import</span>
          </Button>
          <Button size="sm" className="h-8 gap-1" onClick={() => setShowAddForm(true)}>
            <PlusCircle className="h-3.5 w-3.5" />
            <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Add Item</span>
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalItems}</div>
            <p className="text-xs text-muted-foreground">
              {currentUser.role === "branch_manager" ? "in this branch" : "across all branches"}
            </p>
          </CardContent>
        </Card>
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.lowStockCount}</div>
            <p className="text-xs text-muted-foreground">items need restocking</p>
          </CardContent>
        </Card>
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.outOfStockCount}</div>
            <p className="text-xs text-muted-foreground">unavailable items</p>
          </CardContent>
        </Card>
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {inventoryService.formatCost(stats.totalValue)}
            </div>
            <p className="text-xs text-muted-foreground">inventory worth</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={currentTab} onValueChange={setCurrentTab}>
        <div className="flex items-center justify-between flex-wrap gap-2">
          <TabsList>
            <TabsTrigger value="all">All Inventory</TabsTrigger>
            <TabsTrigger value="in-stock">In Stock</TabsTrigger>
            <TabsTrigger value="low-stock">Low Stock</TabsTrigger>
            <TabsTrigger value="out-of-stock">Out of Stock</TabsTrigger>
          </TabsList>
          <div className="relative flex-1 max-w-xs min-w-[200px]">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search inventory..."
              className="pl-8 w-full"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch()
                }
              }}
            />
            {searchInput !== searchQuery && (
              <Button
                size="sm"
                className="absolute right-1 top-1 h-6 px-2"
                onClick={handleSearch}
              >
                Search
              </Button>
            )}
          </div>
        </div>
        <TabsContent value={currentTab}>
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle>Detailed Inventory View</CardTitle>
              <CardDescription>Monitor stock levels and status across all branches.</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">Image</TableHead>
                    <TableHead>Product Name</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead className="hidden md:table-cell">Branch</TableHead>
                    <TableHead className="text-right">Stock</TableHead>
                    <TableHead className="hidden sm:table-cell">Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                          <span className="ml-2">Loading inventory...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : tabFilteredItems.length > 0 ? (
                    tabFilteredItems.map((item) => (
                      <TableRow key={item._id}>
                        <TableCell>
                          <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                            <Package className="h-6 w-6 text-gray-400" />
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">{item.productName}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-mono text-xs">
                            {item.sku}
                          </Badge>
                        </TableCell>
                        <TableCell className="hidden md:table-cell">{item.branchName}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex flex-col items-end">
                            <span className="font-medium">{item.stock}</span>
                            <span className="text-xs text-muted-foreground">
                              Min: {item.minStockLevel}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">{getStatusBadge(item)}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button aria-haspopup="true" size="icon" variant="ghost">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Toggle menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedItem(item)
                                  setShowEditForm(true)
                                }}
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Details
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedItem(item)
                                  setShowStockForm(true)
                                }}
                              >
                                <TrendingUp className="mr-2 h-4 w-4" />
                                Adjust Stock
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : null}
                </TableBody>
              </Table>

              {/* Error State or Empty State */}
              {error ? (
                <div className="mt-6">
                  <InventoryLoadingError
                    error={error}
                    onRetry={() => {
                      clearError()
                      if (currentUser?.role === "branch_manager") {
                        fetchInventoryByBranch(currentUser.branchId, { page: 1, limit: 20 })
                      } else {
                        fetchInventoryItems({ page: 1, limit: 20 })
                      }
                    }}
                    context="loading inventory data"
                  />
                </div>
              ) : shouldShowEmptyState ? (
                <div className="mt-6">
                  {isSearching ? (
                    <NoSearchResultsEmptyState
                      searchQuery={searchQuery}
                      onClearSearch={() => {
                        setSearchQuery('')
                        setSearchInput('')
                        if (currentUser?.role === "branch_manager") {
                          fetchInventoryByBranch(currentUser.branchId, { page: 1, limit: 20 })
                        } else {
                          fetchInventoryItems({ page: 1, limit: 20 })
                        }
                      }}
                      onAddInventory={() => setShowAddForm(true)}
                      canAddInventory={currentUser?.role === 'overall_admin' || currentUser?.role === 'branch_manager'}
                    />
                  ) : (
                    <InventoryEmptyState
                      type={hasFilters ? 'no-filter-results' : 'no-inventory'}
                      onClearFilters={hasFilters ? () => {
                        // Clear filters logic would go here
                        if (currentUser?.role === "branch_manager") {
                          fetchInventoryByBranch(currentUser.branchId, { page: 1, limit: 20 })
                        } else {
                          fetchInventoryItems({ page: 1, limit: 20 })
                        }
                      } : undefined}
                      onAddInventory={() => setShowAddForm(true)}
                      canAddInventory={currentUser?.role === 'overall_admin' || currentUser?.role === 'branch_manager'}
                    />
                  )}
                </div>
              ) : null}

              {totalPages > 1 && !shouldShowEmptyState && !error && (
                <div className="flex items-center justify-between space-x-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {Math.min((currentPage - 1) * pageSize + 1, totalItems)}-{Math.min(currentPage * pageSize, totalItems)} of {totalItems} items
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1 || isLoading}
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" /> Previous
                    </Button>
                    <span className="text-sm text-muted-foreground">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages || isLoading}
                    >
                      Next <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Bulk Import Dialog */}
      <BulkImportDialog
        open={showBulkImportDialog}
        onOpenChange={setShowBulkImportDialog}
        onImportComplete={() => {
          // Refresh inventory data after successful import
          if (currentUser?.role === "branch_manager") {
            fetchInventoryByBranch(currentUser.branchId, { page: currentPage, limit: pageSize })
          } else {
            fetchInventoryItems({ page: currentPage, limit: pageSize })
          }
        }}
      />
    </>
  )
}
