"use client"

import { useState, use<PERSON><PERSON><PERSON>, useEffect } from "react"
import {
  MoreHorizontal,
  PlusCircle,
  Edit,
  Trash2,
  User,
  Users,
  Crown,
  Search,
  ChevronLeft,
  ChevronRight,
  Mail,
  Phone,
  Building2,
  AlertCircle,
  X,
  Loader2,
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { AddEmployeeModal } from "@/components/modals"
import { useEmployeesStore } from "@/stores/employeeStore"
import { useToast } from "@/hooks/use-toast"
import type { User } from "@/lib/auth-types"
import type { Employee, CreateEmployeeData } from "@/types"

// Component now uses real data from Zustand store instead of mock data

interface EmployeeManagementDashboardProps {
  currentUser: User
}

export default function EmployeeManagementDashboard({ currentUser }: EmployeeManagementDashboardProps) {
  console.log('EmployeeDashboard: Component mounting with user:', currentUser)

  const { toast } = useToast()

  // Zustand store
  const {
    employees,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    error,
    currentPage,
    totalPages,
    totalItems,
    pageSize,
    searchQuery,
    selectedTab,
    fetchEmployees,
    createEmployee,
    updateEmployee,
    deleteEmployee,
    setSearchQuery,
    setSelectedTab,
    setPage,
    clearError,
    getFilteredEmployees,
    getEmployeesByBranch,
  } = useEmployeesStore()

  // Modal states
  const [isEmployeeModalOpen, setIsEmployeeModalOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [employeeToDelete, setEmployeeToDelete] = useState<Employee | null>(null)

  // Load employees on component mount
  useEffect(() => {
    console.log('🚀 EmployeeDashboard: useEffect triggered', {
      userRole: currentUser.role,
      userBranchId: currentUser.branchId,
      currentPage,
      pageSize
    })

    const loadEmployees = async () => {
      const filters = currentUser.role === 'branch_manager'
        ? { branchId: currentUser.branchId }
        : {}

      console.log('EmployeeDashboard: About to call fetchEmployees with:', { page: currentPage, limit: pageSize }, filters)
      console.log('EmployeeDashboard: fetchEmployees function type:', typeof fetchEmployees)
      console.log('EmployeeDashboard: fetchEmployees function:', fetchEmployees)

      try {
        const result = await fetchEmployees({ page: currentPage, limit: pageSize }, filters)
        console.log('EmployeeDashboard: fetchEmployees result:', result)
        console.log('EmployeeDashboard: fetchEmployees completed successfully')
      } catch (error) {
        console.error('EmployeeDashboard: fetchEmployees error:', error)
      }
    }

    loadEmployees()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUser.role, currentUser.branchId, currentPage, pageSize])

  // Get filtered employees from store
  const filteredEmployees = useMemo(() => {
    console.log('EmployeeDashboard: Computing filtered employees')
    console.log('EmployeeDashboard: Raw employees from store:', employees)
    console.log('EmployeeDashboard: Employees length:', employees.length)

    const allEmployees = getFilteredEmployees()
    console.log('EmployeeDashboard: Filtered employees (active only):', allEmployees)
    console.log('EmployeeDashboard: Filtered employees length:', allEmployees.length)

    // Additional filtering by user role access
    if (currentUser.role === "branch_manager") {
      const branchFiltered = allEmployees.filter(emp => emp.branchId === currentUser.branchId)
      console.log('EmployeeDashboard: Branch filtered employees:', branchFiltered)
      return branchFiltered
    }

    console.log('EmployeeDashboard: Returning all employees for overall_admin')
    return allEmployees
  }, [employees, getFilteredEmployees, currentUser.role, currentUser.branchId])

  // Pagination (using store pagination for server-side pagination)
  const paginatedEmployees = filteredEmployees

  // Modal handlers
  const handleEmployeeSubmit = async (data: CreateEmployeeData) => {
    try {
      console.log("=== DASHBOARD EMPLOYEE SUBMIT START ===")
      console.log("Dashboard received employee data:", JSON.stringify(data, null, 2))
      console.log("About to call createEmployee from store...")

      const newEmployee = await createEmployee(data)
      console.log("createEmployee returned:", newEmployee)

      if (newEmployee) {
        console.log("Employee created successfully, closing modal and refreshing list")
        setIsEmployeeModalOpen(false)
        // Refresh the employees list
        const filters = currentUser.role === 'branch_manager'
          ? { branchId: currentUser.branchId }
          : {}
        console.log("Fetching updated employees list with filters:", filters)
        await fetchEmployees({ page: currentPage, limit: pageSize }, filters)
        console.log("Employee list refreshed")
      } else {
        console.log("createEmployee returned null/undefined - creation may have failed")
      }
      console.log("=== DASHBOARD EMPLOYEE SUBMIT END ===")
      return newEmployee
    } catch (error) {
      console.error("=== DASHBOARD EMPLOYEE SUBMIT ERROR ===")
      console.error("Error creating employee:", error)
      console.error("Error details:", {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      })
      throw error
    }
  }

  const handleDeleteEmployee = (employee: Employee) => {
    setEmployeeToDelete(employee)
    setIsDeleteDialogOpen(true)
  }

  const confirmDeleteEmployee = async () => {
    if (!employeeToDelete) return

    try {
      const success = await deleteEmployee(employeeToDelete._id)
      if (success) {
        toast({
          title: "Employee Deleted",
          description: `${employeeToDelete.firstName} ${employeeToDelete.lastName} has been successfully deleted.`,
        })
        setIsDeleteDialogOpen(false)
        setEmployeeToDelete(null)
        // Refresh the employees list
        const filters = currentUser.role === 'branch_manager'
          ? { branchId: currentUser.branchId }
          : {}
        await fetchEmployees({ page: currentPage, limit: pageSize }, filters)
      } else {
        toast({
          title: "Delete Failed",
          description: "Failed to delete employee. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting employee:", error)
      toast({
        title: "Delete Failed",
        description: "An error occurred while deleting the employee. Please try again.",
        variant: "destructive",
      })
    }
  }

  const cancelDeleteEmployee = () => {
    setIsDeleteDialogOpen(false)
    setEmployeeToDelete(null)
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "branch_manager": return "default"
      case "sales_rep": return "secondary"
      case "cashier": return "outline"
      case "inventory_manager": return "secondary"
      default: return "outline"
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "Active": return "default"
      case "Inactive": return "secondary"
      case "On Leave": return "outline"
      case "Terminated": return "destructive"
      default: return "outline"
    }
  }

  return (
    <>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="font-semibold text-lg md:text-2xl">Employee Management</h1>
        <div className="flex gap-2">
          {currentUser.role === "overall_admin" && (
            <Button
              size="sm"
              variant="outline"
              className="h-8 gap-1"
              onClick={() => window.location.href = '/admin/managers'}
            >
              <Crown className="h-3.5 w-3.5" />
              <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Manage Users</span>
            </Button>
          )}
          {(currentUser.role === "overall_admin" || currentUser.role === "branch_manager") && (
            <Button 
              size="sm" 
              className="h-8 gap-1" 
              onClick={() => setIsEmployeeModalOpen(true)}
            >
              <PlusCircle className="h-3.5 w-3.5" />
              <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Add Employee</span>
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredEmployees.length}</div>
            <p className="text-xs text-muted-foreground">
              {currentUser.role === "branch_manager" ? "In your branch" : "Across all branches"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Branch Managers</CardTitle>
            <Crown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredEmployees.filter(emp => emp.position === "Branch Manager").length}
            </div>
            <p className="text-xs text-muted-foreground">Active managers</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Staff</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredEmployees.filter(emp => emp.isActive).length}
            </div>
            <p className="text-xs text-muted-foreground">Currently working</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(filteredEmployees.map(emp => emp.department)).size}
            </div>
            <p className="text-xs text-muted-foreground">Active departments</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search employees..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {/* Tabs and Table */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Employees</TabsTrigger>
          <TabsTrigger value="managers">Managers</TabsTrigger>
          <TabsTrigger value="employees">Staff</TabsTrigger>
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="inactive">Inactive</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedTab} className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Employee Directory</CardTitle>
              <CardDescription>
                Manage your team members and their information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Employee</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Branch</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                          <span className="ml-2">Loading employees...</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : paginatedEmployees.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-12">
                        <div className="flex flex-col items-center space-y-4">
                          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                            <Users className="h-8 w-8 text-muted-foreground" />
                          </div>
                          <div className="space-y-2">
                            <h3 className="font-semibold text-lg">No employees found</h3>
                            <p className="text-muted-foreground max-w-sm">
                              {searchQuery
                                ? `No employees match your search for "${searchQuery}"`
                                : selectedTab === 'managers'
                                ? 'No branch managers have been added yet'
                                : selectedTab === 'inactive'
                                ? 'No inactive employees found'
                                : 'No employees have been added to the system yet'
                              }
                            </p>
                          </div>
                          {!searchQuery && (currentUser.role === "overall_admin" || currentUser.role === "branch_manager") && (
                            <Button onClick={() => setIsEmployeeModalOpen(true)}>
                              <PlusCircle className="h-4 w-4 mr-2" />
                              Add First Employee
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    paginatedEmployees.map((employee) => (
                      <TableRow key={employee._id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                              <User className="h-4 w-4" />
                            </div>
                            <div>
                              <div className="font-medium">{employee.firstName} {employee.lastName}</div>
                              <div className="text-sm text-muted-foreground">{employee.email}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getRoleBadgeVariant(employee.position)}>
                            {employee.position === "Branch Manager" && <Crown className="h-3 w-3 mr-1" />}
                            {employee.position}
                          </Badge>
                        </TableCell>
                        <TableCell>{employee.department}</TableCell>
                        <TableCell className="text-sm">{employee.branchName}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(employee.isActive ? "Active" : "Inactive")}>
                            {employee.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2 text-sm">
                            <Mail className="h-3 w-3" />
                            <Phone className="h-3 w-3" />
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Employee
                              </DropdownMenuItem>
                              {currentUser.role === "overall_admin" && (
                                <DropdownMenuItem
                                  className="text-destructive"
                                  onClick={() => handleDeleteEmployee(employee)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Employee
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>


            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalItems)} of {totalItems} employees
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(currentPage - 1)}
              disabled={currentPage <= 1 || isLoading}
            >
              Previous
            </Button>
            <div className="text-sm">
              Page {currentPage} of {totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(currentPage + 1)}
              disabled={currentPage >= totalPages || isLoading}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Error Display - Only show for actual errors, not empty states */}
      {error && !isLoading && employees.length === 0 && error.includes('Failed to fetch') && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
            <Button variant="ghost" size="sm" onClick={clearError}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Professional Modal Components */}
      <AddEmployeeModal
        isOpen={isEmployeeModalOpen}
        onClose={() => setIsEmployeeModalOpen(false)}
        onSubmit={handleEmployeeSubmit}
        isLoading={isCreating}
        currentUserRole={currentUser.role}
        currentUserBranchId={currentUser.branchId}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Employee</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete <strong>{employeeToDelete?.firstName} {employeeToDelete?.lastName}</strong>?
              This action cannot be undone and will permanently remove the employee from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteEmployee}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteEmployee}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Employee"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </>
  )
}
