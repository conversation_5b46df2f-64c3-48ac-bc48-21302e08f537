"use client"

import { useState, useEffect } from "react"
import { 
  Plus, 
  Search, 
  RefreshCw, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Tag, 
  List, 
  Package,
  AlertCircle,
  Filter,
  Download,
  Upload
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import EnhancedCategoryModal from "@/components/modals/enhanced-category-modal"
import { useCategoryStore } from "@/stores/categoryStore"
import type { User } from "@/lib/auth-types"
import type { CreateProductCategoryData } from "@/types"

interface CategoryManagementDashboardProps {
  currentUser: User | null
}

export default function CategoryManagementDashboard({ currentUser }: CategoryManagementDashboardProps) {
  const { toast } = useToast()

  // Zustand store
  const {
    categories,
    activeCategories,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isUploadingImages,
    error,
    pagination,
    filters,
    fetchCategories,
    fetchActiveCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    createCategoryWithImages,
    updateCategoryWithImages,
    setFilters,
    setPagination,
    clearError
  } = useCategoryStore()

  // Local state
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<any>(null)
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState<{
    isOpen: boolean
    category: any
  }>({ isOpen: false, category: null })

  // Load initial data
  useEffect(() => {
    loadInitialData()
  }, [])

  // Handle search and filter changes
  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      handleSearch()
    }, 300)

    return () => clearTimeout(delayedSearch)
  }, [searchTerm, statusFilter])

  const loadInitialData = async () => {
    try {
      await Promise.all([
        fetchCategories(),
        fetchActiveCategories()
      ])
    } catch (error) {
      console.error('Failed to load initial data:', error)
    }
  }

  const handleSearch = async () => {
    const activeOnly = statusFilter === "active"
    await fetchCategories(
      { page: 1, limit: pagination.limit },
      searchTerm,
      statusFilter === "active" ? true : statusFilter === "inactive" ? false : undefined
    )
  }

  const handlePageChange = async (newPage: number) => {
    setPagination({ page: newPage })
    await fetchCategories(
      { page: newPage, limit: pagination.limit },
      searchTerm,
      statusFilter === "active" ? true : statusFilter === "inactive" ? false : undefined
    )
  }

  const handleCategorySubmit = async (data: CreateProductCategoryData & {
    _featuredImageFile?: File | null
    _iconFile?: File | null
  }) => {
    try {
      // Extract image files from data
      const { _featuredImageFile, _iconFile, ...categoryData } = data

      // Prepare image data if files are provided
      const imageData = (_featuredImageFile || _iconFile) ? {
        featuredImage: _featuredImageFile,
        icon: _iconFile,
        iconType: data.iconType,
        iconName: data.iconName
      } : undefined

      if (editingCategory) {
        const result = imageData
          ? await updateCategoryWithImages(editingCategory.id, categoryData, imageData)
          : await updateCategory(editingCategory.id, categoryData)

        if (result) {
          toast({
            title: "Success",
            description: "Category updated successfully",
          })
          setIsCategoryModalOpen(false)
          setEditingCategory(null)
        }
      } else {
        const result = imageData
          ? await createCategoryWithImages(categoryData, imageData)
          : await createCategory(categoryData)

        if (result) {
          toast({
            title: "Success",
            description: "Category created successfully",
          })
          setIsCategoryModalOpen(false)
        }
      }
    } catch (error) {
      console.error("Error with category operation:", error)
      toast({
        title: "Error",
        description: editingCategory ? "Failed to update category" : "Failed to create category",
        variant: "destructive",
      })
    }
  }

  const handleDeleteCategory = async () => {
    if (!deleteConfirmDialog.category) return

    try {
      const result = await deleteCategory(deleteConfirmDialog.category.id)
      if (result) {
        toast({
          title: "Success",
          description: "Category deleted successfully",
        })
        setDeleteConfirmDialog({ isOpen: false, category: null })
      } else {
        toast({
          title: "Error",
          description: "Failed to delete category",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error deleting category:", error)
      toast({
        title: "Error",
        description: "Failed to delete category",
        variant: "destructive",
      })
    }
  }

  const openEditCategoryDialog = (category: any) => {
    setEditingCategory(category)
    setIsCategoryModalOpen(true)
  }

  const openAddCategoryDialog = () => {
    setEditingCategory(null)
    setIsCategoryModalOpen(true)
  }

  const openDeleteConfirmation = (category: any) => {
    setDeleteConfirmDialog({ isOpen: true, category })
  }

  const getStatusBadge = (category: any) => {
    if (category.isActive) {
      return (
        <Badge variant="outline" className="bg-green-100 text-green-700 border-green-200">
          Active
        </Badge>
      )
    } else {
      return (
        <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-200">
          Inactive
        </Badge>
      )
    }
  }

  const filteredCategories = categories.filter(category => {
    if (statusFilter === "active" && !category.isActive) return false
    if (statusFilter === "inactive" && category.isActive) return false
    return true
  })

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Category Management</h1>
          <p className="text-muted-foreground">
            Manage product categories for better organization and navigation
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            className="h-8 gap-1"
            onClick={loadInitialData}
            disabled={isLoading}
          >
            <RefreshCw className={`h-3.5 w-3.5 ${isLoading ? 'animate-spin' : ''}`} />
            <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Refresh</span>
          </Button>
          <Button
            size="sm"
            className="h-8 gap-1"
            onClick={openAddCategoryDialog}
            disabled={isCreating}
          >
            <Plus className="h-3.5 w-3.5" />
            <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">Add Category</span>
          </Button>
        </div>
      </div>

      {/* Error Messages */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={() => {
                clearError()
                loadInitialData()
              }}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Statistics Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Categories</CardTitle>
            <Tag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">{categories.length}</div>
            )}
            <p className="text-xs text-muted-foreground">all categories</p>
          </CardContent>
        </Card>
        
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Categories</CardTitle>
            <List className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-12" />
            ) : (
              <div className="text-2xl font-bold">{activeCategories.length}</div>
            )}
            <p className="text-xs text-muted-foreground">currently active</p>
          </CardContent>
        </Card>
        
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <div className="text-2xl font-bold">
                {categories.reduce((sum, cat) => sum + (cat.productCount || 0), 0)}
              </div>
            )}
            <p className="text-xs text-muted-foreground">across all categories</p>
          </CardContent>
        </Card>
        
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-12" />
            ) : (
              <div className="text-2xl font-bold">
                {categories.length > 0 
                  ? Math.round(categories.reduce((sum, cat) => sum + (cat.productCount || 0), 0) / categories.length)
                  : 0
                }
              </div>
            )}
            <p className="text-xs text-muted-foreground">per category</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter Controls */}
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle>Categories</CardTitle>
          <CardDescription>Manage and organize your product categories</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search categories..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={isLoading}
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="active">Active Only</SelectItem>
                <SelectItem value="inactive">Inactive Only</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Categories Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead className="hidden md:table-cell">Description</TableHead>
                  <TableHead className="text-center">Products</TableHead>
                  <TableHead className="text-center">Status</TableHead>
                  <TableHead className="hidden sm:table-cell">Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  // Loading skeleton
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell className="hidden md:table-cell"><Skeleton className="h-4 w-48" /></TableCell>
                      <TableCell className="text-center"><Skeleton className="h-4 w-8" /></TableCell>
                      <TableCell className="text-center"><Skeleton className="h-6 w-20" /></TableCell>
                      <TableCell className="hidden sm:table-cell"><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-8 w-8" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredCategories.length > 0 ? (
                  filteredCategories.map((category) => (
                    <TableRow key={category.id}>
                      <TableCell className="font-medium">{category.name}</TableCell>
                      <TableCell className="hidden md:table-cell max-w-xs truncate">
                        {category.description}
                      </TableCell>
                      <TableCell className="text-center">{category.productCount || 0}</TableCell>
                      <TableCell className="text-center">{getStatusBadge(category)}</TableCell>
                      <TableCell className="hidden sm:table-cell">
                        {new Date(category.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => openEditCategoryDialog(category)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => openDeleteConfirmation(category)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                      {error ? (
                        <div className="flex flex-col items-center gap-2">
                          <AlertCircle className="h-8 w-8 text-muted-foreground" />
                          <p>Failed to load categories</p>
                          <Button variant="outline" size="sm" onClick={loadInitialData}>
                            Try Again
                          </Button>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center gap-2">
                          <Tag className="h-8 w-8 text-muted-foreground" />
                          <p>No categories found.</p>
                          <Button variant="outline" size="sm" onClick={openAddCategoryDialog}>
                            Create Your First Category
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between space-x-2 py-4">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                {pagination.total} categories
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1 || isLoading}
                >
                  Previous
                </Button>
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    const pageNumber = i + 1
                    return (
                      <Button
                        key={pageNumber}
                        variant={pagination.page === pageNumber ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(pageNumber)}
                        disabled={isLoading}
                      >
                        {pageNumber}
                      </Button>
                    )
                  })}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages || isLoading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Enhanced Category Modal */}
      <EnhancedCategoryModal
        isOpen={isCategoryModalOpen}
        onClose={() => {
          setIsCategoryModalOpen(false)
          setEditingCategory(null)
        }}
        onSubmit={handleCategorySubmit}
        isLoading={isCreating || isUpdating || isUploadingImages}
        initialData={editingCategory}
        mode={editingCategory ? "edit" : "create"}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmDialog.isOpen}
        onOpenChange={(open) => setDeleteConfirmDialog({ isOpen: open, category: null })}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Category</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{deleteConfirmDialog.category?.name}"?
              This action cannot be undone and will affect {deleteConfirmDialog.category?.productCount || 0} products.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteConfirmDialog({ isOpen: false, category: null })}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCategory}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Category"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
