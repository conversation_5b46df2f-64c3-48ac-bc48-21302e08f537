"use client"

import type React from "react"
import { useEffect } from "react" // Keep useState and useEffect for router push
import Link from "next/link"
import Image from "next/image"
import {
  Home,
  Package2,
  Search,
  ShoppingCart,
  Users,
  Warehouse,
  Truck,
  BarChart,
  Settings,
  Package,
  Store,
  Building,
  LogOut,
  UserCheck,
  Shield,
  Tag,
} from "lucide-react"
import { useRouter, usePathname } from "next/navigation"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useTheme } from "next-themes"
import { Moon, Sun } from "lucide-react"
import type { User } from "@/lib/auth-types"
import { useAuthStore } from "@/stores/authStore"
import { SessionNotifications } from "@/components/auth/session-notifications"
import { NotificationBell } from "@/components/notifications/notification-bell"
import { ConnectionStatus } from "@/components/notifications/real-time-notifications"

interface DashboardLayoutProps {
  children: React.ReactNode
  currentUser: User | null
}

export default function DashboardLayout({ children, currentUser }: DashboardLayoutProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { theme, setTheme } = useTheme()
  const { logout } = useAuthStore()

  const handleSignOut = async () => {
    await logout()
    router.push("/")
  }

  // Server-side authentication ensures we have a valid user
  // No need for client-side redirect checks since server handles auth

  const isAdmin = currentUser.role === "overall_admin"
  const canManageEmployees = ["overall_admin", "branch_manager"].includes(currentUser.role)

  return (
    <div className="grid min-h-screen w-full overflow-hidden lg:grid-cols-[280px_1fr]">
      {/* Sidebar Navigation */}
      <div className="hidden border-r bg-muted/40 lg:block">
        <div className="flex h-full max-h-screen flex-col gap-2">
          <div className="flex h-[60px] items-center border-b px-6">
            <Link href="#" className="flex items-center gap-2 font-semibold">
              <Image
                src="/logoTransparent.png"
                alt="Fathahitech Logo"
                width={24}
                height={24}
                className="h-6 w-auto"
              />
              <span className="text-lg">Fathahitech Admin</span>
            </Link>
          </div>
          <div className="flex-1 overflow-auto py-2">
            <nav className="grid items-start px-4 text-sm font-medium">
              <Link
                href="/dashboard"
                className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                  pathname === "/dashboard" ? "bg-muted text-primary" : "text-muted-foreground"
                }`}
              >
                <Home className="h-4 w-4" />
                Dashboard
              </Link>
              <Link
                href="/inventory"
                className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                  pathname === "/inventory" ? "bg-muted text-primary" : "text-muted-foreground"
                }`}
              >
                <Package className="h-4 w-4" />
                Inventory
              </Link>
              <Link
                href="/warehouses"
                className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                  pathname === "/warehouses" ? "bg-muted text-primary" : "text-muted-foreground"
                }`}
              >
                <Warehouse className="h-4 w-4" />
                Warehouses
              </Link>
              <Link
                href="/admin-products"
                className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                  pathname === "/admin-products" ? "bg-muted text-primary" : "text-muted-foreground"
                }`}
              >
                <Package className="h-4 w-4" />
                Manage Products
              </Link>
              <Link
                href="/admin-categories"
                className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                  pathname === "/admin-categories" ? "bg-muted text-primary" : "text-muted-foreground"
                }`}
              >
                <Tag className="h-4 w-4" />
                Categories
              </Link>
              {isAdmin && ( // Only show Shops/Branches link for overall admin
                <Link
                  href="/shops"
                  className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                    pathname === "/shops" || pathname.startsWith("/shops") ? "bg-muted text-primary" : "text-muted-foreground"
                  }`}
                >
                  <Store className="h-4 w-4" />
                  Branches
                </Link>
              )}
              {isAdmin && ( // Only show Managers link for overall admin
                <Link
                  href="/admin/managers"
                  className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                    pathname === "/admin/managers" || pathname.startsWith("/admin/managers") ? "bg-muted text-primary" : "text-muted-foreground"
                  }`}
                >
                  <UserCheck className="h-4 w-4" />
                  Managers
                </Link>
              )}
              {canManageEmployees && ( // Show for overall admin and branch managers
                <Link
                  href="/employees"
                  className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                    pathname === "/employees" ? "bg-muted text-primary" : "text-muted-foreground"
                  }`}
                >
                  <UserCheck className="h-4 w-4" />
                  Employees
                </Link>
              )}
              <Link
                href="/sales"
                className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                  pathname === "/sales" ? "bg-muted text-primary" : "text-muted-foreground"
                }`}
              >
                <ShoppingCart className="h-4 w-4" />
                Sales
              </Link>
              <Link
                href="/delivery"
                className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                  pathname === "/delivery" ? "bg-muted text-primary" : "text-muted-foreground"
                }`}
              >
                <Truck className="h-4 w-4" />
                Delivery
              </Link>
              <Link
                href="/customers"
                className={`flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary ${
                  pathname === "/customers" ? "bg-muted text-primary" : "text-muted-foreground"
                }`}
              >
                <Users className="h-4 w-4" />
                Customers
              </Link>
              {isAdmin && ( // Only show Analytics link for overall admin
                <Link
                  href="/analytics"
                  className={`flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary ${
                    pathname === "/analytics" ? "bg-muted text-primary" : "text-muted-foreground"
                  }`}
                >
                  <BarChart className="h-4 w-4" />
                  Analytics
                </Link>
              )}
              {isAdmin && ( // Only show Session Management for overall admin
                <Link
                  href="/sessions"
                  className={`flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary ${
                    pathname.startsWith("/sessions") ? "bg-muted text-primary" : "text-muted-foreground"
                  }`}
                >
                  <Shield className="h-4 w-4" />
                  Session Management
                </Link>
              )}
              <Link
                href="/settings"
                className={`flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary ${
                  pathname === "/settings" ? "bg-muted text-primary" : "text-muted-foreground"
                }`}
              >
                <Settings className="h-4 w-4" />
                Settings
              </Link>
            </nav>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex flex-col">
        {/* Header */}
        <header className="flex h-14 lg:h-[60px] items-center gap-4 border-b bg-muted/40 px-6">
          <Link href="#" className="lg:hidden">
            <Image
              src="/logoTransparent.png"
              alt="Fathahitech Logo"
              width={24}
              height={24}
              className="h-6 w-auto"
            />
            <span className="sr-only">Home</span>
          </Link>
          <div className="flex-1">{/* Title will be set by individual dashboard pages */}</div>
          <div className="flex flex-1 items-center gap-4 md:ml-auto md:gap-2 lg:gap-4">
            <form className="ml-auto flex-1 sm:flex-initial">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input type="search" placeholder="Search..." className="pl-8 sm:w-[300px] md:w-[200px] lg:w-[300px]" />
              </div>
            </form>
            <ConnectionStatus />
            <NotificationBell showBadge={true} showSettings={true} />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "light" ? "dark" : "light")}
              className="h-9 w-9"
            >
              <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="rounded-full">
                  <Image src="/placeholder-user.jpg" width="32" height="32" className="rounded-full" alt="Avatar" />
                  <span className="sr-only">Toggle user menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>{currentUser.name}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuItem>Support</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" /> Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="flex flex-1 flex-col gap-6 p-4 md:gap-8 md:p-6">{children}</main>
      </div>

      {/* Session Notifications */}
      <SessionNotifications />
    </div>
  )
}
