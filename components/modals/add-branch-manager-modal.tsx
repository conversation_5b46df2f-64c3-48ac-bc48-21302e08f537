"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { 
  Loader2, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  DollarSign,
  Building2,
  Shield,
  Award,
  Plus,
  X,
  Crown
} from "lucide-react"
import BaseModal from "./base-modal"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { CreateBranchManagerData, FormModalProps } from "@/types"
import { useBranchStore } from "@/stores/branchStore"

const branchManagerSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters").max(50, "First name must be less than 50 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters").max(50, "Last name must be less than 50 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 characters"),
  branchId: z.string().min(1, "Please select a branch"),
  hireDate: z.string().min(1, "Hire date is required"),
  salary: z.number().min(1, "Salary must be greater than 0"),
  address: z.object({
    street: z.string().min(1, "Street address is required"),
    city: z.string().min(1, "City is required"),
    region: z.string().min(1, "Region is required"),
    country: z.string().default("Malawi"),
    postalCode: z.string().optional()
  }),
  emergencyContact: z.object({
    name: z.string().min(2, "Emergency contact name is required"),
    phone: z.string().min(10, "Emergency contact phone is required"),
    relationship: z.string().min(1, "Relationship is required"),
  }),
  permissions: z.array(z.string()).default([]),
  managerialExperience: z.number().min(0, "Experience cannot be negative"),
  qualifications: z.array(z.string()).default([]),
})

type BranchManagerFormData = z.infer<typeof branchManagerSchema>

const managerPermissions = [
  "manage_employees", "view_all_reports", "edit_branch_settings",
  "approve_orders", "manage_inventory", "handle_customer_complaints",
  "process_refunds", "view_financial_reports", "manage_schedules",
  "approve_discounts", "manage_suppliers", "conduct_performance_reviews"
]



interface AddBranchManagerModalProps extends FormModalProps<CreateBranchManagerData> {}

export default function AddBranchManagerModal({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
  initialData,
  mode = "create"
}: AddBranchManagerModalProps) {
  const [newQualification, setNewQualification] = useState("")

  // Fetch branches from the store
  const { branches, fetchBranches, isLoading: branchesLoading } = useBranchStore()

  const form = useForm<BranchManagerFormData>({
    resolver: zodResolver(branchManagerSchema),
    defaultValues: {
      firstName: initialData?.firstName || "",
      lastName: initialData?.lastName || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      branchId: initialData?.branchId || "",
      hireDate: initialData?.hireDate || new Date().toISOString().split('T')[0],
      salary: initialData?.salary || 0,
      address: {
        street: initialData?.address?.street || "",
        city: initialData?.address?.city || "",
        region: initialData?.address?.region || "",
        country: initialData?.address?.country || "Malawi",
        postalCode: initialData?.address?.postalCode || ""
      },
      emergencyContact: {
        name: initialData?.emergencyContact?.name || "",
        phone: initialData?.emergencyContact?.phone || "",
        relationship: initialData?.emergencyContact?.relationship || "",
      },
      permissions: initialData?.permissions || managerPermissions, // Default all permissions for managers
      managerialExperience: initialData?.managerialExperience || 0,
      qualifications: initialData?.qualifications || [],
    },
  })

  // Fetch branches when modal opens
  useEffect(() => {
    if (isOpen && branches.length === 0) {
      fetchBranches()
    }
  }, [isOpen, branches.length, fetchBranches])

  const handleSubmit = async (data: BranchManagerFormData) => {
    try {
      console.log("=== BRANCH MANAGER FORM SUBMISSION ===")
      console.log("Raw form data:", data)

      // Transform form data to match API expectations
      const transformedData: CreateBranchManagerData = {
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        position: "Branch Manager", // Required field
        department: "Management", // Required field
        role: "branch_manager", // Required field
        branchId: data.branchId,
        hireDate: data.hireDate,
        salary: data.salary,
        address: data.address, // Already in correct object format
        emergencyContact: data.emergencyContact,
        permissions: data.permissions,
        managerialExperience: data.managerialExperience,
        qualifications: data.qualifications
      }

      console.log("Transformed data:", transformedData)
      console.log("Address object:", transformedData.address)
      console.log("Position field:", transformedData.position)
      console.log("=== SUBMITTING TO PARENT ===")

      await onSubmit(transformedData)
      form.reset()
      onClose()
    } catch (error) {
      console.error("Error submitting branch manager:", error)
    }
  }

  const addQualification = () => {
    if (newQualification.trim()) {
      const currentQualifications = form.getValues("qualifications")
      form.setValue("qualifications", [...currentQualifications, newQualification.trim()])
      setNewQualification("")
    }
  }

  const removeQualification = (index: number) => {
    const currentQualifications = form.getValues("qualifications")
    form.setValue("qualifications", currentQualifications.filter((_, i) => i !== index))
  }

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === "create" ? "Add Branch Manager" : "Edit Branch Manager"}
      description={mode === "create" ? "Create a new branch manager account with full permissions" : "Update branch manager information"}
      size="xl"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Personal Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Crown className="h-5 w-5 text-primary" />
              Manager Personal Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Email Address
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Phone Number
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="+265 1 234 567" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Management Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Management Assignment
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="branchId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Assigned Branch</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select branch to manage" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {branchesLoading ? (
                          <SelectItem value="loading" disabled>
                            Loading branches...
                          </SelectItem>
                        ) : branches.length === 0 ? (
                          <SelectItem value="no-branches" disabled>
                            No branches available
                          </SelectItem>
                        ) : (
                          branches
                            .filter(branch => branch.status === 'Active')
                            .map((branch) => (
                              <SelectItem key={branch._id} value={branch._id}>
                                {branch.name} - {branch.location}
                              </SelectItem>
                            ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="managerialExperience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Award className="h-4 w-4" />
                      Management Experience (Years)
                    </FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="5" 
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>Years of management experience</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Employment Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Employment Details</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="hireDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Start Date
                    </FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="salary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Monthly Salary
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="150000"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>Manager salary in local currency</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Address */}
          <div className="space-y-4">
            <h4 className="text-md font-semibold flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Home Address
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="address.street"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Street Address</FormLabel>
                    <FormControl>
                      <Input placeholder="123 Main Street" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="address.city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>City</FormLabel>
                    <FormControl>
                      <Input placeholder="Lilongwe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="address.region"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Region</FormLabel>
                    <FormControl>
                      <Input placeholder="Central" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="address.country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <FormControl>
                      <Input placeholder="Malawi" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="address.postalCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Postal Code</FormLabel>
                    <FormControl>
                      <Input placeholder="12345" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Emergency Contact */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Emergency Contact</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="emergencyContact.name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contact Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Jane Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="emergencyContact.phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contact Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="+265 9 876 543" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="emergencyContact.relationship"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Relationship</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select relationship" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="spouse">Spouse</SelectItem>
                        <SelectItem value="parent">Parent</SelectItem>
                        <SelectItem value="sibling">Sibling</SelectItem>
                        <SelectItem value="child">Child</SelectItem>
                        <SelectItem value="friend">Friend</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Qualifications */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Award className="h-5 w-5" />
              Qualifications & Certifications
            </h3>
            <div className="flex gap-2">
              <Input
                placeholder="Add qualification (e.g., MBA, Bachelor's in Business)"
                value={newQualification}
                onChange={(e) => setNewQualification(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addQualification())}
              />
              <Button type="button" onClick={addQualification} size="icon">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {form.watch("qualifications").map((qualification, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {qualification}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removeQualification(index)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Manager Permissions */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Manager Permissions
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {managerPermissions.map((permission) => (
                <FormField
                  key={permission}
                  control={form.control}
                  name="permissions"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value?.includes(permission)}
                          onCheckedChange={(checked) => {
                            const updatedPermissions = checked
                              ? [...(field.value || []), permission]
                              : (field.value || []).filter((p) => p !== permission)
                            field.onChange(updatedPermissions)
                          }}
                        />
                      </FormControl>
                      <FormLabel className="text-sm font-normal">
                        {permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </FormLabel>
                    </FormItem>
                  )}
                />
              ))}
            </div>
            <div className="text-sm text-muted-foreground">
              Branch managers have elevated permissions to manage their branch operations effectively.
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="min-w-[140px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {mode === "create" ? "Creating..." : "Updating..."}
                </>
              ) : (
                mode === "create" ? "Create Manager" : "Update Manager"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  )
}
