'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { BaseModal } from '@/components/modals/base-modal'
import { Building2, User, Crown } from 'lucide-react'

const assignManagerSchema = z.object({
  userId: z.string().min(1, 'Please select a user'),
  branchId: z.string().min(1, 'Please select a branch'),
})

type AssignManagerFormData = z.infer<typeof assignManagerSchema>

interface User {
  id: string
  username: string
  email: string
  name: string
  role: 'overall_admin' | 'branch_manager'
  branchId?: string
  branchName?: string
  isActive: boolean
}

interface Branch {
  _id: string
  name: string
  location: string
  manager: string
  managerId?: string
}

interface AssignBranchManagerModalProps {
  isOpen: boolean
  onClose: () => void
  onManagerAssigned: () => void
  selectedUser?: User | null
  users: User[]
  branches: Branch[]
}

export default function AssignBranchManagerModal({
  isOpen,
  onClose,
  onManagerAssigned,
  selectedUser,
  users,
  branches
}: AssignBranchManagerModalProps) {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<AssignManagerFormData>({
    resolver: zodResolver(assignManagerSchema),
    defaultValues: {
      userId: '',
      branchId: '',
    },
  })

  // Set selected user when modal opens
  useEffect(() => {
    if (selectedUser && isOpen) {
      form.setValue('userId', selectedUser.id)
    }
  }, [selectedUser, isOpen, form])

  const handleSubmit = async (data: AssignManagerFormData) => {
    setIsLoading(true)
    try {
      // Update the branch with the new manager
      const response = await fetch(`/api/branches/${data.branchId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify({
          managerId: data.userId
        })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        onManagerAssigned()
        form.reset()
      } else {
        console.error('Error assigning manager:', result.error)
        // You might want to show a toast notification here
      }
    } catch (error) {
      console.error('Error assigning manager:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  // Filter available branches (exclude those that already have managers)
  const availableBranches = branches.filter(branch => !branch.managerId)
  
  // Filter available users (only branch managers without assigned branches)
  const availableUsers = users.filter(user => 
    user.role === 'branch_manager' && 
    user.isActive && 
    !user.branchId
  )

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleClose}
      title="Assign Branch Manager"
      description="Assign a user account as a branch manager to a specific branch"
      size="md"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Building2 className="h-5 w-5 text-primary" />
              Manager Assignment
            </h3>
            
            <FormField
              control={form.control}
              name="userId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Select User Account</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a user to assign as manager" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {availableUsers.length === 0 ? (
                        <div className="p-2 text-sm text-muted-foreground">
                          No available branch manager accounts found
                        </div>
                      ) : (
                        availableUsers.map((user) => (
                          <SelectItem key={user.id} value={user.id}>
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              <div>
                                <div className="font-medium">{user.name}</div>
                                <div className="text-xs text-muted-foreground">
                                  @{user.username} • {user.email}
                                </div>
                              </div>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="branchId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Select Branch</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a branch to manage" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {availableBranches.length === 0 ? (
                        <div className="p-2 text-sm text-muted-foreground">
                          No branches available for assignment
                        </div>
                      ) : (
                        availableBranches.map((branch) => (
                          <SelectItem key={branch._id} value={branch._id}>
                            <div className="flex items-center gap-2">
                              <Building2 className="h-4 w-4" />
                              <div>
                                <div className="font-medium">{branch.name}</div>
                                <div className="text-xs text-muted-foreground">
                                  {branch.location}
                                </div>
                              </div>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Information Box */}
          <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
            <div className="flex items-start gap-2">
              <Crown className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                  Manager Assignment
                </p>
                <p className="text-blue-700 dark:text-blue-300">
                  The selected user will be assigned as the manager of the chosen branch and will gain access to the branch dashboard with management permissions.
                </p>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading || availableUsers.length === 0 || availableBranches.length === 0}
            >
              {isLoading ? 'Assigning...' : 'Assign Manager'}
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  )
}
