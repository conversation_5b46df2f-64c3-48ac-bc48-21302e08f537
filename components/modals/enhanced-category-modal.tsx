"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { 
  Loader2, 
  Tag, 
  FileText, 
  ToggleLeft, 
  Upload, 
  Image as ImageIcon,
  Trash2,
  Star,
  Eye,
  X
} from "lucide-react"
import BaseModal from "./base-modal"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { CreateProductCategoryData, FormModalProps } from "@/types"
import Image from "next/image"

// Enhanced schema with image fields - handle File objects for images
const categorySchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters").max(50, "Name cannot exceed 50 characters"),
  description: z.string().min(10, "Description must be at least 10 characters").max(500, "Description cannot exceed 500 characters"),
  isActive: z.boolean().default(true),
  // Image handling - store as File objects, not URLs
  featuredImage: z.any().optional(), // Will be File object
  icon: z.any().optional(), // Will be File object
  iconType: z.enum(['image', 'lucide']).default('lucide'),
  iconName: z.string().optional().nullable()
})

type CategoryFormData = z.infer<typeof categorySchema>

interface EnhancedCategoryModalProps extends Omit<FormModalProps<CreateProductCategoryData>, 'onSubmit'> {
  onSubmit: (data: CategoryFormData & {
    _featuredImageFile?: File | null
    _iconFile?: File | null
  }) => void
}

// Popular Lucide icons for categories
const POPULAR_ICONS = [
  { name: 'Laptop', value: 'laptop' },
  { name: 'Smartphone', value: 'smartphone' },
  { name: 'Monitor', value: 'monitor' },
  { name: 'Headphones', value: 'headphones' },
  { name: 'Camera', value: 'camera' },
  { name: 'Gamepad', value: 'gamepad-2' },
  { name: 'Keyboard', value: 'keyboard' },
  { name: 'Mouse', value: 'mouse' },
  { name: 'Printer', value: 'printer' },
  { name: 'Router', value: 'router' },
  { name: 'Speaker', value: 'speaker' },
  { name: 'Tablet', value: 'tablet' },
  { name: 'TV', value: 'tv' },
  { name: 'Watch', value: 'watch' },
  { name: 'Cpu', value: 'cpu' },
  { name: 'HardDrive', value: 'hard-drive' },
  { name: 'Memory', value: 'memory-stick' },
  { name: 'Wifi', value: 'wifi' },
  { name: 'Bluetooth', value: 'bluetooth' },
  { name: 'USB', value: 'usb' }
]

export default function EnhancedCategoryModal({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
  initialData,
  mode = "create"
}: EnhancedCategoryModalProps) {
  // Image file state
  const [featuredImageFile, setFeaturedImageFile] = useState<File | null>(null)
  const [iconFile, setIconFile] = useState<File | null>(null)

  // Image preview state
  const [featuredImagePreview, setFeaturedImagePreview] = useState<string | null>(null)
  const [iconPreview, setIconPreview] = useState<string | null>(null)

  // Drag and drop state
  const [isDragOver, setIsDragOver] = useState(false)

  const form = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: initialData?.name || "",
      description: initialData?.description || "",
      isActive: initialData?.isActive ?? true,
      featuredImage: null, // Will be File object
      icon: null, // Will be File object
      iconType: initialData?.iconType || 'lucide',
      iconName: initialData?.iconName || null
    },
  })

  // Reset form when initialData changes
  useEffect(() => {
    if (initialData) {
      form.reset({
        name: initialData.name || "",
        description: initialData.description || "",
        isActive: initialData.isActive ?? true,
        featuredImage: null, // Reset to null, existing images shown via preview
        icon: null, // Reset to null, existing images shown via preview
        iconType: initialData.iconType || 'lucide',
        iconName: initialData.iconName || null
      })
      setFeaturedImagePreview(initialData.featuredImage || null)
      setIconPreview(initialData.icon || null)
      setFeaturedImageFile(null)
      setIconFile(null)
    } else {
      form.reset({
        name: "",
        description: "",
        isActive: true,
        featuredImage: null,
        icon: null,
        iconType: 'lucide',
        iconName: null
      })
      setFeaturedImagePreview(null)
      setIconPreview(null)
      setFeaturedImageFile(null)
      setIconFile(null)
    }
  }, [initialData, form])

  const handleSubmit = (data: CategoryFormData) => {
    // Prepare the category data with image files
    const categoryData = {
      ...data,
      featuredImage: undefined, // Remove File object
      icon: undefined, // Remove File object
      _featuredImageFile: featuredImageFile,
      _iconFile: iconFile,
    }
    onSubmit(categoryData)
  }

  // Image validation function
  const validateImageFile = (file: File): { valid: boolean; error?: string } => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Invalid file type. Please use JPEG, PNG, WebP, or GIF.' }
    }

    if (file.size > maxSize) {
      return { valid: false, error: 'File size too large. Maximum size is 10MB.' }
    }

    return { valid: true }
  }

  // Create image preview
  const createImagePreview = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.readAsDataURL(file)
    })
  }

  // Handle featured image selection
  const handleFeaturedImageSelect = async (file: File) => {
    const validation = validateImageFile(file)
    if (!validation.valid) {
      alert(validation.error)
      return
    }

    form.setValue("featuredImage", file)
    setFeaturedImageFile(file)
    const preview = await createImagePreview(file)
    setFeaturedImagePreview(preview)
  }

  // Handle icon image selection
  const handleIconImageSelect = async (file: File) => {
    const validation = validateImageFile(file)
    if (!validation.valid) {
      alert(validation.error)
      return
    }

    form.setValue("icon", file)
    form.setValue("iconType", "image")
    setIconFile(file)
    const preview = await createImagePreview(file)
    setIconPreview(preview)
  }

  // Remove featured image
  const removeFeaturedImage = () => {
    form.setValue("featuredImage", null)
    setFeaturedImageFile(null)
    setFeaturedImagePreview(null)
  }

  // Remove icon
  const removeIcon = () => {
    form.setValue("icon", null)
    form.setValue("iconType", "lucide")
    form.setValue("iconName", null)
    setIconFile(null)
    setIconPreview(null)
  }

  // Handle Lucide icon selection
  const handleIconSelect = (iconName: string) => {
    form.setValue("iconType", "lucide")
    form.setValue("iconName", iconName)
    form.setValue("icon", null)
    setIconFile(null)
    setIconPreview(null)
  }

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent, type: 'featured' | 'icon') => {
    e.preventDefault()
    setIsDragOver(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      if (type === 'featured') {
        handleFeaturedImageSelect(files[0])
      } else {
        handleIconImageSelect(files[0])
      }
    }
  }

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === "create" ? "Add New Category" : "Edit Category"}
      description={mode === "create" ? "Create a new product category with images and icons" : "Update the category information"}
      size="lg"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="images">Images</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              {/* Category Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Tag className="h-4 w-4" />
                      Category Name
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Gaming Laptops, Smart TVs"
                        {...field}
                        className="focus:ring-2 focus:ring-primary/20"
                      />
                    </FormControl>
                    <FormDescription>
                      Enter a clear, descriptive name for this product category
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Description
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe what products belong in this category..."
                        className="min-h-[100px] focus:ring-2 focus:ring-primary/20"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide a detailed description to help customers understand this category
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Active Status */}
              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="flex items-center gap-2 text-base">
                        <ToggleLeft className="h-4 w-4" />
                        Active Status
                      </FormLabel>
                      <FormDescription>
                        Active categories are visible to customers and can contain products
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </TabsContent>

            <TabsContent value="images" className="space-y-6">
              {/* Featured Image Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ImageIcon className="h-5 w-5" />
                    Featured Image
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {featuredImagePreview ? (
                      <div className="relative">
                        <Image
                          src={featuredImagePreview}
                          alt="Featured image preview"
                          width={300}
                          height={200}
                          className="rounded-lg object-cover w-full h-48"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={removeFeaturedImage}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                        <div className="absolute bottom-2 left-2">
                          <Badge variant="secondary" className="text-xs">
                            {featuredImageFile?.name || 'Existing image'}
                          </Badge>
                        </div>
                      </div>
                    ) : (
                      <div
                        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                          isDragOver ? 'border-primary bg-primary/5' : 'border-gray-300'
                        }`}
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={(e) => handleDrop(e, 'featured')}
                      >
                        <input
                          type="file"
                          accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
                          onChange={(e) => e.target.files?.[0] && handleFeaturedImageSelect(e.target.files[0])}
                          className="hidden"
                          id="featured-image-upload"
                        />
                        <label htmlFor="featured-image-upload" className="cursor-pointer">
                          <Upload className="mx-auto h-12 w-12 text-gray-400" />
                          <p className="mt-2 text-sm text-gray-600">
                            Click to upload or drag and drop
                          </p>
                          <p className="text-xs text-gray-500">
                            PNG, JPG, WEBP up to 10MB
                          </p>
                        </label>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Icon Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5" />
                    Category Icon
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="lucide" className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="lucide">Icon Library</TabsTrigger>
                      <TabsTrigger value="upload">Upload Icon</TabsTrigger>
                    </TabsList>

                    <TabsContent value="lucide" className="space-y-4">
                      <div className="grid grid-cols-6 gap-2">
                        {POPULAR_ICONS.map((icon) => (
                          <Button
                            key={icon.value}
                            type="button"
                            variant={form.watch('iconName') === icon.value ? "default" : "outline"}
                            size="sm"
                            className="h-12 w-12 p-2"
                            onClick={() => handleIconSelect(icon.value)}
                            title={icon.name}
                          >
                            <span className="text-xs">{icon.name.slice(0, 3)}</span>
                          </Button>
                        ))}
                      </div>
                      {form.watch('iconName') && (
                        <div className="text-sm text-gray-600">
                          Selected: {POPULAR_ICONS.find(i => i.value === form.watch('iconName'))?.name}
                        </div>
                      )}
                    </TabsContent>

                    <TabsContent value="upload" className="space-y-4">
                      {iconPreview ? (
                        <div className="relative w-24 h-24">
                          <Image
                            src={iconPreview}
                            alt="Icon preview"
                            width={96}
                            height={96}
                            className="rounded-lg object-cover w-full h-full"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute -top-2 -right-2"
                            onClick={removeIcon}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                          <div className="absolute bottom-0 left-0 right-0">
                            <Badge variant="secondary" className="text-xs w-full">
                              {iconFile?.name || 'Existing icon'}
                            </Badge>
                          </div>
                        </div>
                      ) : (
                        <div
                          className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
                            isDragOver ? 'border-primary bg-primary/5' : 'border-gray-300'
                          }`}
                          onDragOver={handleDragOver}
                          onDragLeave={handleDragLeave}
                          onDrop={(e) => handleDrop(e, 'icon')}
                        >
                          <input
                            type="file"
                            accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
                            onChange={(e) => e.target.files?.[0] && handleIconImageSelect(e.target.files[0])}
                            className="hidden"
                            id="icon-upload"
                          />
                          <label htmlFor="icon-upload" className="cursor-pointer">
                            <Upload className="mx-auto h-8 w-8 text-gray-400" />
                            <p className="mt-2 text-sm text-gray-600">
                              Click to upload or drag and drop
                            </p>
                            <p className="text-xs text-gray-500">
                              PNG, JPG, WEBP up to 10MB
                            </p>
                          </label>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="preview" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="h-5 w-5" />
                    Category Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative flex flex-col items-center justify-end h-64 overflow-hidden rounded-lg shadow-md bg-gradient-to-t from-black/70 to-gray-200">
                    {featuredImagePreview && (
                      <Image
                        src={featuredImagePreview}
                        alt={form.watch('name') || 'Category preview'}
                        width={300}
                        height={200}
                        className="absolute inset-0 w-full h-full object-cover"
                      />
                    )}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                    <div className="relative z-10 p-4 text-white text-left w-full">
                      <h3 className="text-xl font-bold mb-1">
                        {form.watch('name') || 'Category Name'}
                      </h3>
                      <p className="text-sm opacity-90">
                        {form.watch('description') || 'Category description will appear here...'}
                      </p>
                      <div className="mt-2 flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {form.watch('isActive') ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {mode === "create" ? "Creating..." : "Updating..."}
                </>
              ) : (
                mode === "create" ? "Create Category" : "Update Category"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  )
}
