"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  Loader2,
  Package,
  DollarSign,
  Hash,
  FileText,
  Building2,
  Info,
  Upload,
  Image as ImageIcon,
  Trash2,
  X,
  Star,
  Megaphone,
  Tag,
  Calendar,
  Weight,
  Ruler,
  Shield,
  Plus,
  Minus
} from "lucide-react"
import BaseModal from "./base-modal"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { CreateProductData, FormModalProps } from "@/types"
import { getCurrencyOptions, getDefaultCurrency, detectCountryFromBranch, formatPrice } from "@/lib/currency"
import { useBranchStore } from "@/stores/branchStore"
import { useCategoryStore } from "@/stores/categoryStore"

// Comprehensive product schema with all fields
const productSchema = z.object({
  // Essential required fields
  name: z.string().min(2, "Product name must be at least 2 characters").max(200, "Product name must be less than 200 characters"),
  sku: z.string().min(3, "SKU must be at least 3 characters").max(50, "SKU must be less than 50 characters"),
  category: z.string().min(1, "Please select a category"),
  price: z.number().min(0.01, "Price must be greater than 0").max(100000000, "Price cannot exceed 100 million"),
  currency: z.string().min(1, "Please select a currency"),
  stock: z.number().min(0, "Stock cannot be negative"),
  branchId: z.string().min(1, "Please select a branch"),
  description: z.string().min(10, "Description must be at least 10 characters").max(1000, "Description must be less than 1000 characters"),

  // Product details
  originalPrice: z.number().min(0, "Original price cannot be negative").max(100000000, "Original price cannot exceed 100 million").optional(),
  minStockLevel: z.number().min(0, "Minimum stock level cannot be negative").default(5),
  brand: z.string().min(1, "Brand is required").max(50, "Brand must be less than 50 characters"),
  model: z.string().min(1, "Model is required").max(100, "Model must be less than 100 characters"),
  warranty: z.string().min(1, "Warranty is required").max(100, "Warranty must be less than 100 characters"),
  weight: z.number().min(0, "Weight cannot be negative").optional(),
  dimensions: z.object({
    length: z.number().min(0, "Length cannot be negative"),
    width: z.number().min(0, "Width cannot be negative"),
    height: z.number().min(0, "Height cannot be negative"),
  }).optional(),

  // Arrays
  specifications: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  variants: z.array(z.any()).default([]),
  hasVariants: z.boolean().default(false),

  // Images
  featuredImage: z.string().optional(),
  images: z.array(z.string()).default([]),

  // Status and promotional fields
  isActive: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  isPromoted: z.boolean().default(false),
  isOnSale: z.boolean().default(false),
  salePrice: z.number().min(0, "Sale price cannot be negative").max(100000000, "Sale price cannot exceed 100 million").optional(),
  saleStartDate: z.string().optional(),
  saleEndDate: z.string().optional(),
  promotionDescription: z.string().max(200, "Promotion description must be less than 200 characters").optional(),
})

type ProductFormData = z.infer<typeof productSchema>

interface SimpleProductModalProps extends FormModalProps<CreateProductData> {}

export default function SimpleProductModal({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
  initialData,
  mode = "create"
}: SimpleProductModalProps) {
  
  // Image handling state - NO AUTO UPLOAD
  const [featuredImage, setFeaturedImage] = useState<File | null>(null)
  const [featuredImagePreview, setFeaturedImagePreview] = useState<string | null>(null)
  const [additionalImages, setAdditionalImages] = useState<File[]>([])
  const [additionalImagePreviews, setAdditionalImagePreviews] = useState<string[]>([])
  const [isDragOver, setIsDragOver] = useState(false)

  // Form helpers
  const [newSpecification, setNewSpecification] = useState("")
  const [newTag, setNewTag] = useState("")

  // Fetch branches and categories
  const { branches, fetchBranches, isLoading: branchesLoading } = useBranchStore()
  const { activeCategories, fetchActiveCategories, isLoading: categoriesLoading } = useCategoryStore()

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      // Essential fields
      name: initialData?.name || "",
      sku: initialData?.sku || "",
      category: initialData?.category || "",
      price: initialData?.price || 0,
      currency: initialData?.currency || "MWK",
      stock: initialData?.stock || 0,
      branchId: initialData?.branchId || "",
      description: initialData?.description || "",

      // Product details
      originalPrice: initialData?.originalPrice,
      minStockLevel: initialData?.minStockLevel || 5,
      brand: initialData?.brand || "",
      model: initialData?.model || "",
      warranty: initialData?.warranty || "",
      weight: initialData?.weight,
      dimensions: initialData?.dimensions,

      // Arrays
      specifications: initialData?.specifications || [],
      tags: initialData?.tags || [],
      variants: initialData?.variants || [],
      hasVariants: initialData?.hasVariants || false,

      // Images
      featuredImage: initialData?.featuredImage || "",
      images: initialData?.images || [],

      // Status and promotional
      isActive: initialData?.isActive !== undefined ? initialData.isActive : true,
      isFeatured: initialData?.isFeatured || false,
      isPromoted: initialData?.isPromoted || false,
      isOnSale: initialData?.isOnSale || false,
      salePrice: initialData?.salePrice,
      saleStartDate: initialData?.saleStartDate,
      saleEndDate: initialData?.saleEndDate,
      promotionDescription: initialData?.promotionDescription || "",
    },
  })

  // Fetch data when modal opens
  useEffect(() => {
    if (isOpen) {
      if (branches.length === 0) {
        fetchBranches()
      }
      if (activeCategories.length === 0) {
        fetchActiveCategories()
      }
    }
  }, [isOpen, branches.length, activeCategories.length, fetchBranches, fetchActiveCategories])

  // Reset form when modal closes or data changes
  useEffect(() => {
    if (initialData && mode === "edit") {
      form.reset({
        name: initialData.name || "",
        sku: initialData.sku || "",
        category: initialData.category || "",
        price: initialData.price || 0,
        currency: initialData.currency || "MWK",
        stock: initialData.stock || 0,
        branchId: initialData.branchId || "",
        description: initialData.description || "",
        originalPrice: initialData.originalPrice,
        minStockLevel: initialData.minStockLevel || 5,
        brand: initialData.brand || "",
        model: initialData.model || "",
        warranty: initialData.warranty || "",
        weight: initialData.weight,
        dimensions: initialData.dimensions,
        specifications: initialData.specifications || [],
        tags: initialData.tags || [],
        variants: initialData.variants || [],
        hasVariants: initialData.hasVariants || false,
        featuredImage: initialData.featuredImage || "",
        images: initialData.images || [],
        isActive: initialData.isActive !== undefined ? initialData.isActive : true,
        isFeatured: initialData.isFeatured || false,
        isPromoted: initialData.isPromoted || false,
        isOnSale: initialData.isOnSale || false,
        salePrice: initialData.salePrice,
        saleStartDate: initialData.saleStartDate,
        saleEndDate: initialData.saleEndDate,
        promotionDescription: initialData.promotionDescription || "",
      })
    } else {
      form.reset({
        name: "",
        sku: "",
        category: "",
        price: 0,
        currency: "MWK",
        stock: 0,
        branchId: "",
        description: "",
        originalPrice: undefined,
        minStockLevel: 5,
        brand: "",
        model: "",
        warranty: "",
        weight: undefined,
        dimensions: undefined,
        specifications: [],
        tags: [],
        variants: [],
        hasVariants: false,
        featuredImage: "",
        images: [],
        isActive: true,
        isFeatured: false,
        isPromoted: false,
        isOnSale: false,
        salePrice: undefined,
        saleStartDate: undefined,
        saleEndDate: undefined,
        promotionDescription: "",
      })

      // Reset images
      setFeaturedImage(null)
      setFeaturedImagePreview(null)
      setAdditionalImages([])
      setAdditionalImagePreviews([])
    }
  }, [initialData, mode, form])

  const handleSubmit = async (data: ProductFormData) => {
    try {
      // Create product data - pass all the form data
      const productData = {
        ...data,
        // Include image files for later processing if needed
        _featuredImageFile: featuredImage,
        _additionalImageFiles: additionalImages,
      }

      await onSubmit(productData as CreateProductData)

      // Reset everything
      form.reset()
      setFeaturedImage(null)
      setFeaturedImagePreview(null)
      setAdditionalImages([])
      setAdditionalImagePreviews([])
      onClose()
    } catch (error) {
      console.error("Error submitting product:", error)
    }
  }

  // Image validation
  const validateImageFile = (file: File): { valid: boolean; error?: string } => {
    const allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/webp', 
      'image/gif', 'image/bmp', 'image/tiff', 'image/svg+xml'
    ]
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `Invalid file type. Allowed: JPG, PNG, WEBP, GIF, BMP, TIFF, SVG`
      }
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size too large. Maximum size is 10MB`
      }
    }

    return { valid: true }
  }

  // Create image preview
  const createImagePreview = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.readAsDataURL(file)
    })
  }

  // Handle featured image selection - NO AUTO UPLOAD
  const handleFeaturedImageSelect = async (file: File) => {
    const validation = validateImageFile(file)
    if (!validation.valid) {
      alert(validation.error)
      return
    }

    setFeaturedImage(file)
    const preview = await createImagePreview(file)
    setFeaturedImagePreview(preview)
  }

  // Handle additional images selection - NO AUTO UPLOAD
  const handleAdditionalImagesSelect = async (files: FileList) => {
    const validFiles: File[] = []
    const previews: string[] = []

    for (const file of Array.from(files)) {
      const validation = validateImageFile(file)
      if (validation.valid) {
        validFiles.push(file)
        const preview = await createImagePreview(file)
        previews.push(preview)
      } else {
        alert(`${file.name}: ${validation.error}`)
      }
    }

    if (validFiles.length > 0) {
      setAdditionalImages(prev => [...prev, ...validFiles])
      setAdditionalImagePreviews(prev => [...prev, ...previews])
    }
  }

  // Remove featured image
  const removeFeaturedImage = () => {
    setFeaturedImage(null)
    setFeaturedImagePreview(null)
  }

  // Remove additional image
  const removeAdditionalImage = (index: number) => {
    const newImages = additionalImages.filter((_, i) => i !== index)
    const newPreviews = additionalImagePreviews.filter((_, i) => i !== index)
    setAdditionalImages(newImages)
    setAdditionalImagePreviews(newPreviews)
  }

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent, isFeatured: boolean = false) => {
    e.preventDefault()
    setIsDragOver(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      if (isFeatured) {
        handleFeaturedImageSelect(files[0])
      } else {
        handleAdditionalImagesSelect(files)
      }
    }
  }

  // Helper functions for specifications
  const addSpecification = () => {
    if (newSpecification.trim()) {
      const currentSpecs = form.getValues("specifications")
      form.setValue("specifications", [...currentSpecs, newSpecification.trim()])
      setNewSpecification("")
    }
  }

  const removeSpecification = (index: number) => {
    const currentSpecs = form.getValues("specifications")
    form.setValue("specifications", currentSpecs.filter((_, i) => i !== index))
  }

  // Helper functions for tags
  const addTag = () => {
    if (newTag.trim()) {
      const currentTags = form.getValues("tags")
      if (!currentTags.includes(newTag.trim())) {
        form.setValue("tags", [...currentTags, newTag.trim()])
      }
      setNewTag("")
    }
  }

  const removeTag = (index: number) => {
    const currentTags = form.getValues("tags")
    form.setValue("tags", currentTags.filter((_, i) => i !== index))
  }

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === "create" ? "Create New Product" : "Edit Product"}
      description={
        mode === "create"
          ? "Create a comprehensive product with all details, promotional settings, and specifications."
          : "Update all product information including promotional settings and specifications."
      }
      size="xl"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Comprehensive Product Notice */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Package className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  {mode === "create" ? "Comprehensive Product Creation" : "Complete Product Management"}
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  {mode === "create"
                    ? "Create a complete product with all details, promotional settings, specifications, and images. All fields are available for immediate setup."
                    : "Update all aspects of your product including promotional settings, specifications, and detailed information."
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Essential Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <Package className="h-5 w-5" />
              Essential Information
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Product Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Product Name
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Dell XPS 13 Laptop" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* SKU */}
              <FormField
                control={form.control}
                name="sku"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Hash className="h-4 w-4" />
                      SKU
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., DELL-XPS13-001" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Category */}
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categoriesLoading ? (
                          <SelectItem value="loading" disabled>
                            Loading categories...
                          </SelectItem>
                        ) : activeCategories.length === 0 ? (
                          <SelectItem value="no-categories" disabled>
                            No categories available
                          </SelectItem>
                        ) : (
                          activeCategories.map((category) => (
                            <SelectItem key={category.id} value={category.name}>
                              {category.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Branch */}
              <FormField
                control={form.control}
                name="branchId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Building2 className="h-4 w-4" />
                      Branch
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select branch" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {branchesLoading ? (
                          <SelectItem value="loading" disabled>
                            Loading branches...
                          </SelectItem>
                        ) : branches.length === 0 ? (
                          <SelectItem value="no-branches" disabled>
                            No branches available
                          </SelectItem>
                        ) : (
                          branches
                            .filter(branch => branch.status === 'Active')
                            .map((branch) => (
                              <SelectItem key={branch._id} value={branch._id}>
                                {branch.name} - {branch.location}
                              </SelectItem>
                            ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Price */}
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Price ({form.watch("currency") || "MWK"})
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.01"
                        max="100000000"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value
                          if (value === '' || value === '0') {
                            field.onChange(0)
                          } else {
                            const numValue = parseFloat(value)
                            field.onChange(isNaN(numValue) ? 0 : numValue)
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Currency */}
              <FormField
                control={form.control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Currency
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {getCurrencyOptions().map((currency) => (
                          <SelectItem key={currency.value} value={currency.value}>
                            {currency.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Stock */}
              <FormField
                control={form.control}
                name="stock"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Stock</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Brand */}
              <FormField
                control={form.control}
                name="brand"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Brand</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Dell, HP, Apple" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Model */}
              <FormField
                control={form.control}
                name="model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Model</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., XPS 13, Pavilion 15" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Warranty */}
            <FormField
              control={form.control}
              name="warranty"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Warranty
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., 1 year manufacturer warranty" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Description
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Detailed product description..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Promotional Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <Megaphone className="h-5 w-5" />
              Promotional Settings
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Featured */}
              <FormField
                control={form.control}
                name="isFeatured"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-yellow-500" />
                        Featured Product
                      </FormLabel>
                      <FormDescription>
                        Show this product prominently on the homepage
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {/* Promoted */}
              <FormField
                control={form.control}
                name="isPromoted"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="flex items-center gap-2">
                        <Megaphone className="h-4 w-4 text-blue-500" />
                        Promoted Product
                      </FormLabel>
                      <FormDescription>
                        Mark as promoted for special marketing
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {/* On Sale */}
              <FormField
                control={form.control}
                name="isOnSale"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="flex items-center gap-2">
                        <Tag className="h-4 w-4 text-red-500" />
                        On Sale
                      </FormLabel>
                      <FormDescription>
                        Product is currently on sale
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {/* Sale Details - Show only when on sale */}
            {form.watch("isOnSale") && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                <FormField
                  control={form.control}
                  name="salePrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Sale Price ({form.watch("currency") || "MWK"})
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value
                            if (value === '' || value === '0') {
                              field.onChange(0)
                            } else {
                              const numValue = parseFloat(value)
                              field.onChange(isNaN(numValue) ? 0 : numValue)
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="promotionDescription"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Promotion Description</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Black Friday Sale, Limited Time Offer" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="saleStartDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Sale Start Date
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="saleEndDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Sale End Date
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}
          </div>

          {/* Image Upload Section - NO AUTO UPLOAD */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              Product Images (Optional)
            </h3>

            {/* Featured Image */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Featured Image</Label>
              <div
                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                  isDragOver
                    ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, true)}
              >
                {featuredImagePreview ? (
                  <div className="space-y-3">
                    <div className="relative inline-block">
                      <img
                        src={featuredImagePreview}
                        alt="Featured preview"
                        className="max-w-full max-h-48 rounded-lg object-cover"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={removeFeaturedImage}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {featuredImage?.name}
                    </p>
                  </div>
                ) : (
                  <>
                    <input
                      type="file"
                      accept="image/jpeg,image/jpg,image/png,image/webp,image/gif,image/bmp,image/tiff,image/svg+xml"
                      onChange={(e) => e.target.files?.[0] && handleFeaturedImageSelect(e.target.files[0])}
                      className="hidden"
                      id="featured-image-upload"
                    />
                    <label
                      htmlFor="featured-image-upload"
                      className="cursor-pointer w-full h-full min-h-[120px] flex flex-col items-center justify-center"
                    >
                      <Upload className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                        JPG, PNG, WEBP, GIF, BMP, TIFF, SVG up to 10MB
                      </p>
                    </label>
                  </>
                )}
              </div>
            </div>

            {/* Additional Images */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Additional Images (Optional)</Label>
              <div
                className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                  isDragOver
                    ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, false)}
              >
                <input
                  type="file"
                  multiple
                  accept="image/jpeg,image/jpg,image/png,image/webp,image/gif,image/bmp,image/tiff,image/svg+xml"
                  onChange={(e) => e.target.files && handleAdditionalImagesSelect(e.target.files)}
                  className="hidden"
                  id="additional-images-upload"
                />
                <label
                  htmlFor="additional-images-upload"
                  className="cursor-pointer w-full h-full min-h-[120px] flex flex-col items-center justify-center"
                >
                  <Upload className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                  <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                    Click to upload multiple images or drag and drop
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                    JPG, PNG, WEBP, GIF, BMP, TIFF, SVG up to 10MB each
                  </p>
                </label>
              </div>

              {/* Additional Images Preview */}
              {additionalImagePreviews.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                  {additionalImagePreviews.map((preview, index) => (
                    <div key={index} className="relative">
                      <img
                        src={preview}
                        alt={`Additional preview ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-1 right-1 h-6 w-6 p-0"
                        onClick={() => removeAdditionalImage(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                      <p className="text-xs text-gray-500 mt-1 truncate">
                        {additionalImages[index]?.name}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Specifications */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Specifications
            </h3>

            <div className="space-y-3">
              <div className="flex gap-2">
                <Input
                  placeholder="Add a specification (e.g., RAM: 16GB)"
                  value={newSpecification}
                  onChange={(e) => setNewSpecification(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSpecification())}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addSpecification}
                  disabled={!newSpecification.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {form.watch("specifications").length > 0 && (
                <div className="space-y-2">
                  {form.watch("specifications").map((spec, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <span className="flex-1 text-sm">{spec}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeSpecification(index)}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Tags */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Tags
            </h3>

            <div className="space-y-3">
              <div className="flex gap-2">
                <Input
                  placeholder="Add a tag (e.g., gaming, laptop, portable)"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addTag}
                  disabled={!newTag.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {form.watch("tags").length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {form.watch("tags").map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => removeTag(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Additional Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <Ruler className="h-5 w-5" />
              Additional Details
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Original Price */}
              <FormField
                control={form.control}
                name="originalPrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Original Price ({form.watch("currency") || "MWK"}) (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value
                          if (value === '' || value === '0') {
                            field.onChange(undefined)
                          } else {
                            const numValue = parseFloat(value)
                            field.onChange(isNaN(numValue) ? undefined : numValue)
                          }
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Show crossed-out price if product is discounted
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Min Stock Level */}
              <FormField
                control={form.control}
                name="minStockLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Stock Level</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        placeholder="5"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 5)}
                      />
                    </FormControl>
                    <FormDescription>
                      Alert when stock falls below this level
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Weight */}
              <FormField
                control={form.control}
                name="weight"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Weight className="h-4 w-4" />
                      Weight (kg)
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => {
                          const value = e.target.value
                          if (value === '' || value === '0') {
                            field.onChange(undefined)
                          } else {
                            const numValue = parseFloat(value)
                            field.onChange(isNaN(numValue) ? undefined : numValue)
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Dimensions */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Dimensions (cm)</Label>
              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="dimensions.length"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Length</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value
                            const numValue = parseFloat(value) || 0
                            const currentDimensions = form.getValues("dimensions") || {}
                            form.setValue("dimensions", {
                              ...currentDimensions,
                              length: numValue
                            })
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dimensions.width"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Width</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value
                            const numValue = parseFloat(value) || 0
                            const currentDimensions = form.getValues("dimensions") || {}
                            form.setValue("dimensions", {
                              ...currentDimensions,
                              width: numValue
                            })
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dimensions.height"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Height</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value
                            const numValue = parseFloat(value) || 0
                            const currentDimensions = form.getValues("dimensions") || {}
                            form.setValue("dimensions", {
                              ...currentDimensions,
                              height: numValue
                            })
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {mode === "create" ? "Creating..." : "Updating..."}
                </>
              ) : (
                mode === "create" ? "Create Product" : "Update Product"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  )
}
