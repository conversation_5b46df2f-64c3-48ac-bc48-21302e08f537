'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { BaseModal } from '@/components/modals/base-modal'
import { useToast } from '@/hooks/use-toast'

const shelfSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name cannot exceed 100 characters'),
  warehouseId: z.string().min(1, 'Warehouse is required'),
  section: z.string().min(1, 'Section is required').max(10, 'Section cannot exceed 10 characters'),
  row: z.string().min(1, 'Row is required').max(10, 'Row cannot exceed 10 characters'),
  position: z.string().min(1, 'Position is required').max(10, 'Position cannot exceed 10 characters'),
  level: z.number().min(1, 'Level must be at least 1').max(10, 'Level cannot exceed 10'),
  description: z.string().max(300, 'Description cannot exceed 300 characters').optional(),
  capacity: z.number().min(1, 'Capacity must be greater than 0'),
  dimensions: z.object({
    width: z.number().min(0.1, 'Width must be greater than 0'),
    height: z.number().min(0.1, 'Height must be greater than 0'),
    depth: z.number().min(0.1, 'Depth must be greater than 0'),
    unit: z.enum(['cm', 'm']),
  }),
  weightLimit: z.number().min(1, 'Weight limit must be greater than 0'),
  shelfType: z.enum(['standard', 'refrigerated', 'hazmat', 'fragile', 'bulk']),
  accessLevel: z.enum(['ground', 'ladder', 'forklift', 'crane']),
  notes: z.string().max(500, 'Notes cannot exceed 500 characters').optional(),
})

type ShelfFormData = z.infer<typeof shelfSchema>

interface Warehouse {
  _id: string
  name: string
  code: string
}

interface AddShelfModalProps {
  isOpen: boolean
  onClose: () => void
  onShelfCreated: () => void
  warehouses: Warehouse[]
}

export function AddShelfModal({
  isOpen,
  onClose,
  onShelfCreated,
  warehouses
}: AddShelfModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const form = useForm<ShelfFormData>({
    resolver: zodResolver(shelfSchema),
    defaultValues: {
      name: '',
      warehouseId: '',
      section: 'A',
      row: '1',
      position: '1',
      level: 1,
      description: '',
      capacity: 100,
      dimensions: {
        width: 100,
        height: 200,
        depth: 50,
        unit: 'cm',
      },
      weightLimit: 100,
      shelfType: 'standard',
      accessLevel: 'ground',
      notes: '',
    },
  })

  const handleSubmit = async (data: ShelfFormData) => {
    setIsLoading(true)
    try {
      const payload = {
        ...data,
        ...(data.description && { description: data.description }),
        ...(data.notes && { notes: data.notes }),
      }

      console.log('📤 Shelf creation payload:', JSON.stringify(payload, null, 2))

      const response = await fetch('/api/shelves', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify(payload)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast({
          title: "Success",
          description: "Shelf created successfully",
        })
        onShelfCreated()
        form.reset()
        onClose()
      } else {
        console.log('❌ Shelf creation error response:', result)
        const errorMessage = result.details
          ? `Validation errors: ${result.details.map((d: any) => `${d.path.join('.')}: ${d.message}`).join(', ')}`
          : result.error || 'Failed to create shelf'
        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('Error creating shelf:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create shelf",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleClose}
      title="Add New Shelf"
      description="Create a new shelf location within a warehouse"
      size="lg"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Basic Information */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Shelf Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter shelf name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="warehouseId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Warehouse</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select warehouse" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {warehouses.map((warehouse) => (
                      <SelectItem key={warehouse._id} value={warehouse._id}>
                        {warehouse.name} ({warehouse.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Location Information */}
          <div className="grid grid-cols-4 gap-4">
            <FormField
              control={form.control}
              name="section"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Section</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="A" 
                      {...field} 
                      onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="row"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Row</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="1"
                      {...field}
                      onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="position"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Position</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="1"
                      {...field}
                      onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Level</FormLabel>
                  <FormControl>
                    <Input 
                      type="number"
                      placeholder="1"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Capacity */}
          <FormField
            control={form.control}
            name="capacity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Storage Capacity (units)</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="100"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Dimensions */}
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="dimensions.unit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Measurement Unit</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select unit" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="cm">Centimeters (cm)</SelectItem>
                      <SelectItem value="m">Meters (m)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="dimensions.width"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Width ({form.watch('dimensions.unit')})</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.1"
                        placeholder={form.watch('dimensions.unit') === 'cm' ? '100' : '1.0'}
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dimensions.height"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Height ({form.watch('dimensions.unit')})</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.1"
                        placeholder={form.watch('dimensions.unit') === 'cm' ? '200' : '2.0'}
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dimensions.depth"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Depth ({form.watch('dimensions.unit')})</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.1"
                        placeholder={form.watch('dimensions.unit') === 'cm' ? '50' : '0.5'}
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <FormField
            control={form.control}
            name="weightLimit"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Weight Limit (kg)</FormLabel>
                <FormControl>
                  <Input 
                    type="number"
                    placeholder="100"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Type and Access */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="shelfType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Shelf Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select shelf type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="standard">Standard</SelectItem>
                      <SelectItem value="refrigerated">Refrigerated</SelectItem>
                      <SelectItem value="hazmat">Hazmat</SelectItem>
                      <SelectItem value="fragile">Fragile</SelectItem>
                      <SelectItem value="bulk">Bulk Storage</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="accessLevel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Access Level</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select access level" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="ground">Ground Level</SelectItem>
                      <SelectItem value="ladder">Ladder Required</SelectItem>
                      <SelectItem value="forklift">Forklift Required</SelectItem>
                      <SelectItem value="crane">Crane Required</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description (Optional)</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Enter shelf description"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes (Optional)</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Enter any additional notes"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit Button */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creating...' : 'Create Shelf'}
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  )
}
