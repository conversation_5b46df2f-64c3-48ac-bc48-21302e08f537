"use client"

import { use<PERSON>tate, use<PERSON>ffe<PERSON>, use<PERSON>allback } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  Loader2,
  Package,
  Plus,
  X,
  Upload,
  Image as ImageIcon,
  Trash2
} from "lucide-react"
import BaseModal from "./base-modal"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { useCategoryStore } from "@/stores/categoryStore"
import { useBranchStore } from "@/stores/branchStore"
import type { CreateProductData, ProductVariant, FormModalProps } from "@/types"

// Product variant schema
const productVariantSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Variant name is required"),
  sku: z.string().min(1, "Variant SKU is required"),
  price: z.number().min(0, "Price cannot be negative"),
  originalPrice: z.number().optional(),
  stock: z.number().min(0, "Stock cannot be negative"),
  attributes: z.record(z.string()).default({}),
  images: z.array(z.string()).default([]),
  isActive: z.boolean().default(true)
})

// Enhanced product schema
const enhancedProductSchema = z.object({
  name: z.string().min(2, "Product name must be at least 2 characters").max(200, "Product name must be less than 200 characters"),
  sku: z.string().min(3, "SKU must be at least 3 characters").max(50, "SKU must be less than 50 characters"),
  categoryId: z.string().min(1, "Please select a category"),
  categoryName: z.string().min(1, "Category name is required"),
  price: z.number().min(0.01, "Price must be greater than 0").max(100000000, "Price cannot exceed 100 million"),
  originalPrice: z.number().min(0, "Original price cannot be negative").max(100000000, "Original price cannot exceed 100 million").optional(),
  stock: z.number().min(0, "Stock cannot be negative"),
  minStockLevel: z.number().min(0, "Minimum stock level cannot be negative"),
  description: z.string().min(10, "Description must be at least 10 characters").max(1000, "Description must be less than 1000 characters"),
  branchId: z.string().min(1, "Please select a shop"),
  brand: z.string().min(1, "Brand is required").max(50, "Brand must be less than 50 characters"),
  model: z.string().min(1, "Model is required").max(100, "Model must be less than 100 characters"),
  warranty: z.string().min(1, "Warranty information is required"),
  weight: z.number().optional(),
  dimensions: z.object({
    length: z.number().min(0),
    width: z.number().min(0),
    height: z.number().min(0),
  }).optional(),
  tags: z.array(z.object({ value: z.string() })).default([]),
  specifications: z.array(z.object({ value: z.string() })).default([]),
  variants: z.array(productVariantSchema).default([]),
  hasVariants: z.boolean().default(false),
  featuredImage: z.string().min(1, "Featured image is required"),
  images: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
})

type EnhancedProductFormData = z.infer<typeof enhancedProductSchema>

interface EnhancedProductModalProps extends FormModalProps<CreateProductData> {}

export default function EnhancedProductModal({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
  initialData,
  mode = "create"
}: EnhancedProductModalProps) {
  const [newSpec, setNewSpec] = useState("")
  const [newTag, setNewTag] = useState("")
  const [uploadedImages, setUploadedImages] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState("basic")

  // Store hooks
  const { activeCategories, fetchActiveCategories, isLoading: categoriesLoading } = useCategoryStore()
  const { branches, fetchBranches, isLoading: branchesLoading } = useBranchStore()



  const form = useForm<EnhancedProductFormData>({
    resolver: zodResolver(enhancedProductSchema),
    defaultValues: {
      name: "",
      sku: "",
      categoryId: "",
      categoryName: "",
      price: 0,
      originalPrice: undefined,
      stock: 0,
      minStockLevel: 5,
      description: "",
      specifications: [],
      branchId: "",
      brand: "",
      model: "",
      warranty: "",
      weight: undefined,
      dimensions: undefined,
      tags: [],
      variants: [],
      hasVariants: false,
      featuredImage: "",
      images: [],
      isActive: true,
      isFeatured: false,
    }
  })

  const { fields: specFields, append: appendSpec, remove: removeSpec } = useFieldArray({
    control: form.control,
    name: "specifications"
  })

  const { fields: tagFields, append: appendTag, remove: removeTag } = useFieldArray({
    control: form.control,
    name: "tags"
  })

  const { fields: variantFields, append: appendVariant, remove: removeVariant } = useFieldArray({
    control: form.control,
    name: "variants"
  })

  // Load data on mount
  useEffect(() => {
    if (isOpen) {
      if (activeCategories.length === 0) {
        fetchActiveCategories()
      }
      if (branches.length === 0) {
        fetchBranches()
      }
    }
  }, [isOpen, activeCategories.length, fetchActiveCategories, branches.length, fetchBranches])

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      if (initialData) {
        // Convert string arrays to object arrays for form
        const formData = {
          ...initialData,
          tags: initialData.tags?.map(tag => ({ value: tag })) || [],
          specifications: initialData.specifications?.map(spec => ({ value: spec })) || []
        }
        form.reset(formData as EnhancedProductFormData)
        setUploadedImages(initialData.images || [])
      } else {
        form.reset()
        setUploadedImages([])
      }
    }
  }, [isOpen, initialData, form])

  // Handle category selection
  const handleCategoryChange = useCallback((categoryId: string) => {
    const category = activeCategories.find(cat => cat.id === categoryId)
    if (category) {
      form.setValue("categoryId", categoryId)
      form.setValue("categoryName", category.name)
    }
  }, [activeCategories, form])

  // Handle image upload
  const handleImageUpload = useCallback(async (files: FileList) => {
    if (!files.length) return

    try {
      const formData = new FormData()
      Array.from(files).forEach(file => {
        formData.append('files', file)
      })
      formData.append('category', 'product')

      const response = await fetch('/api/images/upload', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const result = await response.json()
        const newImageUrls = result.data.uploadedFiles.map((file: any) => file.url)
        
        setUploadedImages(prev => [...prev, ...newImageUrls])
        
        const currentImages = form.getValues("images")
        form.setValue("images", [...currentImages, ...newImageUrls])
        
        // Set first uploaded image as featured if none exists
        if (!form.getValues("featuredImage") && newImageUrls.length > 0) {
          form.setValue("featuredImage", newImageUrls[0])
        }
      }
    } catch (error) {
      console.error('Image upload failed:', error)
    }
  }, [form])

  // Add specification
  const addSpecification = useCallback(() => {
    if (newSpec.trim()) {
      appendSpec({ value: newSpec.trim() })
      setNewSpec("")
    }
  }, [newSpec, appendSpec])

  // Add tag
  const addTag = useCallback(() => {
    if (newTag.trim()) {
      appendTag({ value: newTag.trim() })
      setNewTag("")
    }
  }, [newTag, appendTag])

  // Add variant
  const addVariant = useCallback(() => {
    const newVariant: ProductVariant = {
      id: Date.now().toString(),
      name: "",
      sku: "",
      price: 0,
      stock: 0,
      attributes: {},
      images: [],
      isActive: true
    }
    appendVariant(newVariant)
    form.setValue("hasVariants", true)
  }, [appendVariant, form])

  const handleSubmit = useCallback((data: EnhancedProductFormData) => {
    // Convert object arrays back to string arrays for API
    const submitData = {
      ...data,
      tags: data.tags.map(tag => tag.value),
      specifications: data.specifications.map(spec => spec.value)
    }
    onSubmit(submitData as CreateProductData)
  }, [onSubmit])

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === "create" ? "Add New Product" : "Edit Product"}
      description={mode === "create" ? "Create a new product with all details" : "Update product information"}
      size="xl"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="images">Images</TabsTrigger>
              <TabsTrigger value="variants">Variants</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Product Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter product name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sku"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SKU</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter SKU" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="categoryId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select onValueChange={handleCategoryChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categoriesLoading ? (
                            <SelectItem value="loading" disabled>
                              Loading categories...
                            </SelectItem>
                          ) : activeCategories.length === 0 ? (
                            <SelectItem value="no-categories" disabled>
                              No categories available
                            </SelectItem>
                          ) : (
                            activeCategories.map((category) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="branchId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shop</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select shop" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {branchesLoading ? (
                            <SelectItem value="loading" disabled>
                              Loading branches...
                            </SelectItem>
                          ) : branches.length === 0 ? (
                            <SelectItem value="no-branches" disabled>
                              No branches available
                            </SelectItem>
                          ) : (
                            branches
                              .filter(branch => branch.status === 'Active')
                              .map((branch) => (
                                <SelectItem key={branch._id} value={branch._id}>
                                  {branch.name} - {branch.location}
                                </SelectItem>
                              ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="brand"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Brand</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter brand" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="model"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Model</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter model" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="warranty"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Warranty</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., 1 year" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0.01"
                          max="100000000"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value
                            if (value === '' || value === '0') {
                              field.onChange(0)
                            } else {
                              const numValue = parseFloat(value)
                              field.onChange(isNaN(numValue) ? 0 : numValue)
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="originalPrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Original Price ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="100000000"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => {
                            const value = e.target.value
                            if (value === '' || value === '0') {
                              field.onChange(undefined)
                            } else {
                              const numValue = parseFloat(value)
                              field.onChange(isNaN(numValue) ? undefined : numValue)
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="stock"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stock Quantity</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="0" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Enter product description"
                        className="min-h-[100px]"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </TabsContent>

            <TabsContent value="images" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ImageIcon className="h-5 w-5" />
                    Product Images
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={(e) => e.target.files && handleImageUpload(e.target.files)}
                      className="hidden"
                      id="image-upload"
                    />
                    <label htmlFor="image-upload" className="cursor-pointer">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2 text-sm text-gray-600">
                        Click to upload images or drag and drop
                      </p>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, WEBP up to 10MB each
                      </p>
                    </label>
                  </div>

                  {uploadedImages.length > 0 && (
                    <div className="grid grid-cols-4 gap-4">
                      {uploadedImages.map((imageUrl, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={imageUrl}
                            alt={`Product image ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg border"
                          />
                          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                            <Button
                              type="button"
                              size="sm"
                              variant="destructive"
                              onClick={() => {
                                const newImages = uploadedImages.filter((_, i) => i !== index)
                                setUploadedImages(newImages)
                                form.setValue("images", newImages)
                                if (form.getValues("featuredImage") === imageUrl) {
                                  form.setValue("featuredImage", newImages[0] || "")
                                }
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                          {form.getValues("featuredImage") === imageUrl && (
                            <Badge className="absolute top-1 left-1 bg-yellow-500">
                              Featured
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  <FormField
                    control={form.control}
                    name="featuredImage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Featured Image</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select featured image" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {uploadedImages.map((imageUrl, index) => (
                              <SelectItem key={index} value={imageUrl}>
                                Image {index + 1}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="variants" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Product Variants
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <FormField
                      control={form.control}
                      name="hasVariants"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              This product has variants
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>

                  {form.watch("hasVariants") && (
                    <div className="space-y-4">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={addVariant}
                        className="w-full"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Variant
                      </Button>

                      {variantFields.map((variant, index) => (
                        <Card key={variant.id} className="p-4">
                          <div className="flex justify-between items-center mb-4">
                            <h4 className="font-medium">Variant {index + 1}</h4>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeVariant(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="grid grid-cols-2 gap-4">
                            <FormField
                              control={form.control}
                              name={`variants.${index}.name`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Variant Name</FormLabel>
                                  <FormControl>
                                    <Input placeholder="e.g., Red Large" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name={`variants.${index}.sku`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Variant SKU</FormLabel>
                                  <FormControl>
                                    <Input placeholder="e.g., PROD-001-RL" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name={`variants.${index}.price`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Price ($)</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      step="0.01"
                                      placeholder="0.00"
                                      {...field}
                                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name={`variants.${index}.stock`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Stock</FormLabel>
                                  <FormControl>
                                    <Input
                                      type="number"
                                      placeholder="0"
                                      {...field}
                                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <div className="grid grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Specifications</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex space-x-2">
                      <Input
                        placeholder="Add specification"
                        value={newSpec}
                        onChange={(e) => setNewSpec(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addSpecification())}
                      />
                      <Button type="button" onClick={addSpecification}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="space-y-2">
                      {specFields.map((spec, index) => (
                        <div key={spec.id} className="flex items-center space-x-2">
                          <Input
                            value={form.watch(`specifications.${index}.value`)}
                            onChange={(e) => form.setValue(`specifications.${index}.value`, e.target.value)}
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSpec(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Tags</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex space-x-2">
                      <Input
                        placeholder="Add tag"
                        value={newTag}
                        onChange={(e) => setNewTag(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      />
                      <Button type="button" onClick={addTag}>
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="flex flex-wrap gap-2">
                      {tagFields.map((tag, index) => (
                        <Badge key={tag.id} variant="secondary" className="flex items-center gap-1">
                          {form.watch(`tags.${index}.value`)}
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-auto p-0 ml-1"
                            onClick={() => removeTag(index)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Additional Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="weight"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Weight (kg)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="minStockLevel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Minimum Stock Level</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="5"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="dimensions.length"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Length (cm)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dimensions.width"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Width (cm)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dimensions.height"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Height (cm)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex items-center space-x-6">
                    <FormField
                      control={form.control}
                      name="isActive"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Active Product
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="isFeatured"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>
                              Featured Product
                            </FormLabel>
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {mode === "create" ? "Create Product" : "Update Product"}
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  )
}
