'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { BaseModal } from '@/components/modals/base-modal'
import { useToast } from '@/hooks/use-toast'
import { useBranchStore } from '@/stores/branchStore'

const warehouseSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name cannot exceed 100 characters'),
  code: z.string().min(2, 'Code must be at least 2 characters').max(20, 'Code cannot exceed 20 characters'),
  description: z.string().max(500, 'Description cannot exceed 500 characters').optional(),
  type: z.enum(['main', 'branch', 'storage', 'distribution']),
  branchId: z.string().optional(),
  address: z.string().min(5, 'Address must be at least 5 characters').max(200, 'Address cannot exceed 200 characters'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  managerId: z.string().optional(),
  isActive: z.boolean(),
  operatingHours: z.object({
    open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
    close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  }),
})

type WarehouseFormData = z.infer<typeof warehouseSchema>

interface Warehouse {
  _id: string
  name: string
  code: string
  description?: string
  type: 'main' | 'branch' | 'storage' | 'distribution'
  branchId?: string
  address: string
  capacity: number
  managerId?: string
  isActive: boolean
  operatingHours: {
    open: string
    close: string
  }
}

interface EditWarehouseModalProps {
  isOpen: boolean
  onClose: () => void
  warehouse: Warehouse | null
  onWarehouseUpdated: () => void
}

export function EditWarehouseModal({
  isOpen,
  onClose,
  warehouse,
  onWarehouseUpdated
}: EditWarehouseModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const { branches } = useBranchStore()

  const form = useForm<WarehouseFormData>({
    resolver: zodResolver(warehouseSchema),
    defaultValues: {
      name: '',
      code: '',
      description: '',
      type: 'storage',
      branchId: 'none',
      address: '',
      capacity: 1000,
      managerId: '',
      isActive: true,
      operatingHours: {
        open: '08:00',
        close: '17:00',
      },
    },
  })

  // Update form when warehouse changes
  useEffect(() => {
    if (warehouse) {
      form.reset({
        name: warehouse.name,
        code: warehouse.code,
        description: warehouse.description || '',
        type: warehouse.type,
        branchId: warehouse.branchId || 'none',
        address: warehouse.address,
        capacity: warehouse.capacity,
        managerId: warehouse.managerId || '',
        isActive: warehouse.isActive,
        operatingHours: {
          open: warehouse.operatingHours.open,
          close: warehouse.operatingHours.close,
        },
      })
    }
  }, [warehouse, form])

  const handleSubmit = async (data: WarehouseFormData) => {
    if (!warehouse) return

    setIsLoading(true)
    try {
      const payload = {
        ...data,
        ...(data.branchId && data.branchId !== 'none' && { branchId: data.branchId }),
        ...(data.managerId && { managerId: data.managerId }),
        ...(data.description && { description: data.description }),
      }

      const response = await fetch(`/api/warehouses/${warehouse._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify(payload)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast({
          title: "Success",
          description: "Warehouse updated successfully",
        })
        onWarehouseUpdated()
        onClose()
      } else {
        throw new Error(result.error || 'Failed to update warehouse')
      }
    } catch (error) {
      console.error('Error updating warehouse:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update warehouse",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  if (!warehouse) return null

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleClose}
      title="Edit Warehouse"
      description="Update warehouse information and settings"
      size="lg"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Warehouse Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter warehouse name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Warehouse Code</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., WH001" 
                      {...field} 
                      onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description (Optional)</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Enter warehouse description"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Warehouse Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select warehouse type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="main">Main Warehouse</SelectItem>
                      <SelectItem value="branch">Branch Warehouse</SelectItem>
                      <SelectItem value="storage">Storage Facility</SelectItem>
                      <SelectItem value="distribution">Distribution Center</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="branchId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Branch (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select branch" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">No specific branch</SelectItem>
                      {branches.map((branch) => (
                        <SelectItem key={branch._id} value={branch._id}>
                          {branch.name} - {branch.location}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Enter warehouse address"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="capacity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Storage Capacity (units)</FormLabel>
                <FormControl>
                  <Input 
                    type="number"
                    placeholder="Enter storage capacity"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Operating Hours */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="operatingHours.open"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Opening Time</FormLabel>
                  <FormControl>
                    <Input 
                      type="time"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="operatingHours.close"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Closing Time</FormLabel>
                  <FormControl>
                    <Input 
                      type="time"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Status */}
          <FormField
            control={form.control}
            name="isActive"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Active Status</FormLabel>
                  <div className="text-sm text-muted-foreground">
                    Enable or disable this warehouse
                  </div>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {/* Submit Button */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Updating...' : 'Update Warehouse'}
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  )
}
