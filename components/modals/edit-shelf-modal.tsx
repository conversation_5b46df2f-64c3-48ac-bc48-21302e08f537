'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { BaseModal } from '@/components/modals/base-modal'
import { useToast } from '@/hooks/use-toast'

const shelfSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name cannot exceed 100 characters'),
  warehouseId: z.string().min(1, 'Warehouse is required'),
  section: z.string().min(1, 'Section is required').max(10, 'Section cannot exceed 10 characters'),
  row: z.string().min(1, 'Row is required').max(10, 'Row cannot exceed 10 characters'),
  position: z.string().min(1, 'Position is required').max(10, 'Position cannot exceed 10 characters'),
  level: z.number().min(1, 'Level must be at least 1').max(10, 'Level cannot exceed 10'),
  description: z.string().max(300, 'Description cannot exceed 300 characters').optional(),
  capacity: z.number().min(1, 'Capacity must be greater than 0'),
  dimensions: z.object({
    width: z.number().min(0.1, 'Width must be greater than 0'),
    height: z.number().min(0.1, 'Height must be greater than 0'),
    depth: z.number().min(0.1, 'Depth must be greater than 0'),
    unit: z.enum(['cm', 'm']),
  }),
  weightLimit: z.number().min(1, 'Weight limit must be greater than 0'),
  shelfType: z.enum(['standard', 'refrigerated', 'hazmat', 'fragile', 'bulk']),
  accessLevel: z.enum(['ground', 'ladder', 'forklift', 'crane']),
  isActive: z.boolean(),
  isReserved: z.boolean(),
  reservedFor: z.string().optional(),
  notes: z.string().max(500, 'Notes cannot exceed 500 characters').optional(),
})

type ShelfFormData = z.infer<typeof shelfSchema>

interface Shelf {
  _id: string
  code: string
  name: string
  warehouseId: string
  section: string
  row: number
  position: number
  level: number
  description?: string
  dimensions: {
    length: number
    width: number
    height: number
  }
  weightLimit: number
  shelfType: 'standard' | 'refrigerated' | 'hazmat' | 'fragile' | 'bulk'
  accessLevel: 'ground' | 'ladder' | 'forklift' | 'crane'
  isActive: boolean
  isReserved: boolean
  reservedFor?: string
  notes?: string
}

interface Warehouse {
  _id: string
  name: string
  code: string
}

interface EditShelfModalProps {
  isOpen: boolean
  onClose: () => void
  shelf: Shelf | null
  onShelfUpdated: () => void
  warehouses: Warehouse[]
}

export function EditShelfModal({
  isOpen,
  onClose,
  shelf,
  onShelfUpdated,
  warehouses
}: EditShelfModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const form = useForm<ShelfFormData>({
    resolver: zodResolver(shelfSchema),
    defaultValues: {
      code: '',
      name: '',
      warehouseId: '',
      section: 'A',
      row: 1,
      position: 1,
      level: 1,
      description: '',
      dimensions: {
        length: 1.0,
        width: 1.0,
        height: 2.0,
      },
      weightLimit: 100,
      shelfType: 'standard',
      accessLevel: 'ground',
      isActive: true,
      isReserved: false,
      reservedFor: '',
      notes: '',
    },
  })

  // Update form when shelf changes
  useEffect(() => {
    if (shelf) {
      form.reset({
        code: shelf.code,
        name: shelf.name,
        warehouseId: shelf.warehouseId,
        section: shelf.section,
        row: shelf.row,
        position: shelf.position,
        level: shelf.level,
        description: shelf.description || '',
        dimensions: {
          length: shelf.dimensions.length,
          width: shelf.dimensions.width,
          height: shelf.dimensions.height,
        },
        weightLimit: shelf.weightLimit,
        shelfType: shelf.shelfType,
        accessLevel: shelf.accessLevel,
        isActive: shelf.isActive,
        isReserved: shelf.isReserved,
        reservedFor: shelf.reservedFor || '',
        notes: shelf.notes || '',
      })
    }
  }, [shelf, form])

  const handleSubmit = async (data: ShelfFormData) => {
    if (!shelf) return

    setIsLoading(true)
    try {
      const payload = {
        ...data,
        ...(data.description && { description: data.description }),
        ...(data.reservedFor && { reservedFor: data.reservedFor }),
        ...(data.notes && { notes: data.notes }),
      }

      const response = await fetch(`/api/shelves/${shelf._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify(payload)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast({
          title: "Success",
          description: "Shelf updated successfully",
        })
        onShelfUpdated()
        onClose()
      } else {
        throw new Error(result.error || 'Failed to update shelf')
      }
    } catch (error) {
      console.error('Error updating shelf:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update shelf",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  if (!shelf) return null

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleClose}
      title="Edit Shelf"
      description="Update shelf information and settings"
      size="lg"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Shelf Code</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., A-01-01-01" 
                      {...field} 
                      onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Shelf Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter shelf name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="warehouseId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Warehouse</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select warehouse" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {warehouses.map((warehouse) => (
                      <SelectItem key={warehouse._id} value={warehouse._id}>
                        {warehouse.name} ({warehouse.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Location Information */}
          <div className="grid grid-cols-4 gap-4">
            <FormField
              control={form.control}
              name="section"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Section</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="A" 
                      {...field} 
                      onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="row"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Row</FormLabel>
                  <FormControl>
                    <Input 
                      type="number"
                      placeholder="1"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="position"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Position</FormLabel>
                  <FormControl>
                    <Input 
                      type="number"
                      placeholder="1"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="level"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Level</FormLabel>
                  <FormControl>
                    <Input 
                      type="number"
                      placeholder="1"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Status Controls */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Active Status</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Enable or disable this shelf
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isReserved"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Reserved</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Mark shelf as reserved
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          {form.watch('isReserved') && (
            <FormField
              control={form.control}
              name="reservedFor"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reserved For</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter reservation details" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* Submit Button */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Updating...' : 'Update Shelf'}
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  )
}
