"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  Loader2,
  Package,
  DollarSign,
  Hash,
  FileText,
  Tag,
  Building2,
  Star,
  ToggleLeft,
  Plus,
  X,
  Info,
  Upload,
  Image as ImageIcon,
  Trash2
} from "lucide-react"
import BaseModal from "./base-modal"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { CreateProductData, FormModalProps, ProductCategory } from "@/types"
import { useBranchStore } from "@/stores/branchStore"
import { useCategoryStore } from "@/stores/categoryStore"

interface CurrentUser {
  role: string
  branchId?: string
}

// Simplified schema with only essential required fields
const productSchema = z.object({
  // Essential required fields
  name: z.string().min(2, "Product name must be at least 2 characters").max(100, "Product name must be less than 100 characters"),
  sku: z.string().min(3, "SKU must be at least 3 characters").max(20, "SKU must be less than 20 characters"),
  category: z.string().min(1, "Please select a category"),
  price: z.number().min(0.01, "Price must be greater than 0").max(100000000, "Price cannot exceed 100 million"),
  stock: z.number().min(0, "Stock cannot be negative"),
  branchId: z.string().min(1, "Please select a branch"),
  description: z.string().min(10, "Description must be at least 10 characters").max(500, "Description must be less than 500 characters"),

  // Optional fields that can be added later
  originalPrice: z.number().min(0, "Original price cannot be negative").max(100000000, "Original price cannot exceed 100 million").optional(),
  minStockLevel: z.number().min(0, "Minimum stock level cannot be negative").default(5),
  brand: z.string().max(50, "Brand must be less than 50 characters").optional(),
  model: z.string().max(50, "Model must be less than 50 characters").optional(),
  warranty: z.string().optional(),
  weight: z.number().optional(),
  dimensions: z.object({
    length: z.number(),
    width: z.number(),
    height: z.number(),
  }).optional(),
  specifications: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  isActive: z.boolean().default(true),
  isFeatured: z.boolean().default(false),

  // Image handling - store as File objects, not URLs
  featuredImage: z.any().optional(), // Will be File object
  additionalImages: z.array(z.any()).default([]), // Will be File objects
})

type ProductFormData = z.infer<typeof productSchema>



interface AddProductModalProps extends FormModalProps<CreateProductData> {}

export default function AddProductModal({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false,
  initialData,
  mode = "create"
}: AddProductModalProps) {
  const [newSpec, setNewSpec] = useState("")
  const [newTag, setNewTag] = useState("")

  // Image handling state
  const [featuredImagePreview, setFeaturedImagePreview] = useState<string | null>(null)
  const [additionalImagePreviews, setAdditionalImagePreviews] = useState<string[]>([])
  const [isDragOver, setIsDragOver] = useState(false)

  // Fetch branches from the store
  const { branches, fetchBranches, isLoading: branchesLoading } = useBranchStore()

  // Fetch categories from the store
  const { activeCategories, fetchActiveCategories, isLoading: categoriesLoading } = useCategoryStore()

  const form = useForm<ProductFormData>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      // Essential required fields
      name: initialData?.name || "",
      sku: initialData?.sku || "",
      category: initialData?.category || "",
      price: initialData?.price || 0,
      stock: initialData?.stock || 0,
      branchId: initialData?.branchId || "",
      description: initialData?.description || "",

      // Optional fields
      originalPrice: initialData?.originalPrice,
      minStockLevel: initialData?.minStockLevel || 5,
      brand: initialData?.brand || "",
      model: initialData?.model || "",
      warranty: initialData?.warranty || "",
      weight: initialData?.weight,
      dimensions: initialData?.dimensions,
      specifications: initialData?.specifications || [],
      tags: initialData?.tags || [],
      isActive: initialData?.isActive ?? true,
      isFeatured: initialData?.isFeatured ?? false,

      // Image fields
      featuredImage: null,
      additionalImages: [],
    },
  })

  // Fetch branches and categories when modal opens
  useEffect(() => {
    if (isOpen) {
      if (branches.length === 0) {
        fetchBranches()
      }
      if (activeCategories.length === 0) {
        fetchActiveCategories()
      }
    }
  }, [isOpen, branches.length, activeCategories.length, fetchBranches, fetchActiveCategories])

  // Reset form when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData) {
      form.reset({
        name: initialData.name || "",
        sku: initialData.sku || "",
        category: initialData.category || "",
        price: initialData.price || 0,
        originalPrice: initialData.originalPrice,
        stock: initialData.stock || 0,
        minStockLevel: initialData.minStockLevel || 5,
        description: initialData.description || "",
        specifications: initialData.specifications || [],
        branchId: initialData.branchId || "",
        brand: initialData.brand || "",
        model: initialData.model || "",
        warranty: initialData.warranty || "",
        weight: initialData.weight,
        dimensions: initialData.dimensions,
        tags: initialData.tags || [],
        isActive: initialData.isActive ?? true,
        isFeatured: initialData.isFeatured ?? false,
      })
    } else {
      form.reset({
        // Essential required fields
        name: "",
        sku: "",
        category: "",
        price: 0,
        stock: 0,
        branchId: "",
        description: "",

        // Optional fields
        originalPrice: undefined,
        minStockLevel: 5,
        brand: "",
        model: "",
        warranty: "",
        weight: undefined,
        dimensions: undefined,
        specifications: [],
        tags: [],
        isActive: true,
        isFeatured: false,

        // Image fields
        featuredImage: null,
        additionalImages: [],
      })

      // Reset image previews
      setFeaturedImagePreview(null)
      setAdditionalImagePreviews([])
    }
  }, [initialData, form])

  const handleSubmit = async (data: ProductFormData) => {
    try {
      // Prepare the product data without image files
      const productData = {
        ...data,
        featuredImage: undefined, // Remove File object
        additionalImages: undefined, // Remove File objects
      }

      // Submit the product data
      await onSubmit(productData as CreateProductData)

      // Reset form and close modal
      form.reset()
      setFeaturedImagePreview(null)
      setAdditionalImagePreviews([])
      onClose()
    } catch (error) {
      console.error("Error submitting product:", error)
    }
  }

  const addSpecification = () => {
    if (newSpec.trim()) {
      const currentSpecs = form.getValues("specifications")
      form.setValue("specifications", [...currentSpecs, newSpec.trim()])
      setNewSpec("")
    }
  }

  const removeSpecification = (index: number) => {
    const currentSpecs = form.getValues("specifications")
    form.setValue("specifications", currentSpecs.filter((_, i) => i !== index))
  }

  const addTag = () => {
    if (newTag.trim()) {
      const currentTags = form.getValues("tags")
      if (!currentTags.includes(newTag.trim())) {
        form.setValue("tags", [...currentTags, newTag.trim()])
      }
      setNewTag("")
    }
  }

  const removeTag = (index: number) => {
    const currentTags = form.getValues("tags")
    form.setValue("tags", currentTags.filter((_, i) => i !== index))
  }

  // Image handling functions
  const validateImageFile = (file: File): { valid: boolean; error?: string } => {
    const allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/webp',
      'image/gif', 'image/bmp', 'image/tiff', 'image/svg+xml'
    ]
    const maxSize = 10 * 1024 * 1024 // 10MB

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `Invalid file type. Allowed: JPG, PNG, WEBP, GIF, BMP, TIFF, SVG`
      }
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size too large. Maximum size is 10MB`
      }
    }

    return { valid: true }
  }

  const createImagePreview = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target?.result as string)
      reader.readAsDataURL(file)
    })
  }

  const handleFeaturedImageSelect = async (file: File) => {
    const validation = validateImageFile(file)
    if (!validation.valid) {
      alert(validation.error)
      return
    }

    form.setValue("featuredImage", file)
    const preview = await createImagePreview(file)
    setFeaturedImagePreview(preview)
  }

  const handleAdditionalImagesSelect = async (files: FileList) => {
    const validFiles: File[] = []
    const previews: string[] = []

    for (const file of Array.from(files)) {
      const validation = validateImageFile(file)
      if (validation.valid) {
        validFiles.push(file)
        const preview = await createImagePreview(file)
        previews.push(preview)
      } else {
        alert(`${file.name}: ${validation.error}`)
      }
    }

    if (validFiles.length > 0) {
      const currentImages = form.getValues("additionalImages") || []
      form.setValue("additionalImages", [...currentImages, ...validFiles])
      setAdditionalImagePreviews(prev => [...prev, ...previews])
    }
  }

  const removeFeaturedImage = () => {
    form.setValue("featuredImage", null)
    setFeaturedImagePreview(null)
  }

  const removeAdditionalImage = (index: number) => {
    const currentImages = form.getValues("additionalImages") || []
    const newImages = currentImages.filter((_, i) => i !== index)
    form.setValue("additionalImages", newImages)

    const newPreviews = additionalImagePreviews.filter((_, i) => i !== index)
    setAdditionalImagePreviews(newPreviews)
  }

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent, isFeatured: boolean = false) => {
    e.preventDefault()
    setIsDragOver(false)

    const files = e.dataTransfer.files
    if (files.length > 0) {
      if (isFeatured) {
        handleFeaturedImageSelect(files[0])
      } else {
        handleAdditionalImagesSelect(files)
      }
    }
  }

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === "create" ? "Add New Product" : "Edit Product"}
      description={
        mode === "create"
          ? "Create a new product with essential information. You can add more details after creation."
          : "Update the product information"
      }
      size="xl"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Essential Information Notice */}
          {mode === "create" && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">Quick Product Creation</h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                    Fill in the essential fields below to create your product quickly. You can edit the product later to add more details like brand, model, warranty, specifications, and additional images.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Essential Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <Package className="h-5 w-5" />
              Essential Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Product Name
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Dell XPS 13 Laptop" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sku"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Hash className="h-4 w-4" />
                    SKU
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., DELL-XPS13-001" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Category and Branch */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    Category
                  </FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categoriesLoading ? (
                        <SelectItem value="loading" disabled>
                          Loading categories...
                        </SelectItem>
                      ) : activeCategories.length === 0 ? (
                        <SelectItem value="no-categories" disabled>
                          No categories available
                        </SelectItem>
                      ) : (
                        activeCategories.map((category) => (
                          <SelectItem key={category.id} value={category.name}>
                            {category.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="branchId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Branch
                  </FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a branch" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {branchesLoading ? (
                        <SelectItem value="loading" disabled>
                          Loading branches...
                        </SelectItem>
                      ) : branches.length === 0 ? (
                        <SelectItem value="no-branches" disabled>
                          No branches available
                        </SelectItem>
                      ) : (
                        branches
                          .filter(branch => branch.status === 'Active')
                          .map((branch) => (
                            <SelectItem key={branch._id} value={branch._id}>
                              {branch.name} - {branch.location}
                            </SelectItem>
                          ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Brand and Model */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="brand"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Brand</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Dell, HP, Samsung" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="model"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Model</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., XPS 13, EliteBook 840" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Price
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0.01"
                      max="100000000"
                      placeholder="0.00"
                      {...field}
                      onChange={(e) => {
                        const value = e.target.value
                        if (value === '' || value === '0') {
                          field.onChange(0)
                        } else {
                          const numValue = parseFloat(value)
                          field.onChange(isNaN(numValue) ? 0 : numValue)
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="originalPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Original Price (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      max="100000000"
                      placeholder="0.00"
                      {...field}
                      onChange={(e) => {
                        const value = e.target.value
                        if (value === '' || value === '0') {
                          field.onChange(undefined)
                        } else {
                          const numValue = parseFloat(value)
                          field.onChange(isNaN(numValue) ? undefined : numValue)
                        }
                      }}
                    />
                  </FormControl>
                  <FormDescription>For showing discounts</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Stock Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="stock"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Current Stock</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="0"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="minStockLevel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Minimum Stock Level</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="5"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormDescription>Alert when stock falls below this level</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Description */}
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Description
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Detailed product description..."
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          </div>

          {/* Featured Image Upload */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <ImageIcon className="h-5 w-5" />
              Product Image (Optional)
            </h3>

            <div className="space-y-4">
              {/* Featured Image */}
              <div>
                <Label className="text-sm font-medium">Featured Image</Label>
                <div
                  className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                    isDragOver
                      ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, true)}
                >
                  {featuredImagePreview ? (
                    <div className="space-y-3">
                      <div className="relative inline-block">
                        <img
                          src={featuredImagePreview}
                          alt="Featured preview"
                          className="max-w-full max-h-48 rounded-lg object-cover"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2"
                          onClick={removeFeaturedImage}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {form.getValues("featuredImage")?.name}
                      </p>
                    </div>
                  ) : (
                    <>
                      <input
                        type="file"
                        accept="image/jpeg,image/jpg,image/png,image/webp,image/gif,image/bmp,image/tiff,image/svg+xml"
                        onChange={(e) => e.target.files?.[0] && handleFeaturedImageSelect(e.target.files[0])}
                        className="hidden"
                        id="featured-image-upload"
                      />
                      <label htmlFor="featured-image-upload" className="cursor-pointer">
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                          Click to upload or drag and drop
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500">
                          JPG, PNG, WEBP, GIF, BMP, TIFF, SVG up to 10MB
                        </p>
                      </label>
                    </>
                  )}
                </div>
              </div>

              {/* Additional Images */}
              <div>
                <Label className="text-sm font-medium">Additional Images (Optional)</Label>
                <div
                  className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                    isDragOver
                      ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, false)}
                >
                  <input
                    type="file"
                    multiple
                    accept="image/jpeg,image/jpg,image/png,image/webp,image/gif,image/bmp,image/tiff,image/svg+xml"
                    onChange={(e) => e.target.files && handleAdditionalImagesSelect(e.target.files)}
                    className="hidden"
                    id="additional-images-upload"
                  />
                  <label htmlFor="additional-images-upload" className="cursor-pointer">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                      Click to upload multiple images or drag and drop
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      JPG, PNG, WEBP, GIF, BMP, TIFF, SVG up to 10MB each
                    </p>
                  </label>
                </div>

                {/* Additional Images Preview */}
                {additionalImagePreviews.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                    {additionalImagePreviews.map((preview, index) => (
                      <div key={index} className="relative">
                        <img
                          src={preview}
                          alt={`Additional preview ${index + 1}`}
                          className="w-full h-24 object-cover rounded-lg"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-1 right-1 h-6 w-6 p-0"
                          onClick={() => removeAdditionalImage(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Optional Fields Notice */}
          <div className="bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-gray-600 dark:text-gray-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">Additional Details</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  After creating this product, you can edit it to add more details like brand, model, warranty, specifications, weight, dimensions, and additional images to make it more informative for customers.
                </p>
              </div>
            </div>
          </div>



          {/* Status Switches */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="flex items-center gap-2">
                      <ToggleLeft className="h-4 w-4" />
                      Active Status
                    </FormLabel>
                    <FormDescription>
                      Active products are visible to customers
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isFeatured"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="flex items-center gap-2">
                      <Star className="h-4 w-4" />
                      Featured Product
                    </FormLabel>
                    <FormDescription>
                      Featured products appear prominently
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="min-w-[120px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {mode === "create" ? "Creating..." : "Updating..."}
                </>
              ) : (
                mode === "create" ? "Create Product" : "Update Product"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  )
}
