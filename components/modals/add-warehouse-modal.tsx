'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { BaseModal } from '@/components/modals/base-modal'
import { useToast } from '@/hooks/use-toast'
import { useBranchStore } from '@/stores/branchStore'
import { useAuthStore } from '@/stores/authStore'

const warehouseSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name cannot exceed 100 characters'),
  code: z.string().min(2, 'Code must be at least 2 characters').max(20, 'Code cannot exceed 20 characters'),
  description: z.string().max(500, 'Description cannot exceed 500 characters').optional(),
  type: z.enum(['main', 'branch', 'storage', 'distribution']),
  branchId: z.string().optional(),
  address: z.string().min(5, 'Address must be at least 5 characters').max(200, 'Address cannot exceed 200 characters'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
  managerId: z.string().optional(),
  operatingHours: z.object({
    open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
    close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  }),
})

type WarehouseFormData = z.infer<typeof warehouseSchema>

interface AddWarehouseModalProps {
  isOpen: boolean
  onClose: () => void
  onWarehouseCreated: () => void
}

export function AddWarehouseModal({
  isOpen,
  onClose,
  onWarehouseCreated
}: AddWarehouseModalProps) {
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const { branches } = useBranchStore()
  const { user } = useAuthStore()

  const form = useForm<WarehouseFormData>({
    resolver: zodResolver(warehouseSchema),
    defaultValues: {
      name: '',
      code: '',
      description: '',
      type: 'storage',
      branchId: user?.role === 'branch_manager' ? user.branchId : 'none',
      address: '',
      capacity: 1000,
      managerId: '',
      operatingHours: {
        open: '08:00',
        close: '17:00',
      },
    },
  })

  // Update form when user data becomes available
  useEffect(() => {
    if (user?.role === 'branch_manager' && user.branchId) {
      form.setValue('branchId', user.branchId)
    }
  }, [user, form])

  const handleSubmit = async (data: WarehouseFormData) => {
    setIsLoading(true)
    try {
      const payload = {
        ...data,
        ...(data.branchId && data.branchId !== 'none' && { branchId: data.branchId }),
        ...(data.managerId && { managerId: data.managerId }),
        ...(data.description && { description: data.description }),
      }

      console.log('📤 Warehouse creation payload:', JSON.stringify(payload, null, 2))
      console.log('📤 User info:', { role: user?.role, branchId: user?.branchId })

      const response = await fetch('/api/warehouses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: JSON.stringify(payload)
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast({
          title: "Success",
          description: "Warehouse created successfully",
        })
        onWarehouseCreated()
        form.reset()
        onClose()
      } else {
        throw new Error(result.error || 'Failed to create warehouse')
      }
    } catch (error) {
      console.error('Error creating warehouse:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create warehouse",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleClose}
      title="Add New Warehouse"
      description="Create a new warehouse or storage facility"
      size="lg"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Warehouse Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter warehouse name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Warehouse Code</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., WH001" 
                      {...field} 
                      onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description (Optional)</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Enter warehouse description"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Warehouse Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select warehouse type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="main">Main Warehouse</SelectItem>
                      <SelectItem value="branch">Branch Warehouse</SelectItem>
                      <SelectItem value="storage">Storage Facility</SelectItem>
                      <SelectItem value="distribution">Distribution Center</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="branchId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Branch {user?.role === 'overall_admin' ? '(Optional)' : ''}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={user?.role === 'branch_manager'}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select branch" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {user?.role === 'overall_admin' && (
                        <SelectItem value="none">No specific branch</SelectItem>
                      )}
                      {branches.map((branch) => (
                        <SelectItem key={branch._id} value={branch._id}>
                          {branch.name} - {branch.location}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                  {user?.role === 'branch_manager' && (
                    <p className="text-sm text-muted-foreground">
                      As a branch manager, warehouses will be created for your assigned branch.
                    </p>
                  )}
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Enter warehouse address"
                    className="resize-none"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="capacity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Storage Capacity (units)</FormLabel>
                <FormControl>
                  <Input 
                    type="number"
                    placeholder="Enter storage capacity"
                    {...field}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Operating Hours */}
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="operatingHours.open"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Opening Time</FormLabel>
                  <FormControl>
                    <Input 
                      type="time"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="operatingHours.close"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Closing Time</FormLabel>
                  <FormControl>
                    <Input 
                      type="time"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creating...' : 'Create Warehouse'}
            </Button>
          </div>
        </form>
      </Form>
    </BaseModal>
  )
}
