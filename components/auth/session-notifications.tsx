'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { X, Shield, Monitor } from 'lucide-react'

/**
 * SessionNotifications - Component to display session-related notifications
 * Shows alerts when user logs in from multiple devices
 */
export function SessionNotifications() {
  const { sessionNotifications, clearSessionNotifications } = useAuthStore()

  // Auto-clear notifications after 10 seconds
  useEffect(() => {
    if (sessionNotifications.length > 0) {
      const timer = setTimeout(() => {
        clearSessionNotifications()
      }, 10000)

      return () => clearTimeout(timer)
    }
  }, [sessionNotifications, clearSessionNotifications])

  if (sessionNotifications.length === 0) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-md">
      {sessionNotifications.map((notification, index) => (
        <Alert key={index} className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
          <Shield className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          <AlertTitle className="text-amber-800 dark:text-amber-200">
            Multiple Device Login Detected
          </AlertTitle>
          <AlertDescription className="text-amber-700 dark:text-amber-300 text-sm">
            {notification}
          </AlertDescription>
          <Button
            variant="ghost"
            size="sm"
            className="absolute top-2 right-2 h-6 w-6 p-0 text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-200"
            onClick={clearSessionNotifications}
          >
            <X className="h-3 w-3" />
          </Button>
        </Alert>
      ))}
    </div>
  )
}

/**
 * SessionManager - Component for managing active sessions
 * Allows users to view and terminate sessions from other devices
 */
export function SessionManager() {
  const { 
    activeSessions, 
    getActiveSessions, 
    terminateSession, 
    terminateAllOtherSessions,
    isLoading 
  } = useAuthStore()

  useEffect(() => {
    // Load active sessions when component mounts
    getActiveSessions()
  }, [getActiveSessions])

  const handleTerminateSession = async (sessionId: string) => {
    const result = await terminateSession(sessionId)
    if (result.success) {
      // Sessions will be refreshed automatically
    }
  }

  const handleTerminateAllOther = async () => {
    const result = await terminateAllOtherSessions()
    if (result.success) {
      // Sessions will be refreshed automatically
    }
  }

  const otherSessions = activeSessions.filter(session => !session.isCurrentSession)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Active Sessions</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={() => getActiveSessions()}
          disabled={isLoading}
        >
          Refresh
        </Button>
      </div>

      {/* Current Session */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-muted-foreground">Current Session</h4>
        {activeSessions
          .filter(session => session.isCurrentSession)
          .map(session => (
            <div
              key={session.id}
              className="flex items-center justify-between p-3 border rounded-lg bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800"
            >
              <div className="flex items-center space-x-3">
                <Monitor className="h-4 w-4 text-green-600 dark:text-green-400" />
                <div>
                  <p className="text-sm font-medium text-green-800 dark:text-green-200">
                    {session.deviceDescription}
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400">
                    {session.locationDescription} • Active since {new Date(session.loginTime).toLocaleString()}
                  </p>
                </div>
              </div>
              <span className="text-xs font-medium text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900 px-2 py-1 rounded">
                Current
              </span>
            </div>
          ))}
      </div>

      {/* Other Sessions */}
      {otherSessions.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-muted-foreground">Other Sessions</h4>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleTerminateAllOther}
              disabled={isLoading}
            >
              Terminate All
            </Button>
          </div>
          
          {otherSessions.map(session => (
            <div
              key={session.id}
              className="flex items-center justify-between p-3 border rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <Monitor className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">
                    {session.deviceDescription}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {session.locationDescription} • Last active {new Date(session.lastActivity).toLocaleString()}
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleTerminateSession(session.id)}
                disabled={isLoading}
              >
                Terminate
              </Button>
            </div>
          ))}
        </div>
      )}

      {otherSessions.length === 0 && (
        <div className="text-center py-6 text-muted-foreground">
          <Monitor className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">No other active sessions</p>
        </div>
      )}
    </div>
  )
}

export default SessionNotifications
