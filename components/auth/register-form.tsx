"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { useAuthStore } from "@/stores/authStore"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Loader2,
  Eye,
  EyeOff,
  User,
  Mail,
  Phone,
  Building2,
  UserPlus,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { z } from "zod"
import type { UserRole } from "@/types/frontend"

// Validation schema
const registerSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  username: z.string().min(3, "Username must be at least 3 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type RegisterFormData = z.infer<typeof registerSchema>
type ValidationErrors = Partial<Record<keyof RegisterFormData, string>>

export default function RegisterForm() {
  const router = useRouter()
  const { register, isLoading, error: authError, clearError, isAuthenticated, user } = useAuthStore()

  // Redirect authenticated users to their dashboard
  useEffect(() => {
    if (isAuthenticated && user) {
      // Use the same redirect logic as login
      const getRedirectUrl = (role: string, branchId?: string) => {
        switch (role) {
          case 'overall_admin':
            return '/dashboard'  // Main dashboard for overall admin
          case 'branch_manager':
            return '/dashboard'  // Main dashboard for branch manager (with role-based filtering)
          case 'sales_person':
            return '/dashboard'  // Main dashboard for sales person
          case 'customer':
            return '/dashboard'  // Main dashboard for customer
          default:
            return '/dashboard'
        }
      }

      const redirectUrl = getRedirectUrl(user.role, user.branchId)
      router.push(redirectUrl)
    }
  }, [isAuthenticated, user, router])

  // Form state
  const [formData, setFormData] = useState<RegisterFormData>({
    name: "",
    username: "",
    email: "",
    phone: "",
    password: "",
    confirmPassword: ""
  })

  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({})
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  // Handle input changes
  const handleInputChange = (field: keyof RegisterFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: undefined }))
    }
    
    // Clear auth error when user starts typing
    if (authError) {
      clearError()
    }
  }

  // Handle select changes
  const handleSelectChange = (field: keyof RegisterFormData) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  // Form validation
  const canSubmit = () => {
    const requiredFields = ['name', 'username', 'email', 'phone', 'password', 'confirmPassword', 'role']
    const hasRequiredFields = requiredFields.every(field => 
      formData[field as keyof RegisterFormData]?.toString().trim()
    )
    const hasBlockingErrors = Object.values(validationErrors).some(error => error !== undefined)
    return hasRequiredFields && !hasBlockingErrors && !isSubmitting && !isLoading
  }

  // Handle form submission
  const handleSubmit = async () => {
    setIsSubmitting(true)
    setSubmitStatus('idle')
    clearError()
    setValidationErrors({})

    try {
      // Validate form data
      const validatedData = registerSchema.parse(formData)
      
      // Prepare registration data (default role is customer)
      const registrationData = {
        name: validatedData.name,
        username: validatedData.username,
        email: validatedData.email,
        phone: validatedData.phone,
        password: validatedData.password,
        role: "customer" as const
      }

      const result = await register(registrationData)

      if (result.success) {
        setSubmitStatus('success')
        
        // Redirect to login page after successful registration
        setTimeout(() => {
          router.push("/login?message=Registration successful! Please sign in.")
        }, 2000)
      } else {
        setSubmitStatus('error')
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: ValidationErrors = {}
        error.errors.forEach((err) => {
          if (err.path[0]) {
            fieldErrors[err.path[0] as keyof RegisterFormData] = err.message
          }
        })
        setValidationErrors(fieldErrors)
        setSubmitStatus('error')
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get field status for styling
  const getFieldStatus = (field: keyof RegisterFormData) => {
    if (validationErrors[field]) return 'error'
    if (formData[field] && !validationErrors[field]) return 'success'
    return 'default'
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
      <div className="w-full max-w-md">
        {/* Logo and Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Image
              src="/logoTransparent.png"
              alt="Fathahitech Logo"
              width={32}
              height={32}
              className="h-8 w-auto mr-2"
            />
            <h1 className="text-2xl font-bold text-foreground">FathahiTech</h1>
          </div>
          <p className="text-muted-foreground">
            Create your account
          </p>
        </div>

        {/* Register Card */}
        <Card className="shadow-lg">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2">
              <UserPlus className="h-6 w-6" />
              Join FathahiTech
            </CardTitle>
            <CardDescription>
              Fill in your details to create an account
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Error Alert */}
            {(authError || submitStatus === 'error') && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {authError || "Please check your information and try again."}
                </AlertDescription>
              </Alert>
            )}

            {/* Success Alert */}
            {submitStatus === 'success' && (
              <Alert className="border-green-200 bg-green-50 text-green-800">
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Registration successful! Redirecting to login page...
                </AlertDescription>
              </Alert>
            )}

            <form className="space-y-4" onSubmit={(e) => e.preventDefault()}>
              {/* Name Field */}
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Full Name
                </Label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  placeholder="Enter your full name"
                  value={formData.name}
                  onChange={handleInputChange("name")}
                  disabled={isLoading || isSubmitting}
                  className={
                    getFieldStatus('name') === 'error'
                      ? 'border-red-500 focus:border-red-500'
                      : getFieldStatus('name') === 'success'
                      ? 'border-green-500 focus:border-green-500'
                      : ''
                  }
                />
                {validationErrors.name && (
                  <p className="text-sm text-red-600">{validationErrors.name}</p>
                )}
              </div>

              {/* Username Field */}
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  type="text"
                  placeholder="Choose a username"
                  value={formData.username}
                  onChange={handleInputChange("username")}
                  disabled={isLoading || isSubmitting}
                  className={
                    getFieldStatus('username') === 'error'
                      ? 'border-red-500 focus:border-red-500'
                      : getFieldStatus('username') === 'success'
                      ? 'border-green-500 focus:border-green-500'
                      : ''
                  }
                />
                {validationErrors.username && (
                  <p className="text-sm text-red-600">{validationErrors.username}</p>
                )}
              </div>

              {/* Email Field */}
              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email Address
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email address"
                  value={formData.email}
                  onChange={handleInputChange("email")}
                  disabled={isLoading || isSubmitting}
                  className={
                    getFieldStatus('email') === 'error'
                      ? 'border-red-500 focus:border-red-500'
                      : getFieldStatus('email') === 'success'
                      ? 'border-green-500 focus:border-green-500'
                      : ''
                  }
                />
                {validationErrors.email && (
                  <p className="text-sm text-red-600">{validationErrors.email}</p>
                )}
              </div>

              {/* Phone Field */}
              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Phone Number
                </Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  placeholder="Enter your phone number"
                  value={formData.phone}
                  onChange={handleInputChange("phone")}
                  disabled={isLoading || isSubmitting}
                  className={
                    getFieldStatus('phone') === 'error'
                      ? 'border-red-500 focus:border-red-500'
                      : getFieldStatus('phone') === 'success'
                      ? 'border-green-500 focus:border-green-500'
                      : ''
                  }
                />
                {validationErrors.phone && (
                  <p className="text-sm text-red-600">{validationErrors.phone}</p>
                )}
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Create a password"
                    value={formData.password}
                    onChange={handleInputChange("password")}
                    disabled={isLoading || isSubmitting}
                    className={
                      getFieldStatus('password') === 'error'
                        ? 'border-red-500 focus:border-red-500 pr-10'
                        : getFieldStatus('password') === 'success'
                        ? 'border-green-500 focus:border-green-500 pr-10'
                        : 'pr-10'
                    }
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isLoading || isSubmitting}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {validationErrors.password && (
                  <p className="text-sm text-red-600">{validationErrors.password}</p>
                )}
              </div>

              {/* Confirm Password Field */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm your password"
                    value={formData.confirmPassword}
                    onChange={handleInputChange("confirmPassword")}
                    disabled={isLoading || isSubmitting}
                    className={
                      getFieldStatus('confirmPassword') === 'error'
                        ? 'border-red-500 focus:border-red-500 pr-10'
                        : getFieldStatus('confirmPassword') === 'success'
                        ? 'border-green-500 focus:border-green-500 pr-10'
                        : 'pr-10'
                    }
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    disabled={isLoading || isSubmitting}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {validationErrors.confirmPassword && (
                  <p className="text-sm text-red-600">{validationErrors.confirmPassword}</p>
                )}
              </div>

              {/* Submit Button */}
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={!canSubmit()}
                className="w-full h-11"
              >
                {isSubmitting || isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Account...
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Create Account
                  </>
                )}
              </Button>
            </form>

            {/* Footer Links */}
            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                Already have an account?{" "}
                <Link href="/login" className="text-primary hover:underline">
                  Sign in
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
