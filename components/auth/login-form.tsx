"use client"

import React, { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { useAuthStore } from "@/stores/authStore"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Loader2,
  Eye,
  EyeOff,
  Building2,
  Mail,
  Lock,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { z } from "zod"

// Validation schema
const loginSchema = z.object({
  username: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address"),
  password: z
    .string()
    .min(1, "Password is required")
    .min(6, "Password must be at least 6 characters long"),
  rememberMe: z.boolean().optional()
})

type LoginFormData = z.infer<typeof loginSchema>
type ValidationErrors = Partial<Record<keyof LoginFormData, string>>

// Role-based redirect helper
const getRedirectUrl = (role: string, branchId?: string) => {
  switch (role) {
    case 'overall_admin':
      return '/dashboard'  // Main dashboard for overall admin
    case 'branch_manager':
      return '/dashboard'  // Main dashboard for branch manager (with role-based filtering)
    case 'sales_person':
      return '/dashboard'  // Main dashboard for sales person
    case 'customer':
      return '/dashboard'  // Main dashboard for customer
    default:
      return '/dashboard'
  }
}

export default function LoginForm() {
  const router = useRouter()
  const { login, isLoading, error: authError, clearError, isAuthenticated, user } = useAuthStore()

  // Redirect authenticated users to their dashboard
  useEffect(() => {
    if (isAuthenticated && user) {
      const redirectUrl = getRedirectUrl(user.role, user.branchId)
      router.push(redirectUrl)
    }
  }, [isAuthenticated, user, router])

  // Form state
  const [formData, setFormData] = useState<LoginFormData>({
    username: "",
    password: "",
    rememberMe: false
  })

  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({})
  const [showPassword, setShowPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  // Handle input changes
  const handleInputChange = (field: keyof LoginFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = field === 'rememberMe' ? e.target.checked : e.target.value
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: undefined }))
    }
    
    // Clear auth error when user starts typing
    if (authError) {
      clearError()
    }
  }

  // Form validation
  const canSubmit = () => {
    const hasRequiredFields = formData.username.trim() && formData.password.trim()
    const hasBlockingErrors = Object.values(validationErrors).some(error => error !== undefined)
    return hasRequiredFields && !hasBlockingErrors && !isSubmitting && !isLoading
  }

  // Handle form submission
  const handleSubmit = async () => {
    setIsSubmitting(true)
    setSubmitStatus('idle')
    clearError()
    setValidationErrors({})

    try {
      // Validate form data
      const validatedData = loginSchema.parse(formData)
      const result = await login(validatedData)
      console.log('Login result:', result)

      if (result.success) {
        setSubmitStatus('success')

        // Get the updated user from the store after successful login
        const currentUser = useAuthStore.getState().user
        console.log('Current user from store:', currentUser)

        if (currentUser) {
          const redirectUrl = getRedirectUrl(currentUser.role, currentUser.branchId)
          console.log('Redirecting to:', redirectUrl)

          // Small delay to show success state
          setTimeout(() => {
            router.push(redirectUrl)
          }, 1000)
        } else {
          console.log('No user found in store, redirecting to default dashboard')
          // Fallback to default dashboard
          setTimeout(() => {
            router.push("/dashboard")
          }, 1000)
        }
      } else {
        setSubmitStatus('error')
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldErrors: ValidationErrors = {}
        error.errors.forEach((err) => {
          if (err.path[0]) {
            fieldErrors[err.path[0] as keyof LoginFormData] = err.message
          }
        })
        setValidationErrors(fieldErrors)
        setSubmitStatus('error')
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get field status for styling
  const getFieldStatus = (field: keyof LoginFormData) => {
    if (validationErrors[field]) return 'error'
    if (formData[field] && !validationErrors[field]) return 'success'
    return 'default'
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
      <div className="w-full max-w-md">
        {/* Logo and Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Image
              src="/logoTransparent.png"
              alt="Fathahitech Logo"
              width={32}
              height={32}
              className="h-8 w-auto mr-2"
            />
            <h1 className="text-2xl font-bold text-foreground">FathahiTech</h1>
          </div>
          <p className="text-muted-foreground">
            Sign in to your account
          </p>
        </div>

        {/* Login Card */}
        <Card className="shadow-lg">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold">Welcome Back</CardTitle>
            <CardDescription>
              Enter your credentials to access your dashboard
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Error Alert */}
            {(authError || submitStatus === 'error') && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {authError || "Please check your credentials and try again."}
                </AlertDescription>
              </Alert>
            )}

            {/* Success Alert */}
            {submitStatus === 'success' && (
              <Alert className="border-green-200 bg-green-50 text-green-800">
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Login successful! Redirecting to your dashboard...
                </AlertDescription>
              </Alert>
            )}

            <form className="space-y-4" onSubmit={(e) => e.preventDefault()}>
              {/* Email Field */}
              <div className="space-y-2">
                <Label htmlFor="username" className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email Address
                </Label>
                <Input
                  id="username"
                  name="username"
                  type="email"
                  placeholder="Enter your email address"
                  value={formData.username}
                  onChange={handleInputChange("username")}
                  disabled={isLoading || isSubmitting}
                  className={
                    getFieldStatus('username') === 'error'
                      ? 'border-red-500 focus:border-red-500'
                      : getFieldStatus('username') === 'success'
                      ? 'border-green-500 focus:border-green-500'
                      : ''
                  }
                />
                {validationErrors.username && (
                  <p className="text-sm text-red-600">{validationErrors.username}</p>
                )}
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <Label htmlFor="password" className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={handleInputChange("password")}
                    disabled={isLoading || isSubmitting}
                    className={
                      getFieldStatus('password') === 'error'
                        ? 'border-red-500 focus:border-red-500 pr-10'
                        : getFieldStatus('password') === 'success'
                        ? 'border-green-500 focus:border-green-500 pr-10'
                        : 'pr-10'
                    }
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isLoading || isSubmitting}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                {validationErrors.password && (
                  <p className="text-sm text-red-600">{validationErrors.password}</p>
                )}
              </div>

              {/* Remember Me */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="rememberMe"
                  checked={formData.rememberMe}
                  onCheckedChange={(checked) => 
                    setFormData(prev => ({ ...prev, rememberMe: !!checked }))
                  }
                  disabled={isLoading || isSubmitting}
                />
                <Label htmlFor="rememberMe" className="text-sm">
                  Remember me
                </Label>
              </div>

              {/* Submit Button */}
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={!canSubmit()}
                className="w-full h-11"
              >
                {isSubmitting || isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  "Sign In"
                )}
              </Button>
            </form>

            {/* Footer Links */}
            <div className="text-center space-y-2">
              <Link
                href="/forgot-password"
                className="text-sm text-primary hover:underline"
              >
                Forgot your password?
              </Link>
              <p className="text-sm text-muted-foreground">
                Don't have an account?{" "}
                <Link href="/register" className="text-primary hover:underline">
                  Sign up
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
