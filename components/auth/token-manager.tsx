'use client'

import { useEffect, useRef } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { useRouter } from 'next/navigation'

/**
 * TokenManager - Handles automatic token refresh and expiration monitoring
 * This component should be included in the root layout to ensure it runs on all pages
 */
export function TokenManager() {
  const router = useRouter()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  
  const {
    isAuthenticated,
    token,
    tokenExpiry,
    isTokenExpired,
    refreshToken,
    logout,
    checkTokenExpiry,
    scheduleTokenRefresh,
  } = useAuthStore()

  // Monitor token expiration
  useEffect(() => {
    if (!isAuthenticated || !token) {
      // Clear any existing intervals/timeouts
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
      return
    }

    // Check token expiry immediately
    checkTokenExpiry()

    // Set up periodic token expiry checking (every minute)
    intervalRef.current = setInterval(() => {
      checkTokenExpiry()
    }, 60 * 1000) // Check every minute

    // Schedule automatic refresh
    scheduleTokenRefresh()

    // Cleanup on unmount or when dependencies change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }
    }
  }, [isAuthenticated, token, tokenExpiry, checkTokenExpiry, scheduleTokenRefresh])

  // Handle expired tokens
  useEffect(() => {
    if (isAuthenticated && isTokenExpired) {
      console.log('Token expired, attempting refresh...')
      
      refreshToken().then((result) => {
        if (!result.success) {
          console.log('Token refresh failed, logging out user')
          logout()
          router.push('/?reason=session-expired')
        }
      }).catch((error) => {
        console.error('Token refresh error:', error)
        logout()
        router.push('/?reason=session-expired')
      })
    }
  }, [isTokenExpired, isAuthenticated, refreshToken, logout, router])

  // Handle page visibility change - refresh token when page becomes visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isAuthenticated && token) {
        // Check if token needs refresh when user returns to the page
        checkTokenExpiry()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [isAuthenticated, token, checkTokenExpiry])

  // Handle window focus - refresh token when window gains focus
  useEffect(() => {
    const handleFocus = () => {
      if (isAuthenticated && token) {
        checkTokenExpiry()
      }
    }

    window.addEventListener('focus', handleFocus)
    
    return () => {
      window.removeEventListener('focus', handleFocus)
    }
  }, [isAuthenticated, token, checkTokenExpiry])

  // Handle network reconnection - refresh token when network comes back
  useEffect(() => {
    const handleOnline = () => {
      if (isAuthenticated && token) {
        console.log('Network reconnected, checking token status...')
        checkTokenExpiry()
      }
    }

    window.addEventListener('online', handleOnline)
    
    return () => {
      window.removeEventListener('online', handleOnline)
    }
  }, [isAuthenticated, token, checkTokenExpiry])

  // This component doesn't render anything
  return null
}

/**
 * TokenRefreshProvider - Higher-order component that wraps the app with token management
 */
export function TokenRefreshProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      <TokenManager />
      {children}
    </>
  )
}

/**
 * Hook for manual token refresh
 */
export function useTokenRefresh() {
  const { refreshToken, isAuthenticated, token } = useAuthStore()
  
  const manualRefresh = async () => {
    if (!isAuthenticated || !token) {
      throw new Error('No active session to refresh')
    }
    
    const result = await refreshToken()
    if (!result.success) {
      throw new Error(result.error || 'Token refresh failed')
    }
    
    return result
  }
  
  return {
    refreshToken: manualRefresh,
    canRefresh: isAuthenticated && !!token,
  }
}

/**
 * Hook for token expiry information
 */
export function useTokenExpiry() {
  const { tokenExpiry, isTokenExpired } = useAuthStore()
  
  const getTimeUntilExpiry = () => {
    if (!tokenExpiry) return null
    
    const now = new Date()
    const timeLeft = tokenExpiry.getTime() - now.getTime()
    
    return Math.max(timeLeft, 0)
  }
  
  const getExpiryStatus = () => {
    const timeLeft = getTimeUntilExpiry()
    
    if (!timeLeft) return 'unknown'
    if (isTokenExpired) return 'expired'
    if (timeLeft < 5 * 60 * 1000) return 'expiring-soon' // Less than 5 minutes
    if (timeLeft < 30 * 60 * 1000) return 'expiring' // Less than 30 minutes
    
    return 'valid'
  }
  
  return {
    tokenExpiry,
    isTokenExpired,
    timeUntilExpiry: getTimeUntilExpiry(),
    expiryStatus: getExpiryStatus(),
  }
}

/**
 * Development helper component to display token status
 */
export function TokenStatusIndicator() {
  const { tokenExpiry, isTokenExpired, timeUntilExpiry, expiryStatus } = useTokenExpiry()
  const { isAuthenticated } = useAuthStore()
  
  // Only show in development
  if (process.env.NODE_ENV !== 'development' || !isAuthenticated) {
    return null
  }
  
  const formatTime = (ms: number | null) => {
    if (!ms) return 'Unknown'
    
    const minutes = Math.floor(ms / (1000 * 60))
    const seconds = Math.floor((ms % (1000 * 60)) / 1000)
    
    return `${minutes}m ${seconds}s`
  }
  
  const getStatusColor = () => {
    switch (expiryStatus) {
      case 'expired': return 'bg-red-500'
      case 'expiring-soon': return 'bg-orange-500'
      case 'expiring': return 'bg-yellow-500'
      case 'valid': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }
  
  return (
    <div className="fixed bottom-4 right-4 z-50 p-2 bg-black bg-opacity-75 text-white text-xs rounded">
      <div className="flex items-center space-x-2">
        <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
        <span>Token: {expiryStatus}</span>
      </div>
      {timeUntilExpiry !== null && (
        <div className="text-xs opacity-75">
          Expires in: {formatTime(timeUntilExpiry)}
        </div>
      )}
    </div>
  )
}
