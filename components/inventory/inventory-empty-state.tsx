'use client'

import { Package, PlusCircle, Search, Warehouse } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface InventoryEmptyStateProps {
  type?: 'no-inventory' | 'no-search-results' | 'no-filter-results'
  searchQuery?: string
  onAddInventory?: () => void
  onClearSearch?: () => void
  onClearFilters?: () => void
  canAddInventory?: boolean
}

export function InventoryEmptyState({
  type = 'no-inventory',
  searchQuery,
  onAddInventory,
  onClearSearch,
  onClearFilters,
  canAddInventory = true
}: InventoryEmptyStateProps) {
  const getEmptyStateContent = () => {
    switch (type) {
      case 'no-search-results':
        return {
          icon: <Search className="h-16 w-16 text-gray-400" />,
          title: 'No search results',
          description: searchQuery 
            ? `No inventory items found for "${searchQuery}"`
            : 'No inventory items match your search criteria',
          actions: (
            <div className="flex flex-col sm:flex-row gap-3">
              {onClearSearch && (
                <Button variant="outline" onClick={onClearSearch}>
                  Clear Search
                </Button>
              )}
              {canAddInventory && onAddInventory && (
                <Button onClick={onAddInventory} className="flex items-center gap-2">
                  <PlusCircle className="h-4 w-4" />
                  Add Inventory Item
                </Button>
              )}
            </div>
          )
        }

      case 'no-filter-results':
        return {
          icon: <Package className="h-16 w-16 text-gray-400" />,
          title: 'No items match filters',
          description: 'No inventory items match your current filter criteria',
          actions: (
            <div className="flex flex-col sm:flex-row gap-3">
              {onClearFilters && (
                <Button variant="outline" onClick={onClearFilters}>
                  Clear Filters
                </Button>
              )}
              {canAddInventory && onAddInventory && (
                <Button onClick={onAddInventory} className="flex items-center gap-2">
                  <PlusCircle className="h-4 w-4" />
                  Add Inventory Item
                </Button>
              )}
            </div>
          )
        }

      case 'no-inventory':
      default:
        return {
          icon: <Warehouse className="h-16 w-16 text-gray-400" />,
          title: 'No inventory items',
          description: 'Get started by adding your first inventory item to track stock levels and manage your products',
          actions: canAddInventory && onAddInventory ? (
            <Button onClick={onAddInventory} className="flex items-center gap-2">
              <PlusCircle className="h-4 w-4" />
              Add Your First Item
            </Button>
          ) : null
        }
    }
  }

  const content = getEmptyStateContent()

  return (
    <Card className="border-dashed border-2 border-gray-200">
      <CardContent className="flex flex-col items-center justify-center py-16 px-6 text-center">
        <div className="mb-6">
          {content.icon}
        </div>
        
        <CardTitle className="text-xl font-semibold text-gray-900 mb-3">
          {content.title}
        </CardTitle>
        
        <CardDescription className="text-gray-600 mb-8 max-w-md">
          {content.description}
        </CardDescription>

        {content.actions && (
          <div className="flex flex-col sm:flex-row gap-3">
            {content.actions}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Specific empty state components for different scenarios
export function NoInventoryEmptyState({ onAddInventory, canAddInventory = true }: {
  onAddInventory?: () => void
  canAddInventory?: boolean
}) {
  return (
    <InventoryEmptyState
      type="no-inventory"
      onAddInventory={onAddInventory}
      canAddInventory={canAddInventory}
    />
  )
}

export function NoSearchResultsEmptyState({ 
  searchQuery, 
  onClearSearch, 
  onAddInventory,
  canAddInventory = true 
}: {
  searchQuery?: string
  onClearSearch?: () => void
  onAddInventory?: () => void
  canAddInventory?: boolean
}) {
  return (
    <InventoryEmptyState
      type="no-search-results"
      searchQuery={searchQuery}
      onClearSearch={onClearSearch}
      onAddInventory={onAddInventory}
      canAddInventory={canAddInventory}
    />
  )
}

export function NoFilterResultsEmptyState({ 
  onClearFilters, 
  onAddInventory,
  canAddInventory = true 
}: {
  onClearFilters?: () => void
  onAddInventory?: () => void
  canAddInventory?: boolean
}) {
  return (
    <InventoryEmptyState
      type="no-filter-results"
      onClearFilters={onClearFilters}
      onAddInventory={onAddInventory}
      canAddInventory={canAddInventory}
    />
  )
}
