"use client"

import { Suspense, ReactNode } from 'react'
import { InventoryErrorBoundary, InventoryLoadingError, InventoryNetworkError, InventoryPermissionError } from './inventory-error-boundary'
import { InventoryDashboardLoading } from './inventory-loading'
import { useInventoryStore } from '@/stores/inventoryStore'
import type { User } from '@/lib/auth-types'

interface InventoryWrapperProps {
  children: ReactNode
  currentUser: User | null
  fallbackComponent?: ReactNode
  showLoadingOverlay?: boolean
}

export function InventoryWrapper({ 
  children, 
  currentUser, 
  fallbackComponent,
  showLoadingOverlay = false 
}: InventoryWrapperProps) {
  const { error, isLoading, handleError, canRetry, retryLastOperation } = useInventoryStore()

  // Handle different error types
  const renderError = () => {
    if (!error) return null

    switch (error.type) {
      case 'network':
        return (
          <InventoryNetworkError 
            onRetry={canRetry() ? retryLastOperation : undefined}
          />
        )
      
      case 'permission':
        return <InventoryPermissionError />
      
      case 'validation':
      case 'server':
      case 'unknown':
      default:
        return (
          <InventoryLoadingError
            error={error.message}
            onRetry={canRetry() ? retryLastOperation : undefined}
            context="inventory operation"
          />
        )
    }
  }

  // Show error state if there's an error
  if (error) {
    return (
      <InventoryErrorBoundary
        onError={(err, errorInfo) => {
          console.error('Inventory Error Boundary:', err, errorInfo)
          handleError(err, 'error-boundary')
        }}
      >
        {renderError()}
      </InventoryErrorBoundary>
    )
  }

  // Show loading state
  if (isLoading && showLoadingOverlay) {
    return (
      <InventoryErrorBoundary
        onError={(err, errorInfo) => {
          console.error('Inventory Error Boundary:', err, errorInfo)
          handleError(err, 'error-boundary')
        }}
      >
        {fallbackComponent || <InventoryDashboardLoading />}
      </InventoryErrorBoundary>
    )
  }

  // Normal render with error boundary
  return (
    <InventoryErrorBoundary
      onError={(err, errorInfo) => {
        console.error('Inventory Error Boundary:', err, errorInfo)
        handleError(err, 'error-boundary')
      }}
    >
      <Suspense fallback={fallbackComponent || <InventoryDashboardLoading />}>
        {children}
      </Suspense>
    </InventoryErrorBoundary>
  )
}

// Higher-order component for wrapping inventory pages
export function withInventoryWrapper<P extends { currentUser: User | null }>(
  Component: React.ComponentType<P>,
  options?: {
    fallbackComponent?: ReactNode
    showLoadingOverlay?: boolean
  }
) {
  const WrappedComponent = (props: P) => (
    <InventoryWrapper
      currentUser={props.currentUser}
      fallbackComponent={options?.fallbackComponent}
      showLoadingOverlay={options?.showLoadingOverlay}
    >
      <Component {...props} />
    </InventoryWrapper>
  )

  WrappedComponent.displayName = `withInventoryWrapper(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Specific wrappers for different inventory components
export function InventoryDashboardWrapper({ 
  children, 
  currentUser 
}: { 
  children: ReactNode
  currentUser: User | null 
}) {
  return (
    <InventoryWrapper
      currentUser={currentUser}
      fallbackComponent={<InventoryDashboardLoading />}
      showLoadingOverlay={true}
    >
      {children}
    </InventoryWrapper>
  )
}

export function InventoryFormWrapper({ 
  children, 
  currentUser 
}: { 
  children: ReactNode
  currentUser: User | null 
}) {
  return (
    <InventoryWrapper
      currentUser={currentUser}
      fallbackComponent={
        <div className="space-y-4">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              <div className="h-10 bg-gray-200 rounded"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      }
    >
      {children}
    </InventoryWrapper>
  )
}

// Hook for using inventory error handling in components
export function useInventoryErrorHandling() {
  const { error, handleError, canRetry, retryLastOperation, clearError } = useInventoryStore()

  const handleAsyncOperation = async <T,>(
    operation: () => Promise<T>,
    context?: string
  ): Promise<{ success: boolean; data?: T; error?: string }> => {
    try {
      clearError()
      const result = await operation()
      return { success: true, data: result }
    } catch (err) {
      const inventoryError = handleError(err, context)
      return { success: false, error: inventoryError.message }
    }
  }

  const retryOperation = async <T,>(
    operation: () => Promise<T>,
    context?: string
  ): Promise<{ success: boolean; data?: T; error?: string }> => {
    if (!canRetry()) {
      return { success: false, error: 'Maximum retry attempts reached' }
    }

    return handleAsyncOperation(operation, context)
  }

  return {
    error,
    canRetry: canRetry(),
    handleAsyncOperation,
    retryOperation,
    clearError
  }
}

// Context provider for inventory error handling
import { createContext, useContext } from 'react'

interface InventoryErrorContextType {
  error: ReturnType<typeof useInventoryStore>['error']
  handleError: ReturnType<typeof useInventoryStore>['handleError']
  canRetry: boolean
  retryLastOperation: ReturnType<typeof useInventoryStore>['retryLastOperation']
  clearError: ReturnType<typeof useInventoryStore>['clearError']
}

const InventoryErrorContext = createContext<InventoryErrorContextType | null>(null)

export function InventoryErrorProvider({ children }: { children: ReactNode }) {
  const { error, handleError, canRetry, retryLastOperation, clearError } = useInventoryStore()

  const value: InventoryErrorContextType = {
    error,
    handleError,
    canRetry: canRetry(),
    retryLastOperation,
    clearError
  }

  return (
    <InventoryErrorContext.Provider value={value}>
      {children}
    </InventoryErrorContext.Provider>
  )
}

export function useInventoryErrorContext() {
  const context = useContext(InventoryErrorContext)
  if (!context) {
    throw new Error('useInventoryErrorContext must be used within an InventoryErrorProvider')
  }
  return context
}

// Utility function to check if user has inventory permissions
export function checkInventoryPermissions(user: User | null, operation: 'read' | 'write' | 'delete') {
  if (!user) return false

  switch (operation) {
    case 'read':
      return ['overall_admin', 'branch_manager'].includes(user.role)
    case 'write':
      return ['overall_admin', 'branch_manager'].includes(user.role)
    case 'delete':
      return user.role === 'overall_admin'
    default:
      return false
  }
}

// Component to check permissions before rendering inventory content
export function InventoryPermissionGate({ 
  children, 
  currentUser, 
  operation = 'read',
  fallback
}: {
  children: ReactNode
  currentUser: User | null
  operation?: 'read' | 'write' | 'delete'
  fallback?: ReactNode
}) {
  const hasPermission = checkInventoryPermissions(currentUser, operation)

  if (!hasPermission) {
    return fallback || <InventoryPermissionError />
  }

  return <>{children}</>
}
