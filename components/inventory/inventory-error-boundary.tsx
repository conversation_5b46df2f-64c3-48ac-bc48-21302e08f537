"use client"

import React, { Component, ReactNode } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug, Package } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  errorId: string
}

export class InventoryErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `INV_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error to your error reporting service
    console.error('Inventory Error Boundary caught an error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // Call the onError prop if provided
    this.props.onError?.(error, errorInfo)

    // You can also log the error to an error reporting service here
    // Example: Sentry.captureException(error, { contexts: { react: errorInfo } })
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    })
  }

  handleReportError = () => {
    const { error, errorInfo, errorId } = this.state
    
    // Create error report
    const errorReport = {
      errorId,
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // Copy to clipboard for easy reporting
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2))
      .then(() => {
        alert('Error details copied to clipboard. Please share this with support.')
      })
      .catch(() => {
        console.log('Error report:', errorReport)
        alert('Error details logged to console. Please check browser console and share with support.')
      })
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <Card className="max-w-2xl mx-auto mt-8">
          <CardHeader>
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-8 w-8 text-red-500" />
              <div>
                <CardTitle className="text-red-700">Inventory System Error</CardTitle>
                <CardDescription>
                  Something went wrong with the inventory component
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Bug className="h-4 w-4 text-red-600" />
                <span className="font-medium text-red-800">Error Details</span>
                <Badge variant="outline" className="text-xs">
                  ID: {this.state.errorId}
                </Badge>
              </div>
              <p className="text-sm text-red-700 mb-2">
                {this.state.error?.message || 'An unexpected error occurred'}
              </p>
              {process.env.NODE_ENV === 'development' && this.state.error?.stack && (
                <details className="mt-2">
                  <summary className="text-xs text-red-600 cursor-pointer hover:text-red-800">
                    Technical Details (Development)
                  </summary>
                  <pre className="mt-2 text-xs text-red-600 bg-red-100 p-2 rounded overflow-auto max-h-32">
                    {this.state.error.stack}
                  </pre>
                </details>
              )}
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">What you can do:</h4>
              <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                <li>Try refreshing the page or clicking "Try Again"</li>
                <li>Check your internet connection</li>
                <li>Clear your browser cache and cookies</li>
                <li>If the problem persists, report this error to support</li>
              </ul>
            </div>

            <div className="flex flex-wrap gap-3">
              <Button onClick={this.handleRetry} className="flex items-center gap-2">
                <RefreshCw className="h-4 w-4" />
                Try Again
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => window.location.reload()}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh Page
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/dashboard'}
                className="flex items-center gap-2"
              >
                <Home className="h-4 w-4" />
                Go to Dashboard
              </Button>
              
              <Button 
                variant="outline" 
                onClick={this.handleReportError}
                className="flex items-center gap-2"
              >
                <Bug className="h-4 w-4" />
                Report Error
              </Button>
            </div>

            <div className="text-xs text-gray-500 pt-4 border-t">
              <p>Error ID: {this.state.errorId}</p>
              <p>Time: {new Date().toLocaleString()}</p>
            </div>
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

// Hook for functional components to handle errors
export function useInventoryErrorHandler() {
  const handleError = React.useCallback((error: Error, context?: string) => {
    console.error(`Inventory Error${context ? ` (${context})` : ''}:`, error)
    
    // You can add additional error handling logic here
    // such as sending to an error reporting service
    
    return {
      message: error.message,
      timestamp: new Date().toISOString(),
      context
    }
  }, [])

  return { handleError }
}

// Higher-order component for wrapping inventory components
export function withInventoryErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) {
  const WrappedComponent = (props: P) => (
    <InventoryErrorBoundary fallback={fallback}>
      <Component {...props} />
    </InventoryErrorBoundary>
  )

  WrappedComponent.displayName = `withInventoryErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Specific error components for different scenarios
export function InventoryLoadingError({
  error,
  onRetry,
  context = "loading inventory data"
}: {
  error: string
  onRetry?: () => void
  context?: string
}) {
  // Determine error type more precisely
  const isAuthenticationError = error.includes('Authentication required') ||
                               error.includes('401') ||
                               error.includes('Unauthorized') ||
                               error.includes('Token')

  const isNetworkError = error.includes('fetch') ||
                        error.includes('network') ||
                        error.includes('connection') ||
                        error.includes('timeout') ||
                        error.includes('Failed to fetch')

  const isServerError = error.includes('500') ||
                       error.includes('Internal Server Error') ||
                       error.includes('Service Unavailable')

  const isValidationError = error.includes('validation') ||
                           error.includes('invalid') ||
                           error.includes('required')

  // Show appropriate message based on error type
  const getErrorContent = () => {
    if (isAuthenticationError) {
      return {
        icon: <AlertTriangle className="h-12 w-12 text-amber-500 mx-auto mb-4" />,
        title: "Authentication Issue",
        message: "Your session may have expired. Please refresh the page or log in again.",
        actionText: "Refresh Page"
      }
    }

    if (isNetworkError) {
      return {
        icon: <RefreshCw className="h-12 w-12 text-blue-500 mx-auto mb-4" />,
        title: "Connection Problem",
        message: "Unable to connect to the server. Please check your internet connection and try again.",
        actionText: "Retry Connection"
      }
    }

    if (isServerError) {
      return {
        icon: <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />,
        title: "Server Error",
        message: "The server is experiencing issues. Please try again in a few moments.",
        actionText: "Try Again"
      }
    }

    if (isValidationError) {
      return {
        icon: <AlertTriangle className="h-12 w-12 text-orange-500 mx-auto mb-4" />,
        title: "Data Issue",
        message: "There was a problem with the request. Please check your input and try again.",
        actionText: "Try Again"
      }
    }

    // Default error case - should rarely be shown
    return {
      icon: <AlertTriangle className="h-12 w-12 text-gray-500 mx-auto mb-4" />,
      title: "Something Went Wrong",
      message: "An unexpected error occurred. Please try refreshing the page.",
      actionText: "Refresh"
    }
  }

  const content = getErrorContent()

  return (
    <div className="flex items-center justify-center p-8">
      <div className="text-center max-w-md">
        {content.icon}
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {content.title}
        </h3>
        <p className="text-gray-600 mb-4">{content.message}</p>
        {onRetry && (
          <Button onClick={onRetry} className="flex items-center gap-2 mx-auto">
            <RefreshCw className="h-4 w-4" />
            {content.actionText}
          </Button>
        )}
      </div>
    </div>
  )
}

export function InventoryNetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <div className="flex items-center justify-center p-8">
      <div className="text-center max-w-md">
        <AlertTriangle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Connection Error
        </h3>
        <p className="text-gray-600 mb-4">
          Unable to connect to the server. Please check your internet connection and try again.
        </p>
        {onRetry && (
          <Button onClick={onRetry} className="flex items-center gap-2 mx-auto">
            <RefreshCw className="h-4 w-4" />
            Retry Connection
          </Button>
        )}
      </div>
    </div>
  )
}

export function InventoryPermissionError() {
  return (
    <div className="flex items-center justify-center p-8">
      <div className="text-center max-w-md">
        <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Access Denied
        </h3>
        <p className="text-gray-600 mb-4">
          You don't have permission to access this inventory data. Please contact your administrator.
        </p>
        <Button 
          variant="outline" 
          onClick={() => window.location.href = '/dashboard'}
          className="flex items-center gap-2 mx-auto"
        >
          <Home className="h-4 w-4" />
          Go to Dashboard
        </Button>
      </div>
    </div>
  )
}
