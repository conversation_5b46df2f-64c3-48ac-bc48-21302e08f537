"use client"

import { useState, useEffect } from "react"
import { 
  AlertTriangle, 
  Package, 
  TrendingDown, 
  Bell, 
  Settings,
  RefreshCw,
  Eye,
  EyeOff
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useInventoryStore } from "@/stores/inventoryStore"
import { useNotificationStore } from "@/stores/notificationStore"
import { useBusinessNotifications } from "@/components/providers/notification-provider"
import { inventoryService } from "@/services/frontend"
import type { User } from "@/lib/auth-types"
import type { Inventory } from "@/types/frontend"

interface InventoryAlertsProps {
  currentUser: User | null
}

interface AlertSettings {
  lowStockEnabled: boolean
  outOfStockEnabled: boolean
  expiryWarningEnabled: boolean
  autoRefresh: boolean
  refreshInterval: number // in minutes
}

export default function InventoryAlerts({ currentUser }: InventoryAlertsProps) {
  const { 
    inventoryItems, 
    isLoading, 
    fetchInventoryItems, 
    fetchInventoryByBranch,
    getLowStockItems,
    getOutOfStockItems
  } = useInventoryStore()
  
  const { showInventoryAlert } = useBusinessNotifications()
  
  const [alertSettings, setAlertSettings] = useState<AlertSettings>({
    lowStockEnabled: true,
    outOfStockEnabled: true,
    expiryWarningEnabled: true,
    autoRefresh: false,
    refreshInterval: 30
  })
  
  const [lastChecked, setLastChecked] = useState<Date | null>(null)
  const [showDismissed, setShowDismissed] = useState(false)
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(new Set())

  // Get filtered inventory items based on user role
  const filteredItems = currentUser?.role === 'branch_manager' 
    ? inventoryItems.filter(item => item.branchId === currentUser.branchId)
    : inventoryItems

  // Get alert items
  const lowStockItems = getLowStockItems(currentUser?.role === 'branch_manager' ? currentUser.branchId : undefined)
  const outOfStockItems = getOutOfStockItems(currentUser?.role === 'branch_manager' ? currentUser.branchId : undefined)
  
  // Get expiring items (within 30 days)
  const expiringItems = filteredItems.filter(item => {
    if (!item.expiryDate) return false
    const expiryDate = new Date(item.expiryDate)
    const thirtyDaysFromNow = new Date()
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)
    return expiryDate <= thirtyDaysFromNow && expiryDate > new Date()
  })

  // Check for new alerts and trigger notifications
  const checkForAlerts = async () => {
    if (!currentUser) return

    try {
      // Refresh inventory data
      if (currentUser.role === 'branch_manager') {
        await fetchInventoryByBranch(currentUser.branchId, { page: 1, limit: 100 })
      } else {
        await fetchInventoryItems({ page: 1, limit: 100 })
      }

      setLastChecked(new Date())

      // Trigger notifications for new alerts
      if (alertSettings.lowStockEnabled) {
        lowStockItems.forEach(item => {
          if (!dismissedAlerts.has(`low-${item._id}`)) {
            showInventoryAlert(
              `Low Stock Alert: ${item.productName}`,
              `Only ${item.stock} units remaining (minimum: ${item.minStockLevel})`,
              { productId: item._id, currentStock: item.stock, threshold: item.minStockLevel }
            )
          }
        })
      }

      if (alertSettings.outOfStockEnabled) {
        outOfStockItems.forEach(item => {
          if (!dismissedAlerts.has(`out-${item._id}`)) {
            showInventoryAlert(
              `Out of Stock: ${item.productName}`,
              `Product is completely out of stock and unavailable for sale`,
              { productId: item._id, currentStock: 0 }
            )
          }
        })
      }

      if (alertSettings.expiryWarningEnabled) {
        expiringItems.forEach(item => {
          if (!dismissedAlerts.has(`expiry-${item._id}`)) {
            const daysUntilExpiry = Math.ceil((new Date(item.expiryDate!).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
            showInventoryAlert(
              `Expiry Warning: ${item.productName}`,
              `Product expires in ${daysUntilExpiry} days`,
              { productId: item._id, expiryDate: item.expiryDate, daysUntilExpiry }
            )
          }
        })
      }
    } catch (error) {
      console.error('Error checking for alerts:', error)
    }
  }

  // Auto-refresh functionality
  useEffect(() => {
    if (!alertSettings.autoRefresh || !currentUser) return

    const interval = setInterval(checkForAlerts, alertSettings.refreshInterval * 60 * 1000)
    return () => clearInterval(interval)
  }, [alertSettings.autoRefresh, alertSettings.refreshInterval, currentUser])

  // Initial load
  useEffect(() => {
    if (currentUser) {
      checkForAlerts()
    }
  }, [currentUser])

  const dismissAlert = (type: 'low' | 'out' | 'expiry', itemId: string) => {
    const alertKey = `${type}-${itemId}`
    setDismissedAlerts(prev => new Set([...prev, alertKey]))
  }

  const getAlertPriority = (item: Inventory, type: 'low' | 'out' | 'expiry') => {
    switch (type) {
      case 'out':
        return 'critical'
      case 'low':
        return item.stock <= Math.floor(item.minStockLevel / 2) ? 'high' : 'medium'
      case 'expiry':
        const daysUntilExpiry = Math.ceil((new Date(item.expiryDate!).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
        return daysUntilExpiry <= 7 ? 'high' : 'medium'
      default:
        return 'medium'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const allAlerts = [
    ...outOfStockItems.map(item => ({ ...item, alertType: 'out' as const, priority: getAlertPriority(item, 'out') })),
    ...lowStockItems.map(item => ({ ...item, alertType: 'low' as const, priority: getAlertPriority(item, 'low') })),
    ...expiringItems.map(item => ({ ...item, alertType: 'expiry' as const, priority: getAlertPriority(item, 'expiry') }))
  ].sort((a, b) => {
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 }
    return priorityOrder[a.priority as keyof typeof priorityOrder] - priorityOrder[b.priority as keyof typeof priorityOrder]
  })

  const visibleAlerts = showDismissed 
    ? allAlerts 
    : allAlerts.filter(alert => !dismissedAlerts.has(`${alert.alertType}-${alert._id}`))

  if (!currentUser) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Inventory Alerts</h2>
          <p className="text-muted-foreground">
            Monitor stock levels and receive notifications for critical inventory issues
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={checkForAlerts} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Alert Settings</DialogTitle>
                <DialogDescription>
                  Configure when and how you receive inventory alerts
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="low-stock">Low Stock Alerts</Label>
                  <Switch
                    id="low-stock"
                    checked={alertSettings.lowStockEnabled}
                    onCheckedChange={(checked) => 
                      setAlertSettings(prev => ({ ...prev, lowStockEnabled: checked }))
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="out-of-stock">Out of Stock Alerts</Label>
                  <Switch
                    id="out-of-stock"
                    checked={alertSettings.outOfStockEnabled}
                    onCheckedChange={(checked) => 
                      setAlertSettings(prev => ({ ...prev, outOfStockEnabled: checked }))
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="expiry-warning">Expiry Warnings</Label>
                  <Switch
                    id="expiry-warning"
                    checked={alertSettings.expiryWarningEnabled}
                    onCheckedChange={(checked) => 
                      setAlertSettings(prev => ({ ...prev, expiryWarningEnabled: checked }))
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-refresh">Auto Refresh</Label>
                  <Switch
                    id="auto-refresh"
                    checked={alertSettings.autoRefresh}
                    onCheckedChange={(checked) => 
                      setAlertSettings(prev => ({ ...prev, autoRefresh: checked }))
                    }
                  />
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Alert Summary */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{outOfStockItems.length}</div>
            <p className="text-xs text-muted-foreground">items unavailable</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{lowStockItems.length}</div>
            <p className="text-xs text-muted-foreground">items need restocking</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Expiring Soon</CardTitle>
            <Package className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{expiringItems.length}</div>
            <p className="text-xs text-muted-foreground">expire within 30 days</p>
          </CardContent>
        </Card>
      </div>

      {/* Alerts Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Active Alerts</CardTitle>
              <CardDescription>
                {lastChecked && `Last checked: ${lastChecked.toLocaleString()}`}
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDismissed(!showDismissed)}
            >
              {showDismissed ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
              {showDismissed ? 'Hide Dismissed' : 'Show Dismissed'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {visibleAlerts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Bell className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No active alerts</p>
              <p className="text-sm">All inventory levels are within normal ranges</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Alert Type</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Current Stock</TableHead>
                  <TableHead>Details</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {visibleAlerts.map((alert) => {
                  const isDismissed = dismissedAlerts.has(`${alert.alertType}-${alert._id}`)
                  
                  return (
                    <TableRow key={`${alert.alertType}-${alert._id}`} className={isDismissed ? 'opacity-50' : ''}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{alert.productName}</div>
                          <div className="text-sm text-muted-foreground">{alert.sku}</div>
                          <div className="text-xs text-muted-foreground">{alert.branchName}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={
                          alert.alertType === 'out' ? 'border-red-200 text-red-800' :
                          alert.alertType === 'low' ? 'border-orange-200 text-orange-800' :
                          'border-yellow-200 text-yellow-800'
                        }>
                          {alert.alertType === 'out' ? 'Out of Stock' :
                           alert.alertType === 'low' ? 'Low Stock' :
                           'Expiring Soon'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getPriorityColor(alert.priority)}>
                          {alert.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-mono text-sm">
                          {alert.stock} units
                          {alert.alertType === 'low' && (
                            <div className="text-xs text-muted-foreground">
                              Min: {alert.minStockLevel}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {alert.alertType === 'expiry' && alert.expiryDate && (
                          <div className="text-sm">
                            Expires: {new Date(alert.expiryDate).toLocaleDateString()}
                          </div>
                        )}
                        {alert.alertType === 'low' && (
                          <div className="text-sm text-muted-foreground">
                            {Math.round((alert.stock / alert.minStockLevel) * 100)}% of minimum
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {!isDismissed && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => dismissAlert(alert.alertType, alert._id)}
                          >
                            Dismiss
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
