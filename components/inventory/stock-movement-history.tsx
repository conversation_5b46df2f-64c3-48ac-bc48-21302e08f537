// components/inventory/stock-movement-history.tsx
"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import {
  Calendar,
  TrendingUp,
  TrendingDown,
  RotateCcw,
  User as UserIcon,
  Package,
  MapPin,
  FileText,
  Filter,
  Download
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { apiClient } from "@/services/frontend"
import type { User } from "@/lib/auth-types"

interface StockMovement {
  _id: string
  type: 'Inventory'
  description: string
  userId: string
  userName: string
  branchId?: string
  branchName?: string
  metadata?: {
    inventoryId: string
    productName: string
    movementType: 'in' | 'out' | 'adjustment'
    quantity: number
    previousStock: number
    newStock: number
    reason: string
    reference?: string
  }
  timestamp: string
  createdAt: string
}

interface StockMovementHistoryProps {
  currentUser: User | null
  inventoryId?: string
  productName?: string
}

export default function StockMovementHistory({ 
  currentUser, 
  inventoryId, 
  productName 
}: StockMovementHistoryProps) {
  const [movements, setMovements] = useState<StockMovement[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState({
    movementType: 'all',
    dateFrom: '',
    dateTo: '',
    search: ''
  })

  const loadMovements = async () => {
    if (!currentUser) return

    setIsLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        type: 'Inventory',
        ...(inventoryId && { inventoryId }),
        ...(currentUser.role === 'branch_manager' && { branchId: currentUser.branchId }),
        ...(filters.movementType && filters.movementType !== 'all' && { movementType: filters.movementType }),
        ...(filters.dateFrom && { dateFrom: filters.dateFrom }),
        ...(filters.dateTo && { dateTo: filters.dateTo }),
        ...(filters.search && { search: filters.search }),
        limit: '50'
      })

      const response = await apiClient.get(`/api/activity-logs?${params}`)

      if (response.success && response.data) {
        setMovements(response.data as StockMovement[])
      } else {
        setError(response.error || 'Failed to load stock movements')
      }
    } catch (error) {
      console.error('Error loading stock movements:', error)
      setError('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadMovements()
  }, [currentUser, inventoryId, filters])

  const getMovementIcon = (movementType: string) => {
    switch (movementType) {
      case 'in':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'out':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'adjustment':
        return <RotateCcw className="h-4 w-4 text-blue-600" />
      default:
        return <Package className="h-4 w-4 text-gray-600" />
    }
  }

  const getMovementBadge = (movementType: string) => {
    switch (movementType) {
      case 'in':
        return <Badge className="bg-green-100 text-green-800">Stock In</Badge>
      case 'out':
        return <Badge className="bg-red-100 text-red-800">Stock Out</Badge>
      case 'adjustment':
        return <Badge className="bg-blue-100 text-blue-800">Adjustment</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const formatQuantityChange = (movement: StockMovement) => {
    if (!movement.metadata) return ''
    
    const { movementType, quantity, previousStock, newStock } = movement.metadata
    const sign = movementType === 'in' ? '+' : movementType === 'out' ? '-' : '±'
    
    return `${sign}${quantity} (${previousStock} → ${newStock})`
  }

  const exportMovements = () => {
    const csvContent = [
      ['Date', 'Product', 'Branch', 'Type', 'Quantity Change', 'Reason', 'User', 'Reference'].join(','),
      ...movements.map(movement => [
        format(new Date(movement.timestamp), 'yyyy-MM-dd HH:mm:ss'),
        movement.metadata?.productName || '',
        movement.branchName || '',
        movement.metadata?.movementType || '',
        formatQuantityChange(movement),
        movement.metadata?.reason || '',
        movement.userName,
        movement.metadata?.reference || ''
      ].map(field => `"${field}"`).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `stock-movements-${format(new Date(), 'yyyy-MM-dd')}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Stock Movement History
              {productName && <span className="text-muted-foreground">- {productName}</span>}
            </CardTitle>
            <CardDescription>
              Track all inventory changes and stock movements
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Filter Stock Movements</DialogTitle>
                  <DialogDescription>
                    Filter movements by type, date range, or search terms
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="movementType">Movement Type</Label>
                    <Select
                      value={filters.movementType}
                      onValueChange={(value) => setFilters(prev => ({ ...prev, movementType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="All types" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All types</SelectItem>
                        <SelectItem value="in">Stock In</SelectItem>
                        <SelectItem value="out">Stock Out</SelectItem>
                        <SelectItem value="adjustment">Adjustment</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="dateFrom">From Date</Label>
                      <Input
                        id="dateFrom"
                        type="date"
                        value={filters.dateFrom}
                        onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="dateTo">To Date</Label>
                      <Input
                        id="dateTo"
                        type="date"
                        value={filters.dateTo}
                        onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="search">Search</Label>
                    <Input
                      id="search"
                      placeholder="Search by product, reason, or reference..."
                      value={filters.search}
                      onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setFilters({ movementType: 'all', dateFrom: '', dateTo: '', search: '' })}
                    >
                      Clear
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            <Button variant="outline" size="sm" onClick={exportMovements} disabled={movements.length === 0}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="text-center py-8 text-red-600">
            <p>{error}</p>
            <Button variant="outline" onClick={loadMovements} className="mt-2">
              Try Again
            </Button>
          </div>
        )}

        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading movements...</p>
          </div>
        ) : movements.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No stock movements found</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date & Time</TableHead>
                <TableHead>Product</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Quantity Change</TableHead>
                <TableHead>Reason</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Reference</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {movements.map((movement) => (
                <TableRow key={movement._id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <div className="font-medium">
                          {format(new Date(movement.timestamp), 'MMM dd, yyyy')}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {format(new Date(movement.timestamp), 'HH:mm:ss')}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{movement.metadata?.productName}</div>
                      {movement.branchName && (
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <MapPin className="h-3 w-3" />
                          {movement.branchName}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getMovementIcon(movement.metadata?.movementType || '')}
                      {getMovementBadge(movement.metadata?.movementType || '')}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="font-mono text-sm">
                      {formatQuantityChange(movement)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="max-w-xs truncate" title={movement.metadata?.reason}>
                      {movement.metadata?.reason}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <UserIcon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{movement.userName}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {movement.metadata?.reference && (
                      <div className="flex items-center gap-1">
                        <FileText className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm font-mono">{movement.metadata.reference}</span>
                      </div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
