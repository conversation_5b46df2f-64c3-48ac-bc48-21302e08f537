"use client"

import { useState, useEffect } from "react"
import { 
  TrendingUp, 
  TrendingDown, 
  RotateCcw, 
  Package, 
  AlertTriangle,
  Activity,
  Calendar,
  BarChart3
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import StockMovementHistory from "./stock-movement-history"
import { apiClient } from "@/services/frontend"
import type { User } from "@/lib/auth-types"

interface MovementStats {
  totalMovements: number
  stockInMovements: number
  stockOutMovements: number
  adjustmentMovements: number
  recentMovements: any[]
  trendData: any[]
  topProducts: any[]
  timeframe: string
  dateRange: {
    start: string
    end: string
  }
}

interface StockMovementDashboardProps {
  currentUser: User | null
}

export default function StockMovementDashboard({ currentUser }: StockMovementDashboardProps) {
  const [stats, setStats] = useState<MovementStats | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [timeframe, setTimeframe] = useState('7d')

  const loadStats = async () => {
    if (!currentUser) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await apiClient.post('/api/activity-logs', {
        type: 'Inventory',
        timeframe,
        ...(currentUser.role === 'branch_manager' && { branchId: currentUser.branchId })
      })

      if (response.success && response.stats) {
        setStats(response.stats)
      } else {
        setError(response.error || 'Failed to load statistics')
      }
    } catch (error) {
      console.error('Error loading movement stats:', error)
      setError('An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadStats()
  }, [currentUser, timeframe])

  const getTimeframeLabel = (tf: string) => {
    switch (tf) {
      case '1d': return 'Last 24 Hours'
      case '7d': return 'Last 7 Days'
      case '30d': return 'Last 30 Days'
      case '90d': return 'Last 90 Days'
      default: return 'Last 7 Days'
    }
  }

  if (!currentUser) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Stock Movement Tracking</h1>
          <p className="text-muted-foreground">
            Monitor inventory changes and track stock movements across your {currentUser.role === 'branch_manager' ? 'branch' : 'organization'}
          </p>
        </div>
        <Select value={timeframe} onValueChange={setTimeframe}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1d">Last 24 Hours</SelectItem>
            <SelectItem value="7d">Last 7 Days</SelectItem>
            <SelectItem value="30d">Last 30 Days</SelectItem>
            <SelectItem value="90d">Last 90 Days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Statistics Cards */}
      {error ? (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Statistics</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={loadStats}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      ) : isLoading ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : stats ? (
        <>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Movements</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalMovements}</div>
                <p className="text-xs text-muted-foreground">
                  {getTimeframeLabel(timeframe)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Stock In</CardTitle>
                <TrendingUp className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.stockInMovements}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.totalMovements > 0 ? Math.round((stats.stockInMovements / stats.totalMovements) * 100) : 0}% of total
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Stock Out</CardTitle>
                <TrendingDown className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats.stockOutMovements}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.totalMovements > 0 ? Math.round((stats.stockOutMovements / stats.totalMovements) * 100) : 0}% of total
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Adjustments</CardTitle>
                <RotateCcw className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats.adjustmentMovements}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.totalMovements > 0 ? Math.round((stats.adjustmentMovements / stats.totalMovements) * 100) : 0}% of total
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Movements & Top Products */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Recent Movements
                </CardTitle>
                <CardDescription>Latest stock movements</CardDescription>
              </CardHeader>
              <CardContent>
                {stats.recentMovements.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No recent movements</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {stats.recentMovements.slice(0, 5).map((movement, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {movement.metadata?.movementType === 'in' && (
                            <TrendingUp className="h-4 w-4 text-green-600" />
                          )}
                          {movement.metadata?.movementType === 'out' && (
                            <TrendingDown className="h-4 w-4 text-red-600" />
                          )}
                          {movement.metadata?.movementType === 'adjustment' && (
                            <RotateCcw className="h-4 w-4 text-blue-600" />
                          )}
                          <div>
                            <div className="font-medium text-sm">
                              {movement.metadata?.productName}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {movement.branchName}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline" className="text-xs">
                            {movement.metadata?.movementType === 'in' ? '+' : 
                             movement.metadata?.movementType === 'out' ? '-' : '±'}
                            {movement.metadata?.quantity}
                          </Badge>
                          <div className="text-xs text-muted-foreground mt-1">
                            {new Date(movement.timestamp).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Most Active Products
                </CardTitle>
                <CardDescription>Products with most movements</CardDescription>
              </CardHeader>
              <CardContent>
                {stats.topProducts.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No product activity</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {stats.topProducts.slice(0, 5).map((product, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium text-sm">{product._id}</div>
                          <div className="text-xs text-muted-foreground">
                            {product.movementCount} movements
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium text-sm">
                            {product.totalQuantityMoved} units
                          </div>
                          <Badge variant="outline" className="text-xs">
                            #{index + 1}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </>
      ) : null}

      {/* Movement History */}
      <Tabs defaultValue="history" className="w-full">
        <TabsList>
          <TabsTrigger value="history">Movement History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="history" className="space-y-4">
          <StockMovementHistory currentUser={currentUser} />
        </TabsContent>
        
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Movement Analytics</CardTitle>
              <CardDescription>
                Detailed analytics and trends for stock movements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Analytics charts coming soon</p>
                <p className="text-sm">This will include movement trends, patterns, and forecasting</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
