"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Plus, Package, Store, Building2, Tag, Users, Crown } from "lucide-react"
import {
  AddCategoryModal,
  AddProductModal,
  AddShopModal,
  AddBranchModal,
  AddEmployeeModal,
  AddBranchManagerModal
} from "@/components/modals"
import {
  CreateProductCategoryData,
  CreateProductData,
  CreateShopData,
  CreateBranchData,
  CreateEmployeeData,
  CreateBranchManagerData
} from "@/types"

export default function ModalDemo() {
  const [categoryModalOpen, setCategoryModalOpen] = useState(false)
  const [productModalOpen, setProductModalOpen] = useState(false)
  const [shopModalOpen, setShopModalOpen] = useState(false)
  const [branchModalOpen, setBranchModalOpen] = useState(false)
  const [employeeModalOpen, setEmployeeModalOpen] = useState(false)
  const [branchManagerModalOpen, setBranchManagerModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const handleCategorySubmit = async (data: CreateProductCategoryData) => {
    setIsLoading(true)
    console.log("Category data:", data)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsLoading(false)
    alert("Category created successfully!")
  }

  const handleProductSubmit = async (data: CreateProductData) => {
    setIsLoading(true)
    console.log("Product data:", data)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsLoading(false)
    alert("Product created successfully!")
  }

  const handleShopSubmit = async (data: CreateShopData) => {
    setIsLoading(true)
    console.log("Shop data:", data)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsLoading(false)
    alert("Shop created successfully!")
  }

  const handleBranchSubmit = async (data: CreateBranchData) => {
    setIsLoading(true)
    console.log("Branch data:", data)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsLoading(false)
    alert("Branch created successfully!")
  }

  const handleEmployeeSubmit = async (data: CreateEmployeeData) => {
    setIsLoading(true)
    console.log("Employee data:", data)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsLoading(false)
    alert("Employee created successfully!")
  }

  const handleBranchManagerSubmit = async (data: CreateBranchManagerData) => {
    setIsLoading(true)
    console.log("Branch Manager data:", data)
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    setIsLoading(false)
    alert("Branch Manager created successfully!")
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Professional Modal Forms Demo</h1>
        <p className="text-muted-foreground">
          Test the professional modal form components with TypeScript validation
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Category Modal */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-2">
              <Tag className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>Product Category</CardTitle>
            <CardDescription>
              Add new product categories with validation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => setCategoryModalOpen(true)}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </Button>
          </CardContent>
        </Card>

        {/* Product Modal */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-2">
              <Package className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>Product</CardTitle>
            <CardDescription>
              Add new products with comprehensive details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => setProductModalOpen(true)}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Button>
          </CardContent>
        </Card>

        {/* Shop Modal */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-2">
              <Store className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>Shop</CardTitle>
            <CardDescription>
              Add new shop locations with contact info
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => setShopModalOpen(true)}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Shop
            </Button>
          </CardContent>
        </Card>

        {/* Branch Modal */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-2">
              <Building2 className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>Branch</CardTitle>
            <CardDescription>
              Add new branch locations and details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => setBranchModalOpen(true)}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Branch
            </Button>
          </CardContent>
        </Card>

        {/* Employee Modal */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-2">
              <Users className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>Employee</CardTitle>
            <CardDescription>
              Add new employees with role-based permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => setEmployeeModalOpen(true)}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Employee
            </Button>
          </CardContent>
        </Card>

        {/* Branch Manager Modal */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-2">
              <Crown className="h-6 w-6 text-primary" />
            </div>
            <CardTitle>Branch Manager</CardTitle>
            <CardDescription>
              Add branch managers with elevated permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button
              onClick={() => setBranchManagerModalOpen(true)}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Manager
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Modal Components */}
      <AddCategoryModal
        isOpen={categoryModalOpen}
        onClose={() => setCategoryModalOpen(false)}
        onSubmit={handleCategorySubmit}
        isLoading={isLoading}
      />

      <AddProductModal
        isOpen={productModalOpen}
        onClose={() => setProductModalOpen(false)}
        onSubmit={handleProductSubmit}
        isLoading={isLoading}
      />

      <AddShopModal
        isOpen={shopModalOpen}
        onClose={() => setShopModalOpen(false)}
        onSubmit={handleShopSubmit}
        isLoading={isLoading}
      />

      <AddBranchModal
        isOpen={branchModalOpen}
        onClose={() => setBranchModalOpen(false)}
        onSubmit={handleBranchSubmit}
        isLoading={isLoading}
      />

      <AddEmployeeModal
        isOpen={employeeModalOpen}
        onClose={() => setEmployeeModalOpen(false)}
        onSubmit={handleEmployeeSubmit}
        isLoading={isLoading}
        currentUserRole="overall_admin"
      />

      <AddBranchManagerModal
        isOpen={branchManagerModalOpen}
        onClose={() => setBranchManagerModalOpen(false)}
        onSubmit={handleBranchManagerSubmit}
        isLoading={isLoading}
      />
    </div>
  )
}
