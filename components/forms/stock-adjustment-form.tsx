"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useInventoryStore } from "@/stores/inventoryStore"
import { toast } from "sonner"
import type { Inventory, StockMovement } from "@/types/frontend"

const stockAdjustmentSchema = z.object({
  type: z.enum(["in", "out", "adjustment"], {
    required_error: "Please select a movement type",
  }),
  quantity: z.number().min(1, "Quantity must be greater than 0"),
  reason: z.string().min(1, "Reason is required"),
  reference: z.string().optional(),
  cost: z.number().min(0, "Cost must be 0 or greater").optional(),
})

type StockAdjustmentFormData = z.infer<typeof stockAdjustmentSchema>

interface StockAdjustmentFormProps {
  inventoryItem: Inventory
  onSuccess?: () => void
  onCancel?: () => void
}

export default function StockAdjustmentForm({ 
  inventoryItem, 
  onSuccess, 
  onCancel 
}: StockAdjustmentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const { updateStock } = useInventoryStore()

  const form = useForm<StockAdjustmentFormData>({
    resolver: zodResolver(stockAdjustmentSchema),
    defaultValues: {
      type: "in",
      quantity: 1,
      reason: "",
      reference: "",
      cost: 0,
    },
  })

  const movementType = form.watch("type")
  const quantity = form.watch("quantity")

  // Calculate new stock level
  const calculateNewStock = () => {
    const currentStock = inventoryItem.stock
    if (movementType === "in") {
      return currentStock + quantity
    } else if (movementType === "out") {
      return Math.max(0, currentStock - quantity)
    } else {
      // For adjustment, the quantity represents the final stock level
      return quantity
    }
  }

  const getMovementTypeLabel = (type: string) => {
    switch (type) {
      case "in":
        return "Stock In"
      case "out":
        return "Stock Out"
      case "adjustment":
        return "Stock Adjustment"
      default:
        return type
    }
  }

  const getMovementTypeColor = (type: string) => {
    switch (type) {
      case "in":
        return "bg-green-100 text-green-800"
      case "out":
        return "bg-red-100 text-red-800"
      case "adjustment":
        return "bg-blue-100 text-blue-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const onSubmit = async (data: StockAdjustmentFormData) => {
    setIsSubmitting(true)
    
    try {
      // Validate stock out doesn't exceed current stock
      if (data.type === "out" && data.quantity > inventoryItem.stock) {
        toast.error(`Cannot remove ${data.quantity} items. Only ${inventoryItem.stock} available.`)
        return
      }

      const movement: StockMovement = {
        type: data.type,
        quantity: data.quantity,
        reason: data.reason,
        reference: data.reference,
        cost: data.cost,
      }

      const result = await updateStock(inventoryItem._id, movement)

      if (result.success) {
        toast.success("Stock updated successfully")
        form.reset()
        onSuccess?.()
      } else {
        toast.error(result.error || "Failed to update stock")
      }
    } catch (error) {
      console.error("Error updating stock:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardHeader>
        <CardTitle>Stock Adjustment</CardTitle>
        <CardDescription>
          Adjust stock levels for {inventoryItem.productName}
        </CardDescription>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Current Stock:</span>
          <Badge variant="outline">{inventoryItem.stock} units</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Movement Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Movement Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select movement type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="in">Stock In (Add inventory)</SelectItem>
                      <SelectItem value="out">Stock Out (Remove inventory)</SelectItem>
                      <SelectItem value="adjustment">Stock Adjustment (Set exact amount)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Quantity */}
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {movementType === "adjustment" ? "New Stock Level" : "Quantity"}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      max={movementType === "out" ? inventoryItem.stock : undefined}
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                    />
                  </FormControl>
                  <FormDescription>
                    {movementType === "out" && `Maximum: ${inventoryItem.stock} units`}
                    {movementType === "adjustment" && "Set the exact stock level"}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Stock Preview */}
            <div className="p-4 bg-muted rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <span>Current Stock:</span>
                <span className="font-medium">{inventoryItem.stock} units</span>
              </div>
              <div className="flex items-center justify-between text-sm mt-2">
                <span>After {getMovementTypeLabel(movementType)}:</span>
                <span className="font-medium">
                  <Badge className={getMovementTypeColor(movementType)}>
                    {calculateNewStock()} units
                  </Badge>
                </span>
              </div>
            </div>

            {/* Reason */}
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Explain the reason for this stock movement..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Reference */}
            <FormField
              control={form.control}
              name="reference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reference (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., PO-001, Invoice #123" {...field} />
                  </FormControl>
                  <FormDescription>
                    Reference number or document ID
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Cost (for stock in) */}
            {movementType === "in" && (
              <FormField
                control={form.control}
                name="cost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cost per Unit (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Cost per unit for this stock addition
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="flex justify-end space-x-4">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Updating..." : "Update Stock"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
