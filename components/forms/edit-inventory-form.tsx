"use client"

import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useInventoryStore } from "@/stores/inventoryStore"
import { toast } from "sonner"
import type { Inventory, UpdateInventoryData } from "@/types/frontend"

const editInventorySchema = z.object({
  minStockLevel: z.number().min(0, "Minimum stock level must be 0 or greater"),
  maxStockLevel: z.number().min(1, "Maximum stock level must be greater than 0"),
  cost: z.number().min(0, "Cost must be 0 or greater"),
  location: z.string().min(1, "Location is required"),
  supplier: z.string().optional(),
  batchNumber: z.string().optional(),
  expiryDate: z.string().optional(),
}).refine((data) => data.maxStockLevel >= data.minStockLevel, {
  message: "Maximum stock level must be greater than or equal to minimum stock level",
  path: ["maxStockLevel"],
})

type EditInventoryFormData = z.infer<typeof editInventorySchema>

interface EditInventoryFormProps {
  inventoryItem: Inventory
  onSuccess?: () => void
  onCancel?: () => void
}

export default function EditInventoryForm({ 
  inventoryItem, 
  onSuccess, 
  onCancel 
}: EditInventoryFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const { updateInventoryItem } = useInventoryStore()

  const form = useForm<EditInventoryFormData>({
    resolver: zodResolver(editInventorySchema),
    defaultValues: {
      minStockLevel: inventoryItem.minStockLevel,
      maxStockLevel: inventoryItem.maxStockLevel,
      cost: inventoryItem.cost,
      location: inventoryItem.location,
      supplier: inventoryItem.supplier || "",
      batchNumber: inventoryItem.batchNumber || "",
      expiryDate: inventoryItem.expiryDate ? inventoryItem.expiryDate.split('T')[0] : "",
    },
  })

  const getStockStatus = () => {
    if (inventoryItem.stock === 0) {
      return { label: "Out of Stock", color: "bg-red-100 text-red-800" }
    } else if (inventoryItem.stock <= inventoryItem.minStockLevel) {
      return { label: "Low Stock", color: "bg-yellow-100 text-yellow-800" }
    } else {
      return { label: "In Stock", color: "bg-green-100 text-green-800" }
    }
  }

  const stockStatus = getStockStatus()

  const onSubmit = async (data: EditInventoryFormData) => {
    setIsSubmitting(true)
    
    try {
      const updateData: UpdateInventoryData = {
        minStockLevel: data.minStockLevel,
        maxStockLevel: data.maxStockLevel,
        cost: data.cost,
        location: data.location,
        supplier: data.supplier || undefined,
        batchNumber: data.batchNumber || undefined,
        expiryDate: data.expiryDate || undefined,
      }

      const result = await updateInventoryItem(inventoryItem._id, updateData)

      if (result.success) {
        toast.success("Inventory item updated successfully")
        onSuccess?.()
      } else {
        toast.error(result.error || "Failed to update inventory item")
      }
    } catch (error) {
      console.error("Error updating inventory item:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Edit Inventory Item</CardTitle>
        <CardDescription>
          Update inventory settings for {inventoryItem.productName}
        </CardDescription>
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">SKU:</span>
            <Badge variant="outline">{inventoryItem.sku}</Badge>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">Current Stock:</span>
            <Badge variant="outline">{inventoryItem.stock} units</Badge>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">Status:</span>
            <Badge className={stockStatus.color}>{stockStatus.label}</Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Read-only Product Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted rounded-lg">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Product Name</Label>
                <p className="text-sm font-medium">{inventoryItem.productName}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Branch</Label>
                <p className="text-sm font-medium">{inventoryItem.branchName}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Min Stock Level */}
              <FormField
                control={form.control}
                name="minStockLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Stock Level</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Alert when stock falls below this level
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Max Stock Level */}
              <FormField
                control={form.control}
                name="maxStockLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maximum Stock Level</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormDescription>
                      Maximum capacity for this item
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Cost */}
              <FormField
                control={form.control}
                name="cost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cost per Unit (MWK)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location */}
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Storage Location</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Warehouse A, Shelf 1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Supplier */}
              <FormField
                control={form.control}
                name="supplier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Supplier name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Batch Number */}
              <FormField
                control={form.control}
                name="batchNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Batch Number (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Batch/Lot number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Expiry Date */}
            <FormField
              control={form.control}
              name="expiryDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expiry Date (Optional)</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormDescription>
                    Leave empty if the product doesn't expire
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Updating..." : "Update Inventory Item"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
