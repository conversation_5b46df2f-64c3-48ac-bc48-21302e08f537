"use client"

import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useInventoryStore } from "@/stores/inventoryStore"
import { useProductsStore } from "@/stores/productsStore"
import { useBranchesStore } from "@/stores/branchesStore"
import { useWarehouseStore } from "@/stores/warehouseStore"
import { useShelfStore } from "@/stores/shelfStore"
import { useAuthStore } from "@/stores/authStore"
import { toast } from "sonner"
import type { CreateInventoryData } from "@/types/frontend"

const addInventorySchema = z.object({
  productId: z.string().min(1, "Please select a product"),
  branchId: z.string().min(1, "Please select a branch"),
  stock: z.number().min(0, "Stock must be 0 or greater"),
  minStockLevel: z.number().min(0, "Minimum stock level must be 0 or greater"),
  maxStockLevel: z.number().min(1, "Maximum stock level must be greater than 0"),
  cost: z.number().min(0, "Cost must be 0 or greater"),
  // Enhanced location fields
  locationType: z.enum(["warehouse", "shelf"], { required_error: "Please select a location type" }),
  warehouseId: z.string().optional(),
  shelfId: z.string().optional(),
  location: z.string().min(1, "Location is required"), // Fallback for manual entry
  supplier: z.string().optional(),
  batchNumber: z.string().optional(),
  expiryDate: z.string().optional(),
}).refine((data) => {
  // If warehouse is selected, warehouseId is required
  if (data.locationType === "warehouse" && !data.warehouseId) {
    return false
  }
  // If shelf is selected, shelfId is required
  if (data.locationType === "shelf" && !data.shelfId) {
    return false
  }
  return true
}, {
  message: "Please select a warehouse or shelf based on your location type",
  path: ["warehouseId"]
})

type AddInventoryFormData = z.infer<typeof addInventorySchema>

interface AddInventoryFormProps {
  onSuccess?: () => void
  onCancel?: () => void
}

export default function AddInventoryForm({ onSuccess, onCancel }: AddInventoryFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { createInventoryItem } = useInventoryStore()
  const { products, fetchProducts } = useProductsStore()
  const { branches, fetchBranches } = useBranchesStore()
  const { warehouses, fetchWarehouses } = useWarehouseStore()
  const { shelves, fetchShelves } = useShelfStore()
  const { currentUser } = useAuthStore()

  const form = useForm<AddInventoryFormData>({
    resolver: zodResolver(addInventorySchema),
    defaultValues: {
      productId: "",
      branchId: currentUser?.role === "branch_manager" ? currentUser.branchId : "",
      stock: 0,
      minStockLevel: 5,
      maxStockLevel: 100,
      cost: 0,
      locationType: currentUser?.role === "overall_admin" ? "warehouse" : "shelf",
      warehouseId: "",
      shelfId: "",
      location: "",
      supplier: "",
      batchNumber: "",
      expiryDate: "",
    },
  })

  // Load products, branches, warehouses, and shelves on mount
  useEffect(() => {
    fetchProducts({ page: 1, limit: 100 })
    if (currentUser?.role === "overall_admin") {
      fetchBranches({ page: 1, limit: 100 })
      fetchWarehouses({ page: 1, limit: 100 })
    } else if (currentUser?.role === "branch_manager" && currentUser.branchId) {
      // Branch managers can see warehouses for their branch and main/distribution warehouses
      fetchWarehouses({ page: 1, limit: 100 }, { branchId: currentUser.branchId })
      fetchShelves({ page: 1, limit: 100 }, { warehouseId: undefined }) // Load all accessible shelves
    }
  }, [fetchProducts, fetchBranches, fetchWarehouses, fetchShelves, currentUser?.role, currentUser?.branchId])

  // Watch for product selection to auto-fill some fields
  const selectedProductId = form.watch("productId")
  const selectedProduct = products.find(p => p._id === selectedProductId)

  // Watch for warehouse selection to load shelves
  const selectedWarehouseId = form.watch("warehouseId")
  const locationType = form.watch("locationType")

  useEffect(() => {
    if (selectedProduct) {
      form.setValue("cost", selectedProduct.price || 0)
    }
  }, [selectedProduct, form])

  // Load shelves when warehouse is selected
  useEffect(() => {
    if (selectedWarehouseId && locationType === "shelf") {
      fetchShelves({ page: 1, limit: 100 }, { warehouseId: selectedWarehouseId })
    }
  }, [selectedWarehouseId, locationType, fetchShelves])

  // Auto-generate location string based on selection
  useEffect(() => {
    if (locationType === "warehouse" && selectedWarehouseId) {
      const warehouse = warehouses.find(w => w._id === selectedWarehouseId)
      if (warehouse) {
        form.setValue("location", `${warehouse.name} (${warehouse.code})`)
      }
    } else if (locationType === "shelf") {
      const selectedShelfId = form.watch("shelfId")
      const shelf = shelves.find(s => s._id === selectedShelfId)
      if (shelf) {
        form.setValue("location", `${shelf.warehouseName} - ${shelf.code}`)
      }
    }
  }, [locationType, selectedWarehouseId, form.watch("shelfId"), warehouses, shelves, form])

  const onSubmit = async (data: AddInventoryFormData) => {
    setIsSubmitting(true)
    
    try {
      // Get product and branch names
      const product = products.find(p => p._id === data.productId)
      const branch = branches.find(b => b._id === data.branchId)
      
      if (!product) {
        toast.error("Selected product not found")
        return
      }

      if (!branch && currentUser?.role === "overall_admin") {
        toast.error("Selected branch not found")
        return
      }

      // Get warehouse and shelf information
      const warehouse = data.warehouseId ? warehouses.find(w => w._id === data.warehouseId) : null
      const shelf = data.shelfId ? shelves.find(s => s._id === data.shelfId) : null

      const inventoryData: CreateInventoryData = {
        productId: data.productId,
        productName: product.name,
        sku: product.sku,
        branchId: data.branchId,
        stock: data.stock,
        minStockLevel: data.minStockLevel,
        maxStockLevel: data.maxStockLevel,
        cost: data.cost,
        location: data.location,
        // Enhanced location data
        warehouseId: data.warehouseId,
        warehouseName: warehouse?.name,
        warehouseCode: warehouse?.code,
        shelfId: data.shelfId,
        shelfCode: shelf?.code,
        shelfName: shelf?.name,
        supplier: data.supplier,
        batchNumber: data.batchNumber,
        expiryDate: data.expiryDate,
        lastRestocked: new Date().toISOString(),
      }

      const result = await createInventoryItem(inventoryData)

      if (result.success) {
        toast.success("Inventory item added successfully")
        form.reset()
        onSuccess?.()
      } else {
        toast.error(result.error || "Failed to add inventory item")
      }
    } catch (error) {
      console.error("Error adding inventory item:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Add Inventory Item</CardTitle>
        <CardDescription>
          Add a new product to your branch inventory
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Product Selection */}
              <FormField
                control={form.control}
                name="productId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Product</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a product" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {products.map((product) => (
                          <SelectItem key={product._id} value={product._id}>
                            {product.name} ({product.sku})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Branch Selection (only for overall_admin) */}
              {currentUser?.role === "overall_admin" && (
                <FormField
                  control={form.control}
                  name="branchId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Branch</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a branch" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {branches.map((branch) => (
                            <SelectItem key={branch._id} value={branch._id}>
                              {branch.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Stock Quantity */}
              <FormField
                control={form.control}
                name="stock"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Stock</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Min Stock Level */}
              <FormField
                control={form.control}
                name="minStockLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Min Stock Level</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Max Stock Level */}
              <FormField
                control={form.control}
                name="maxStockLevel"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Max Stock Level</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Cost */}
              <FormField
                control={form.control}
                name="cost"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cost per Unit (MWK)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location Type Selection */}
              <FormField
                control={form.control}
                name="locationType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select location type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {currentUser?.role === "overall_admin" && (
                          <SelectItem value="warehouse">Warehouse</SelectItem>
                        )}
                        <SelectItem value="shelf">Shelf</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {currentUser?.role === "overall_admin"
                        ? "Choose warehouse for general storage or shelf for specific location"
                        : "Select a specific shelf for precise inventory tracking"
                      }
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Warehouse/Shelf Selection */}
            {locationType === "warehouse" && currentUser?.role === "overall_admin" && (
              <FormField
                control={form.control}
                name="warehouseId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Warehouse</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a warehouse" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {warehouses.map((warehouse) => (
                          <SelectItem key={warehouse._id} value={warehouse._id}>
                            {warehouse.name} ({warehouse.code}) - {warehouse.type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose the warehouse where this inventory will be stored
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {locationType === "shelf" && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Warehouse Selection for Shelf */}
                <FormField
                  control={form.control}
                  name="warehouseId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Warehouse</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select warehouse first" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {warehouses.map((warehouse) => (
                            <SelectItem key={warehouse._id} value={warehouse._id}>
                              {warehouse.name} ({warehouse.code})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select warehouse to see available shelves
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Shelf Selection */}
                <FormField
                  control={form.control}
                  name="shelfId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shelf</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={!selectedWarehouseId}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={selectedWarehouseId ? "Select a shelf" : "Select warehouse first"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {shelves
                            .filter(shelf => shelf.warehouseId === selectedWarehouseId && shelf.isActive)
                            .map((shelf) => (
                              <SelectItem key={shelf._id} value={shelf._id}>
                                {shelf.code} - {shelf.name}
                                {shelf.availabilityStatus && (
                                  <span className={`ml-2 text-xs ${
                                    shelf.availabilityStatus === 'available' ? 'text-green-600' :
                                    shelf.availabilityStatus === 'nearly_full' ? 'text-yellow-600' :
                                    shelf.availabilityStatus === 'full' ? 'text-red-600' :
                                    'text-gray-600'
                                  }`}>
                                    ({shelf.availabilityStatus})
                                  </span>
                                )}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Choose the specific shelf for this inventory
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Generated Location Display */}
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Generated Location</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Location will be auto-generated based on your selection"
                      readOnly
                      className="bg-gray-50 dark:bg-gray-800"
                    />
                  </FormControl>
                  <FormDescription>
                    This field is automatically populated based on your warehouse/shelf selection
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Supplier */}
              <FormField
                control={form.control}
                name="supplier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Supplier name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Batch Number */}
              <FormField
                control={form.control}
                name="batchNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Batch Number (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Batch/Lot number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Expiry Date */}
            <FormField
              control={form.control}
              name="expiryDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expiry Date (Optional)</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormDescription>
                    Leave empty if the product doesn't expire
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4">
              {onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Adding..." : "Add Inventory Item"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
