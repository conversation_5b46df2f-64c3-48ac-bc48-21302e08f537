"use client"

import type React from "react"

import { BarChartIcon } from "lucide-react" // Renamed to avoid conflict with component name

interface BarChartProps extends React.SVGProps<SVGSVGElement> {}

export default function BarChart(props: BarChartProps) {
  return (
    <div className="flex flex-col items-center justify-center h-full w-full text-muted-foreground">
      <BarChartIcon {...props} />
      <span className="mt-2 text-sm">Chart Placeholder</span>
    </div>
  )
}
