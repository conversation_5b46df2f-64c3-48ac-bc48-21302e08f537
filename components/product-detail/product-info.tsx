"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Star, ShoppingCart } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { BranchAwareAddToCart } from "@/components/cart/branch-aware-add-to-cart"
import { formatPrice } from "@/lib/currency"

interface ProductInfoProps {
  product: {
    id: string
    name: string
    price: number
    originalPrice?: number
    salePrice?: number
    currency?: string
    availability: "In Stock" | "Low Stock" | "Out of Stock"
    description: string
    specifications: string[]
    rating: number
    reviewsCount: number
    category?: string
    branchId?: string
    branchName?: string
    sku?: string
    stock?: number
    isOnSale?: boolean
    isPromoted?: boolean
  }
}

export default function ProductInfo({ product }: ProductInfoProps) {
  const getBadgeVariant = (availability: string) => {
    switch (availability) {
      case "In Stock":
        return "default"
      case "Low Stock":
        return "destructive"
      case "Out of Stock":
        return "secondary"
      default:
        return "outline"
    }
  }

  const getBadgeColors = (availability: string) => {
    switch (availability) {
      case "In Stock":
        return "bg-green-100 text-green-700 border-green-200"
      case "Low Stock":
        return "bg-yellow-100 text-yellow-700 border-yellow-200"
      case "Out of Stock":
        return "bg-red-100 text-red-700 border-red-200"
      default:
        return ""
    }
  }

  // Calculate price display
  const getPriceDisplay = () => {
    if (product.isOnSale && product.salePrice) {
      return {
        currentPrice: product.salePrice,
        originalPrice: product.price,
        savings: product.price - product.salePrice,
        savingsPercent: Math.round(((product.price - product.salePrice) / product.price) * 100)
      }
    } else if (product.originalPrice && product.originalPrice > product.price) {
      return {
        currentPrice: product.price,
        originalPrice: product.originalPrice,
        savings: product.originalPrice - product.price,
        savingsPercent: Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
      }
    }
    return {
      currentPrice: product.price,
      originalPrice: null,
      savings: 0,
      savingsPercent: 0
    }
  }

  const priceInfo = getPriceDisplay()

  return (
    <div className="space-y-6">
      <h1 className="text-4xl font-bold tracking-tight">{product.name}</h1>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-1 text-yellow-500">
          {[...Array(5)].map((_, i) => (
            <Star key={i} className={`h-5 w-5 ${i < product.rating ? "fill-current" : "text-gray-300"}`} />
          ))}
          <span className="ml-2 text-muted-foreground text-sm">({product.reviewsCount} Reviews)</span>
        </div>
        <Separator orientation="vertical" className="h-6" />
        <Badge variant={getBadgeVariant(product.availability)} className={getBadgeColors(product.availability)}>
          {product.availability}
        </Badge>
      </div>
      {/* Price Display */}
      <div className="space-y-2">
        <div className="flex items-center gap-4">
          <span className="text-5xl font-extrabold text-primary">
            {formatPrice(priceInfo.currentPrice, product.currency || 'MWK')}
          </span>
          {priceInfo.originalPrice && (
            <span className="text-2xl text-muted-foreground line-through">
              {formatPrice(priceInfo.originalPrice, product.currency || 'MWK')}
            </span>
          )}
        </div>
        {priceInfo.savings > 0 && (
          <div className="flex items-center gap-2">
            <Badge className="bg-red-100 text-red-700 border-red-200">
              Save {formatPrice(priceInfo.savings, product.currency || 'MWK')} ({priceInfo.savingsPercent}% off)
            </Badge>
            {product.isOnSale && (
              <Badge className="bg-red-500 text-white">
                ON SALE
              </Badge>
            )}
            {product.isPromoted && (
              <Badge className="bg-blue-500 text-white">
                PROMOTED
              </Badge>
            )}
          </div>
        )}
      </div>
      <p className="text-lg text-muted-foreground leading-relaxed">{product.description}</p>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-xl font-semibold">Specifications</h3>
        <ul className="list-disc list-inside text-muted-foreground space-y-2">
          {product.specifications.map((spec, index) => (
            <li key={index}>{spec}</li>
          ))}
        </ul>
      </div>

      <Separator />

      <BranchAwareAddToCart
        product={{
          id: product.id,
          name: product.name,
          price: priceInfo.currentPrice,
          image: undefined, // Will be handled by the product detail page
          category: product.category,
          branchId: product.branchId,
          branchName: product.branchName,
          stock: product.stock || (product.availability === "Out of Stock" ? 0 : 50),
          sku: product.sku
        }}
        disabled={product.availability === "Out of Stock"}
        className="w-full py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-shadow"
      >
        <ShoppingCart className="mr-3 h-6 w-6" />
        {product.availability === "Out of Stock" ? "Out of Stock" : `Add to Cart - ${formatPrice(priceInfo.currentPrice, product.currency || 'MWK')}`}
      </BranchAwareAddToCart>
    </div>
  )
}
