"use client"

import Link from "next/link"
import Image from "next/image"
import { Card, CardContent, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import type { Product } from "@/types/frontend"

interface RelatedProductsProps {
  products: Product[]
  isLoading?: boolean
}

export default function RelatedProducts({ products, isLoading = false }: RelatedProductsProps) {
  return (
    <section className="py-12 md:py-16 bg-gray-50 dark:bg-gray-900 rounded-xl">
      <div className="container px-4 md:px-6">
        <h2 className="text-3xl font-bold tracking-tight mb-8 text-center">You Might Also Like</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {isLoading ? (
            [...Array(3)].map((_, i) => (
              <Card key={i} className="flex flex-col items-center text-center p-4 shadow-sm rounded-xl">
                <Skeleton className="w-full h-48 mb-4" />
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2 mb-4" />
                <Skeleton className="h-10 w-full" />
              </Card>
            ))
          ) : products.length > 0 ? (
            products.map((product) => (
            <Card
              key={product._id || product.id}
              className="flex flex-col items-center text-center p-4 shadow-sm hover:shadow-md transition-shadow rounded-xl"
            >
              <Link
                href={`/products/${product._id || product.id}`}
                className="relative w-full h-40 flex items-center justify-center overflow-hidden mb-4 group"
              >
                <Image
                  src={product.featuredImage || product.images?.[0] || "/placeholder.svg"}
                  alt={product.name}
                  width={120}
                  height={120}
                  className="object-contain transition-transform duration-300 ease-in-out group-hover:scale-105"
                  loading="lazy"
                />
              </Link>
              <CardContent className="p-0 flex flex-col items-center">
                <CardTitle className="text-lg font-semibold mb-2 line-clamp-2">{product.name}</CardTitle>
                <p className="text-xl font-bold text-primary mb-4">${product.price.toFixed(2)}</p>
                <Button
                  variant="outline"
                  className="w-full shadow-sm hover:shadow-md transition-shadow bg-transparent"
                  asChild
                >
                  <Link href={`/products/${product._id || product.id}`}>View Details</Link>
                </Button>
              </CardContent>
            </Card>
          ))
          ) : (
            <div className="col-span-full text-center text-muted-foreground">
              No related products found
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
