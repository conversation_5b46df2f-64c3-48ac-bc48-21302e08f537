"use client"

import Image from "next/image"
import { useState } from "react"
import { cn } from "@/lib/utils"

interface ProductImageGalleryProps {
  images: string[]
  alt: string
}

export default function ProductImageGallery({ images, alt }: ProductImageGalleryProps) {
  const [mainImage, setMainImage] = useState(images[0])

  return (
    <div className="grid gap-4 lg:grid-cols-5">
      <div className="hidden lg:flex flex-col gap-2 overflow-y-auto max-h-[500px] pr-2">
        {images.map((image, index) => (
          <button
            key={index}
            onClick={() => setMainImage(image)}
            className={cn(
              "relative w-24 h-24 rounded-lg overflow-hidden border-2 transition-all",
              mainImage === image
                ? "border-primary ring-2 ring-primary"
                : "border-transparent hover:border-gray-200 dark:hover:border-gray-700",
            )}
          >
            <Image
              src={image || "/placeholder.svg"}
              alt={`${alt} thumbnail ${index + 1}`}
              width={96}
              height={96}
              className="object-cover w-full h-full"
            />
            <span className="sr-only">View image {index + 1}</span>
          </button>
        ))}
      </div>
      <div className="lg:col-span-4 relative aspect-video md:aspect-[16/9] rounded-xl overflow-hidden bg-muted flex items-center justify-center">
        <Image
          src={mainImage || "/placeholder.svg"}
          alt={alt}
          width={800}
          height={600}
          className="object-contain w-full h-full"
          priority
        />
      </div>
    </div>
  )
}
