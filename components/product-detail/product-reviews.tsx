"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Star } from "lucide-react"
import { Separator } from "@/components/ui/separator"

interface Review {
  id: string
  author: string
  rating: number
  date: string
  comment: string
}

interface ProductReviewsProps {
  reviews: Review[]
  averageRating: number
  totalReviews: number
}

export default function ProductReviews({ reviews, averageRating, totalReviews }: ProductReviewsProps) {
  return (
    <section className="py-12 md:py-16">
      <div className="container px-4 md:px-6">
        <h2 className="text-3xl font-bold tracking-tight mb-8 text-center">Customer Reviews</h2>
        <div className="flex flex-col md:flex-row items-center justify-center gap-4 mb-8">
          <div className="flex items-center gap-2 text-4xl font-bold text-primary">
            {averageRating.toFixed(1)}
            <Star className="h-8 w-8 fill-current text-yellow-500" />
          </div>
          <span className="text-lg text-muted-foreground">({totalReviews} reviews)</span>
        </div>
        <Separator className="mb-8" />
        <div className="grid gap-8">
          {reviews.map((review) => (
            <Card key={review.id} className="shadow-sm hover:shadow-md transition-shadow rounded-xl">
              <CardHeader className="flex flex-row items-center gap-4 pb-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={`/placeholder-user.jpg?name=${review.author}`} alt={review.author} />
                  <AvatarFallback>
                    {review.author
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="grid gap-1">
                  <CardTitle className="text-lg">{review.author}</CardTitle>
                  <CardDescription className="text-sm">{review.date}</CardDescription>
                </div>
                <div className="ml-auto flex items-center gap-1 text-yellow-500">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className={`h-4 w-4 ${i < review.rating ? "fill-current" : "text-gray-300"}`} />
                  ))}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">{review.comment}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
