'use client'

import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  useNotifications, 
  useToast, 
  useSystemNotifications, 
  useBusinessNotifications 
} from '@/components/providers/notification-provider'
import { 
  Bell, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  ShoppingCart, 
  Package, 
  Shield, 
  DollarSign 
} from 'lucide-react'

export function NotificationExamples() {
  const { unreadCount, isConnected, hasPermission, requestPermission } = useNotifications()
  const { showToast, showSuccess, showError, showWarning, showInfo } = useToast()
  const { showSystemAlert, showSecurityAlert } = useSystemNotifications()
  const { showInventoryAlert, showOrderAlert } = useBusinessNotifications()

  const handleBasicToasts = () => {
    showSuccess('Success!', 'Operation completed successfully')
    
    setTimeout(() => {
      showInfo('Information', 'This is an informational message')
    }, 1000)
    
    setTimeout(() => {
      showWarning('Warning', 'Please review this warning message')
    }, 2000)
    
    setTimeout(() => {
      showError('Error', 'Something went wrong')
    }, 3000)
  }

  const handleCustomToast = () => {
    showToast(
      'Custom Notification',
      'This is a custom notification with actions',
      {
        category: 'system',
        priority: 'medium',
        autoHide: false,
        actions: [
          {
            id: 'view',
            label: 'View Details',
            variant: 'primary',
            url: '/dashboard'
          },
          {
            id: 'dismiss',
            label: 'Dismiss',
            variant: 'outline'
          }
        ]
      }
    )
  }

  const handleSystemAlerts = () => {
    showSystemAlert(
      'System Maintenance',
      'Scheduled maintenance will begin in 30 minutes',
      'high'
    )
    
    setTimeout(() => {
      showSecurityAlert(
        'Security Alert',
        'Unusual login activity detected from new location'
      )
    }, 1500)
  }

  const handleBusinessNotifications = () => {
    showInventoryAlert(
      'Low Stock Alert',
      'iPhone 15 Pro is running low in stock (5 units remaining)',
      { productId: 'iphone-15-pro', currentStock: 5, threshold: 10 }
    )
    
    setTimeout(() => {
      showOrderAlert(
        'New Order Received',
        'Order #ORD-2024-001 has been placed',
        'ORD-2024-001'
      )
    }, 1000)
  }

  const handleCriticalAlert = () => {
    showToast(
      'Critical System Alert',
      'Database connection lost. Immediate action required.',
      {
        category: 'system',
        priority: 'critical',
        autoHide: false,
        actions: [
          {
            id: 'troubleshoot',
            label: 'Troubleshoot',
            variant: 'destructive',
            url: '/dashboard/system/troubleshoot'
          },
          {
            id: 'contact-support',
            label: 'Contact Support',
            variant: 'outline',
            action: 'contact-support'
          }
        ]
      }
    )
  }

  const handleBulkNotifications = () => {
    const notifications = [
      { title: 'Order Shipped', message: 'Order #001 has been shipped', category: 'orders' as const },
      { title: 'Payment Received', message: 'Payment for Order #002 received', category: 'financial' as const },
      { title: 'Stock Updated', message: 'Inventory levels updated for 15 products', category: 'inventory' as const },
      { title: 'User Registered', message: 'New customer account created', category: 'users' as const },
      { title: 'Report Ready', message: 'Monthly sales report is ready', category: 'reports' as const }
    ]

    notifications.forEach((notif, index) => {
      setTimeout(() => {
        showToast(notif.title, notif.message, {
          category: notif.category,
          priority: 'low',
          autoHide: true,
          hideDelay: 4000
        })
      }, index * 500)
    })
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification System Examples
          </CardTitle>
          <CardDescription>
            Test different types of notifications and their behaviors
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Status Information */}
          <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            <Separator orientation="vertical" className="h-4" />
            <div className="flex items-center gap-2">
              <Badge variant="secondary">{unreadCount} unread</Badge>
            </div>
            <Separator orientation="vertical" className="h-4" />
            <div className="flex items-center gap-2">
              <span className="text-sm">
                Browser notifications: {hasPermission ? 'Enabled' : 'Disabled'}
              </span>
              {!hasPermission && (
                <Button size="sm" variant="outline" onClick={requestPermission}>
                  Enable
                </Button>
              )}
            </div>
          </div>

          {/* Basic Toast Examples */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Basic Toast Notifications
            </h3>
            <div className="flex flex-wrap gap-2">
              <Button onClick={handleBasicToasts} variant="outline">
                Show Basic Toasts
              </Button>
              <Button onClick={handleCustomToast} variant="outline">
                Custom Toast with Actions
              </Button>
            </div>
          </div>

          <Separator />

          {/* System Notifications */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-500" />
              System Notifications
            </h3>
            <div className="flex flex-wrap gap-2">
              <Button onClick={handleSystemAlerts} variant="outline">
                System & Security Alerts
              </Button>
              <Button onClick={handleCriticalAlert} variant="destructive">
                Critical Alert
              </Button>
            </div>
          </div>

          <Separator />

          {/* Business Notifications */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <ShoppingCart className="h-5 w-5 text-purple-500" />
              Business Notifications
            </h3>
            <div className="flex flex-wrap gap-2">
              <Button onClick={handleBusinessNotifications} variant="outline">
                <Package className="h-4 w-4 mr-2" />
                Inventory & Orders
              </Button>
              <Button onClick={handleBulkNotifications} variant="outline">
                Bulk Notifications
              </Button>
            </div>
          </div>

          <Separator />

          {/* Usage Examples */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Info className="h-5 w-5 text-blue-500" />
              Usage Examples
            </h3>
            <div className="text-sm text-muted-foreground space-y-2">
              <p><strong>Basic Usage:</strong></p>
              <code className="block bg-muted p-2 rounded text-xs">
                {`const { showSuccess, showError } = useToast()
showSuccess('Success!', 'Operation completed')
showError('Error!', 'Something went wrong')`}
              </code>
              
              <p><strong>Custom Notifications:</strong></p>
              <code className="block bg-muted p-2 rounded text-xs">
                {`const { showToast } = useNotifications()
showToast('Title', 'Message', {
  category: 'orders',
  priority: 'high',
  actions: [{ id: 'view', label: 'View', url: '/orders' }]
})`}
              </code>
              
              <p><strong>Business Notifications:</strong></p>
              <code className="block bg-muted p-2 rounded text-xs">
                {`const { showInventoryAlert, showOrderAlert } = useBusinessNotifications()
showInventoryAlert('Low Stock', 'Product running low')
showOrderAlert('New Order', 'Order received', 'ORDER-123')`}
              </code>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
