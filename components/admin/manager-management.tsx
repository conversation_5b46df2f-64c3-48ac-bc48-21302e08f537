'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Plus, 
  Search, 
  Crown, 
  Building2,
  Mail,
  Phone,
  Calendar,
  MoreHorizontal,
  Edit,
  Trash2,
  UserPlus
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useAuthStore } from '@/stores/authStore'
import { useBranchStore } from '@/stores/branchStore'
import AddUserModal from '@/components/modals/add-user-modal'
import AssignBranchManagerModal from '@/components/modals/assign-branch-manager-modal'

interface User {
  id: string
  username: string
  email: string
  name: string
  role: 'overall_admin' | 'branch_manager'
  branchId?: string
  branchName?: string
  isActive: boolean
  lastLogin?: string
  createdAt: string
}

export default function ManagerManagement() {
  const { user: currentUser } = useAuthStore()
  const { branches, fetchBranches } = useBranchStore()
  
  const [users, setUsers] = useState<User[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false)
  const [isAssignManagerModalOpen, setIsAssignManagerModalOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)

  // Fetch users and branches
  useEffect(() => {
    fetchUsers()
    fetchBranches()
  }, [fetchBranches])

  const fetchUsers = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      })
      
      if (response.ok) {
        const result = await response.json()
        console.log('Users API response:', result)
        if (result.success) {
          setUsers(result.data || [])
        }
      } else {
        console.error('Failed to fetch users:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Filter users based on search query
  const filteredUsers = (users || []).filter(user => {
    if (!user) return false
    const query = searchQuery.toLowerCase()
    return (
      user?.name?.toLowerCase().includes(query) ||
      user?.username?.toLowerCase().includes(query) ||
      user?.email?.toLowerCase().includes(query)
    )
  })

  console.log('Users state:', users)
  console.log('Filtered users:', filteredUsers)

  const handleUserCreated = () => {
    fetchUsers()
    setIsAddUserModalOpen(false)
  }

  const handleManagerAssigned = () => {
    fetchUsers()
    fetchBranches()
    setIsAssignManagerModalOpen(false)
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'overall_admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'branch_manager':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  const formatRole = (role: string) => {
    switch (role) {
      case 'overall_admin':
        return 'Overall Admin'
      case 'branch_manager':
        return 'Branch Manager'
      default:
        return role
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin & Manager Accounts</h1>
          <p className="text-muted-foreground">
            Manage administrator and branch manager accounts with system access
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => setIsAddUserModalOpen(true)}
            className="gap-2"
          >
            <UserPlus className="h-4 w-4" />
            Add Admin/Manager
          </Button>
          <Button
            variant="outline"
            onClick={() => setIsAssignManagerModalOpen(true)}
            className="gap-2"
          >
            <Building2 className="h-4 w-4" />
            Assign Branch Manager
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            User Accounts
          </CardTitle>
          <CardDescription>
            Manage administrator and manager accounts with login credentials and system access
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users by name, username, or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          {/* Users List */}
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-muted-foreground mt-2">Loading users...</p>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No accounts found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ? 'No accounts match your search criteria.' : 'No admin or manager accounts have been created yet.'}
              </p>
              <Button onClick={() => setIsAddUserModalOpen(true)} className="gap-2">
                <UserPlus className="h-4 w-4" />
                Add First Admin/Manager
              </Button>
            </div>
          ) : (
            <div className="grid gap-4">
              {filteredUsers.map((user) => (
                <Card key={user?.id || Math.random()} className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                        {user?.role === 'overall_admin' ? (
                          <Crown className="h-5 w-5 text-primary" />
                        ) : (
                          <Building2 className="h-5 w-5 text-primary" />
                        )}
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{user?.name || 'Unknown User'}</h3>
                          <Badge className={getRoleColor(user?.role || '')}>
                            {formatRole(user?.role || '')}
                          </Badge>
                          {!user?.isActive && (
                            <Badge variant="secondary">Inactive</Badge>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground space-y-1">
                          <div className="flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {user?.email || 'No email'}
                          </div>
                          <div>@{user?.username || 'unknown'}</div>
                          {user?.branchName && (
                            <div className="flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {user.branchName}
                            </div>
                          )}
                          {user?.lastLogin && (
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              Last login: {new Date(user.lastLogin).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setSelectedUser(user)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit User
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => {
                            setSelectedUser(user)
                            setIsAssignManagerModalOpen(true)
                          }}
                          disabled={user?.role !== 'branch_manager'}
                        >
                          <Building2 className="h-4 w-4 mr-2" />
                          Assign to Branch
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="h-4 w-4 mr-2" />
                          Deactivate User
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modals */}
      <AddUserModal
        isOpen={isAddUserModalOpen}
        onClose={() => setIsAddUserModalOpen(false)}
        onUserCreated={handleUserCreated}
      />

      <AssignBranchManagerModal
        isOpen={isAssignManagerModalOpen}
        onClose={() => setIsAssignManagerModalOpen(false)}
        onManagerAssigned={handleManagerAssigned}
        selectedUser={selectedUser}
        users={users.filter(u => u.role === 'branch_manager')}
        branches={branches}
      />
    </div>
  )
}
