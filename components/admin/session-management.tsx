'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Shield, 
  AlertTriangle, 
  Users, 
  Activity,
  Search,
  Filter,
  MoreHorizontal,
  Trash2,
  Eye,
  RefreshCw
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Checkbox } from '@/components/ui/checkbox'
import { apiClient } from '@/services/frontend'
import { useAdminSocket } from '@/components/providers/socket-provider'
import { toast } from 'sonner'

// Types
interface SessionData {
  _id: string
  userId: string
  deviceInfo: {
    browser: string
    browserVersion: string
    os: string
    osVersion: string
    device: string
    deviceType: 'desktop' | 'mobile' | 'tablet'
  }
  location: {
    ipAddress: string
    country?: string
    city?: string
  }
  loginTime: string
  lastActivity: string
  expiresAt: string
  isActive: boolean
  logoutTime?: string
  logoutReason?: string
  user: {
    username: string
    email: string
    name: string
    role: string
  }
}

interface SessionStats {
  overview: {
    totalSessions: number
    activeSessions: number
    totalUsers: number
    activeUsers: number
  }
  deviceBreakdown: Array<{ _id: string; count: number }>
  osBreakdown: Array<{ _id: string; count: number }>
  securityAlerts: Array<{
    _id: string
    user: { username: string; name: string; email: string }
    uniqueIPCount: number
    uniqueDeviceCount: number
    sessionCount: number
    riskLevel: 'low' | 'medium' | 'high'
  }>
}

// Device icon component
const DeviceIcon = ({ deviceType }: { deviceType: string }) => {
  switch (deviceType) {
    case 'mobile':
      return <Smartphone className="h-4 w-4" />
    case 'tablet':
      return <Tablet className="h-4 w-4" />
    default:
      return <Monitor className="h-4 w-4" />
  }
}

// Risk level badge component
const RiskBadge = ({ level }: { level: string }) => {
  const variants = {
    low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  
  return (
    <Badge className={variants[level as keyof typeof variants] || variants.low}>
      {level.toUpperCase()}
    </Badge>
  )
}

export function SessionManagement() {
  const [sessions, setSessions] = useState<SessionData[]>([])
  const [stats, setStats] = useState<SessionStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [selectedSessions, setSelectedSessions] = useState<string[]>([])
  const [filters, setFilters] = useState({
    search: '',
    isActive: 'all',
    deviceType: 'all',
    page: 1,
    limit: 20
  })
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    hasNext: false,
    hasPrev: false
  })

  // Socket.IO integration for real-time updates
  const socket = useAdminSocket()

  // Real-time session updates
  useEffect(() => {
    if (socket.authenticated && socket.isAdmin) {
      // Update session stats from real-time data
      if (socket.sessionStats) {
        setStats(prevStats => ({
          ...prevStats,
          totalSessions: socket.sessionStats.totalSessions,
          activeSessions: socket.sessionStats.activeSessions,
          onlineUsers: socket.sessionStats.onlineUsers
        }))
      }

      // Handle real-time session events
      const handleSessionCreated = (data: any) => {
        // Add new session to the list if it matches current filters
        setSessions(prev => [data, ...prev])
        toast.success(`New session created: ${data.userInfo?.username}`)
      }

      const handleSessionTerminated = (data: any) => {
        // Remove terminated session from the list
        setSessions(prev => prev.filter(s => s._id !== data.sessionId))
        setSelectedSessions(prev => prev.filter(id => id !== data.sessionId))
        toast.info(`Session terminated: ${data.reason}`)
      }

      const handleSessionUpdated = (data: any) => {
        // Update session in the list
        setSessions(prev => prev.map(s =>
          s._id === data.sessionId ? { ...s, ...data.updates } : s
        ))
      }

      // Listen for real-time events
      socket.on('sessionCreated', handleSessionCreated)
      socket.on('sessionTerminated', handleSessionTerminated)
      socket.on('sessionUpdated', handleSessionUpdated)

      return () => {
        socket.off('sessionCreated', handleSessionCreated)
        socket.off('sessionTerminated', handleSessionTerminated)
        socket.off('sessionUpdated', handleSessionUpdated)
      }
    }
  }, [socket.authenticated, socket.isAdmin, socket.sessionStats])

  // Load sessions
  const loadSessions = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      if (filters.search) params.append('search', filters.search)
      if (filters.isActive && filters.isActive !== 'all') params.append('isActive', filters.isActive)
      if (filters.deviceType && filters.deviceType !== 'all') params.append('deviceType', filters.deviceType)
      params.append('page', filters.page.toString())
      params.append('limit', filters.limit.toString())

      const response = await apiClient.get(`/api/admin/sessions?${params}`)
      
      if (response.success) {
        setSessions(response.sessions || [])
        setPagination(response.pagination || {})
      }
    } catch (error) {
      console.error('Failed to load sessions:', error)
    } finally {
      setLoading(false)
    }
  }

  // Load statistics
  const loadStats = async () => {
    try {
      const response = await apiClient.get('/api/admin/sessions/stats?timeframe=7d')
      if (response.success) {
        setStats(response.stats)
      }
    } catch (error) {
      console.error('Failed to load stats:', error)
    }
  }

  // Terminate sessions (with Socket.IO real-time updates)
  const terminateSessions = async (sessionIds: string[]) => {
    try {
      // Use Socket.IO for real-time termination if connected
      if (socket.authenticated && socket.isAdmin) {
        for (const sessionId of sessionIds) {
          socket.terminateSession(sessionId, 'admin_forced')
        }
        setSelectedSessions([])
        toast.success(`${sessionIds.length} session(s) terminated`)
      } else {
        // Fallback to API call
        const response = await apiClient.delete('/api/admin/sessions', {
          sessionIds,
          reason: 'admin_forced'
        })

        if (response.success) {
          setSelectedSessions([])
          loadSessions()
          loadStats()
          toast.success(`${sessionIds.length} session(s) terminated`)
        }
      }
    } catch (error) {
      console.error('Failed to terminate sessions:', error)
      toast.error('Failed to terminate sessions')
    }
  }

  // Terminate all user sessions
  const terminateUserSessions = async (userId: string) => {
    try {
      // Get all sessions for this user
      const userSessions = sessions.filter(s => s.userId === userId)

      if (socket.authenticated && socket.isAdmin) {
        for (const session of userSessions) {
          socket.terminateSession(session._id, 'admin_forced_all_user_sessions')
        }
        toast.success(`All sessions for user terminated`)
      } else {
        // Fallback to API call
        const response = await apiClient.delete('/api/admin/sessions', {
          userId,
          reason: 'admin_forced'
        })

        if (response.success) {
          loadSessions()
          loadStats()
          toast.success(`All sessions for user terminated`)
        }
      }
    } catch (error) {
      console.error('Failed to terminate user sessions:', error)
      toast.error('Failed to terminate user sessions')
    }
  }

  useEffect(() => {
    loadSessions()
    loadStats()
  }, [filters])

  const handleSelectSession = (sessionId: string, checked: boolean) => {
    if (checked) {
      setSelectedSessions([...selectedSessions, sessionId])
    } else {
      setSelectedSessions(selectedSessions.filter(id => id !== sessionId))
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSessions(sessions.map(s => s._id))
    } else {
      setSelectedSessions([])
    }
  }

  return (
    <div className="space-y-6">
      {/* Real-time Connection Status */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {socket.connected ? (
                <>
                  <div className="flex items-center gap-2 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    <span className="text-sm font-medium">Real-time Connected</span>
                  </div>
                  {socket.securityAlerts.length > 0 && (
                    <Badge variant="destructive" className="ml-4">
                      {socket.securityAlerts.length} Alert{socket.securityAlerts.length !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </>
              ) : (
                <div className="flex items-center gap-2 text-yellow-600">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                  <span className="text-sm font-medium">Using Polling Mode</span>
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              {socket.sessionStats && (
                <div className="text-sm text-muted-foreground">
                  {socket.sessionStats.onlineUsers} online • {socket.sessionStats.activeSessions} active sessions
                </div>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  loadSessions()
                  loadStats()
                }}
                disabled={loading}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Overview */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.overview.activeSessions}</div>
              <p className="text-xs text-muted-foreground">
                {stats.overview.activeUsers} active users
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Sessions</CardTitle>
              <Monitor className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.overview.totalSessions}</div>
              <p className="text-xs text-muted-foreground">Last 7 days</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.overview.totalUsers}</div>
              <p className="text-xs text-muted-foreground">Registered users</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Security Alerts</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.securityAlerts.length}</div>
              <p className="text-xs text-muted-foreground">Suspicious activity</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Security Alerts */}
      {stats?.securityAlerts && stats.securityAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security Alerts
            </CardTitle>
            <CardDescription>
              Users with suspicious session activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.securityAlerts.map((alert) => (
                <Alert key={alert._id} className="border-amber-200 bg-amber-50 dark:border-amber-800 dark:bg-amber-950">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="flex items-center justify-between">
                    <div>
                      <strong>{alert.user.name}</strong> ({alert.user.username}) - 
                      {alert.sessionCount} sessions, {alert.uniqueIPCount} IPs, {alert.uniqueDeviceCount} devices
                    </div>
                    <div className="flex items-center gap-2">
                      <RiskBadge level={alert.riskLevel} />
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => terminateUserSessions(alert._id)}
                      >
                        Terminate All
                      </Button>
                    </div>
                  </AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Session Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Session Management</CardTitle>
              <CardDescription>
                View and manage user sessions across all devices
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadSessions()}
                disabled={loading}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              {selectedSessions.length > 0 && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => terminateSessions(selectedSessions)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Terminate Selected ({selectedSessions.length})
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex items-center gap-4 mb-4">
            <div className="flex-1">
              <Input
                placeholder="Search by username, email, or IP..."
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value, page: 1 })}
                className="max-w-sm"
              />
            </div>
            <Select
              value={filters.isActive}
              onValueChange={(value) => setFilters({ ...filters, isActive: value === 'all' ? '' : value, page: 1 })}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="true">Active</SelectItem>
                <SelectItem value="false">Inactive</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={filters.deviceType}
              onValueChange={(value) => setFilters({ ...filters, deviceType: value === 'all' ? '' : value, page: 1 })}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Device" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="desktop">Desktop</SelectItem>
                <SelectItem value="mobile">Mobile</SelectItem>
                <SelectItem value="tablet">Tablet</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Sessions Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedSessions.length === sessions.length && sessions.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Device</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Login Time</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sessions.map((session) => (
                  <TableRow key={session._id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedSessions.includes(session._id)}
                        onCheckedChange={(checked) => handleSelectSession(session._id, checked as boolean)}
                      />
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{session.user.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {session.user.username} • {session.user.role}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <DeviceIcon deviceType={session.deviceInfo.deviceType} />
                        <div>
                          <div className="text-sm">
                            {session.deviceInfo.browser} {session.deviceInfo.browserVersion}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {session.deviceInfo.os} {session.deviceInfo.osVersion}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {session.location.ipAddress}
                        {session.location.city && session.location.country && (
                          <div className="text-xs text-muted-foreground">
                            {session.location.city}, {session.location.country}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(session.loginTime).toLocaleString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={session.isActive ? 'default' : 'secondary'}>
                        {session.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => terminateSessions([session._id])}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Terminate Session
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => terminateUserSessions(session.userId)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Terminate All User Sessions
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.currentPage - 1) * filters.limit) + 1} to{' '}
                {Math.min(pagination.currentPage * filters.limit, pagination.totalCount)} of{' '}
                {pagination.totalCount} sessions
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({ ...filters, page: filters.page - 1 })}
                  disabled={!pagination.hasPrev}
                >
                  Previous
                </Button>
                <span className="text-sm">
                  Page {pagination.currentPage} of {pagination.totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setFilters({ ...filters, page: filters.page + 1 })}
                  disabled={!pagination.hasNext}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

// Session Analytics Component
export function SessionAnalytics() {
  const [stats, setStats] = useState<SessionStats | null>(null)
  const [timeframe, setTimeframe] = useState('7d')
  const [loading, setLoading] = useState(false)

  const loadStats = async () => {
    setLoading(true)
    try {
      const response = await apiClient.get(`/api/admin/sessions/stats?timeframe=${timeframe}`)
      if (response.success) {
        setStats(response.stats)
      }
    } catch (error) {
      console.error('Failed to load stats:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadStats()
  }, [timeframe])

  if (!stats) return <div>Loading...</div>

  return (
    <div className="space-y-6">
      {/* Timeframe Selector */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Session Analytics</h2>
        <Select value={timeframe} onValueChange={setTimeframe}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1d">Last 24h</SelectItem>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Device Breakdown */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Device Types</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.deviceBreakdown.map((device) => (
                <div key={device._id} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <DeviceIcon deviceType={device._id} />
                    <span className="capitalize">{device._id || 'Unknown'}</span>
                  </div>
                  <Badge variant="secondary">{device.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Operating Systems</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {stats.osBreakdown.slice(0, 5).map((os) => (
                <div key={os._id} className="flex items-center justify-between">
                  <span>{os._id || 'Unknown'}</span>
                  <Badge variant="secondary">{os.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SessionManagement
