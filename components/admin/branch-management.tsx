'use client'

import { useState, useEffect } from 'react'
import { Plus, Search, Edit, Trash2, MapPin, Phone, Mail, Clock, User } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { useBranchStore } from '@/stores/branchStore'
import { useAuthStore } from '@/stores/authStore'

interface User {
  id: string
  username: string
  email: string
  name: string
  role: 'overall_admin' | 'branch_manager'
  branchId?: string
  isActive: boolean
}

interface Branch {
  _id: string
  name: string
  location: string
  country: string
  region: string
  manager: string
  managerId: string
  status: 'Active' | 'Inactive' | 'Opening Soon' | 'Maintenance'
  address: string
  phone: string
  email: string
  description: string
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  totalProducts: number
  totalSales: number
  createdAt: string
  updatedAt: string
}

interface CreateBranchData {
  name: string
  location: string
  country: string
  region: string
  managerId?: string
  description: string
  address: string
  phone: string
  email: string
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
}

export default function BranchManagement() {
  const { toast } = useToast()
  const { user } = useAuthStore()
  const {
    branches,
    isLoading,
    error,
    fetchBranches,
    createBranch,
    updateBranch,
    deleteBranch
  } = useBranchStore()

  const [searchQuery, setSearchQuery] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null)
  const [users, setUsers] = useState<User[]>([])
  const [usersLoading, setUsersLoading] = useState(false)
  const [formData, setFormData] = useState<CreateBranchData>({
    name: '',
    location: '',
    country: 'Malawi',
    region: '',
    managerId: user?.userId || '', // Default to current user
    description: '',
    address: '',
    phone: '',
    email: '',
    operatingHours: {
      open: '08:00',
      close: '17:00',
      timezone: 'Africa/Blantyre'
    }
  })

  useEffect(() => {
    fetchBranches()
    fetchUsers()
  }, [fetchBranches])

  // Fetch users for manager assignment
  const fetchUsers = async () => {
    try {
      setUsersLoading(true)
      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        }
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setUsers(result.data || [])
        }
      }
    } catch (error) {
      console.error('Error fetching users:', error)
    } finally {
      setUsersLoading(false)
    }
  }

  const filteredBranches = branches.filter(branch =>
    branch.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    branch.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
    branch.region.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleCreateBranch = async () => {
    try {
      const result = await createBranch(formData)
      if (result) {
        toast({
          title: "Success",
          description: "Branch created successfully",
        })
        setIsCreateModalOpen(false)
        resetForm()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create branch",
        variant: "destructive",
      })
    }
  }

  const handleEditBranch = async () => {
    if (!selectedBranch) return
    
    try {
      const result = await updateBranch(selectedBranch._id, formData)
      if (result) {
        toast({
          title: "Success",
          description: "Branch updated successfully",
        })
        setIsEditModalOpen(false)
        setSelectedBranch(null)
        resetForm()
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update branch",
        variant: "destructive",
      })
    }
  }

  const handleDeleteBranch = async (branchId: string) => {
    if (!confirm('Are you sure you want to delete this branch?')) return
    
    try {
      const result = await deleteBranch(branchId)
      if (result) {
        toast({
          title: "Success",
          description: "Branch deleted successfully",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete branch",
        variant: "destructive",
      })
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      location: '',
      country: 'Malawi',
      region: '',
      managerId: '',
      description: '',
      address: '',
      phone: '',
      email: '',
      operatingHours: {
        open: '08:00',
        close: '17:00',
        timezone: 'Africa/Blantyre'
      }
    })
  }

  const openEditModal = (branch: Branch) => {
    setSelectedBranch(branch)
    setFormData({
      name: branch.name,
      location: branch.location,
      country: branch.country,
      region: branch.region,
      managerId: branch.managerId,
      description: branch.description,
      address: branch.address,
      phone: branch.phone,
      email: branch.email,
      operatingHours: branch.operatingHours
    })
    setIsEditModalOpen(true)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800'
      case 'Inactive': return 'bg-red-100 text-red-800'
      case 'Opening Soon': return 'bg-blue-100 text-blue-800'
      case 'Maintenance': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Branch Management</h1>
          <p className="text-muted-foreground">
            Manage your store branches and locations
          </p>
        </div>
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Branch
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Branch</DialogTitle>
              <DialogDescription>
                Add a new branch location to your store network
              </DialogDescription>
            </DialogHeader>
            <BranchForm
              formData={formData}
              setFormData={setFormData}
              onSubmit={handleCreateBranch}
              onCancel={() => setIsCreateModalOpen(false)}
              isLoading={isLoading}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search branches..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* Branches Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredBranches.map((branch) => (
          <Card key={branch._id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{branch.name}</CardTitle>
                  <CardDescription className="flex items-center mt-1">
                    <MapPin className="h-4 w-4 mr-1" />
                    {branch.location}, {branch.region}
                  </CardDescription>
                </div>
                <Badge className={getStatusColor(branch.status)}>
                  {branch.status}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                  {branch.phone}
                </div>
                <div className="flex items-center">
                  <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                  {branch.email}
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                  {branch.operatingHours.open} - {branch.operatingHours.close}
                </div>
              </div>
              
              <div className="flex justify-between items-center mt-4 pt-4 border-t">
                <div className="text-sm text-muted-foreground">
                  Manager: {branch.manager}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openEditModal(branch)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteBranch(branch._id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Branch</DialogTitle>
            <DialogDescription>
              Update branch information
            </DialogDescription>
          </DialogHeader>
          <BranchForm
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleEditBranch}
            onCancel={() => setIsEditModalOpen(false)}
            isLoading={isLoading}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Branch Form Component
interface BranchFormProps {
  formData: CreateBranchData
  setFormData: (data: CreateBranchData) => void
  onSubmit: () => void
  onCancel: () => void
  isLoading: boolean
}

function BranchForm({ formData, setFormData, onSubmit, onCancel, isLoading }: BranchFormProps) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name">Branch Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="Downtown Store"
          />
        </div>
        <div>
          <Label htmlFor="location">Location</Label>
          <Input
            id="location"
            value={formData.location}
            onChange={(e) => setFormData({ ...formData, location: e.target.value })}
            placeholder="City Center"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="country">Country</Label>
          <Select
            value={formData.country}
            onValueChange={(value) => setFormData({ ...formData, country: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Malawi">Malawi</SelectItem>
              <SelectItem value="Zambia">Zambia</SelectItem>
              <SelectItem value="Zimbabwe">Zimbabwe</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="region">Region</Label>
          <Input
            id="region"
            value={formData.region}
            onChange={(e) => setFormData({ ...formData, region: e.target.value })}
            placeholder="Central"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="address">Address</Label>
        <Textarea
          id="address"
          value={formData.address}
          onChange={(e) => setFormData({ ...formData, address: e.target.value })}
          placeholder="Full address"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            value={formData.phone}
            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
            placeholder="+265999123456"
          />
        </div>
        <div>
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
            placeholder="<EMAIL>"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="manager">Branch Manager (Optional)</Label>
        <Select
          value={formData.managerId || 'none'}
          onValueChange={(value) => setFormData({
            ...formData,
            managerId: value === 'none' ? undefined : value
          })}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a manager or leave unassigned" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">No manager assigned</SelectItem>
            {usersLoading ? (
              <div className="p-2 text-sm text-muted-foreground">Loading users...</div>
            ) : (
              users
                .filter(user =>
                  user.isActive &&
                  (user.role === 'branch_manager' || user.role === 'overall_admin') &&
                  !user.branchId // Only show unassigned users
                )
                .map((user) => (
                  <SelectItem key={user.id} value={user.id}>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      <div>
                        <div className="font-medium">{user.name}</div>
                        <div className="text-xs text-muted-foreground">
                          @{user.username} • {user.email} • {user.role === 'overall_admin' ? 'Admin' : 'Branch Manager'}
                        </div>
                      </div>
                    </div>
                  </SelectItem>
                ))
            )}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder="Branch description"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="open">Opening Time</Label>
          <Input
            id="open"
            type="time"
            value={formData.operatingHours.open}
            onChange={(e) => setFormData({
              ...formData,
              operatingHours: { ...formData.operatingHours, open: e.target.value }
            })}
          />
        </div>
        <div>
          <Label htmlFor="close">Closing Time</Label>
          <Input
            id="close"
            type="time"
            value={formData.operatingHours.close}
            onChange={(e) => setFormData({
              ...formData,
              operatingHours: { ...formData.operatingHours, close: e.target.value }
            })}
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2 pt-4">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={onSubmit} disabled={isLoading}>
          {isLoading ? 'Saving...' : 'Save Branch'}
        </Button>
      </div>
    </div>
  )
}
