"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { MessageCircle, Phone, MapPin, Clock, User, Truck, CreditCard, AlertCircle, CheckCircle } from 'lucide-react'
import { useWhatsAppCheckout } from '@/hooks/use-whatsapp-checkout'
import { useCartStore } from '@/stores/cartStore'
import type { CheckoutData } from '@/services/frontend/whatsappService'

interface WhatsAppCheckoutProps {
  onSuccess?: () => void
}

export default function WhatsAppCheckout({ onSuccess }: WhatsAppCheckoutProps) {
  const { items, summary, clearCart } = useCartStore()
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    email: '',
    address: ''
  })
  const [deliveryMethod, setDeliveryMethod] = useState<'pickup' | 'delivery'>('pickup')
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'mobile_money' | 'bank_transfer'>('cash')
  const [notes, setNotes] = useState('')
  const [isSubmitted, setIsSubmitted] = useState(false)

  const { isLoading, error, contact, initiateCheckout, clearError } = useWhatsAppCheckout({
    onSuccess: (whatsappUrl, contactInfo) => {
      console.log('✅ WhatsApp checkout successful:', contactInfo)
      setIsSubmitted(true)
      onSuccess?.()
    },
    onError: (errorMessage) => {
      console.error('❌ WhatsApp checkout failed:', errorMessage)
    }
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    clearError()

    // Validation
    if (!customerInfo.name.trim() || !customerInfo.phone.trim()) {
      return
    }

    // Determine branch ID from cart items (use the first item's branch)
    const branchId = items.length > 0 ? items[0].branchId : undefined

    const checkoutData: CheckoutData = {
      customerInfo,
      items,
      summary,
      deliveryMethod,
      paymentMethod,
      notes: notes.trim() || undefined
    }

    await initiateCheckout(checkoutData, branchId)
  }

  const handleInputChange = (field: keyof typeof customerInfo, value: string) => {
    setCustomerInfo(prev => ({ ...prev, [field]: value }))
  }

  if (isSubmitted) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <CardTitle className="text-2xl text-green-600">Order Sent Successfully!</CardTitle>
          <CardDescription>
            Your order has been sent via WhatsApp. Please check your WhatsApp for confirmation.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {contact && (
            <Alert>
              <Phone className="h-4 w-4" />
              <AlertDescription>
                <strong>Contact Details:</strong><br />
                {contact.type === 'branch' && contact.branchName && (
                  <>Branch: {contact.branchName}<br /></>
                )}
                {contact.type === 'admin' && contact.adminName && (
                  <>Administrator: {contact.adminName}<br /></>
                )}
                Phone: {contact.primaryContact}
                {contact.branchLocation && (
                  <><br />Location: {contact.branchLocation}</>
                )}
              </AlertDescription>
            </Alert>
          )}
          
          <div className="flex gap-3">
            <Button 
              onClick={() => {
                clearCart()
                window.location.href = '/products'
              }}
              className="flex-1"
            >
              Continue Shopping
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setIsSubmitted(false)}
              className="flex-1"
            >
              Place Another Order
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5 text-green-600" />
          WhatsApp Checkout
        </CardTitle>
        <CardDescription>
          Complete your order by sending the details via WhatsApp to our team.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Customer Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <User className="h-5 w-5" />
              Customer Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  value={customerInfo.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter your full name"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={customerInfo.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+265 1 234 567"
                  required
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email Address (Optional)</Label>
              <Input
                id="email"
                type="email"
                value={customerInfo.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Textarea
                id="address"
                value={customerInfo.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Enter your delivery address (required for delivery)"
                rows={3}
              />
            </div>
          </div>

          {/* Delivery Method */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Delivery Method
            </h3>
            
            <RadioGroup value={deliveryMethod} onValueChange={(value: 'pickup' | 'delivery') => setDeliveryMethod(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="pickup" id="pickup" />
                <Label htmlFor="pickup" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Store Pickup (Free)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="delivery" id="delivery" />
                <Label htmlFor="delivery" className="flex items-center gap-2">
                  <Truck className="h-4 w-4" />
                  Home Delivery
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Payment Method */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Method
            </h3>
            
            <RadioGroup value={paymentMethod} onValueChange={(value: 'cash' | 'mobile_money' | 'bank_transfer') => setPaymentMethod(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="cash" id="cash" />
                <Label htmlFor="cash">Cash on Delivery/Pickup</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="mobile_money" id="mobile_money" />
                <Label htmlFor="mobile_money">Mobile Money</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="bank_transfer" id="bank_transfer" />
                <Label htmlFor="bank_transfer">Bank Transfer</Label>
              </div>
            </RadioGroup>
          </div>

          {/* Additional Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Additional Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Any special instructions or requests..."
              rows={3}
            />
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Submit Button */}
          <Button 
            type="submit" 
            className="w-full bg-green-600 hover:bg-green-700 text-white"
            disabled={isLoading || !customerInfo.name.trim() || !customerInfo.phone.trim()}
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Preparing WhatsApp Message...
              </>
            ) : (
              <>
                <MessageCircle className="h-4 w-4 mr-2" />
                Send Order via WhatsApp
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
