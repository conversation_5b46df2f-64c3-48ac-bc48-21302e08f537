"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CreditCard, MapPin, Package } from "lucide-react"
import { cn } from "@/lib/utils"
import { useRouter } from "next/navigation"

interface CheckoutFormProps {
  onOrderComplete: () => void
}

export default function CheckoutForm({ onOrderComplete }: CheckoutFormProps) {
  const [step, setStep] = useState(1) // 1: Shipping, 2: Payment, 3: Review
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    address: "",
    city: "",
    country: "",
    zip: "",
    phone: "",
    email: "",
    paymentMethod: "credit-card",
    cardNumber: "",
    cardName: "",
    cardExpiry: "",
    cardCvc: "",
  })
  const [errors, setErrors] = useState<Record<string, string>>({})
  const router = useRouter()

  const validateStep = () => {
    const currentErrors: Record<string, string> = {}
    let isValid = true

    if (step === 1) {
      if (!formData.firstName) currentErrors.firstName = "First name is required."
      if (!formData.lastName) currentErrors.lastName = "Last name is required."
      if (!formData.address) currentErrors.address = "Address is required."
      if (!formData.city) currentErrors.city = "City is required."
      if (!formData.country) currentErrors.country = "Country is required."
      if (!formData.zip) currentErrors.zip = "Zip code is required."
      if (!formData.phone) currentErrors.phone = "Phone number is required."
      if (!formData.email) {
        currentErrors.email = "Email is required."
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        currentErrors.email = "Email is invalid."
      }
    } else if (step === 2) {
      if (formData.paymentMethod === "credit-card") {
        if (!formData.cardNumber) currentErrors.cardNumber = "Card number is required."
        if (!formData.cardName) currentErrors.cardName = "Name on card is required."
        if (!formData.cardExpiry) currentErrors.cardExpiry = "Expiry date is required."
        if (!formData.cardCvc) currentErrors.cardCvc = "CVC is required."
      }
      // Add validation for other payment methods if they exist
    }

    setErrors(currentErrors)
    isValid = Object.keys(currentErrors).length === 0
    return isValid
  }

  const handleNext = () => {
    if (validateStep()) {
      setStep((prev) => prev + 1)
    }
  }

  const handleBack = () => {
    setStep((prev) => prev - 1)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
    setErrors((prev) => ({ ...prev, [name]: "" })) // Clear error on change
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateStep()) {
      // Simulate order processing
      console.log("Order submitted:", formData)
      onOrderComplete()
      router.push("/checkout/success")
    }
  }

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <div className="grid gap-6">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <MapPin className="h-5 w-5 text-primary" /> Shipping Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input id="firstName" name="firstName" value={formData.firstName} onChange={handleChange} required />
                {errors.firstName && <p className="text-red-500 text-sm">{errors.firstName}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input id="lastName" name="lastName" value={formData.lastName} onChange={handleChange} required />
                {errors.lastName && <p className="text-red-500 text-sm">{errors.lastName}</p>}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input id="address" name="address" value={formData.address} onChange={handleChange} required />
              {errors.address && <p className="text-red-500 text-sm">{errors.address}</p>}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input id="city" name="city" value={formData.city} onChange={handleChange} required />
                {errors.city && <p className="text-red-500 text-sm">{errors.city}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Input id="country" name="country" value={formData.country} onChange={handleChange} required />
                {errors.country && <p className="text-red-500 text-sm">{errors.country}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="zip">Zip Code</Label>
                <Input id="zip" name="zip" value={formData.zip} onChange={handleChange} required />
                {errors.zip && <p className="text-red-500 text-sm">{errors.zip}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input id="phone" name="phone" value={formData.phone} onChange={handleChange} required />
                {errors.phone && <p className="text-red-500 text-sm">{errors.phone}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input id="email" name="email" type="email" value={formData.email} onChange={handleChange} required />
                {errors.email && <p className="text-red-500 text-sm">{errors.email}</p>}
              </div>
            </div>
          </div>
        )
      case 2:
        return (
          <div className="grid gap-6">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-primary" /> Payment Information
            </h3>
            <RadioGroup
              defaultValue="credit-card"
              value={formData.paymentMethod}
              onValueChange={(value) => setFormData((prev) => ({ ...prev, paymentMethod: value }))}
              className="grid gap-4"
            >
              <Label
                htmlFor="credit-card"
                className="flex items-center gap-3 rounded-lg border p-4 cursor-pointer has-[:checked]:border-primary has-[:checked]:ring-2 has-[:checked]:ring-primary transition-all"
              >
                <RadioGroupItem id="credit-card" value="credit-card" className="peer sr-only" />
                <div className="flex items-center gap-3">
                  <CreditCard className="h-6 w-6 text-muted-foreground peer-checked:text-primary" />
                  <span className="font-medium">Credit Card</span>
                </div>
              </Label>
              {/* Add other payment methods here if needed */}
            </RadioGroup>

            {formData.paymentMethod === "credit-card" && (
              <div className="grid gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cardNumber">Card Number</Label>
                  <Input
                    id="cardNumber"
                    name="cardNumber"
                    value={formData.cardNumber}
                    onChange={handleChange}
                    placeholder="XXXX XXXX XXXX XXXX"
                    required
                  />
                  {errors.cardNumber && <p className="text-red-500 text-sm">{errors.cardNumber}</p>}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cardName">Name on Card</Label>
                  <Input id="cardName" name="cardName" value={formData.cardName} onChange={handleChange} required />
                  {errors.cardName && <p className="text-red-500 text-sm">{errors.cardName}</p>}
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cardExpiry">Expiry Date (MM/YY)</Label>
                    <Input
                      id="cardExpiry"
                      name="cardExpiry"
                      value={formData.cardExpiry}
                      onChange={handleChange}
                      placeholder="MM/YY"
                      required
                    />
                    {errors.cardExpiry && <p className="text-red-500 text-sm">{errors.cardExpiry}</p>}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cardCvc">CVC</Label>
                    <Input
                      id="cardCvc"
                      name="cardCvc"
                      value={formData.cardCvc}
                      onChange={handleChange}
                      placeholder="XXX"
                      required
                    />
                    {errors.cardCvc && <p className="text-red-500 text-sm">{errors.cardCvc}</p>}
                  </div>
                </div>
              </div>
            )}
          </div>
        )
      case 3:
        return (
          <div className="grid gap-6">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              <Package className="h-5 w-5 text-primary" /> Review Your Order
            </h3>
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>Shipping Details</CardTitle>
              </CardHeader>
              <CardContent className="text-muted-foreground">
                <p>
                  {formData.firstName} {formData.lastName}
                </p>
                <p>{formData.address}</p>
                <p>
                  {formData.city}, {formData.zip}
                </p>
                <p>{formData.country}</p>
                <p>Phone: {formData.phone}</p>
                <p>Email: {formData.email}</p>
              </CardContent>
            </Card>
            <Card className="shadow-sm">
              <CardHeader>
                <CardTitle>Payment Method</CardTitle>
              </CardHeader>
              <CardContent className="text-muted-foreground">
                <p>Credit Card ending in {formData.cardNumber.slice(-4)}</p>
              </CardContent>
            </Card>
            {/* Order Summary will be handled by the parent component */}
          </div>
        )
      default:
        return null
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-lg rounded-xl">
      <CardHeader>
        <CardTitle className="text-3xl font-bold text-center">Checkout</CardTitle>
        <CardDescription className="text-center">Complete your purchase in a few easy steps.</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between mb-8">
          <div className="flex flex-col items-center">
            <div
              className={cn(
                "w-10 h-10 rounded-full flex items-center justify-center font-bold",
                step >= 1 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground",
              )}
            >
              1
            </div>
            <span className="text-sm mt-2">Shipping</span>
          </div>
          <div className="flex-1 border-t-2 border-dashed mt-5 mx-2" />
          <div className="flex flex-col items-center">
            <div
              className={cn(
                "w-10 h-10 rounded-full flex items-center justify-center font-bold",
                step >= 2 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground",
              )}
            >
              2
            </div>
            <span className="text-sm mt-2">Payment</span>
          </div>
          <div className="flex-1 border-t-2 border-dashed mt-5 mx-2" />
          <div className="flex flex-col items-center">
            <div
              className={cn(
                "w-10 h-10 rounded-full flex items-center justify-center font-bold",
                step >= 3 ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground",
              )}
            >
              3
            </div>
            <span className="text-sm mt-2">Review</span>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {renderStepContent()}
          <div className="flex justify-between mt-8">
            {step > 1 && (
              <Button type="button" variant="outline" onClick={handleBack}>
                Back
              </Button>
            )}
            {step < 3 && (
              <Button type="button" onClick={handleNext} className={cn({ "ml-auto": step === 1 })}>
                Next
              </Button>
            )}
            {step === 3 && (
              <Button type="submit" className="ml-auto">
                Place Order
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
