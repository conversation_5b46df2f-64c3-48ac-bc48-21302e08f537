"use client"

import { CheckCircle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"

export default function CheckoutSuccess() {
  return (
    <div className="flex min-h-[calc(100vh-60px)] items-center justify-center bg-gray-50 dark:bg-gray-950 p-4">
      <Card className="w-full max-w-md text-center shadow-xl rounded-xl">
        <CardHeader className="flex flex-col items-center justify-center space-y-4 pt-8">
          <CheckCircle className="h-20 w-20 text-green-500" />
          <CardTitle className="text-4xl font-bold">Order Placed!</CardTitle>
          <CardDescription className="text-lg text-muted-foreground">
            Thank you for your purchase. Your order has been successfully placed and is being processed.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6 pb-8">
          <p className="text-md text-muted-foreground">
            Your order number is: <span className="font-semibold text-primary">#FTH-2025-98765</span>
          </p>
          <div className="flex flex-col gap-4">
            <Button asChild size="lg" className="py-3 text-lg font-semibold shadow-md hover:shadow-lg">
              <Link href="/dashboard">Track Your Order</Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="py-3 text-lg font-semibold shadow-sm hover:shadow-md bg-transparent"
            >
              <Link href="/">Continue Shopping</Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
