"use client"

import { <PERSON>, <PERSON>, Palette } from "lucide-react"
import { useTheme } from "next-themes"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu"
import { useState, useEffect } from "react"

export default function ThemeToggle() {
  const { setTheme, theme } = useTheme()
  const [colorTheme, setColorTheme] = useState("blue")

  useEffect(() => {
    const savedColorTheme = localStorage.getItem("color-theme") || "blue"
    setColorTheme(savedColorTheme)
    applyColorTheme(savedColorTheme)
  }, [])

  const applyColorTheme = (color: string) => {
    const root = document.documentElement
    root.className = root.className.replace(/theme-\w+/g, '')
    root.classList.add(`theme-${color}`)
    localStorage.setItem("color-theme", color)
    setColorTheme(color)
  }

  const colorThemes = [
    { name: "Sky Blue", value: "blue", color: "bg-sky-500" },
    { name: "Dark Green", value: "green", color: "bg-green-700" },
    { name: "Forest Green", value: "forest", color: "bg-emerald-600" },
    { name: "Red", value: "red", color: "bg-red-500" },
  ]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Theme Mode</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => setTheme("light")}>
          <Sun className="mr-2 h-4 w-4" />
          Light
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          <Moon className="mr-2 h-4 w-4" />
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          <Palette className="mr-2 h-4 w-4" />
          System
        </DropdownMenuItem>

        <DropdownMenuSeparator />
        <DropdownMenuLabel>Color Theme</DropdownMenuLabel>
        {colorThemes.map((colorOption) => (
          <DropdownMenuItem
            key={colorOption.value}
            onClick={() => applyColorTheme(colorOption.value)}
            className="flex items-center justify-between"
          >
            <div className="flex items-center">
              <div className={`mr-2 h-4 w-4 rounded-full ${colorOption.color}`} />
              {colorOption.name}
            </div>
            {colorTheme === colorOption.value && (
              <div className="h-2 w-2 rounded-full bg-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
