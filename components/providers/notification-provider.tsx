'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { useNotificationStore } from '@/stores/notificationStore'
import { NotificationToastContainer } from '@/components/notifications/notification-toast'
import type { PlatformNotification, NotificationCategory, NotificationPriority } from '@/types/notifications'

interface NotificationContextType {
  // Toast notifications
  showToast: (
    title: string,
    message: string,
    options?: {
      category?: NotificationCategory
      priority?: NotificationPriority
      autoHide?: boolean
      hideDelay?: number
      actions?: Array<{
        id: string
        label: string
        variant?: 'primary' | 'secondary' | 'destructive' | 'outline'
        url?: string
        action?: string
      }>
    }
  ) => void

  // Success/Error helpers
  showSuccess: (title: string, message?: string) => void
  showError: (title: string, message?: string) => void
  showWarning: (title: string, message?: string) => void
  showInfo: (title: string, message?: string) => void

  // System notifications
  showSystemAlert: (title: string, message: string, priority?: NotificationPriority) => void
  showSecurityAlert: (title: string, message: string) => void
  showInventoryAlert: (title: string, message: string, data?: any) => void
  showOrderAlert: (title: string, message: string, orderId?: string) => void

  // Notification state
  unreadCount: number
  isConnected: boolean
  hasPermission: boolean
  requestPermission: () => Promise<boolean>
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

interface NotificationProviderProps {
  children: React.ReactNode
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const { isAuthenticated, user } = useAuthStore()
  const { 
    unreadCount, 
    isConnected, 
    addNotification, 
    connectSocket, 
    disconnectSocket, 
    fetchNotifications 
  } = useNotificationStore()
  
  const [hasPermission, setHasPermission] = useState(false)

  // Check browser notification permission
  useEffect(() => {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      setHasPermission(Notification.permission === 'granted')
    }
  }, [])

  // Initialize notification system for authenticated users
  useEffect(() => {
    if (isAuthenticated && user) {
      // Fetch initial notifications
      fetchNotifications()
      
      // Connect to real-time notifications
      connectSocket()
      
      // Request browser notification permission
      requestPermission()
      
      // Cleanup on unmount
      return () => {
        disconnectSocket()
      }
    }
  }, [isAuthenticated, user])

  const requestPermission = async (): Promise<boolean> => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return false
    }

    if (Notification.permission === 'granted') {
      setHasPermission(true)
      return true
    }

    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission()
      const granted = permission === 'granted'
      setHasPermission(granted)
      return granted
    }

    return false
  }

  const createNotification = (
    title: string,
    message: string,
    category: NotificationCategory,
    priority: NotificationPriority,
    options?: {
      autoHide?: boolean
      hideDelay?: number
      actions?: Array<{
        id: string
        label: string
        variant?: 'primary' | 'secondary' | 'destructive' | 'outline'
        url?: string
        action?: string
      }>
      data?: any
    }
  ): PlatformNotification => {
    return {
      id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      category,
      type: 'toast',
      priority,
      status: 'unread',
      title,
      message,
      target: { userIds: user ? [user.id] : [] },
      data: options?.data || {},
      actions: options?.actions?.map(action => ({
        ...action,
        type: action.url ? 'link' : 'button'
      })),
      createdAt: new Date(),
      createdBy: user?.id || 'system',
      source: 'app',
      channels: ['in_app'],
      deliveryStatus: { in_app: 'delivered' }
    }
  }

  const showToast = (
    title: string,
    message: string,
    options?: {
      category?: NotificationCategory
      priority?: NotificationPriority
      autoHide?: boolean
      hideDelay?: number
      actions?: Array<{
        id: string
        label: string
        variant?: 'primary' | 'secondary' | 'destructive' | 'outline'
        url?: string
        action?: string
      }>
    }
  ) => {
    const notification = createNotification(
      title,
      message,
      options?.category || 'system',
      options?.priority || 'medium',
      options
    )
    addNotification(notification)
  }

  const showSuccess = (title: string, message = 'Operation completed successfully') => {
    showToast(title, message, {
      category: 'system',
      priority: 'low',
      autoHide: true,
      hideDelay: 3000
    })
  }

  const showError = (title: string, message = 'An error occurred') => {
    showToast(title, message, {
      category: 'system',
      priority: 'high',
      autoHide: false
    })
  }

  const showWarning = (title: string, message = 'Please review this warning') => {
    showToast(title, message, {
      category: 'system',
      priority: 'medium',
      autoHide: true,
      hideDelay: 5000
    })
  }

  const showInfo = (title: string, message = 'Information') => {
    showToast(title, message, {
      category: 'system',
      priority: 'low',
      autoHide: true,
      hideDelay: 4000
    })
  }

  const showSystemAlert = (title: string, message: string, priority: NotificationPriority = 'medium') => {
    showToast(title, message, {
      category: 'system',
      priority,
      autoHide: priority === 'low' || priority === 'medium'
    })
  }

  const showSecurityAlert = (title: string, message: string) => {
    showToast(title, message, {
      category: 'security',
      priority: 'critical',
      autoHide: false,
      actions: [
        {
          id: 'review',
          label: 'Review',
          variant: 'primary',
          url: '/dashboard/security'
        }
      ]
    })
  }

  const showInventoryAlert = (title: string, message: string, data?: any) => {
    showToast(title, message, {
      category: 'inventory',
      priority: 'medium',
      autoHide: true,
      hideDelay: 6000,
      actions: [
        {
          id: 'view-inventory',
          label: 'View Inventory',
          variant: 'outline',
          url: '/dashboard/inventory'
        }
      ]
    })
  }

  const showOrderAlert = (title: string, message: string, orderId?: string) => {
    showToast(title, message, {
      category: 'orders',
      priority: 'medium',
      autoHide: true,
      hideDelay: 5000,
      actions: orderId ? [
        {
          id: 'view-order',
          label: 'View Order',
          variant: 'outline',
          url: `/dashboard/orders/${orderId}`
        }
      ] : undefined
    })
  }

  const contextValue: NotificationContextType = {
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showSystemAlert,
    showSecurityAlert,
    showInventoryAlert,
    showOrderAlert,
    unreadCount,
    isConnected,
    hasPermission,
    requestPermission
  }

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      {/* Global notification toast container */}
      {isAuthenticated && <NotificationToastContainer />}
    </NotificationContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

// Convenience hooks for specific notification types
export function useToast() {
  const { showToast, showSuccess, showError, showWarning, showInfo } = useNotifications()
  return { showToast, showSuccess, showError, showWarning, showInfo }
}

export function useSystemNotifications() {
  const { showSystemAlert, showSecurityAlert } = useNotifications()
  return { showSystemAlert, showSecurityAlert }
}

export function useBusinessNotifications() {
  const { showInventoryAlert, showOrderAlert } = useNotifications()
  return { showInventoryAlert, showOrderAlert }
}
