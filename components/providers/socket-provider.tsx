'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useSocket } from '@/lib/socket/client'
import { useAuthStore } from '@/stores/authStore'
import { toast } from 'sonner'
import { SocketErrorBoundary } from '@/components/error-boundaries/socket-error-boundary'
import { notificationFallback } from '@/lib/fallback/notification-fallback'

interface SocketContextType {
  connected: boolean
  authenticated: boolean
  user: any
  connecting: boolean
  error: string | null
  connect: () => void
  disconnect: () => void
  authenticate: (token: string) => Promise<any>
  joinAdminRoom: () => void
  leaveAdminRoom: () => void
  terminateSession: (sessionId: string, reason: string) => void
  updateStatus: (status: 'online' | 'offline' | 'away') => void
  on: (event: string, listener: (...args: any[]) => void) => void
  off: (event: string, listener?: (...args: any[]) => void) => void
}

const SocketContext = createContext<SocketContextType | null>(null)

export function useSocketContext() {
  const context = useContext(SocketContext)
  if (!context) {
    throw new Error('useSocketContext must be used within a SocketProvider')
  }
  return context
}

interface SocketProviderProps {
  children: React.ReactNode
}

export function SocketProvider({ children }: SocketProviderProps) {
  const socket = useSocket({ autoConnect: false })
  const { user, token } = useAuthStore()
  const [autoAuthAttempted, setAutoAuthAttempted] = useState(false)
  const [socketError, setSocketError] = useState<string | null>(null)
  const [fallbackMode, setFallbackMode] = useState(false)

  // Handle socket errors gracefully
  useEffect(() => {
    const handleSocketError = (error: string) => {
      setSocketError(error)

      // Enable fallback mode for timeout and connection errors
      if (error.includes('timeout') || error.includes('Connection failed')) {
        setFallbackMode(true)
        console.log('Enabling fallback mode due to socket error:', error)
      }
    }

    if (socket.error) {
      handleSocketError(socket.error)
    }

    // Clear error when connection is restored
    if (socket.connected && socketError) {
      setSocketError(null)
      setFallbackMode(false)
    }
  }, [socket.error, socket.connected, socketError])

  // Auto-connect and authenticate when user is logged in
  useEffect(() => {
    if (user && token && !autoAuthAttempted) {
      setAutoAuthAttempted(true)

      try {
        if (!socket.connected) {
          socket.connect()
        }

        // Wait for connection then authenticate
        const authenticateWhenConnected = () => {
          if (socket.connected && !socket.authenticated) {
            socket.authenticate(token).catch((error) => {
              console.error('Socket authentication failed:', error)
              // Don't show error toast for timeout issues in production
              if (!error.message?.includes('timeout')) {
                toast.error('Real-time connection failed')
              }
              setFallbackMode(true)
            })
          }
        }

        if (socket.connected) {
          authenticateWhenConnected()
        } else {
          socket.on('connect', authenticateWhenConnected)
        }

        return () => {
          socket.off('connect', authenticateWhenConnected)
        }
      } catch (error) {
        console.error('Socket initialization error:', error)
        setFallbackMode(true)
      }
    }
  }, [user, token, socket.connected, autoAuthAttempted])

  // Disconnect when user logs out
  useEffect(() => {
    if (!user && socket.connected) {
      socket.disconnect()
      setAutoAuthAttempted(false)
    }
  }, [user, socket.connected])

  // Listen for session termination notifications
  useEffect(() => {
    if (socket.authenticated) {
      const handleSessionTerminated = (data: any) => {
        if (data.userId === user?.id) {
          toast.error(`Session terminated: ${data.reason}`)
          // Optionally redirect to login or show modal
        }
      }

      const handleSecurityAlert = (alert: any) => {
        if (alert.severity === 'high' || alert.severity === 'critical') {
          toast.error(`Security Alert: ${alert.message}`)
        } else {
          toast.warning(`Security Alert: ${alert.message}`)
        }
      }

      const handleAdminNotification = (notification: any) => {
        toast.info(notification.message)
      }

      socket.on('sessionTerminated', handleSessionTerminated)
      socket.on('securityAlert', handleSecurityAlert)
      socket.on('adminNotification', handleAdminNotification)

      return () => {
        socket.off('sessionTerminated', handleSessionTerminated)
        socket.off('securityAlert', handleSecurityAlert)
        socket.off('adminNotification', handleAdminNotification)
      }
    }
  }, [socket.authenticated, user?.id])

  // Update user status based on page visibility
  useEffect(() => {
    if (socket.authenticated) {
      const handleVisibilityChange = () => {
        const status = document.hidden ? 'away' : 'online'
        socket.updateStatus(status)
      }

      document.addEventListener('visibilitychange', handleVisibilityChange)
      
      // Set initial status
      socket.updateStatus('online')

      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange)
        socket.updateStatus('offline')
      }
    }
  }, [socket.authenticated])

  const contextValue: SocketContextType = {
    connected: socket.connected,
    authenticated: socket.authenticated,
    user: socket.user,
    connecting: socket.connecting,
    error: socket.error,
    connect: socket.connect,
    disconnect: socket.disconnect,
    authenticate: socket.authenticate,
    joinAdminRoom: socket.joinAdminRoom,
    leaveAdminRoom: socket.leaveAdminRoom,
    terminateSession: socket.terminateSession,
    updateStatus: socket.updateStatus,
    on: socket.on,
    off: socket.off
  }

  // Start fallback polling if socket fails
  useEffect(() => {
    if (fallbackMode && user) {
      console.log('Starting notification fallback polling')
      notificationFallback.startPolling(30000) // Poll every 30 seconds

      return () => {
        notificationFallback.stopPolling()
      }
    }
  }, [fallbackMode, user])

  return (
    <SocketErrorBoundary>
      <SocketContext.Provider value={contextValue}>
        {children}
      </SocketContext.Provider>
    </SocketErrorBoundary>
  )
}

// Hook for admin-specific socket functionality
export function useAdminSocket() {
  const socket = useSocketContext()
  const { user } = useAuthStore()
  const [sessionStats, setSessionStats] = useState({
    totalSessions: 0,
    activeSessions: 0,
    onlineUsers: 0
  })
  const [securityAlerts, setSecurityAlerts] = useState<any[]>([])
  const [recentSessions, setRecentSessions] = useState<any[]>([])

  useEffect(() => {
    if (socket.authenticated && user?.role === 'overall_admin') {
      // Join admin room
      socket.joinAdminRoom()

      // Listen for admin events
      const handleSessionCreated = (data: any) => {
        setRecentSessions(prev => [data, ...prev.slice(0, 9)]) // Keep last 10
        toast.success(`New session created: ${data.userInfo?.username}`)
      }

      const handleSessionTerminated = (data: any) => {
        setRecentSessions(prev => prev.filter(s => s.sessionId !== data.sessionId))
        toast.info(`Session terminated: ${data.reason}`)
      }

      const handleSessionStatsUpdated = (stats: any) => {
        setSessionStats(stats)
      }

      const handleSecurityAlert = (alert: any) => {
        setSecurityAlerts(prev => [alert, ...prev.slice(0, 49)]) // Keep last 50
      }

      const handleUserStatusChanged = (data: any) => {
        // Update user status in UI if needed
        console.log('User status changed:', data)
      }

      socket.on('sessionCreated', handleSessionCreated)
      socket.on('sessionTerminated', handleSessionTerminated)
      socket.on('sessionStatsUpdated', handleSessionStatsUpdated)
      socket.on('securityAlert', handleSecurityAlert)
      socket.on('userStatusChanged', handleUserStatusChanged)

      return () => {
        socket.leaveAdminRoom()
        socket.off('sessionCreated', handleSessionCreated)
        socket.off('sessionTerminated', handleSessionTerminated)
        socket.off('sessionStatsUpdated', handleSessionStatsUpdated)
        socket.off('securityAlert', handleSecurityAlert)
        socket.off('userStatusChanged', handleUserStatusChanged)
      }
    }
  }, [socket.authenticated, user?.role])

  const clearSecurityAlerts = () => {
    setSecurityAlerts([])
  }

  return {
    ...socket,
    sessionStats,
    securityAlerts,
    recentSessions,
    clearSecurityAlerts,
    isAdmin: user?.role === 'overall_admin'
  }
}
