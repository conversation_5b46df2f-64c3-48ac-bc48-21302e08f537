// components/layout/header.tsx
"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect } from "react"
import Image from "next/image"
import { Package2, <PERSON>u, User, <PERSON>, LogOut } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import ThemeToggle from "@/components/theme/theme-toggle"
import { NotificationBell, NotificationBellCompact } from "@/components/notifications/notification-bell"
import { EnhancedCartButton } from "@/components/cart/floating-cart-button"
import { useAuthStore } from "@/stores/authStore"
import { useNotificationStore } from "@/stores/notificationStore"
import { cn } from "@/lib/utils"

export default function Header() {
  const pathname = usePathname()
  const { isAuthenticated, user, logout } = useAuthStore()
  const {
    unreadCount,
    isConnected,
    connectSocket,
    disconnectSocket,
    fetchNotifications
  } = useNotificationStore()

  // Initialize notification system for authenticated users
  useEffect(() => {
    if (isAuthenticated && user) {
      // Add a small delay to ensure auth token is set in API client
      const initializeNotifications = async () => {
        try {
          // Fetch initial notifications
          await fetchNotifications()

          // Connect to real-time notifications
          connectSocket()
        } catch (error) {
          console.error('Failed to initialize notifications:', error)
        }
      }

      // Use setTimeout to ensure auth token is set
      const timeoutId = setTimeout(initializeNotifications, 100)

      // Cleanup on unmount
      return () => {
        clearTimeout(timeoutId)
        disconnectSocket()
      }
    }
  }, [isAuthenticated, user, fetchNotifications, connectSocket, disconnectSocket])

  const isActive = (path: string) => {
    if (path === "/") {
      return pathname === "/"
    }
    return pathname.startsWith(path)
  }

  const handleLogout = async () => {
    try {
      await logout()
      // Redirect to home page after logout
      window.location.href = '/'
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between px-4 md:px-6">
        <Link href="/" className="flex items-center gap-2 font-semibold text-lg">
          <Image
            src="/logoTransparent.png"
            alt="Fathahitech Logo"
            width={32}
            height={32}
            className="h-8 w-auto"
            priority
          />
          <span>Fathahitech</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-6 text-sm font-medium">
          <Link
            href="/"
            className={cn(
              "transition-colors hover:text-primary relative",
              isActive("/")
                ? "text-primary font-semibold after:absolute after:bottom-[-20px] after:left-0 after:right-0 after:h-0.5 after:bg-primary after:rounded-full"
                : "text-muted-foreground"
            )}
          >
            Home
          </Link>
          <Link
            href="/products"
            className={cn(
              "transition-colors hover:text-primary relative",
              isActive("/products")
                ? "text-primary font-semibold after:absolute after:bottom-[-20px] after:left-0 after:right-0 after:h-0.5 after:bg-primary after:rounded-full"
                : "text-muted-foreground"
            )}
          >
            Products
          </Link>
          {isAuthenticated && (
            <Link
              href="/dashboard"
              className={cn(
                "transition-colors hover:text-primary relative",
                isActive("/dashboard")
                  ? "text-primary font-semibold after:absolute after:bottom-[-20px] after:left-0 after:right-0 after:h-0.5 after:bg-primary after:rounded-full"
                  : "text-muted-foreground"
              )}
            >
              Dashboard
            </Link>
          )}
          <Link
            href="#"
            className="text-muted-foreground transition-colors hover:text-primary"
          >
            Contact
          </Link>
        </nav>

        {/* Right side actions */}
        <div className="flex items-center gap-2">
          <ThemeToggle />

          {/* Notifications (for authenticated users) */}
          {isAuthenticated && (
            <div className="relative">
              <NotificationBell
                showBadge={true}
                showSettings={user?.role === 'overall_admin'}
                variant="default"
                className="relative"
              />

              {/* Connection status indicator */}
              {isConnected && (
                <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-green-500 rounded-full border border-background" />
              )}

              {/* Unread count badge */}
              {unreadCount > 0 && (
                <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs bg-red-500 hover:bg-red-600">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </Badge>
              )}
            </div>
          )}

          {/* Shopping Cart */}
          <EnhancedCartButton />

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <User className="h-5 w-5" />
                <span className="sr-only">User menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {!isAuthenticated ? (
                <>
                  <DropdownMenuItem asChild>
                    <Link href="/login">Login</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/register">Register</Link>
                  </DropdownMenuItem>
                </>
              ) : (
                <>
                  {/* User Info */}
                  <div className="px-2 py-1.5">
                    <p className="text-sm font-medium">{user?.name || user?.username}</p>
                    <p className="text-xs text-muted-foreground">{user?.email}</p>
                  </div>
                  <DropdownMenuSeparator />

                  {/* Navigation Items */}
                  <DropdownMenuItem asChild>
                    <Link href="/dashboard">
                      <User className="mr-2 h-4 w-4" />
                      Dashboard
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/orders">
                      <Package2 className="mr-2 h-4 w-4" />
                      My Orders
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account">
                      <User className="mr-2 h-4 w-4" />
                      Account Settings
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator />

                  {/* Logout */}
                  <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Mobile Menu */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-6 w-6" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              {/* Mobile Notifications */}
              {isAuthenticated && (
                <div className="flex items-center justify-between p-4 border-b">
                  <h3 className="font-semibold">Notifications</h3>
                  <NotificationBellCompact
                    showBadge={true}
                    showSettings={false}
                    className="h-8 w-8"
                  />
                </div>
              )}

              <nav className="flex flex-col gap-4 mt-6">
                <Link
                  href="/"
                  className={cn(
                    "transition-colors hover:text-primary px-3 py-2 rounded-md",
                    isActive("/")
                      ? "text-primary font-semibold bg-primary/10"
                      : "text-muted-foreground"
                  )}
                >
                  Home
                </Link>
                <Link
                  href="/products"
                  className={cn(
                    "transition-colors hover:text-primary px-3 py-2 rounded-md",
                    isActive("/products")
                      ? "text-primary font-semibold bg-primary/10"
                      : "text-muted-foreground"
                  )}
                >
                  Products
                </Link>
                {isAuthenticated && (
                  <Link
                    href="/dashboard"
                    className={cn(
                      "transition-colors hover:text-primary px-3 py-2 rounded-md",
                      isActive("/dashboard")
                        ? "text-primary font-semibold bg-primary/10"
                        : "text-muted-foreground"
                    )}
                  >
                    Dashboard
                  </Link>
                )}
                <Link
                  href="#"
                  className="text-muted-foreground transition-colors hover:text-primary px-3 py-2 rounded-md"
                >
                  Contact
                </Link>

                {/* Authentication Section */}
                <div className="border-t pt-4 mt-4">
                  {!isAuthenticated ? (
                    <div className="flex flex-col gap-2">
                      <Link
                        href="/login"
                        className="text-muted-foreground transition-colors hover:text-primary px-3 py-2 rounded-md"
                      >
                        Login
                      </Link>
                      <Link
                        href="/register"
                        className="text-muted-foreground transition-colors hover:text-primary px-3 py-2 rounded-md"
                      >
                        Register
                      </Link>
                    </div>
                  ) : (
                    <div className="flex flex-col gap-2">
                      {/* User Info */}
                      <div className="px-3 py-2">
                        <p className="text-sm font-medium">{user?.name || user?.username}</p>
                        <p className="text-xs text-muted-foreground">{user?.email}</p>
                      </div>

                      {/* Account Links */}
                      <Link
                        href="/orders"
                        className="text-muted-foreground transition-colors hover:text-primary px-3 py-2 rounded-md flex items-center"
                      >
                        <Package2 className="mr-2 h-4 w-4" />
                        My Orders
                      </Link>
                      <Link
                        href="/account"
                        className="text-muted-foreground transition-colors hover:text-primary px-3 py-2 rounded-md flex items-center"
                      >
                        <User className="mr-2 h-4 w-4" />
                        Account Settings
                      </Link>

                      {/* Logout Button */}
                      <button
                        onClick={handleLogout}
                        className="text-red-600 hover:text-red-700 transition-colors px-3 py-2 rounded-md flex items-center text-left"
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        Logout
                      </button>
                    </div>
                  )}
                </div>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
