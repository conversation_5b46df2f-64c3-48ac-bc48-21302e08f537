// public/sw.js - Service Worker for push notifications

const CACHE_NAME = 'fathahitech-notifications-v1'
const urlsToCache = [
  '/',
  '/favicon.ico',
  '/sounds/notification-low.mp3',
  '/sounds/notification-medium.mp3',
  '/sounds/notification-high.mp3',
  '/sounds/notification-urgent.mp3'
]

// Install event
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache')
        return cache.addAll(urlsToCache)
      })
      .catch((error) => {
        console.error('Cache installation failed:', error)
      })
  )
})

// Activate event
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
})

// Fetch event
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request)
      })
  )
})

// Push event - Handle incoming push notifications
self.addEventListener('push', (event) => {
  console.log('Push notification received:', event)
  
  let notificationData = {}
  
  try {
    if (event.data) {
      notificationData = event.data.json()
    }
  } catch (error) {
    console.error('Error parsing push notification data:', error)
    notificationData = {
      title: 'FathahiTech Notification',
      body: 'You have a new notification',
      icon: '/favicon.ico'
    }
  }
  
  const options = {
    body: notificationData.body || notificationData.message || 'You have a new notification',
    icon: notificationData.icon || '/favicon.ico',
    badge: '/badge-icon.png',
    tag: notificationData.tag || 'fathahitech-notification',
    data: notificationData.data || {},
    requireInteraction: notificationData.priority === 'urgent',
    silent: notificationData.priority === 'low',
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/icons/view.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icons/dismiss.png'
      }
    ],
    vibrate: notificationData.priority === 'urgent' ? [200, 100, 200] : [100]
  }
  
  event.waitUntil(
    self.registration.showNotification(
      notificationData.title || 'FathahiTech Notification',
      options
    )
  )
})

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event)
  
  event.notification.close()
  
  const action = event.action
  const notificationData = event.notification.data
  
  if (action === 'dismiss') {
    // Just close the notification
    return
  }
  
  // Default action or 'view' action
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        // Try to focus existing window
        for (const client of clientList) {
          if (client.url.includes(self.location.origin) && 'focus' in client) {
            return client.focus()
          }
        }
        
        // Open new window if no existing window found
        if (clients.openWindow) {
          const targetUrl = notificationData?.url || '/'
          return clients.openWindow(targetUrl)
        }
      })
      .catch((error) => {
        console.error('Error handling notification click:', error)
      })
  )
})

// Notification close event
self.addEventListener('notificationclose', (event) => {
  console.log('Notification closed:', event)
  
  // Track notification dismissal analytics if needed
  const notificationData = event.notification.data
  
  if (notificationData?.trackDismissal) {
    // Send analytics data to server
    fetch('/api/analytics/notification-dismissed', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        notificationId: notificationData.id,
        dismissedAt: new Date().toISOString()
      })
    }).catch((error) => {
      console.error('Error tracking notification dismissal:', error)
    })
  }
})

// Background sync event (for offline functionality)
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag)
  
  if (event.tag === 'notification-sync') {
    event.waitUntil(
      syncNotifications()
    )
  }
})

// Sync notifications when back online
async function syncNotifications() {
  try {
    // Get pending notifications from IndexedDB or localStorage
    const pendingNotifications = await getPendingNotifications()
    
    for (const notification of pendingNotifications) {
      try {
        // Send notification to server
        await fetch('/api/notifications/sync', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(notification)
        })
        
        // Remove from pending list
        await removePendingNotification(notification.id)
      } catch (error) {
        console.error('Error syncing notification:', error)
      }
    }
  } catch (error) {
    console.error('Error during notification sync:', error)
  }
}

// Helper functions for offline storage
async function getPendingNotifications() {
  // Implement IndexedDB or localStorage logic
  // This is a simplified version
  try {
    const stored = localStorage.getItem('pendingNotifications')
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error('Error getting pending notifications:', error)
    return []
  }
}

async function removePendingNotification(notificationId) {
  try {
    const pending = await getPendingNotifications()
    const filtered = pending.filter(n => n.id !== notificationId)
    localStorage.setItem('pendingNotifications', JSON.stringify(filtered))
  } catch (error) {
    console.error('Error removing pending notification:', error)
  }
}

// Message event - Handle messages from main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker received message:', event.data)
  
  const { type, payload } = event.data
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
      
    case 'GET_VERSION':
      event.ports[0].postMessage({ version: CACHE_NAME })
      break
      
    case 'CLEAR_CACHE':
      caches.delete(CACHE_NAME)
        .then(() => {
          event.ports[0].postMessage({ success: true })
        })
        .catch((error) => {
          event.ports[0].postMessage({ success: false, error: error.message })
        })
      break
      
    case 'STORE_NOTIFICATION':
      // Store notification for offline sync
      storePendingNotification(payload)
        .then(() => {
          event.ports[0].postMessage({ success: true })
        })
        .catch((error) => {
          event.ports[0].postMessage({ success: false, error: error.message })
        })
      break
      
    default:
      console.log('Unknown message type:', type)
  }
})

async function storePendingNotification(notification) {
  try {
    const pending = await getPendingNotifications()
    pending.push({
      ...notification,
      id: notification.id || Date.now().toString(),
      timestamp: new Date().toISOString()
    })
    localStorage.setItem('pendingNotifications', JSON.stringify(pending))
  } catch (error) {
    console.error('Error storing pending notification:', error)
    throw error
  }
}

// Error event
self.addEventListener('error', (event) => {
  console.error('Service Worker error:', event.error)
})

// Unhandled rejection event
self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker unhandled rejection:', event.reason)
})

console.log('Service Worker loaded successfully')
