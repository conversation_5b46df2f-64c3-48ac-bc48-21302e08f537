"use client"

import { useState, useEffect, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"
import CheckoutForm from "@/components/checkout/checkout-form"
import OrderSummary from "@/components/checkout/order-summary"
import WhatsAppCheckout from "@/components/checkout/whatsapp-checkout"
import { useCartStore } from "@/stores/cartStore"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { MessageCircle, CreditCard } from "lucide-react"
import Link from "next/link"

function CheckoutPageContent() {
  const { items, summary, isEmpty } = useCartStore()
  const searchParams = useSearchParams()
  const [checkoutMethod, setCheckoutMethod] = useState<'traditional' | 'whatsapp'>('whatsapp')

  // Set initial checkout method based on URL parameter
  useEffect(() => {
    const method = searchParams.get('method')
    if (method === 'whatsapp') {
      setCheckoutMethod('whatsapp')
    } else if (method === 'traditional') {
      setCheckoutMethod('traditional')
    }
  }, [searchParams])

  const handleOrderComplete = () => {
    // In a real app, this would trigger a backend order creation
    console.log("Order completed, redirecting to success page...")
    // The CheckoutForm already handles the router.push("/checkout/success")
  }

  const handleWhatsAppSuccess = () => {
    console.log("WhatsApp order sent successfully")
    // Could redirect to a success page or show a success message
  }

  // If cart is empty, show empty state
  if (isEmpty) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 py-12 md:py-20 bg-gray-50 dark:bg-gray-950">
          <div className="container px-4 md:px-6">
            <div className="max-w-md mx-auto text-center space-y-4">
              <h1 className="text-2xl font-bold">Your cart is empty</h1>
              <p className="text-muted-foreground">
                Add some products to your cart before proceeding to checkout.
              </p>
              <Button asChild>
                <Link href="/products">
                  Browse Products
                </Link>
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 py-12 md:py-20 bg-gray-50 dark:bg-gray-950">
        <div className="container px-4 md:px-6">
          <div className="max-w-6xl mx-auto">
            <div className="mb-8 text-center">
              <h1 className="text-3xl font-bold mb-2">Checkout</h1>
              <p className="text-muted-foreground">
                Choose your preferred checkout method to complete your order
              </p>
            </div>

            <Tabs value={checkoutMethod} onValueChange={(value) => setCheckoutMethod(value as 'traditional' | 'whatsapp')} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-8">
                <TabsTrigger value="whatsapp" className="flex items-center gap-2">
                  <MessageCircle className="h-4 w-4" />
                  WhatsApp Checkout
                </TabsTrigger>
                <TabsTrigger value="traditional" className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Traditional Checkout
                </TabsTrigger>
              </TabsList>

              <TabsContent value="whatsapp" className="space-y-8">
                <div className="grid lg:grid-cols-2 gap-12">
                  <WhatsAppCheckout onSuccess={handleWhatsAppSuccess} />
                  <OrderSummary
                    items={items.map(item => ({
                      id: item.id,
                      name: item.name,
                      price: item.price,
                      quantity: item.quantity,
                      image: item.image || "/images/placeholder-product.png"
                    }))}
                    subtotal={summary.subtotal}
                    shipping={summary.shipping}
                    tax={summary.tax}
                    total={summary.total}
                  />
                </div>
              </TabsContent>

              <TabsContent value="traditional" className="space-y-8">
                <div className="grid lg:grid-cols-2 gap-12">
                  <CheckoutForm onOrderComplete={handleOrderComplete} />
                  <OrderSummary
                    items={items.map(item => ({
                      id: item.id,
                      name: item.name,
                      price: item.price,
                      quantity: item.quantity,
                      image: item.image || "/images/placeholder-product.png"
                    }))}
                    subtotal={summary.subtotal}
                    shipping={summary.shipping}
                    tax={summary.tax}
                    total={summary.total}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}

export default function CheckoutPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 py-12 md:py-20 bg-gray-50 dark:bg-gray-950">
          <div className="container px-4 md:px-6">
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                <p className="text-muted-foreground">Loading checkout...</p>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    }>
      <CheckoutPageContent />
    </Suspense>
  )
}
