// app/(dashboard)/sessions/page.tsx - Admin Session Management Page

'use client'

import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import SessionManagement, { SessionAnalytics } from '@/components/admin/session-management'
import { Shield, Activity, BarChart3 } from 'lucide-react'

export default function AdminSessionsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-3">
        <Shield className="h-8 w-8 text-primary" />
        <div>
          <h1 className="text-3xl font-bold">Session Management</h1>
          <p className="text-muted-foreground">
            Monitor and manage user sessions across all devices and platforms
          </p>
        </div>
      </div>

      {/* Session Management Tabs */}
      <Tabs defaultValue="sessions" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="sessions" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Active Sessions
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Session Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sessions" className="space-y-6">
          <SessionManagement />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <SessionAnalytics />
        </TabsContent>
      </Tabs>
    </div>
  )
}
