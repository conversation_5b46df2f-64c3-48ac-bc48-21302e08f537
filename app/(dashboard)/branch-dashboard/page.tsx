// app/admin/branches/[branchId]/dashboard/page.tsx - Branch Dashboard Page

'use client'

import React, { useEffect, useState } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { useBranchStore } from '@/stores/branchStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Users,
  Store,
  ShoppingCart,
  TrendingUp,
  LogOut,
  BarChart3,
  Package,
  MapPin,
  AlertCircle
} from 'lucide-react'

interface BranchStats {
  dailySales: number
  dailySalesChange: number
  ordersToday: number
  ordersChange: number
  totalProducts: number
  lowStockItems: number
  totalCustomers: number
  newCustomersWeek: number
}

interface BranchData {
  _id: string
  name: string
  location: string
  country: string
  region: string
  manager: string
  status: string
  address: string
  phone: string
  email: string
  totalProducts: number
  totalSales: number
}

export default function BranchDashboard() {
  const { user, logout } = useAuthStore()
  const { branches, selectedBranch, fetchBranchById, isLoading, error } = useBranchStore()

  const [branchData, setBranchData] = useState<BranchData | null>(null)
  const [branchStats, setBranchStats] = useState<BranchStats | null>(null)
  const [statsLoading, setStatsLoading] = useState(true)

  const handleLogout = async () => {
    await logout()
    // Redirect to home page after logout
    window.location.href = "/"
  }

  // Get branch ID from user data
  const branchId = user?.branchId

  // Fetch branch data and stats
  useEffect(() => {
    const loadBranchData = async () => {
      if (!branchId) return

      try {
        // Fetch branch details
        const branch = await fetchBranchById(branchId)
        if (branch) {
          setBranchData(branch)
        }

        // Fetch branch statistics (mock for now, replace with real API)
        setStatsLoading(true)
        // Simulate API call delay
        setTimeout(() => {
          setBranchStats({
            dailySales: Math.floor(Math.random() * 5000) + 1000,
            dailySalesChange: Math.floor(Math.random() * 30) - 10,
            ordersToday: Math.floor(Math.random() * 50) + 10,
            ordersChange: Math.floor(Math.random() * 20) - 5,
            totalProducts: branch?.totalProducts || Math.floor(Math.random() * 200) + 50,
            lowStockItems: Math.floor(Math.random() * 20) + 5,
            totalCustomers: Math.floor(Math.random() * 500) + 100,
            newCustomersWeek: Math.floor(Math.random() * 20) + 2
          })
          setStatsLoading(false)
        }, 1000)
      } catch (error) {
        console.error('Failed to load branch data:', error)
        setStatsLoading(false)
      }
    }

    loadBranchData()
  }, [branchId, fetchBranchById])

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Loading...</h1>
        </div>
      </div>
    )
  }

  if (!branchId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">No Branch Assigned</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Please contact your administrator to assign you to a branch.
          </p>
        </div>
      </div>
    )
  }

  const branchName = branchData?.name || selectedBranch?.name || `Branch ${branchId}`

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {branchName}
              </h1>
              <Badge variant="secondary" className="ml-3">
                Branch Manager
              </Badge>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Welcome, {user.name}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Branch Dashboard
          </h2>
          {isLoading ? (
            <div className="mt-2 flex items-center">
              <Skeleton className="h-4 w-4 mr-1" />
              <Skeleton className="h-4 w-48" />
            </div>
          ) : error ? (
            <div className="mt-2 flex items-center text-red-600">
              <AlertCircle className="h-4 w-4 mr-1" />
              <span>Error loading branch data: {error}</span>
            </div>
          ) : (
            <div className="mt-2">
              <p className="text-gray-600 dark:text-gray-400 flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                Manage operations for {branchName}
              </p>
              {branchData && (
                <div className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  <span>{branchData.address}</span>
                  {branchData.phone && (
                    <span className="ml-4">📞 {branchData.phone}</span>
                  )}
                  {branchData.email && (
                    <span className="ml-4">✉️ {branchData.email}</span>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Daily Sales</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {statsLoading ? (
                <Skeleton className="h-8 w-24 mb-2" />
              ) : (
                <div className="text-2xl font-bold">
                  ${branchStats?.dailySales.toLocaleString() || '0'}
                </div>
              )}
              {statsLoading ? (
                <Skeleton className="h-4 w-32" />
              ) : (
                <p className={`text-xs ${
                  (branchStats?.dailySalesChange || 0) >= 0
                    ? 'text-green-600'
                    : 'text-red-600'
                }`}>
                  {(branchStats?.dailySalesChange || 0) >= 0 ? '+' : ''}
                  {branchStats?.dailySalesChange || 0}% from yesterday
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Orders Today</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {statsLoading ? (
                <Skeleton className="h-8 w-16 mb-2" />
              ) : (
                <div className="text-2xl font-bold">{branchStats?.ordersToday || 0}</div>
              )}
              {statsLoading ? (
                <Skeleton className="h-4 w-28" />
              ) : (
                <p className={`text-xs ${
                  (branchStats?.ordersChange || 0) >= 0
                    ? 'text-green-600'
                    : 'text-red-600'
                }`}>
                  {(branchStats?.ordersChange || 0) >= 0 ? '+' : ''}
                  {branchStats?.ordersChange || 0} from yesterday
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {statsLoading ? (
                <Skeleton className="h-8 w-20 mb-2" />
              ) : (
                <div className="text-2xl font-bold">{branchStats?.totalProducts || 0}</div>
              )}
              {statsLoading ? (
                <Skeleton className="h-4 w-32" />
              ) : (
                <p className="text-xs text-muted-foreground">
                  {branchStats?.lowStockItems || 0} low stock items
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {statsLoading ? (
                <Skeleton className="h-8 w-16 mb-2" />
              ) : (
                <div className="text-2xl font-bold">{branchStats?.totalCustomers || 0}</div>
              )}
              {statsLoading ? (
                <Skeleton className="h-4 w-28" />
              ) : (
                <p className="text-xs text-muted-foreground">
                  +{branchStats?.newCustomersWeek || 0} new this week
                </p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ShoppingCart className="h-5 w-5" />
                <span>Process Orders</span>
              </CardTitle>
              <CardDescription>
                View and process pending orders
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                View Orders
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>Inventory Management</span>
              </CardTitle>
              <CardDescription>
                Manage branch inventory and stock
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                Manage Inventory
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Customer Management</span>
              </CardTitle>
              <CardDescription>
                View and manage branch customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                Manage Customers
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Branch Reports</span>
              </CardTitle>
              <CardDescription>
                View branch performance reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                View Reports
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Store className="h-5 w-5" />
                <span>Branch Settings</span>
              </CardTitle>
              <CardDescription>
                Configure branch-specific settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                Branch Settings
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
