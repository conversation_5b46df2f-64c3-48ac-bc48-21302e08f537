import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { DollarSign, TrendingUp, ShoppingCart, Calendar, Download, Filter } from "lucide-react"
import { getCurrentUser } from "@/lib/auth"

export default async function SalesPage() {
  const currentUser = await getCurrentUser()

  if (!currentUser) {
    return null
  }

  // Mock sales data
  const salesData = [
    {
      id: "sale-001",
      orderNumber: "ORD-2024-001",
      customer: "John Doe",
      products: "Gaming Laptop Pro",
      amount: 1299.99,
      status: "Completed",
      branch: "Blantyre",
      date: "2024-01-15",
      paymentMethod: "Credit Card"
    },
    {
      id: "sale-002", 
      orderNumber: "ORD-2024-002",
      customer: "<PERSON>",
      products: "4K Smart TV",
      amount: 899.99,
      status: "Processing",
      branch: "Lilongwe",
      date: "2024-01-14",
      paymentMethod: "Mobile Money"
    },
    {
      id: "sale-003",
      orderNumber: "ORD-2024-003", 
      customer: "Mike Johnson",
      products: "Desktop Workstation",
      amount: 1599.99,
      status: "Shipped",
      branch: "Mzuzu",
      date: "2024-01-13",
      paymentMethod: "Bank Transfer"
    },
    {
      id: "sale-004",
      orderNumber: "ORD-2024-004",
      customer: "Sarah Wilson",
      products: "Network Router Pro",
      amount: 299.99,
      status: "Completed",
      branch: "Lusaka",
      date: "2024-01-12",
      paymentMethod: "Cash"
    }
  ]

  // Filter sales by branch for branch managers
  const filteredSales = currentUser.role === "branch_manager" && currentUser.branchId
    ? salesData.filter(sale => sale.branch.toLowerCase().includes(currentUser.branchId!.split('-')[1]))
    : salesData

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed": return "bg-green-100 text-green-800"
      case "Processing": return "bg-yellow-100 text-yellow-800"
      case "Shipped": return "bg-blue-100 text-blue-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="font-semibold text-lg md:text-2xl">Sales Management</h1>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-3.5 w-3.5 mr-1" /> Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-3.5 w-3.5 mr-1" /> Export
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales Today</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$2,199.98</div>
            <p className="text-xs text-muted-foreground">+15% from yesterday</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders Today</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">+2 from yesterday</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Order Value</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$749.99</div>
            <p className="text-xs text-muted-foreground">+8% from last week</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$45,231.89</div>
            <p className="text-xs text-muted-foreground">+20.1% from last month</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Sales</CardTitle>
          <CardDescription>
            {currentUser.role === "branch_manager" 
              ? `Sales for your branch` 
              : "Sales across all branches"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order #</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Products</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Branch</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Payment</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSales.map((sale) => (
                <TableRow key={sale.id}>
                  <TableCell className="font-medium">{sale.orderNumber}</TableCell>
                  <TableCell>{sale.customer}</TableCell>
                  <TableCell>{sale.products}</TableCell>
                  <TableCell>${sale.amount.toFixed(2)}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(sale.status)}>
                      {sale.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{sale.branch}</TableCell>
                  <TableCell>{sale.date}</TableCell>
                  <TableCell>{sale.paymentMethod}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </>
  )
}
