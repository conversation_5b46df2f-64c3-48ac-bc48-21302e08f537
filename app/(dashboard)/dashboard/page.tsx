"use client"

import { useState, useEffect } from "react"
import { CardDescription } from "@/components/ui/card"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Package,
  Store,
  ShoppingCart,
  Truck,
  DollarSign,
  TrendingUp,
  PlusCircle,
  ListOrdered,
  Warehouse,
  Loader2,
  AlertCircle,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import BarChart from "@/components/charts/bar-chart"
import { useAuthStore } from "@/stores/authStore"
import { useDashboardMetrics } from "@/hooks/use-dashboard-metrics"
import { useActivityLogs } from "@/hooks/use-activity-logs"
import { useCategoryStats } from "@/hooks/use-category-stats"
import { formatDistanceToNow } from "date-fns"
import { useAdminSocket } from "@/components/providers/socket-provider"
import { toast } from "sonner"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import {
  Users,
  Settings,
  Shield,
  Activity,
  Monitor,
} from "lucide-react"

export default function DashboardOverviewPage() {
  const { user: currentUser } = useAuthStore()
  const socket = useAdminSocket()
  const [sessionStats, setSessionStats] = useState<any>(null)

  // Determine branch ID for filtering data
  const branchId = currentUser?.role === "branch_manager" ? currentUser.branchId : undefined

  // Check if user is admin (overall_admin)
  const isAdmin = currentUser?.role === "overall_admin"

  // Fetch dashboard metrics and activity logs
  const {
    data: dashboardData,
    isLoading: metricsLoading,
    error: metricsError,
    refetch: refetchMetrics
  } = useDashboardMetrics({
    branchId,
    period: 30,
    autoFetch: true,
    refreshInterval: 5 * 60 * 1000 // Refresh every 5 minutes
  })

  const {
    data: activityLogs,
    isLoading: activitiesLoading,
    error: activitiesError,
    refetch: refetchActivities
  } = useActivityLogs({
    limit: 10,
    branchId,
    autoFetch: true,
    refreshInterval: 2 * 60 * 1000 // Refresh every 2 minutes
  })

  const {
    data: categoryStats,
    isLoading: categoryStatsLoading,
    error: categoryStatsError,
    refetch: refetchCategoryStats
  } = useCategoryStats({
    branchId,
    period: 30,
    autoFetch: true,
    refreshInterval: 10 * 60 * 1000 // Refresh every 10 minutes
  })

  // Load session statistics for admin users
  useEffect(() => {
    if (isAdmin) {
      const loadSessionStats = async () => {
        try {
          const response = await fetch('/api/admin/sessions/stats?timeframe=1d')
          const data = await response.json()
          if (data.success) {
            setSessionStats(data.stats)
          }
        } catch (error) {
          console.error('Failed to load session stats:', error)
        }
      }
      loadSessionStats()
    }
  }, [isAdmin])

  // Real-time session statistics updates for admin users
  useEffect(() => {
    if (isAdmin && socket.authenticated && socket.isAdmin) {
      if (socket.sessionStats) {
        setSessionStats(prevStats => ({
          ...prevStats,
          overview: {
            ...prevStats?.overview,
            activeSessions: socket.sessionStats.activeSessions,
            onlineUsers: socket.sessionStats.onlineUsers
          }
        }))
      }
    }
  }, [isAdmin, socket.authenticated, socket.isAdmin, socket.sessionStats])

  // Real-time notifications for admin security alerts
  useEffect(() => {
    if (isAdmin && socket.authenticated && socket.isAdmin) {
      if (socket.securityAlerts.length > 0) {
        const latestAlert = socket.securityAlerts[0]
        if (latestAlert.severity === 'high' || latestAlert.severity === 'critical') {
          toast.error(`Security Alert: ${latestAlert.message}`)
        }
      }
    }
  }, [isAdmin, socket.securityAlerts])

  // User validation is now handled in the layout
  if (!currentUser) {
    return null
  }

  // Show error state if critical data failed to load
  const hasErrors = metricsError && activitiesError

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <h1 className="font-semibold text-lg md:text-2xl">
            {isAdmin ? "Admin Dashboard" : "Branch Dashboard"}
          </h1>
          <Badge variant="secondary">
            {currentUser?.role === 'overall_admin' ? 'Overall Admin' :
             currentUser?.role === 'branch_manager' ? 'Branch Manager' : 'User'}
          </Badge>
          {(metricsLoading || activitiesLoading || categoryStatsLoading) && (
            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
          )}
        </div>
        <div className="flex gap-2">
          <Button size="sm" variant="outline">
            <PlusCircle className="h-3.5 w-3.5 mr-1" /> Add New Product
          </Button>
          <Button size="sm">
            <ListOrdered className="h-3.5 w-3.5 mr-1" /> View All Orders
          </Button>
          {hasErrors && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                refetchMetrics()
                refetchActivities()
                refetchCategoryStats()
              }}
            >
              <AlertCircle className="h-3.5 w-3.5 mr-1" /> Retry
            </Button>
          )}
        </div>
      </div>

      {/* Global error alert */}
      {hasErrors && (
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load dashboard data. Please check your connection and try again.
          </AlertDescription>
        </Alert>
      )}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* Total Products Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-4 w-32" />
              </div>
            ) : metricsError ? (
              <div className="text-2xl font-bold text-muted-foreground">--</div>
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {dashboardData?.overview.totalProducts?.toLocaleString() || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {currentUser?.role === "branch_manager" ? "in your branch" : "across all branches"}
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Total Orders Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ListOrdered className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-4 w-32" />
              </div>
            ) : metricsError ? (
              <div className="text-2xl font-bold text-muted-foreground">--</div>
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {dashboardData?.overview.totalOrders?.toLocaleString() || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.overview.ordersGrowth !== undefined && (
                    <span className={dashboardData.overview.ordersGrowth >= 0 ? "text-green-600" : "text-red-600"}>
                      {dashboardData.overview.ordersGrowth >= 0 ? "+" : ""}{dashboardData.overview.ordersGrowth.toFixed(1)}% from last month
                    </span>
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Total Sales Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-24" />
                <Skeleton className="h-4 w-32" />
              </div>
            ) : metricsError ? (
              <div className="text-2xl font-bold text-muted-foreground">--</div>
            ) : (
              <>
                <div className="text-2xl font-bold">
                  ${dashboardData?.overview.totalSales?.toLocaleString() || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.overview.salesGrowth !== undefined && (
                    <span className={dashboardData.overview.salesGrowth >= 0 ? "text-green-600" : "text-red-600"}>
                      {dashboardData.overview.salesGrowth >= 0 ? "+" : ""}{dashboardData.overview.salesGrowth.toFixed(1)}% from last month
                    </span>
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Pending Orders Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-4 w-28" />
              </div>
            ) : metricsError ? (
              <div className="text-2xl font-bold text-muted-foreground">--</div>
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {dashboardData?.alerts.pendingOrders || 0}
                </div>
                <p className="text-xs text-muted-foreground">awaiting processing</p>
              </>
            )}
          </CardContent>
        </Card>
        {/* Total Customers Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-4 w-32" />
              </div>
            ) : metricsError ? (
              <div className="text-2xl font-bold text-muted-foreground">--</div>
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {dashboardData?.overview.totalCustomers?.toLocaleString() || 0}
                </div>
                <p className="text-xs text-muted-foreground">registered customers</p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Recent Orders Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-4 w-28" />
              </div>
            ) : metricsError ? (
              <div className="text-2xl font-bold text-muted-foreground">--</div>
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {dashboardData?.recentOrders?.length || 0}
                </div>
                <p className="text-xs text-muted-foreground">in the last 30 days</p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Top Selling Product Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Selling Product</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-32" />
                <Skeleton className="h-4 w-24" />
              </div>
            ) : metricsError ? (
              <div className="text-2xl font-bold text-muted-foreground">--</div>
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {dashboardData?.topProducts?.[0]?.productName || "No data"}
                </div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.topProducts?.[0]?.totalQuantity
                    ? `${dashboardData.topProducts[0].totalQuantity} units sold`
                    : "No sales data"
                  }
                </p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Low Stock Alert Card */}
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
            <Warehouse className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-4 w-28" />
              </div>
            ) : metricsError ? (
              <div className="text-2xl font-bold text-muted-foreground">--</div>
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {dashboardData?.alerts.lowStockCount || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {dashboardData?.alerts.lowStockCount > 0 ? "need restocking" : "all items in stock"}
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Admin-Only Session Management Section */}
      {isAdmin && sessionStats && (
        <>
          <div className="mt-8 mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Session Management
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Monitor active sessions and security alerts
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-3 mb-8">
            {/* Active Sessions Card */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  Active Sessions
                  {socket.connected && (
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  )}
                </CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {socket.sessionStats?.activeSessions || sessionStats.overview?.activeSessions || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {socket.sessionStats?.onlineUsers || sessionStats.overview?.activeUsers || 0} active users
                  {socket.connected && <span className="text-green-600 ml-1">• Live</span>}
                </p>
              </CardContent>
            </Card>

            {/* Today's Logins Card */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Today's Logins</CardTitle>
                <Monitor className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {sessionStats.overview?.totalSessions || 0}
                </div>
                <p className="text-xs text-muted-foreground">Last 24 hours</p>
              </CardContent>
            </Card>

            {/* Security Alerts Card */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Security Alerts</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-600">
                  {sessionStats.securityAlerts?.length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Suspicious activity detected
                </p>
              </CardContent>
            </Card>
          </div>
        </>
      )}

      {/* Category Statistics Section */}
      {categoryStats && (
        <>
          <div className="mt-8 mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2 flex items-center gap-2">
              Category Analytics
              {categoryStatsLoading && <Loader2 className="h-4 w-4 animate-spin" />}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Product category performance and insights
            </p>
          </div>

          {/* Category Overview Cards */}
          <div className="grid gap-6 md:grid-cols-4 mb-6">
            {/* Total Categories */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Categories</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{categoryStats.overview.totalCategories}</div>
                <p className="text-xs text-muted-foreground">
                  {categoryStats.overview.activeCategories} active
                </p>
              </CardContent>
            </Card>

            {/* Categories with Products */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">With Products</CardTitle>
                <Store className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{categoryStats.overview.categoriesWithProducts}</div>
                <p className="text-xs text-muted-foreground">
                  {categoryStats.overview.utilizationRate}% utilization rate
                </p>
              </CardContent>
            </Card>

            {/* Empty Categories */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Empty Categories</CardTitle>
                <Warehouse className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-600">
                  {categoryStats.overview.emptyCategories}
                </div>
                <p className="text-xs text-muted-foreground">need products</p>
              </CardContent>
            </Card>

            {/* Top Category by Sales */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Top Performer</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {categoryStats.topCategoriesBySales[0]?.name || "No data"}
                </div>
                <p className="text-xs text-muted-foreground">
                  {categoryStats.topCategoriesBySales[0]?.totalSales
                    ? `$${categoryStats.topCategoriesBySales[0].totalSales.toLocaleString()}`
                    : "No sales data"
                  }
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Category Performance Tables */}
          <div className="grid gap-6 lg:grid-cols-2 mb-8">
            {/* Top Categories by Sales */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle>Top Categories by Sales</CardTitle>
                <CardDescription>Best performing categories in the last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                {categoryStats.topCategoriesBySales.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Category</TableHead>
                        <TableHead className="text-right">Sales</TableHead>
                        <TableHead className="text-right">Orders</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {categoryStats.topCategoriesBySales.slice(0, 5).map((category, index) => (
                        <TableRow key={category.name}>
                          <TableCell className="font-medium">{category.name}</TableCell>
                          <TableCell className="text-right">
                            ${category.totalSales.toLocaleString()}
                          </TableCell>
                          <TableCell className="text-right">{category.orderCount}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <p className="text-center text-muted-foreground py-8">No sales data available</p>
                )}
              </CardContent>
            </Card>

            {/* Top Categories by Product Count */}
            <Card className="shadow-sm hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle>Categories by Product Count</CardTitle>
                <CardDescription>Categories with the most products</CardDescription>
              </CardHeader>
              <CardContent>
                {categoryStats.topCategoriesByProducts.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Category</TableHead>
                        <TableHead className="text-right">Products</TableHead>
                        <TableHead className="text-right">Avg Price</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {categoryStats.topCategoriesByProducts.slice(0, 5).map((category, index) => (
                        <TableRow key={category.name}>
                          <TableCell className="font-medium">{category.name}</TableCell>
                          <TableCell className="text-right">{category.productCount}</TableCell>
                          <TableCell className="text-right">
                            ${category.averagePrice.toLocaleString()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <p className="text-center text-muted-foreground py-8">No product data available</p>
                )}
              </CardContent>
            </Card>
          </div>
        </>
      )}

      {/* Category Stats Error State */}
      {categoryStatsError && (
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load category statistics.
            <Button
              variant="link"
              className="p-0 h-auto ml-1"
              onClick={refetchCategoryStats}
            >
              Try again
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 lg:grid-cols-2">
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Recent Activity
              {activitiesLoading && <Loader2 className="h-4 w-4 animate-spin" />}
            </CardTitle>
            <CardDescription>Latest updates on orders, inventory, and sales.</CardDescription>
          </CardHeader>
          <CardContent>
            {activitiesError ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Failed to load recent activities.
                  <Button
                    variant="link"
                    className="p-0 h-auto ml-1"
                    onClick={refetchActivities}
                  >
                    Try again
                  </Button>
                </AlertDescription>
              </Alert>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Time</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {activitiesLoading ? (
                    // Loading skeleton rows
                    Array.from({ length: 5 }).map((_, index) => (
                      <TableRow key={index}>
                        <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                        <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                        <TableCell className="text-right"><Skeleton className="h-4 w-20 ml-auto" /></TableCell>
                      </TableRow>
                    ))
                  ) : activityLogs.length > 0 ? (
                    activityLogs.map((activity) => (
                      <TableRow key={activity._id}>
                        <TableCell className="font-medium">{activity.type}</TableCell>
                        <TableCell>{activity.description}</TableCell>
                        <TableCell className="text-right text-muted-foreground">
                          {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center text-muted-foreground py-8">
                        No recent activities found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
        <Card className="shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle>Sales Trend (Last 7 Days)</CardTitle>
            <CardDescription>Daily sales performance.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-48 w-full bg-muted rounded-lg flex items-center justify-center text-muted-foreground">
              {/* Placeholder for a chart */}
              <BarChart className="h-12 w-12 opacity-50" />
              <span className="ml-2">Chart Placeholder</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions Section */}
      <div className="mt-8">
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Quick Actions
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {isAdmin ? "Manage your system and operations" : "Manage your branch operations"}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Product Management - Available to all */}
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>Product Management</span>
              </CardTitle>
              <CardDescription>
                Add, edit, and manage products
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                Manage Products
              </Button>
            </CardContent>
          </Card>

          {/* Order Management - Available to all */}
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ShoppingCart className="h-5 w-5" />
                <span>Order Management</span>
              </CardTitle>
              <CardDescription>
                View and manage customer orders
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                Manage Orders
              </Button>
            </CardContent>
          </Card>

          {/* Inventory Management - Available to all */}
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Warehouse className="h-5 w-5" />
                <span>Inventory Management</span>
              </CardTitle>
              <CardDescription>
                Track and manage stock levels
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                Manage Inventory
              </Button>
            </CardContent>
          </Card>

          {/* Admin-Only Actions */}
          {isAdmin && (
            <>
              {/* User Management - Admin Only */}
              <Card className="shadow-sm hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="h-5 w-5" />
                    <span>User Management</span>
                  </CardTitle>
                  <CardDescription>
                    Manage users, roles, and permissions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">
                    Manage Users
                  </Button>
                </CardContent>
              </Card>

              {/* Branch Management - Admin Only */}
              <Card className="shadow-sm hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Store className="h-5 w-5" />
                    <span>Branch Management</span>
                  </CardTitle>
                  <CardDescription>
                    Manage branches, locations, and operations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Link href="/shops">
                    <Button className="w-full">
                      Manage Branches
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Session Management - Admin Only */}
              <Card className="shadow-sm hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Session Management</span>
                  </CardTitle>
                  <CardDescription>
                    Monitor and manage user sessions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Link href="/sessions">
                    <Button className="w-full">
                      Manage Sessions
                    </Button>
                  </Link>
                </CardContent>
              </Card>

              {/* Analytics - Admin Only */}
              <Card className="shadow-sm hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5" />
                    <span>Analytics</span>
                  </CardTitle>
                  <CardDescription>
                    View sales reports and analytics
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">
                    View Analytics
                  </Button>
                </CardContent>
              </Card>

              {/* System Settings - Admin Only */}
              <Card className="shadow-sm hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Settings className="h-5 w-5" />
                    <span>System Settings</span>
                  </CardTitle>
                  <CardDescription>
                    Configure system settings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">
                    System Settings
                  </Button>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>
    </>
  )
}
