'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { toast } from 'sonner'
import { 
  Bell, 
  Settings, 
  TestTube, 
  Users, 
  ShoppingCart, 
  Package, 
  Shield, 
  Megaphone,
  DollarSign,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { apiClient } from '@/services/frontend'
import { useNotificationStore } from '@/stores/notificationStore'

export default function NotificationTestPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [testResults, setTestResults] = useState<any[]>([])
  const { fetchNotifications } = useNotificationStore()

  const handleInitPreferences = async () => {
    setIsLoading(true)
    try {
      const response = await apiClient.post('/api/notifications/init', {
        action: 'init_preferences'
      })
      
      if (response.success) {
        toast.success('Notification preferences initialized successfully')
        setTestResults(prev => [...prev, {
          action: 'init_preferences',
          success: true,
          message: 'Preferences initialized',
          timestamp: new Date()
        }])
      } else {
        toast.error(response.error || 'Failed to initialize preferences')
      }
    } catch (error) {
      toast.error('Failed to initialize preferences')
      console.error('Init preferences error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSendWelcome = async () => {
    setIsLoading(true)
    try {
      const response = await apiClient.post('/api/notifications/init', {
        action: 'send_welcome'
      })
      
      if (response.success) {
        toast.success('Welcome notification sent')
        setTestResults(prev => [...prev, {
          action: 'send_welcome',
          success: true,
          message: 'Welcome notification sent',
          timestamp: new Date()
        }])
        
        // Refresh notifications
        await fetchNotifications(true)
      } else {
        toast.error(response.error || 'Failed to send welcome notification')
      }
    } catch (error) {
      toast.error('Failed to send welcome notification')
      console.error('Send welcome error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateSamples = async () => {
    setIsLoading(true)
    try {
      const response = await apiClient.post('/api/notifications/init', {
        action: 'create_samples'
      })
      
      if (response.success) {
        toast.success(`${response.count} sample notifications created`)
        setTestResults(prev => [...prev, {
          action: 'create_samples',
          success: true,
          message: `${response.count} samples created`,
          timestamp: new Date()
        }])
        
        // Refresh notifications
        await fetchNotifications(true)
      } else {
        toast.error(response.error || 'Failed to create sample notifications')
      }
    } catch (error) {
      toast.error('Failed to create sample notifications')
      console.error('Create samples error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestBusinessEvents = async () => {
    setIsLoading(true)
    try {
      const response = await apiClient.post('/api/notifications/init', {
        action: 'test_business_events'
      })
      
      if (response.success) {
        toast.success(`${response.count} business event notifications created`)
        setTestResults(prev => [...prev, {
          action: 'test_business_events',
          success: true,
          message: `${response.count} business events created`,
          data: response.notifications,
          timestamp: new Date()
        }])
        
        // Refresh notifications
        await fetchNotifications(true)
      } else {
        toast.error(response.error || 'Failed to create business event notifications')
      }
    } catch (error) {
      toast.error('Failed to create business event notifications')
      console.error('Test business events error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleInitAllUsers = async () => {
    setIsLoading(true)
    try {
      const response = await apiClient.post('/api/notifications/init', {
        action: 'init_all_users'
      })
      
      if (response.success) {
        toast.success(`Initialized ${response.results.success} users successfully`)
        setTestResults(prev => [...prev, {
          action: 'init_all_users',
          success: true,
          message: `${response.results.success}/${response.results.total} users initialized`,
          data: response.results,
          timestamp: new Date()
        }])
      } else {
        toast.error(response.error || 'Failed to initialize all users')
      }
    } catch (error) {
      toast.error('Failed to initialize all users')
      console.error('Init all users error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const clearResults = () => {
    setTestResults([])
    toast.info('Test results cleared')
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Notification System Test</h1>
          <p className="text-muted-foreground">
            Test and initialize the notification system components
          </p>
        </div>
        <Badge variant="outline" className="flex items-center gap-2">
          <TestTube className="h-4 w-4" />
          Testing Environment
        </Badge>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Initialization Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              System Initialization
            </CardTitle>
            <CardDescription>
              Initialize notification preferences and system components
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleInitPreferences}
              disabled={isLoading}
              className="w-full"
              variant="outline"
            >
              {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Settings className="h-4 w-4 mr-2" />}
              Initialize User Preferences
            </Button>
            
            <Button
              onClick={handleSendWelcome}
              disabled={isLoading}
              className="w-full"
              variant="outline"
            >
              {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Bell className="h-4 w-4 mr-2" />}
              Send Welcome Notification
            </Button>
            
            <Button
              onClick={handleInitAllUsers}
              disabled={isLoading}
              className="w-full"
              variant="outline"
            >
              {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Users className="h-4 w-4 mr-2" />}
              Initialize All Users (Admin)
            </Button>
          </CardContent>
        </Card>

        {/* Sample Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube className="h-5 w-5" />
              Sample Notifications
            </CardTitle>
            <CardDescription>
              Create sample notifications for testing the UI and functionality
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleCreateSamples}
              disabled={isLoading}
              className="w-full"
              variant="outline"
            >
              {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Bell className="h-4 w-4 mr-2" />}
              Create Sample Notifications
            </Button>
            
            <Button
              onClick={handleTestBusinessEvents}
              disabled={isLoading}
              className="w-full"
              variant="outline"
            >
              {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <ShoppingCart className="h-4 w-4 mr-2" />}
              Test Business Events
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Business Event Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Business Event Types</CardTitle>
          <CardDescription>
            Examples of notifications that will be created during business events
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <Package className="h-8 w-8 text-orange-500" />
              <div>
                <h4 className="font-medium">Inventory Alerts</h4>
                <p className="text-sm text-muted-foreground">Low stock, out of stock</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <ShoppingCart className="h-8 w-8 text-blue-500" />
              <div>
                <h4 className="font-medium">Order Updates</h4>
                <p className="text-sm text-muted-foreground">New orders, status changes</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <Shield className="h-8 w-8 text-red-500" />
              <div>
                <h4 className="font-medium">Security Alerts</h4>
                <p className="text-sm text-muted-foreground">Login attempts, suspicious activity</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <Megaphone className="h-8 w-8 text-pink-500" />
              <div>
                <h4 className="font-medium">Campaigns</h4>
                <p className="text-sm text-muted-foreground">Sales, discounts, offers</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <DollarSign className="h-8 w-8 text-green-500" />
              <div>
                <h4 className="font-medium">Financial</h4>
                <p className="text-sm text-muted-foreground">Payments, refunds</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <Users className="h-8 w-8 text-purple-500" />
              <div>
                <h4 className="font-medium">User Events</h4>
                <p className="text-sm text-muted-foreground">Registration, role changes</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Test Results</CardTitle>
              <Button variant="outline" size="sm" onClick={clearResults}>
                Clear Results
              </Button>
            </div>
            <CardDescription>
              Results from notification system tests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {result.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    )}
                    <div>
                      <h4 className="font-medium">{result.action.replace(/_/g, ' ').toUpperCase()}</h4>
                      <p className="text-sm text-muted-foreground">{result.message}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge variant={result.success ? 'default' : 'destructive'}>
                      {result.success ? 'Success' : 'Failed'}
                    </Badge>
                    <p className="text-xs text-muted-foreground mt-1">
                      {result.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
