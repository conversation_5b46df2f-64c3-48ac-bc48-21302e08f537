'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { NotificationCenter } from '@/components/notifications/notification-center'
import { NotificationSummary } from '@/components/notifications/notification-bell'
import { useNotificationStore } from '@/stores/notificationStore'
import { Bell, Settings, BarChart3, TestTube } from 'lucide-react'
import Link from 'next/link'

export default function NotificationsPage() {
  const { 
    unreadCount, 
    totalCount, 
    isConnected,
    getNotificationsByCategory,
    getNotificationsByPriority 
  } = useNotificationStore()

  // Get category statistics
  const categoryStats = [
    { category: 'security', label: 'Security', count: getNotificationsByCategory('security').length, color: 'text-red-600' },
    { category: 'orders', label: 'Orders', count: getNotificationsByCategory('orders').length, color: 'text-blue-600' },
    { category: 'inventory', label: 'Inventory', count: getNotificationsByCategory('inventory').length, color: 'text-green-600' },
    { category: 'delivery', label: 'Delivery', count: getNotificationsByCategory('delivery').length, color: 'text-indigo-600' },
    { category: 'campaigns', label: 'Campaigns', count: getNotificationsByCategory('campaigns').length, color: 'text-pink-600' },
    { category: 'financial', label: 'Financial', count: getNotificationsByCategory('financial').length, color: 'text-emerald-600' }
  ]

  // Get priority statistics
  const priorityStats = [
    { priority: 'critical', label: 'Critical', count: getNotificationsByPriority('critical').length, color: 'text-red-600' },
    { priority: 'urgent', label: 'Urgent', count: getNotificationsByPriority('urgent').length, color: 'text-purple-600' },
    { priority: 'high', label: 'High', count: getNotificationsByPriority('high').length, color: 'text-orange-600' },
    { priority: 'medium', label: 'Medium', count: getNotificationsByPriority('medium').length, color: 'text-yellow-600' },
    { priority: 'low', label: 'Low', count: getNotificationsByPriority('low').length, color: 'text-blue-600' }
  ]

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Bell className="h-8 w-8" />
            Notifications
            {isConnected && (
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
            )}
          </h1>
          <p className="text-muted-foreground">
            Manage your notifications and preferences
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Link href="/dashboard/notifications/test">
            <Button variant="outline" size="sm">
              <TestTube className="h-4 w-4 mr-2" />
              Test System
            </Button>
          </Link>
          
          <Badge variant={isConnected ? 'default' : 'secondary'} className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-gray-400'}`} />
            {isConnected ? 'Connected' : 'Disconnected'}
          </Badge>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Notifications</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCount}</div>
            <p className="text-xs text-muted-foreground">
              All notifications received
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unread</CardTitle>
            <Badge variant="destructive" className="h-6 w-6 rounded-full p-0 flex items-center justify-center text-xs">
              {unreadCount}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{unreadCount}</div>
            <p className="text-xs text-muted-foreground">
              Require your attention
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Priority</CardTitle>
            <Badge variant="outline" className="text-orange-600">
              {getNotificationsByPriority('high').length + getNotificationsByPriority('critical').length + getNotificationsByPriority('urgent').length}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {getNotificationsByPriority('high').length + getNotificationsByPriority('critical').length + getNotificationsByPriority('urgent').length}
            </div>
            <p className="text-xs text-muted-foreground">
              High priority notifications
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Connection Status</CardTitle>
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
              {isConnected ? 'Live' : 'Offline'}
            </div>
            <p className="text-xs text-muted-foreground">
              Real-time updates
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="notifications" className="space-y-6">
        <TabsList>
          <TabsTrigger value="notifications">All Notifications</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <NotificationCenter
            variant="page"
            showHeader={false}
            showFilters={true}
            showPreferences={false}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Category Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  By Category
                </CardTitle>
                <CardDescription>
                  Notification distribution by category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {categoryStats.map((stat) => (
                    <div key={stat.category} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full bg-current ${stat.color}`} />
                        <span className="text-sm font-medium">{stat.label}</span>
                      </div>
                      <Badge variant="outline">{stat.count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Priority Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  By Priority
                </CardTitle>
                <CardDescription>
                  Notification distribution by priority level
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {priorityStats.map((stat) => (
                    <div key={stat.priority} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full bg-current ${stat.color}`} />
                        <span className="text-sm font-medium">{stat.label}</span>
                      </div>
                      <Badge variant="outline">{stat.count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Summary Widget */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Summary</CardTitle>
              <CardDescription>
                Overview of your notification activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <NotificationSummary />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Notification Preferences
              </CardTitle>
              <CardDescription>
                Customize how you receive notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Use the notification bell in the header to access your preferences, or visit the dedicated preferences page.
                </p>
                
                <div className="flex gap-2">
                  <Button variant="outline" asChild>
                    <Link href="/dashboard/settings/notifications">
                      <Settings className="h-4 w-4 mr-2" />
                      Open Preferences
                    </Link>
                  </Button>
                  
                  <Button variant="outline" asChild>
                    <Link href="/dashboard/notifications/test">
                      <TestTube className="h-4 w-4 mr-2" />
                      Test Notifications
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* System Information */}
          <Card>
            <CardHeader>
              <CardTitle>System Information</CardTitle>
              <CardDescription>
                Current notification system status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <h4 className="font-medium">Real-time Connection</h4>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
                    <span className="text-sm">{isConnected ? 'Connected' : 'Disconnected'}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h4 className="font-medium">Browser Notifications</h4>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted' 
                        ? 'bg-green-500' 
                        : 'bg-yellow-500'
                    }`} />
                    <span className="text-sm">
                      {typeof window !== 'undefined' && 'Notification' in window 
                        ? Notification.permission === 'granted' 
                          ? 'Enabled' 
                          : 'Permission needed'
                        : 'Not supported'
                      }
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
