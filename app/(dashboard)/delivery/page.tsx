import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Truck, Package, Clock, MapPin, Phone, Download } from "lucide-react"
import { getCurrentUser } from "@/lib/auth"

export default async function DeliveryPage() {
  const currentUser = await getCurrentUser()

  if (!currentUser) {
    return null
  }

  // Mock delivery data
  const deliveries = [
    {
      id: "del-001",
      orderNumber: "ORD-2024-001",
      customer: "<PERSON>",
      address: "123 Main St, Blantyre",
      phone: "+265 999 123 456",
      products: "Gaming Laptop Pro",
      status: "In Transit",
      driver: "<PERSON>",
      estimatedDelivery: "2024-01-16 14:00",
      branch: "Blantyre",
      trackingNumber: "TRK001234"
    },
    {
      id: "del-002",
      orderNumber: "ORD-2024-002", 
      customer: "<PERSON>",
      address: "456 Oak Ave, Lilongwe",
      phone: "+265 888 987 654",
      products: "4K Smart TV",
      status: "Pending",
      driver: "Not Assigned",
      estimatedDelivery: "2024-01-17 10:00",
      branch: "Lilongwe",
      trackingNumber: "TRK001235"
    },
    {
      id: "del-003",
      orderNumber: "ORD-2024-003",
      customer: "Mike Johnson", 
      address: "789 Pine Rd, Mzuzu",
      phone: "+265 777 456 789",
      products: "Desktop Workstation",
      status: "Delivered",
      driver: "James Phiri",
      estimatedDelivery: "2024-01-15 16:30",
      branch: "Mzuzu",
      trackingNumber: "TRK001236"
    },
    {
      id: "del-004",
      orderNumber: "ORD-2024-004",
      customer: "Sarah Wilson",
      address: "321 Cedar St, Lusaka",
      phone: "+260 977 123 456",
      products: "Network Router Pro",
      status: "Out for Delivery",
      driver: "Moses Mwanza",
      estimatedDelivery: "2024-01-16 11:00",
      branch: "Lusaka",
      trackingNumber: "TRK001237"
    }
  ]

  // Filter deliveries by branch for branch managers
  const filteredDeliveries = currentUser.role === "branch_manager" && currentUser.branchId
    ? deliveries.filter(delivery => delivery.branch.toLowerCase().includes(currentUser.branchId!.split('-')[1]))
    : deliveries

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Delivered": return "bg-green-100 text-green-800"
      case "In Transit": return "bg-blue-100 text-blue-800"
      case "Out for Delivery": return "bg-purple-100 text-purple-800"
      case "Pending": return "bg-yellow-100 text-yellow-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Delivered": return <Package className="h-4 w-4" />
      case "In Transit": return <Truck className="h-4 w-4" />
      case "Out for Delivery": return <MapPin className="h-4 w-4" />
      case "Pending": return <Clock className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="font-semibold text-lg md:text-2xl">Delivery Management</h1>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-3.5 w-3.5 mr-1" /> Export Report
          </Button>
          <Button size="sm">
            <Truck className="h-3.5 w-3.5 mr-1" /> Schedule Delivery
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Deliveries</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">Awaiting dispatch</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Transit</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">On the way</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered Today</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">15</div>
            <p className="text-xs text-muted-foreground">+3 from yesterday</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Delivery Time</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.5 days</div>
            <p className="text-xs text-muted-foreground">-0.3 days from last week</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Delivery Tracking</CardTitle>
          <CardDescription>
            {currentUser.role === "branch_manager" 
              ? `Deliveries for your branch` 
              : "All deliveries across branches"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order #</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Address</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Products</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Driver</TableHead>
                <TableHead>ETA</TableHead>
                <TableHead>Tracking</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDeliveries.map((delivery) => (
                <TableRow key={delivery.id}>
                  <TableCell className="font-medium">{delivery.orderNumber}</TableCell>
                  <TableCell>{delivery.customer}</TableCell>
                  <TableCell className="max-w-[200px] truncate">{delivery.address}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Phone className="h-3 w-3" />
                      {delivery.phone}
                    </div>
                  </TableCell>
                  <TableCell>{delivery.products}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(delivery.status)}>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(delivery.status)}
                        {delivery.status}
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>{delivery.driver}</TableCell>
                  <TableCell>{delivery.estimatedDelivery}</TableCell>
                  <TableCell>
                    <Button variant="outline" size="sm">
                      {delivery.trackingNumber}
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </>
  )
}
