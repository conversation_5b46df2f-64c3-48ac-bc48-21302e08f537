'use client'

import { useAuthStore } from '@/stores/authStore'
import { AuthGuard, RoleGuard } from '@/lib/auth/guards'
import BranchManagement from '@/components/admin/branch-management'

export default function ShopsPage() {
  return (
    <AuthGuard>
      <RoleGuard requiredRoles={['overall_admin', 'branch_manager']}>
        <div className="container mx-auto py-6">
          <BranchManagement />
        </div>
      </RoleGuard>
    </AuthGuard>
  )
}
