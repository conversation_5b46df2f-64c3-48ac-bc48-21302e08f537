import InventoryDashboard from "@/components/dashboard/inventory-dashboard"
import { InventoryErrorBoundary } from "@/components/inventory/inventory-error-boundary"
import { getCurrentUser } from "@/lib/auth"

export default async function InventoryPage() {
  const currentUser = await getCurrentUser()

  // User validation is now handled in the layout
  if (!currentUser) {
    return null
  }

  return (
    <InventoryErrorBoundary>
      <InventoryDashboard currentUser={currentUser} />
    </InventoryErrorBoundary>
  )
}
