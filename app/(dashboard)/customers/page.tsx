import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Users, UserPlus, Mail, Phone, MapPin, Search, Download } from "lucide-react"
import { getCurrentUser } from "@/lib/auth"

export default async function CustomersPage() {
  const currentUser = await getCurrentUser()

  if (!currentUser) {
    return null
  }

  // Mock customer data
  const customers = [
    {
      id: "cust-001",
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+265 999 123 456",
      address: "123 Main St, Blantyre",
      totalOrders: 5,
      totalSpent: 3299.95,
      lastOrder: "2024-01-15",
      status: "Active",
      preferredBranch: "<PERSON><PERSON><PERSON>",
      joinDate: "2023-06-15"
    },
    {
      id: "cust-002",
      name: "<PERSON>", 
      email: "<EMAIL>",
      phone: "+265 888 987 654",
      address: "456 Oak Ave, <PERSON>ongwe",
      totalOrders: 3,
      totalSpent: 1899.97,
      lastOrder: "2024-01-14",
      status: "Active",
      preferredBranch: "Lilongwe",
      joinDate: "2023-08-22"
    },
    {
      id: "cust-003",
      name: "Mike Johnson",
      email: "<EMAIL>", 
      phone: "+265 777 456 789",
      address: "789 Pine Rd, Mzuzu",
      totalOrders: 2,
      totalSpent: 2199.98,
      lastOrder: "2024-01-13",
      status: "Active",
      preferredBranch: "Mzuzu",
      joinDate: "2023-11-10"
    },
    {
      id: "cust-004",
      name: "Sarah Wilson",
      email: "<EMAIL>",
      phone: "+260 977 123 456", 
      address: "321 Cedar St, Lusaka",
      totalOrders: 1,
      totalSpent: 299.99,
      lastOrder: "2024-01-12",
      status: "New",
      preferredBranch: "Lusaka",
      joinDate: "2024-01-10"
    },
    {
      id: "cust-005",
      name: "David Brown",
      email: "<EMAIL>",
      phone: "+265 666 789 123",
      address: "654 Elm St, Blantyre", 
      totalOrders: 8,
      totalSpent: 5499.92,
      lastOrder: "2023-12-20",
      status: "VIP",
      preferredBranch: "Blantyre",
      joinDate: "2023-03-05"
    }
  ]

  // Filter customers by branch for branch managers
  const filteredCustomers = currentUser.role === "branch_manager" && currentUser.branchId
    ? customers.filter(customer => customer.preferredBranch.toLowerCase().includes(currentUser.branchId!.split('-')[1]))
    : customers

  const getStatusColor = (status: string) => {
    switch (status) {
      case "VIP": return "bg-purple-100 text-purple-800"
      case "Active": return "bg-green-100 text-green-800"
      case "New": return "bg-blue-100 text-blue-800"
      case "Inactive": return "bg-gray-100 text-gray-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="font-semibold text-lg md:text-2xl">Customer Management</h1>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-3.5 w-3.5 mr-1" /> Export
          </Button>
          <Button size="sm">
            <UserPlus className="h-3.5 w-3.5 mr-1" /> Add Customer
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2,350</div>
            <p className="text-xs text-muted-foreground">+180 new this month</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,890</div>
            <p className="text-xs text-muted-foreground">80.4% of total</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">VIP Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <p className="text-xs text-muted-foreground">6.6% of total</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Customer Value</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$1,249</div>
            <p className="text-xs text-muted-foreground">+12% from last month</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Customer Directory</CardTitle>
          <CardDescription>
            {currentUser.role === "branch_manager" 
              ? `Customers for your branch` 
              : "All customers across branches"}
          </CardDescription>
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search customers..." className="max-w-sm" />
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Customer</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Address</TableHead>
                <TableHead>Orders</TableHead>
                <TableHead>Total Spent</TableHead>
                <TableHead>Last Order</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Branch</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCustomers.map((customer) => (
                <TableRow key={customer.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{customer.name}</div>
                      <div className="text-sm text-muted-foreground flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {customer.email}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Phone className="h-3 w-3" />
                      {customer.phone}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 max-w-[200px]">
                      <MapPin className="h-3 w-3 flex-shrink-0" />
                      <span className="truncate">{customer.address}</span>
                    </div>
                  </TableCell>
                  <TableCell>{customer.totalOrders}</TableCell>
                  <TableCell>${customer.totalSpent.toFixed(2)}</TableCell>
                  <TableCell>{customer.lastOrder}</TableCell>
                  <TableCell>
                    <Badge className={getStatusColor(customer.status)}>
                      {customer.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{customer.preferredBranch}</TableCell>
                  <TableCell>
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </>
  )
}
