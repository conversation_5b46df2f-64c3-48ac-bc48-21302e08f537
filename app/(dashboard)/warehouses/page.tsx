'use client'

import { useState } from 'react'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import WarehouseManagement from '@/components/warehouse/warehouse-management'
import ShelfManagement from '@/components/warehouse/shelf-management'
import { Warehouse, Layers } from 'lucide-react'

export default function WarehousesPage() {
  const [activeTab, setActiveTab] = useState('warehouses')

  return (
    <div className="container mx-auto py-6">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Warehouse & Storage Management</h1>
          <p className="text-muted-foreground">
            Manage warehouses, storage facilities, and shelf organization for inventory management
          </p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="warehouses" className="flex items-center gap-2">
              <Warehouse className="h-4 w-4" />
              Warehouses
            </TabsTrigger>
            <TabsTrigger value="shelves" className="flex items-center gap-2">
              <Layers className="h-4 w-4" />
              Shelves
            </TabsTrigger>
          </TabsList>

          <TabsContent value="warehouses" className="space-y-6">
            <WarehouseManagement />
          </TabsContent>

          <TabsContent value="shelves" className="space-y-6">
            <ShelfManagement />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
