import type React from "react"
import { redirect } from "next/navigation"
import { getCurrentUser } from "@/lib/auth"
import DashboardLayout from "@/components/dashboard/dashboard-layout"

interface DashboardGroupLayoutProps {
  children: React.ReactNode
}

export default async function DashboardGroupLayout({ children }: DashboardGroupLayoutProps) {
  const currentUser = await getCurrentUser()

  // Redirect to login if no user is found
  if (!currentUser) {
    redirect("/login")
  }

  return (
    <DashboardLayout currentUser={currentUser}>
      {children}
    </DashboardLayout>
  )
}
