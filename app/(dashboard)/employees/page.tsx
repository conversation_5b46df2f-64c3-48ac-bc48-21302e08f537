import { redirect } from "next/navigation"
import { getCurrentUser } from "@/lib/auth"
import EmployeeManagementDashboard from "@/components/dashboard/employee-management-dashboard"

export default async function EmployeesPage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect("/login")
  }

  // Only allow overall_admin and branch_manager to access employee management
  if (!["overall_admin", "branch_manager"].includes(user.role)) {
    redirect("/dashboard")
  }

  return (
    <div className="flex flex-col space-y-6 p-6">
      <EmployeeManagementDashboard currentUser={user} />
    </div>
  )
}
