'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { AuthGuard, RoleGuard } from '@/lib/auth/guards'
import BranchManagement from '@/components/admin/branch-management'

export default function BranchesPage() {
  const { user } = useAuthStore()

  return (
    <AuthGuard>
      <RoleGuard requiredRoles={['overall_admin']}>
        <div className="container mx-auto py-6">
          <BranchManagement />
        </div>
      </RoleGuard>
    </AuthGuard>
  )
}
