'use client'

import { useEffect } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { AuthGuard, RoleGuard } from '@/lib/auth/guards'
import ManagerManagement from '@/components/admin/manager-management'

export default function ManagersPage() {
  const { user } = useAuthStore()

  return (
    <AuthGuard>
      <RoleGuard requiredRoles={['overall_admin']}>
        <div className="container mx-auto py-6">
          <ManagerManagement />
        </div>
      </RoleGuard>
    </AuthGuard>
  )
}
