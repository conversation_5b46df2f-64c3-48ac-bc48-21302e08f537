import type React from "react"
import type { Metadata } from "next/types"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { TokenRefreshProvider, TokenStatusIndicator } from "@/components/auth/token-manager"
import { SocketProvider } from "@/components/providers/socket-provider"
import { NotificationProvider } from "@/components/providers/notification-provider"
import { FloatingCartButton } from "@/components/cart/floating-cart-button"
import { Toaster } from "@/components/ui/sonner"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Fathahitech Multi-Shop Management",
  description: "Admin dashboard for Fathahitech's multi-branch electronic systems management.",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <TokenRefreshProvider>
            <SocketProvider>
              <NotificationProvider>
                {children}
                <FloatingCartButton />
                <TokenStatusIndicator />
                <Toaster />
              </NotificationProvider>
            </SocketProvider>
          </TokenRefreshProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
