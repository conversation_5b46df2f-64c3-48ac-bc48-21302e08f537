// app/admin/branches/[branchId]/dashboard/page.tsx - Branch Dashboard Page

'use client'

import React from 'react'
import { useParams } from 'next/navigation'
import { useAuthStore } from '@/stores/authStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Store, 
  ShoppingCart, 
  TrendingUp, 
  LogOut,
  BarChart3,
  Package,
  MapPin
} from 'lucide-react'

export default function BranchDashboard() {
  const params = useParams()
  const branchId = params.branchId as string
  const { user, logout } = useAuthStore()

  const handleLogout = async () => {
    await logout()
    // Redirect to home page after logout
    window.location.href = "/"
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Loading...</h1>
        </div>
      </div>
    )
  }

  // Get branch name based on branchId (this would normally come from an API)
  const getBranchName = (id: string) => {
    const branches: Record<string, string> = {
      '6890b2a7229e1cb9abcaf206': 'FathahiTech Blantyre',
      '6890b2a7229e1cb9abcaf207': 'FathahiTech Lilongwe',
      '6890b2a7229e1cb9abcaf208': 'FathahiTech Mzuzu',
      '6890b2a7229e1cb9abcaf209': 'FathahiTech Lusaka'
    }
    return branches[id] || `Branch ${id}`
  }

  const branchName = getBranchName(branchId)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {branchName}
              </h1>
              <Badge variant="secondary" className="ml-3">
                Branch Manager
              </Badge>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Welcome, {user.name}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Logout</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Branch Dashboard
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2 flex items-center">
            <MapPin className="h-4 w-4 mr-1" />
            Manage operations for {branchName}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Daily Sales</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">$2,345</div>
              <p className="text-xs text-muted-foreground">
                +12.5% from yesterday
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Orders Today</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">23</div>
              <p className="text-xs text-muted-foreground">
                +8 from yesterday
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">156</div>
              <p className="text-xs text-muted-foreground">
                12 low stock items
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">89</div>
              <p className="text-xs text-muted-foreground">
                +5 new this week
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <ShoppingCart className="h-5 w-5" />
                <span>Process Orders</span>
              </CardTitle>
              <CardDescription>
                View and process pending orders
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                View Orders
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <span>Inventory Management</span>
              </CardTitle>
              <CardDescription>
                Manage branch inventory and stock
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                Manage Inventory
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Customer Management</span>
              </CardTitle>
              <CardDescription>
                View and manage branch customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                Manage Customers
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Branch Reports</span>
              </CardTitle>
              <CardDescription>
                View branch performance reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                View Reports
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Store className="h-5 w-5" />
                <span>Branch Settings</span>
              </CardTitle>
              <CardDescription>
                Configure branch-specific settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                Branch Settings
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
