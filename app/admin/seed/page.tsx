"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Database, CheckCircle, XCircle } from 'lucide-react'

export default function SeedPage() {
  const [isSeeding, setIsSeeding] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message?: string; error?: string } | null>(null)

  const handleSeed = async () => {
    setIsSeeding(true)
    setResult(null)

    try {
      const response = await fetch('/api/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({
        success: false,
        error: 'Failed to connect to seeding API'
      })
    } finally {
      setIsSeeding(false)
    }
  }

  return (
    <div className="container mx-auto py-8 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-6 w-6" />
            Database Seeding
          </CardTitle>
          <CardDescription>
            Populate the database with initial data including admin users, sample shops, and employees.
            This should only be run in development environment.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              <div className="flex items-center gap-2">
                {result.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <XCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  {result.success ? result.message : result.error}
                </AlertDescription>
              </div>
            </Alert>
          )}

          <div className="space-y-2">
            <h3 className="font-semibold">What will be created:</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>Admin users (Winston Mhango and FathahiTech Admin)</li>
              <li>Sample shops in Blantyre, Lilongwe, Mzuzu, and Lusaka</li>
              <li>Sample employees for each shop (managers, sales reps, cashiers, etc.)</li>
              <li>Proper relationships between users, shops, and employees</li>
            </ul>
          </div>

          <Button 
            onClick={handleSeed} 
            disabled={isSeeding}
            className="w-full"
          >
            {isSeeding ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Seeding Database...
              </>
            ) : (
              <>
                <Database className="mr-2 h-4 w-4" />
                Seed Database
              </>
            )}
          </Button>

          <div className="text-xs text-muted-foreground">
            <strong>Note:</strong> This operation is only available in development environment.
            Existing data will not be duplicated.
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
