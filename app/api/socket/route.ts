// app/api/socket/route.ts - Socket.IO API route for Vercel deployment

import { NextRequest, NextResponse } from 'next/server'
import { socketManager } from '@/lib/socket/server'

// Global variable to track if Socket.IO is initialized
let isSocketInitialized = false

/**
 * GET /api/socket - Socket.IO endpoint information and initialization
 * Attempts to initialize Socket.IO server for local development
 */
export async function GET(request: NextRequest) {
  try {
    const baseUrl = request.nextUrl.origin
    const isProduction = process.env.NODE_ENV === 'production'
    const isVercel = process.env.VERCEL === '1'

    // Try to initialize Socket.IO for local development
    if (!isProduction && !isVercel && !isSocketInitialized) {
      try {
        // Get the HTTP server instance from Next.js
        const server = (global as any).__httpServer

        if (server) {
          socketManager.initialize(server)
          isSocketInitialized = true
          console.log('Socket.IO server initialized successfully')
        }
      } catch (error) {
        console.warn('Failed to initialize Socket.IO:', error)
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Socket.IO endpoint',
      initialized: isSocketInitialized,
      environment: {
        production: isProduction,
        vercel: isVercel,
        socketSupported: !isVercel
      },
      info: {
        note: isVercel
          ? 'Socket.IO WebSocket server is not available on Vercel serverless functions'
          : 'Socket.IO server available for real-time features',
        alternatives: isVercel ? [
          'Use polling-based real-time updates via API endpoints',
          'Implement Server-Sent Events (SSE) for real-time notifications',
          'Use external Socket.IO hosting service (Railway, Heroku, etc.)',
          'Deploy Socket.IO server separately and update NEXT_PUBLIC_SOCKET_URL'
        ] : [
          'Socket.IO WebSocket server is running',
          'Real-time session monitoring available',
          'Live notifications and updates enabled'
        ],
        endpoints: {
          notifications: `${baseUrl}/api/notifications`,
          polling: `${baseUrl}/api/notifications/poll`,
          sse: `${baseUrl}/api/notifications/stream`,
          socket: isSocketInitialized ? `${baseUrl}` : null
        }
      }
    })
  } catch (error) {
    console.error('Socket.IO endpoint error:', error)
    return NextResponse.json(
      { success: false, error: 'Socket.IO endpoint error' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/socket - Handle Socket.IO-like events via HTTP
 * Fallback for Socket.IO functionality using HTTP requests
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { event, data } = body

    // Handle different Socket.IO-like events
    switch (event) {
      case 'connect':
        return NextResponse.json({
          success: true,
          message: 'Connected to HTTP-based real-time service',
          connectionId: `http_${Date.now()}`
        })

      case 'notification:send':
        // Handle notification sending
        console.log('HTTP notification send:', data)
        return NextResponse.json({
          success: true,
          message: 'Notification sent via HTTP'
        })

      case 'notification:broadcast':
        // Handle notification broadcasting
        console.log('HTTP notification broadcast:', data)
        return NextResponse.json({
          success: true,
          message: 'Notification broadcasted via HTTP'
        })

      case 'heartbeat':
        return NextResponse.json({
          success: true,
          timestamp: new Date().toISOString()
        })

      default:
        return NextResponse.json(
          { success: false, error: 'Unknown event type' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Socket.IO HTTP fallback error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to process Socket.IO event' },
      { status: 500 }
    )
  }
}
