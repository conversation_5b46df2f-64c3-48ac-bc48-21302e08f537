import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth/middleware'
import { getUserById, changeUserPassword } from '@/services/backend'
import { z } from 'zod'

const changePasswordSchema = z.object({
  newPassword: z.string().min(6).max(100)
})

// POST /api/users/[id]/change-password - Change user password
export const POST = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const userId = params.id
    const body = await request.json()
    const validation = changePasswordSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const { newPassword } = validation.data

    // Get current user to check permissions
    const currentUser = await getUserById(userId)
    if (!currentUser.success) {
      return NextResponse.json(
        { success: false, error: currentUser.error },
        { status: currentUser.error === 'User not found' ? 404 : 500 }
      )
    }

    // Check access permissions
    if (user.role === 'branch_manager') {
      // Branch managers can only change passwords for users in their branch
      if (currentUser.data!.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Access denied' },
          { status: 403 }
        )
      }
    }

    const result = await changeUserPassword(userId, newPassword, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Change password API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to change password' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
