import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getUserById, updateUser, deleteUser, changeUserPassword } from '@/services/backend'
import { z } from 'zod'

const updateUserSchema = z.object({
  username: z.string().min(3).max(50).optional(),
  email: z.string().email().optional(),
  role: z.enum(['overall_admin', 'branch_manager']).optional(),
  branchId: z.string().optional(),
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/).optional(),
  isActive: z.boolean().optional()
})

const changePasswordSchema = z.object({
  newPassword: z.string().min(6).max(100)
})

// GET /api/users/[id] - Get user by ID
export const GET = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const userId = params.id

    const result = await getUserById(userId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'User not found' ? 404 : 500 }
      )
    }

    // Check access permissions
    if (user.role === 'branch_manager') {
      // Branch managers can only view users in their branch
      if (result.data!.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Access denied' },
          { status: 403 }
        )
      }
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get user API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch user' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PUT /api/users/[id] - Update user
export const PUT = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const userId = params.id
    const body = await request.json()
    const validation = updateUserSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    // Get current user to check permissions
    const currentUser = await getUserById(userId)
    if (!currentUser.success) {
      return NextResponse.json(
        { success: false, error: currentUser.error },
        { status: currentUser.error === 'User not found' ? 404 : 500 }
      )
    }

    // Check access permissions
    if (user.role === 'branch_manager') {
      // Branch managers can only update users in their branch
      if (currentUser.data!.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Access denied' },
          { status: 403 }
        )
      }
      
      // Branch managers cannot change roles or create overall admins
      if (data.role === 'overall_admin') {
        return NextResponse.json(
          { success: false, error: 'Branch managers cannot create overall admin users' },
          { status: 403 }
        )
      }
      
      // Branch managers cannot move users to different branches
      if (data.branchId && data.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Branch managers cannot move users to different branches' },
          { status: 403 }
        )
      }
    }

    // Check branch access if branchId is being updated
    if (data.branchId) {
      const branchCheck = checkBranchAccess(user, data.branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await updateUser(userId, data, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Update user API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update user' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// DELETE /api/users/[id] - Delete user (soft delete)
export const DELETE = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const userId = params.id

    // Get current user to check permissions
    const currentUser = await getUserById(userId)
    if (!currentUser.success) {
      return NextResponse.json(
        { success: false, error: currentUser.error },
        { status: currentUser.error === 'User not found' ? 404 : 500 }
      )
    }

    // Check access permissions
    if (user.role === 'branch_manager') {
      // Branch managers can only delete users in their branch
      if (currentUser.data!.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Access denied' },
          { status: 403 }
        )
      }
    }

    const result = await deleteUser(userId, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Delete user API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete user' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
