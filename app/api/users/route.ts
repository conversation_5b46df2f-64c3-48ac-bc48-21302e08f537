import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getUsers, createUser, serviceUtils } from '@/services/backend'
import { z } from 'zod'

const createUserSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email(),
  password: z.string().min(6).max(100),
  name: z.string().min(2).max(100),
  role: z.enum(['branch_manager', 'overall_admin']),
  branchId: z.string().optional(),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/).optional()
})

// GET /api/users - Get all users
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const pagination = serviceUtils.validatePagination(
      parseInt(searchParams.get('page') || '1'),
      parseInt(searchParams.get('limit') || '10')
    )

    const filters = {
      search: serviceUtils.sanitizeSearchQuery(searchParams.get('search') || undefined),
      role: searchParams.get('role') || undefined,
      branchId: searchParams.get('branchId') || undefined,
      isActive: searchParams.get('isActive') === 'true' ? true : searchParams.get('isActive') === 'false' ? false : undefined,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
    }

    // Check branch access if branchId is specified
    if (filters.branchId) {
      const branchCheck = checkBranchAccess(user, filters.branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await getUsers(filters, pagination, user.role, user.branchId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get users API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin']
})

// POST /api/users - Create new user
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    console.log('📝 User creation request body:', JSON.stringify(body, null, 2))

    const validation = createUserSchema.safeParse(body)
    console.log('🔍 Schema validation result:', validation.success)

    if (!validation.success) {
      console.log('❌ Validation errors:', JSON.stringify(validation.error.errors, null, 2))
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    // Check branch access if branchId is provided
    if (data.branchId) {
      const branchCheck = checkBranchAccess(user, data.branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    // Branch managers can only create users for their own branch
    if (user.role === 'branch_manager') {
      if (!data.branchId || data.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Branch managers can only create users for their own branch' },
          { status: 403 }
        )
      }
      // Branch managers can only create other branch managers, not overall admins
      if (data.role === 'overall_admin') {
        return NextResponse.json(
          { success: false, error: 'Branch managers cannot create overall admin users' },
          { status: 403 }
        )
      }
    }

    const result = await createUser(data, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Create user API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create user' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
