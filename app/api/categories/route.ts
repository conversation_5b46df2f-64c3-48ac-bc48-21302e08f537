import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import ProductCategory from '@/lib/database/models/ProductCategory'
import Product from '@/lib/database/models/Product'
import { z } from 'zod'

const createCategorySchema = z.object({
  name: z.string().min(2).max(50),
  description: z.string().min(10).max(500),
  isActive: z.boolean().default(true),
  featuredImage: z.string().optional().nullable(), // Can be URL or will be set after upload
  icon: z.string().optional().nullable(), // Can be URL or will be set after upload
  iconType: z.enum(['image', 'lucide']).default('lucide'),
  iconName: z.string().optional().nullable()
})

// GET /api/categories - Get all categories
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const search = searchParams.get('search') || ''
    const activeOnly = searchParams.get('activeOnly') === 'true'

    const skip = (page - 1) * limit

    // Build filter
    const filter: any = {}
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ]
    }
    if (activeOnly) {
      filter.isActive = true
    }

    const [categories, total] = await Promise.all([
      ProductCategory.find(filter)
        .sort({ name: 1 })
        .skip(skip)
        .limit(limit),
      ProductCategory.countDocuments(filter)
    ])

    // Transform _id to id for frontend compatibility
    const transformedCategories = categories.map(category => {
      const categoryObj = category.toJSON()
      return categoryObj
    })

    return NextResponse.json({
      success: true,
      data: transformedCategories,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get categories API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/categories - Create new category
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    await connectDB()

    const body = await request.json()
    const validation = createCategorySchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    // Generate slug from name
    const slug = data.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()

    // Check if category name already exists
    const existingCategory = await ProductCategory.findOne({
      name: { $regex: new RegExp(`^${data.name}$`, 'i') }
    })

    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Category name already exists' },
        { status: 400 }
      )
    }

    const category = await ProductCategory.create({
      ...data,
      slug,
      createdBy: user.userId,
      updatedBy: user.userId
    })

    return NextResponse.json({
      success: true,
      data: category
    }, { status: 201 })
  } catch (error) {
    console.error('Create category API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create category' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin']
})
