import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import ProductCategory from '@/lib/database/models/ProductCategory'
import Product from '@/lib/database/models/Product'
import { Order } from '@/lib/database'

// GET /api/categories/stats - Get category statistics
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)
    const branchId = searchParams.get('branchId') || undefined
    const period = parseInt(searchParams.get('period') || '30')
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined

    // Check branch access if branchId is specified
    if (branchId) {
      const branchCheck = checkBranchAccess(user, branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    // Build date filter for orders
    const dateFilter: any = {}
    if (startDate && endDate) {
      dateFilter.createdAt = { $gte: startDate, $lte: endDate }
    } else {
      // Default to last period days
      dateFilter.createdAt = {
        $gte: new Date(Date.now() - period * 24 * 60 * 60 * 1000)
      }
    }

    // Build branch filter for orders
    const branchFilter: any = {}
    if (user.role === 'branch_manager' && user.branchId) {
      branchFilter.branchId = user.branchId
    } else if (branchId) {
      branchFilter.branchId = branchId
    }

    const baseOrderFilter = { ...dateFilter, ...branchFilter, status: { $ne: 'cancelled' } }

    // Get category statistics
    const [
      totalCategories,
      activeCategories,
      categoriesWithProducts,
      topCategoriesBySales,
      topCategoriesByProducts,
      categoriesWithLowStock,
      categoryPerformance
    ] = await Promise.all([
      // Total categories count
      ProductCategory.countDocuments({}),

      // Active categories count
      ProductCategory.countDocuments({ isActive: true }),

      // Categories with products
      ProductCategory.aggregate([
        {
          $lookup: {
            from: 'products',
            let: { categoryName: '$name' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$category', '$$categoryName'] },
                  isActive: true
                }
              }
            ],
            as: 'products'
          }
        },
        {
          $addFields: {
            productCount: { $size: '$products' }
          }
        },
        {
          $match: { productCount: { $gt: 0 } }
        },
        {
          $project: {
            name: 1,
            productCount: 1,
            isActive: 1
          }
        },
        { $sort: { productCount: -1 } }
      ]),

      // Top categories by sales (last period)
      Order.aggregate([
        { $match: baseOrderFilter },
        { $unwind: '$items' },
        {
          $lookup: {
            from: 'products',
            localField: 'items.productId',
            foreignField: '_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        {
          $group: {
            _id: '$product.category',
            totalSales: { $sum: '$items.subtotal' },
            totalQuantity: { $sum: '$items.quantity' },
            orderCount: { $sum: 1 },
            averageOrderValue: { $avg: '$items.subtotal' }
          }
        },
        { $sort: { totalSales: -1 } },
        { $limit: 10 }
      ]),

      // Top categories by product count
      Product.aggregate([
        { $match: { isActive: true } },
        {
          $group: {
            _id: '$category',
            productCount: { $sum: 1 },
            averagePrice: { $avg: '$price' },
            totalStock: { $sum: '$stock' }
          }
        },
        { $sort: { productCount: -1 } },
        { $limit: 10 }
      ]),

      // Categories with low stock products
      Product.aggregate([
        { 
          $match: { 
            isActive: true,
            $expr: { $lte: ['$stock', '$lowStockThreshold'] }
          }
        },
        {
          $group: {
            _id: '$category',
            lowStockCount: { $sum: 1 },
            totalProducts: { $sum: 1 }
          }
        },
        { $sort: { lowStockCount: -1 } }
      ]),

      // Category performance metrics
      ProductCategory.aggregate([
        { $match: { isActive: true } },
        {
          $lookup: {
            from: 'products',
            let: { categoryName: '$name' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$category', '$$categoryName'] },
                  isActive: true
                }
              }
            ],
            as: 'products'
          }
        },
        {
          $addFields: {
            productCount: { $size: '$products' },
            averagePrice: { $avg: '$products.price' },
            totalStock: { $sum: '$products.stock' },
            lowStockProducts: {
              $size: {
                $filter: {
                  input: '$products',
                  cond: { $lte: ['$$this.stock', '$$this.lowStockThreshold'] }
                }
              }
            }
          }
        },
        {
          $project: {
            name: 1,
            description: 1,
            productCount: 1,
            averagePrice: { $round: ['$averagePrice', 2] },
            totalStock: 1,
            lowStockProducts: 1,
            stockHealthPercentage: {
              $cond: {
                if: { $gt: ['$productCount', 0] },
                then: {
                  $round: [
                    {
                      $multiply: [
                        {
                          $divide: [
                            { $subtract: ['$productCount', '$lowStockProducts'] },
                            '$productCount'
                          ]
                        },
                        100
                      ]
                    },
                    1
                  ]
                },
                else: 100
              }
            }
          }
        },
        { $sort: { productCount: -1 } }
      ])
    ])

    // Calculate additional metrics
    const categoriesWithProductsCount = categoriesWithProducts.length
    const emptyCategoriesCount = activeCategories - categoriesWithProductsCount

    const response = NextResponse.json({
      success: true,
      data: {
        overview: {
          totalCategories,
          activeCategories,
          categoriesWithProducts: categoriesWithProductsCount,
          emptyCategories: emptyCategoriesCount,
          utilizationRate: activeCategories > 0 ? Math.round((categoriesWithProductsCount / activeCategories) * 100) : 0
        },
        topCategoriesBySales: topCategoriesBySales.map(cat => ({
          name: cat._id,
          totalSales: cat.totalSales,
          totalQuantity: cat.totalQuantity,
          orderCount: cat.orderCount,
          averageOrderValue: Math.round(cat.averageOrderValue * 100) / 100
        })),
        topCategoriesByProducts: topCategoriesByProducts.map(cat => ({
          name: cat._id,
          productCount: cat.productCount,
          averagePrice: Math.round(cat.averagePrice * 100) / 100,
          totalStock: cat.totalStock
        })),
        categoriesWithLowStock: categoriesWithLowStock.map(cat => ({
          name: cat._id,
          lowStockCount: cat.lowStockCount,
          totalProducts: cat.totalProducts,
          lowStockPercentage: Math.round((cat.lowStockCount / cat.totalProducts) * 100)
        })),
        categoryPerformance,
        filters: {
          branchId,
          period,
          startDate: startDate?.toISOString(),
          endDate: endDate?.toISOString()
        }
      }
    })

    return response
  } catch (error) {
    console.error('Category stats API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch category statistics' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
