import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import ProductCategory from '@/lib/database/models/ProductCategory'
import { uploadToGridFS, validateImageFile, generateUniqueFilename } from '@/lib/utils/gridfs'

/**
 * POST /api/categories/[id]/images - Upload images for a specific category
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await connectDB()

    // Verify authentication
    const authResult = await authMiddleware(request, ['overall_admin'])
    if (!authResult.success) {
      return authResult.response || NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { user } = authResult
    const categoryId = (await params).id

    // Check if category exists
    const category = await ProductCategory.findById(categoryId)
    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const featuredImageFile = formData.get('featuredImage') as File | null
    const iconFile = formData.get('icon') as File | null
    const iconType = formData.get('iconType') as string || 'lucide'
    const iconName = formData.get('iconName') as string || null

    const files: File[] = []
    if (featuredImageFile) files.push(featuredImageFile)
    if (iconFile) files.push(iconFile)

    if (files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No images provided' },
        { status: 400 }
      )
    }

    // Validate files
    const validationErrors: string[] = []
    for (const file of files) {
      const validation = validateImageFile(file)
      if (!validation.valid) {
        validationErrors.push(`${file.name}: ${validation.error}`)
      }
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'File validation failed',
          details: validationErrors
        },
        { status: 400 }
      )
    }

    // Upload files
    const uploadResults: { [key: string]: any } = {}
    
    if (featuredImageFile) {
      try {
        const filename = generateUniqueFilename(featuredImageFile.name, 'category_featured')
        const result = await uploadToGridFS(featuredImageFile, filename, {
          category: 'category',
          categoryId: categoryId,
          categoryName: category.name,
          imageType: 'featured',
          uploadedBy: user.userId,
          branchId: user.branchId
        })
        uploadResults.featuredImage = result
      } catch (error) {
        console.error(`Failed to upload featured image:`, error)
        validationErrors.push(`Featured image: Upload failed`)
      }
    }

    if (iconFile) {
      try {
        const filename = generateUniqueFilename(iconFile.name, 'category_icon')
        const result = await uploadToGridFS(iconFile, filename, {
          category: 'category',
          categoryId: categoryId,
          categoryName: category.name,
          imageType: 'icon',
          uploadedBy: user.userId,
          branchId: user.branchId
        })
        uploadResults.icon = result
      } catch (error) {
        console.error(`Failed to upload icon:`, error)
        validationErrors.push(`Icon: Upload failed`)
      }
    }

    if (Object.keys(uploadResults).length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'All uploads failed',
          details: validationErrors
        },
        { status: 500 }
      )
    }

    // Prepare update data
    const updateData: any = {
      updatedBy: user.userId,
      updatedAt: new Date()
    }

    if (uploadResults.featuredImage) {
      updateData.featuredImage = uploadResults.featuredImage.url
    }

    if (uploadResults.icon) {
      updateData.icon = uploadResults.icon.url
      updateData.iconType = 'image' // Set to image since we uploaded a file
    } else if (iconType === 'lucide' && iconName) {
      // If no icon file but lucide icon specified
      updateData.iconType = 'lucide'
      updateData.iconName = iconName
      updateData.icon = null // Clear any existing icon URL
    }

    // Update category
    const updatedCategory = await ProductCategory.findByIdAndUpdate(
      categoryId,
      updateData,
      { new: true, runValidators: true }
    )

    return NextResponse.json({
      success: true,
      data: {
        category: updatedCategory?.toJSON(),
        uploadedImages: uploadResults,
        totalUploaded: Object.keys(uploadResults).length,
        errors: validationErrors.length > 0 ? validationErrors : undefined
      }
    })
  } catch (error) {
    console.error('Category image upload API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to upload category images' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/categories/[id]/images - Remove category images
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    // Verify authentication
    const authResult = await authMiddleware(request, ['overall_admin'])
    if (!authResult.success) {
      return authResult.response || NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { user } = authResult
    const categoryId = (await params).id

    // Check if category exists
    const category = await ProductCategory.findById(categoryId)
    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    const { searchParams } = new URL(request.url)
    const imageType = searchParams.get('type') // 'featured' or 'icon'

    const updateData: any = {
      updatedBy: user.userId,
      updatedAt: new Date()
    }

    if (imageType === 'featured') {
      updateData.featuredImage = null
    } else if (imageType === 'icon') {
      updateData.icon = null
      updateData.iconType = 'lucide'
      updateData.iconName = null
    } else {
      // Remove all images
      updateData.featuredImage = null
      updateData.icon = null
      updateData.iconType = 'lucide'
      updateData.iconName = null
    }

    // Update category
    const updatedCategory = await ProductCategory.findByIdAndUpdate(
      categoryId,
      updateData,
      { new: true, runValidators: true }
    )

    return NextResponse.json({
      success: true,
      data: {
        category: updatedCategory?.toJSON(),
        removedImageType: imageType || 'all'
      }
    })
  } catch (error) {
    console.error('Category image removal API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to remove category images' },
      { status: 500 }
    )
  }
}
