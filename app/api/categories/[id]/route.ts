import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware, type UserRole } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import ProductCategory from '@/lib/database/models/ProductCategory'
import Product from '@/lib/database/models/Product'
import { z } from 'zod'

const updateCategorySchema = z.object({
  name: z.string().min(2).max(50).optional(),
  description: z.string().min(10).max(500).optional(),
  isActive: z.boolean().optional(),
  featuredImage: z.string().optional().nullable(), // Can be URL or will be set after upload
  icon: z.string().optional().nullable(), // Can be URL or will be set after upload
  iconType: z.enum(['image', 'lucide']).optional(),
  iconName: z.string().optional().nullable()
})

// GET /api/categories/[id] - Get category by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply authentication manually
    const requiredRoles: UserRole[] = ['overall_admin', 'branch_manager']
    const authResult = await authMiddleware(request, requiredRoles)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      )
    }
    if (authResult.response) {
      return authResult.response
    }

    await connectDB()

    const categoryId = (await params).id

    const category = await ProductCategory.findById(categoryId)

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: category.toJSON()
    })
  } catch (error) {
    console.error('Get category API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch category' },
      { status: 500 }
    )
  }
}

// PUT /api/categories/[id] - Update category
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply authentication manually - only overall_admin can update categories
    const requiredRoles: UserRole[] = ['overall_admin']
    const authResult = await authMiddleware(request, requiredRoles)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      )
    }
    if (authResult.response) {
      return authResult.response
    }

    await connectDB()

    const categoryId = (await params).id

    const body = await request.json()
    const validation = updateCategorySchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const updates = validation.data

    // Check if category exists
    const existingCategory = await ProductCategory.findById(categoryId)
    if (!existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    // Check if new name already exists (if name is being updated)
    if (updates.name && updates.name !== existingCategory.name) {
      const nameExists = await ProductCategory.findOne({ 
        name: { $regex: new RegExp(`^${updates.name}$`, 'i') },
        _id: { $ne: categoryId }
      })

      if (nameExists) {
        return NextResponse.json(
          { success: false, error: 'Category name already exists' },
          { status: 400 }
        )
      }
    }

    const category = await ProductCategory.findByIdAndUpdate(
      categoryId,
      {
        ...updates,
        updatedBy: authResult.user!.userId,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    )

    return NextResponse.json({
      success: true,
      data: category?.toJSON()
    })
  } catch (error) {
    console.error('Update category API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update category' },
      { status: 500 }
    )
  }
}

// DELETE /api/categories/[id] - Delete category
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Apply authentication manually - only overall_admin can delete categories
    const requiredRoles: UserRole[] = ['overall_admin']
    const authResult = await authMiddleware(request, requiredRoles)
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      )
    }
    if (authResult.response) {
      return authResult.response
    }

    await connectDB()

    const categoryId = (await params).id

    // Check if category exists
    const category = await ProductCategory.findById(categoryId)
    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    // Check if category has products
    const productCount = await Product.countDocuments({ 
      category: category.name,
      isActive: true 
    })

    if (productCount > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Cannot delete category. It has ${productCount} active products. Please move or delete the products first.` 
        },
        { status: 400 }
      )
    }

    // Soft delete - set isActive to false
    await ProductCategory.findByIdAndUpdate(categoryId, {
      isActive: false,
      updatedBy: authResult.user!.userId,
      updatedAt: new Date()
    })

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully'
    })
  } catch (error) {
    console.error('Delete category API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete category' },
      { status: 500 }
    )
  }
}
