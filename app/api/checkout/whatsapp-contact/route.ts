import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/database'
import Shop from '@/lib/database/models/Shop'
import User from '@/lib/database/models/User'
import { Types } from 'mongoose'

// GET /api/checkout/whatsapp-contact - Get WhatsApp contact for checkout (public access)
export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)
    const branchId = searchParams.get('branchId')

    console.log('📞 WhatsApp contact API called for branchId:', branchId)

    let contactInfo: any = null

    if (branchId) {
      // Get specific branch and its manager contact
      const branch = await Shop.findById(branchId).lean()
      
      if (branch) {
        console.log('🏢 Found branch:', branch.name)
        
        // Get manager contact info
        let managerContact = null
        if (branch.managerId && Types.ObjectId.isValid(branch.managerId)) {
          console.log('🔍 Looking for manager with ID:', branch.managerId)
          const manager = await User.findById(branch.managerId).lean()
          if (manager && manager.phone) {
            managerContact = {
              name: manager.name,
              phone: manager.phone,
              role: 'Branch Manager'
            }
            console.log('👤 Found branch manager contact:', manager.name)
          } else {
            console.log('⚠️ Manager found but no phone number available')
          }
        } else if (branch.managerId) {
          console.log('⚠️ Invalid managerId format:', branch.managerId, '(expected ObjectId)')
        }

        contactInfo = {
          type: 'branch',
          branchId: branch._id.toString(),
          branchName: branch.name,
          branchPhone: branch.phone,
          branchEmail: branch.email,
          branchAddress: branch.address,
          branchLocation: `${branch.location}, ${branch.region}, ${branch.country}`,
          manager: managerContact,
          operatingHours: branch.operatingHours,
          primaryContact: managerContact?.phone || branch.phone
        }
      }
    }

    // If no specific branch or branch not found, get overall admin contact
    if (!contactInfo) {
      console.log('🔍 Looking for overall admin contact...')
      
      const overallAdmin = await User.findOne({ 
        role: 'overall_admin', 
        isActive: true,
        phone: { $exists: true, $ne: null }
      }).lean()

      if (overallAdmin) {
        console.log('👑 Found overall admin contact:', overallAdmin.name)
        
        contactInfo = {
          type: 'admin',
          adminId: overallAdmin._id.toString(),
          adminName: overallAdmin.name,
          adminPhone: overallAdmin.phone,
          adminEmail: overallAdmin.email,
          role: 'Overall Administrator',
          primaryContact: overallAdmin.phone
        }
      }
    }

    // Fallback to any available contact
    if (!contactInfo) {
      console.log('⚠️ No specific contact found, using fallback...')
      
      // Try to find any branch with contact info
      const anyBranch = await Shop.findOne({ 
        phone: { $exists: true, $ne: null },
        status: 'Active'
      }).lean()

      if (anyBranch) {
        contactInfo = {
          type: 'fallback',
          branchId: anyBranch._id.toString(),
          branchName: anyBranch.name,
          branchPhone: anyBranch.phone,
          branchEmail: anyBranch.email,
          branchAddress: anyBranch.address,
          branchLocation: `${anyBranch.location}, ${anyBranch.region}, ${anyBranch.country}`,
          primaryContact: anyBranch.phone,
          note: 'General customer service'
        }
      }
    }

    if (!contactInfo) {
      console.log('❌ No contact information available')
      return NextResponse.json({
        success: false,
        error: 'No contact information available for checkout'
      }, { status: 404 })
    }

    console.log('✅ Returning contact info:', contactInfo.type, contactInfo.primaryContact)

    return NextResponse.json({
      success: true,
      data: contactInfo
    })
  } catch (error) {
    console.error('WhatsApp contact API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch contact information' },
      { status: 500 }
    )
  }
}
