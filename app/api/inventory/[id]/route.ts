import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getInventoryById, updateInventory, updateStock } from '@/services/backend'
import { z } from 'zod'

const updateInventorySchema = z.object({
  stock: z.number().min(0).optional(),
  minStockLevel: z.number().min(0).optional(),
  maxStockLevel: z.number().min(0).optional(),
  cost: z.number().min(0).optional(),
  location: z.string().min(1).max(100).optional(),
  supplier: z.string().max(100).optional(),
  batchNumber: z.string().max(50).optional(),
  expiryDate: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  lastRestocked: z.string().transform(str => str ? new Date(str) : undefined).optional()
})

const stockMovementSchema = z.object({
  type: z.enum(['in', 'out', 'adjustment']),
  quantity: z.number().min(0),
  reason: z.string().min(1).max(200),
  reference: z.string().max(100).optional(),
  cost: z.number().min(0).optional()
})

// GET /api/inventory/[id] - Get inventory item by ID
export const GET = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const inventoryId = params.id

    const result = await getInventoryById(inventoryId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Inventory item not found' ? 404 : 500 }
      )
    }

    // Check branch access
    const branchCheck = checkBranchAccess(user, result.data!.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get inventory item API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch inventory item' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PUT /api/inventory/[id] - Update inventory item
export const PUT = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const inventoryId = params.id
    const body = await request.json()
    const validation = updateInventorySchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    // Get current inventory to check branch access
    const currentInventory = await getInventoryById(inventoryId)
    if (!currentInventory.success) {
      return NextResponse.json(
        { success: false, error: currentInventory.error },
        { status: currentInventory.error === 'Inventory item not found' ? 404 : 500 }
      )
    }

    // Check branch access
    const branchCheck = checkBranchAccess(user, currentInventory.data!.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    const result = await updateInventory(inventoryId, data, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Update inventory API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update inventory item' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PATCH /api/inventory/[id] - Update stock levels
export const PATCH = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const inventoryId = params.id
    const body = await request.json()
    const validation = stockMovementSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const movement = validation.data

    // Get current inventory to check branch access
    const currentInventory = await getInventoryById(inventoryId)
    if (!currentInventory.success) {
      return NextResponse.json(
        { success: false, error: currentInventory.error },
        { status: currentInventory.error === 'Inventory item not found' ? 404 : 500 }
      )
    }

    // Check branch access
    const branchCheck = checkBranchAccess(user, currentInventory.data!.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    const result = await updateStock(inventoryId, movement, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Update stock API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update stock' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
