import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getInventory, createInventory, serviceUtils } from '@/services/backend'
import { z } from 'zod'

const createInventorySchema = z.object({
  productId: z.string().min(1),
  productName: z.string().min(1).max(200),
  sku: z.string().min(1).max(50),
  branchId: z.string().min(1),
  stock: z.number().min(0),
  minStockLevel: z.number().min(0),
  maxStockLevel: z.number().min(0),
  cost: z.number().min(0),
  location: z.string().min(1).max(100),
  supplier: z.string().max(100).optional(),
  batchNumber: z.string().max(50).optional(),
  expiryDate: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  lastRestocked: z.string().transform(str => new Date(str))
})

// GET /api/inventory - Get all inventory items
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const pagination = serviceUtils.validatePagination(
      parseInt(searchParams.get('page') || '1'),
      parseInt(searchParams.get('limit') || '10')
    )

    const filters = {
      search: serviceUtils.sanitizeSearchQuery(searchParams.get('search') || undefined),
      branchId: searchParams.get('branchId') || undefined,
      productId: searchParams.get('productId') || undefined,
      lowStock: searchParams.get('lowStock') === 'true',
      outOfStock: searchParams.get('outOfStock') === 'true',
      location: searchParams.get('location') || undefined,
      supplier: searchParams.get('supplier') || undefined,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
    }

    // Check branch access if branchId is specified
    if (filters.branchId) {
      const branchCheck = checkBranchAccess(user, filters.branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await getInventory(filters, pagination, user.role, user.branchId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get inventory API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch inventory' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/inventory - Create new inventory item
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const validation = createInventorySchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    // Check branch access
    const branchCheck = checkBranchAccess(user, data.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    const result = await createInventory(data, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Create inventory API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create inventory item' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
