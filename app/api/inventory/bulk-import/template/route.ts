import { NextRequest, NextResponse } from 'next/server'

// Sample data for templates
const SAMPLE_DATA = [
  {
    productName: 'Wireless Bluetooth Headphones',
    sku: 'WBH-001',
    category: 'Electronics',
    stock: 50,
    cost: 25.99,
    price: 49.99,
    minStockLevel: 10,
    supplier: 'TechSupplier Inc',
    location: 'Warehouse A',
    description: 'High-quality wireless headphones with noise cancellation',
    barcode: '1234567890123',
    unit: 'pcs',
    status: 'active'
  },
  {
    productName: 'Organic Coffee Beans',
    sku: 'OCB-002',
    category: 'Food & Beverages',
    stock: 100,
    cost: 8.50,
    price: 15.99,
    minStockLevel: 20,
    supplier: 'Coffee Roasters Ltd',
    location: 'Storage Room B',
    description: 'Premium organic coffee beans from Ethiopia',
    barcode: '2345678901234',
    unit: 'kg',
    status: 'active'
  },
  {
    productName: 'Cotton T-Shirt',
    sku: 'CTS-003',
    category: 'Clothing',
    stock: 75,
    cost: 5.00,
    price: 19.99,
    minStockLevel: 15,
    supplier: 'Fashion Wholesale',
    location: 'Display Area',
    description: '100% cotton comfortable t-shirt available in multiple sizes',
    barcode: '3456789012345',
    unit: 'pcs',
    status: 'active'
  }
]

/**
 * GET /api/inventory/bulk-import/template - Download import templates
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') || 'csv'

    if (format === 'csv') {
      // Generate CSV template
      const headers = Object.keys(SAMPLE_DATA[0])
      const csvHeaders = headers.join(',')
      
      const csvRows = SAMPLE_DATA.map(item => 
        headers.map(header => {
          const value = item[header as keyof typeof item]
          // Escape values that contain commas or quotes
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`
          }
          return value
        }).join(',')
      )

      const csvContent = [csvHeaders, ...csvRows].join('\n')

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="inventory-template.csv"'
        }
      })
    } else if (format === 'json') {
      // Generate JSON template
      const jsonContent = JSON.stringify(SAMPLE_DATA, null, 2)

      return new NextResponse(jsonContent, {
        headers: {
          'Content-Type': 'application/json',
          'Content-Disposition': 'attachment; filename="inventory-template.json"'
        }
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid format. Use csv or json.' },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error('Template download error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to generate template' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/inventory/bulk-import/template/fields - Get field information
 */
export async function POST(request: NextRequest) {
  try {
    const fieldInfo = {
      required: [
        {
          name: 'productName',
          type: 'string',
          description: 'Name of the product',
          example: 'Wireless Bluetooth Headphones'
        },
        {
          name: 'sku',
          type: 'string',
          description: 'Stock Keeping Unit (unique identifier)',
          example: 'WBH-001'
        },
        {
          name: 'category',
          type: 'string',
          description: 'Product category',
          example: 'Electronics'
        },
        {
          name: 'stock',
          type: 'number',
          description: 'Current stock quantity',
          example: 50
        },
        {
          name: 'cost',
          type: 'number',
          description: 'Cost price per unit',
          example: 25.99
        },
        {
          name: 'price',
          type: 'number',
          description: 'Selling price per unit',
          example: 49.99
        }
      ],
      optional: [
        {
          name: 'minStockLevel',
          type: 'number',
          description: 'Minimum stock level for alerts',
          example: 10,
          default: 10
        },
        {
          name: 'supplier',
          type: 'string',
          description: 'Supplier name',
          example: 'TechSupplier Inc',
          default: 'Unknown'
        },
        {
          name: 'location',
          type: 'string',
          description: 'Storage location',
          example: 'Warehouse A',
          default: 'Main Warehouse'
        },
        {
          name: 'description',
          type: 'string',
          description: 'Product description',
          example: 'High-quality wireless headphones',
          default: ''
        },
        {
          name: 'barcode',
          type: 'string',
          description: 'Product barcode',
          example: '1234567890123',
          default: ''
        },
        {
          name: 'unit',
          type: 'string',
          description: 'Unit of measurement',
          example: 'pcs',
          default: 'pcs'
        },
        {
          name: 'status',
          type: 'string',
          description: 'Product status (active/inactive)',
          example: 'active',
          default: 'active'
        }
      ],
      validation: {
        sku: 'Must be unique and contain only letters, numbers, hyphens, and underscores',
        stock: 'Must be a non-negative integer',
        cost: 'Must be a positive number',
        price: 'Must be a positive number',
        minStockLevel: 'Must be a non-negative integer',
        status: 'Must be either "active" or "inactive"'
      }
    }

    return NextResponse.json({
      success: true,
      data: fieldInfo
    })

  } catch (error) {
    console.error('Field info error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get field information' },
      { status: 500 }
    )
  }
}
