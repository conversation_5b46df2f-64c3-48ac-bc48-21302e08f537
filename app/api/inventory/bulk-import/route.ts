import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import Inventory from '@/lib/database/models/Inventory'
import Shop from '@/lib/database/models/Shop'
import Papa from 'papaparse'

interface ImportError {
  row: number
  field: string
  message: string
  data?: any
}

interface ImportResult {
  success: boolean
  total: number
  imported: number
  failed: number
  errors: ImportError[]
}

// Required fields for inventory items
const REQUIRED_FIELDS = [
  'productName',
  'sku',
  'category',
  'stock',
  'cost',
  'price'
]

// Optional fields with defaults
const OPTIONAL_FIELDS = {
  minStockLevel: 10,
  supplier: 'Unknown',
  location: 'Main Warehouse',
  description: '',
  barcode: '',
  unit: 'pcs',
  status: 'active'
}

/**
 * POST /api/inventory/bulk-import - Handle bulk import of inventory items
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to database
    await connectDB()

    // Verify authentication
    const authResult = await authMiddleware(request, ['overall_admin', 'branch_manager'])
    if (!authResult.success) {
      return authResult.response || NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { user } = authResult

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      )
    }

    // Validate file type
    const validTypes = ['text/csv', 'application/json']
    const isCSV = file.name.toLowerCase().endsWith('.csv') || file.type === 'text/csv'
    const isJSON = file.name.toLowerCase().endsWith('.json') || file.type === 'application/json'

    if (!isCSV && !isJSON) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Please upload CSV or JSON files only.' },
        { status: 400 }
      )
    }

    // Read file content
    const fileContent = await file.text()
    let data: any[] = []

    try {
      if (isCSV) {
        // Parse CSV
        const parseResult = Papa.parse(fileContent, {
          header: true,
          skipEmptyLines: true,
          transformHeader: (header) => header.trim()
        })

        if (parseResult.errors.length > 0) {
          return NextResponse.json(
            { success: false, error: 'CSV parsing error: ' + parseResult.errors[0].message },
            { status: 400 }
          )
        }

        data = parseResult.data
      } else {
        // Parse JSON
        const jsonData = JSON.parse(fileContent)
        data = Array.isArray(jsonData) ? jsonData : [jsonData]
      }
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Failed to parse file content' },
        { status: 400 }
      )
    }

    if (data.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No data found in file' },
        { status: 400 }
      )
    }

    // Get user's branch
    let userBranch = null
    if (user.branchId) {
      userBranch = await Shop.findById(user.branchId)
      if (!userBranch) {
        return NextResponse.json(
          { success: false, error: 'User branch not found' },
          { status: 400 }
        )
      }
    }

    // Process import
    const result = await processImport(data, user, userBranch)

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('Bulk import error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function processImport(data: any[], user: any, userBranch: any): Promise<ImportResult> {
  const result: ImportResult = {
    success: true,
    total: data.length,
    imported: 0,
    failed: 0,
    errors: []
  }

  for (let i = 0; i < data.length; i++) {
    const row = data[i]
    const rowNumber = i + 1

    try {
      // Validate required fields
      const validationErrors = validateRow(row, rowNumber)
      if (validationErrors.length > 0) {
        result.errors.push(...validationErrors)
        result.failed++
        continue
      }

      // Prepare inventory item data
      const branchId = userBranch ? userBranch._id.toString() : (row.branchId || null)
      const branchName = userBranch ? userBranch.name : (row.branchName || 'Unknown Branch')

      const inventoryData = {
        productId: row.productId || `prod_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        productName: row.productName?.trim(),
        sku: row.sku?.trim(),
        branchId,
        branchName,
        stock: parseInt(row.stock) || 0,
        cost: parseFloat(row.cost) || 0,
        minStockLevel: parseInt(row.minStockLevel) || OPTIONAL_FIELDS.minStockLevel,
        maxStockLevel: parseInt(row.maxStockLevel) || (parseInt(row.minStockLevel) || OPTIONAL_FIELDS.minStockLevel) * 5,
        supplier: row.supplier?.trim() || OPTIONAL_FIELDS.supplier,
        location: row.location?.trim() || OPTIONAL_FIELDS.location,
        lastRestocked: new Date()
      }

      // Check for duplicate SKU
      const existingItem = await Inventory.findOne({ sku: inventoryData.sku })
      if (existingItem) {
        result.errors.push({
          row: rowNumber,
          field: 'sku',
          message: `SKU '${inventoryData.sku}' already exists`,
          data: row
        })
        result.failed++
        continue
      }

      // Create inventory item
      await Inventory.create(inventoryData)
      result.imported++

    } catch (error) {
      console.error(`Error processing row ${rowNumber}:`, error)
      result.errors.push({
        row: rowNumber,
        field: 'general',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        data: row
      })
      result.failed++
    }
  }

  result.success = result.imported > 0
  return result
}

function validateRow(row: any, rowNumber: number): ImportError[] {
  const errors: ImportError[] = []

  // Check required fields
  for (const field of REQUIRED_FIELDS) {
    if (!row[field] || String(row[field]).trim() === '') {
      errors.push({
        row: rowNumber,
        field,
        message: `Required field '${field}' is missing or empty`
      })
    }
  }

  // Validate data types and formats
  if (row.stock && isNaN(parseInt(row.stock))) {
    errors.push({
      row: rowNumber,
      field: 'stock',
      message: 'Stock must be a valid number'
    })
  }

  if (row.cost && isNaN(parseFloat(row.cost))) {
    errors.push({
      row: rowNumber,
      field: 'cost',
      message: 'Cost must be a valid number'
    })
  }

  if (row.price && isNaN(parseFloat(row.price))) {
    errors.push({
      row: rowNumber,
      field: 'price',
      message: 'Price must be a valid number'
    })
  }

  if (row.minStockLevel && isNaN(parseInt(row.minStockLevel))) {
    errors.push({
      row: rowNumber,
      field: 'minStockLevel',
      message: 'Minimum stock level must be a valid number'
    })
  }

  // Validate SKU format (alphanumeric, hyphens, underscores)
  if (row.sku && !/^[a-zA-Z0-9_-]+$/.test(row.sku)) {
    errors.push({
      row: rowNumber,
      field: 'sku',
      message: 'SKU can only contain letters, numbers, hyphens, and underscores'
    })
  }

  return errors
}
