import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getActivityLogs, createActivityLog, serviceUtils } from '@/services/backend'

// GET /api/activity-logs - Get activity logs with filtering
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const pagination = serviceUtils.validatePagination(
      parseInt(searchParams.get('page') || '1'),
      parseInt(searchParams.get('limit') || '20')
    )

    const filters = {
      type: searchParams.get('type') || undefined,
      userId: searchParams.get('userId') || undefined,
      branchId: searchParams.get('branchId') || undefined,
      search: serviceUtils.sanitizeSearchQuery(searchParams.get('search') || undefined),
      startDate: searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined,
      endDate: searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined
    }

    // Check branch access if branchId is specified
    if (filters.branchId) {
      const branchCheck = checkBranchAccess(user, filters.branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await getActivityLogs(filters, pagination, user.role, user.branchId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get activity logs API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch activity logs' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/activity-logs - Create new activity log (for manual logging)
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const { type, description, metadata, branchId } = body

    // Validate required fields
    if (!type || !description) {
      return NextResponse.json(
        { success: false, error: 'Type and description are required' },
        { status: 400 }
      )
    }

    // Check branch access if branchId is provided
    if (branchId) {
      const branchCheck = checkBranchAccess(user, branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const data = {
      type,
      description,
      metadata: metadata || {},
      branchId: branchId || user.branchId
    }

    const result = await createActivityLog(data, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Create activity log API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create activity log' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
