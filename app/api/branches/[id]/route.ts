import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth/middleware'
import { getShopById, updateShop, deleteShop } from '@/services/backend'
import { z } from 'zod'

const updateBranchSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  location: z.string().min(1).max(100).optional(),
  country: z.string().min(1).max(50).optional(),
  region: z.string().min(1).max(50).optional(),
  managerId: z.string().min(1).optional(),
  description: z.string().min(1).max(500).optional(),
  address: z.string().min(1).max(200).optional(),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/).optional(),
  email: z.string().email().optional(),
  status: z.enum(['Active', 'Inactive', 'Opening Soon', 'Maintenance']).optional(),
  operatingHours: z.object({
    open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    timezone: z.string()
  }).optional(),
  coordinates: z.object({
    lat: z.number().min(-90).max(90),
    lng: z.number().min(-180).max(180)
  }).optional(),
  image: z.string().url().optional()
})

// GET /api/branches/[id] - Get branch by ID
export const GET = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    console.log('=== BRANCH API GET BY ID START ===')
    const branchId = (await params).id
    console.log('API: Getting branch with ID:', branchId)
    console.log('API: User:', user.userId, user.username)

    const result = await getShopById(branchId)
    console.log('API: Backend service result:', {
      success: result.success,
      hasData: !!result.data,
      error: result.error
    })

    if (result.success) {
      return NextResponse.json(result, { status: 200 })
    } else {
      console.log('API: Branch not found:', result.error)
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 404 }
      )
    }
  } catch (error) {
    console.error('=== BRANCH API GET BY ID ERROR ===')
    console.error('API: Exception:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PUT /api/branches/[id] - Update branch
export const PUT = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    console.log('=== BRANCH API UPDATE START ===')
    const branchId = (await params).id
    console.log('API: Updating branch with ID:', branchId)
    console.log('API: User:', user.userId, user.username)

    const body = await request.json()
    console.log('API: Received update data:', body)

    const validation = updateBranchSchema.safeParse(body)

    if (!validation.success) {
      console.log('API: Validation failed:', validation.error.errors)
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    console.log('API: Validated data:', validation.data)

    // Check permissions
    if (user.role === 'branch_manager') {
      // Branch managers can only update their own branch
      const branchResult = await getShopById(branchId)
      if (!branchResult.success || !branchResult.data) {
        return NextResponse.json(
          { success: false, error: 'Branch not found' },
          { status: 404 }
        )
      }

      if (branchResult.data.managerId !== user.userId) {
        console.log('API: Access denied - branch manager can only update own branch')
        return NextResponse.json(
          { success: false, error: 'You can only update your own branch' },
          { status: 403 }
        )
      }
    }

    const result = await updateShop(branchId, validation.data, user.userId, user.username)
    console.log('API: Update result:', {
      success: result.success,
      hasData: !!result.data,
      error: result.error
    })

    if (result.success) {
      console.log('API: Branch updated successfully')
      return NextResponse.json(result, { status: 200 })
    } else {
      console.log('API: Branch update failed:', result.error)
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('=== BRANCH API UPDATE ERROR ===')
    console.error('API: Exception during branch update:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// DELETE /api/branches/[id] - Delete branch
export const DELETE = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    console.log('=== BRANCH API DELETE START ===')
    const branchId = (await params).id
    console.log('API: Deleting branch with ID:', branchId)
    console.log('API: User:', user.userId, user.username)

    // Only overall_admin can delete branches
    if (user.role !== 'overall_admin') {
      console.log('API: Access denied - user role:', user.role)
      return NextResponse.json(
        { success: false, error: 'Only overall administrators can delete branches' },
        { status: 403 }
      )
    }

    const result = await deleteShop(branchId, user.userId, user.username)
    console.log('API: Delete result:', {
      success: result.success,
      error: result.error
    })

    if (result.success) {
      console.log('API: Branch deleted successfully')
      return NextResponse.json(result, { status: 200 })
    } else {
      console.log('API: Branch deletion failed:', result.error)
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('=== BRANCH API DELETE ERROR ===')
    console.error('API: Exception during branch deletion:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin']
})
