import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth/middleware'
import { getShops, createShop } from '@/services/backend'
import { z } from 'zod'

const createBranchSchema = z.object({
  name: z.string().min(1).max(100),
  location: z.string().min(1).max(100),
  country: z.string().min(1).max(50),
  region: z.string().min(1).max(50),
  managerId: z.string().optional(), // Optional for new branches without managers
  description: z.string().min(1).max(500),
  address: z.string().min(1).max(200),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/),
  email: z.string().email(),
  operatingHours: z.object({
    open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    timezone: z.string().default('Africa/Blantyre')
  }),
  coordinates: z.object({
    lat: z.number().min(-90).max(90),
    lng: z.number().min(-180).max(180)
  }).optional(),
  image: z.string().url().optional()
})

// GET /api/branches - Get all branches
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    console.log('=== BRANCH API GET START ===')
    console.log('API: User:', user.userId, user.username)

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const country = searchParams.get('country') || ''
    const region = searchParams.get('region') || ''
    const status = searchParams.get('status') || ''

    console.log('API: Query params:', { page, limit, search, country, region, status })

    const filters = {
      search,
      country,
      region,
      status,
      sortBy: 'name',
      sortOrder: 'asc' as const
    }

    const result = await getShops(filters, { page, limit }, user.role, user.branchId)
    console.log('API: Backend service result:', {
      success: result.success,
      dataLength: result.data?.length || 0,
      pagination: result.pagination
    })

    if (result.success) {
      return NextResponse.json(result, { status: 200 })
    } else {
      console.log('API: Backend service failed:', result.error)
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('=== BRANCH API GET ERROR ===')
    console.error('API: Exception:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/branches - Create new branch
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    console.log('=== BRANCH API CREATE START ===')
    console.log('API: User:', user.userId, user.username)

    const body = await request.json()
    console.log('API: Received branch data:', body)

    const validation = createBranchSchema.safeParse(body)

    if (!validation.success) {
      console.log('API: Validation failed:', validation.error.errors)
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    console.log('API: Validated data:', validation.data)

    // Only overall_admin can create branches
    if (user.role !== 'overall_admin') {
      console.log('API: Access denied - user role:', user.role)
      return NextResponse.json(
        { success: false, error: 'Only overall administrators can create branches' },
        { status: 403 }
      )
    }

    console.log('=== API ROUTE CREATE BRANCH START ===')
    console.log('API: Creating branch with user:', user.userId, user.username)
    console.log('API: Final validated data being sent to service:', JSON.stringify(validation.data, null, 2))

    const result = await createShop(validation.data, user.userId, user.username)
    console.log('API: Branch creation result:', {
      success: result.success,
      hasData: !!result.data,
      error: result.error
    })

    if (result.success) {
      console.log('API: Branch created successfully')
      return NextResponse.json(result, { status: 201 })
    } else {
      console.log('API: Branch creation failed:', result.error)
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('=== BRANCH API CREATE ERROR ===')
    console.error('API: Exception during branch creation:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin']
})
