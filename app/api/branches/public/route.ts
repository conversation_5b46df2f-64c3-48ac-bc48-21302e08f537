import { NextRequest, NextResponse } from 'next/server'
import { getShops } from '@/services/backend'

// GET /api/branches/public - Get all active branches for customers
export async function GET(request: NextRequest) {
  try {
    console.log('=== PUBLIC BRANCHES API START ===')

    const { searchParams } = new URL(request.url)
    const country = searchParams.get('country') || ''
    const region = searchParams.get('region') || ''

    console.log('API: Query params:', { country, region })

    const filters = {
      search: '',
      country,
      region,
      status: 'Active', // Only show active branches to customers
      sortBy: 'name',
      sortOrder: 'asc' as const
    }

    // Get all active branches without pagination for customer selection
    const result = await getShops(filters, { page: 1, limit: 100 }, 'customer', undefined)
    console.log('API: Backend service result:', {
      success: result.success,
      dataLength: result.data?.length || 0
    })

    if (result.success) {
      // Transform data for customer use - only include necessary fields
      const publicBranches = result.data?.map(branch => ({
        id: branch._id,
        name: branch.name,
        location: branch.location,
        country: branch.country,
        region: branch.region,
        address: branch.address,
        phone: branch.phone,
        email: branch.email,
        description: branch.description,
        image: branch.image,
        operatingHours: branch.operatingHours,
        coordinates: branch.coordinates
      })) || []

      return NextResponse.json({
        success: true,
        data: publicBranches,
        total: publicBranches.length
      }, { status: 200 })
    } else {
      console.log('API: Backend service failed:', result.error)
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('=== PUBLIC BRANCHES API ERROR ===')
    console.error('API: Exception:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
