import { NextRequest, NextResponse } from 'next/server'

// GET /api - API documentation and health check
export async function GET(request: NextRequest) {
  const baseUrl = request.nextUrl.origin

  const apiDocumentation = {
    name: 'Fathahitech API',
    version: '1.0.0',
    description: 'Multi-branch technology retail management system API',
    baseUrl,
    authentication: {
      type: 'Bearer Token (JWT)',
      loginEndpoint: `${baseUrl}/api/auth/login`,
      refreshEndpoint: `${baseUrl}/api/auth/refresh`
    },
    endpoints: {
      authentication: {
        'POST /api/auth/login': 'User login',
        'POST /api/auth/register': 'User registration',
        'POST /api/auth/logout': 'User logout',
        'POST /api/auth/refresh': 'Refresh access token',
        'GET /api/auth/me': 'Get current user profile',
        'PUT /api/auth/me': 'Update user profile',
        'POST /api/auth/change-password': 'Change password',
        'POST /api/auth/reset-password': 'Request password reset',
        'PUT /api/auth/reset-password': 'Confirm password reset'
      },
      shops: {
        'GET /api/shops': 'Get all shops with pagination and filtering',
        'POST /api/shops': 'Create new shop (admin only)',
        'GET /api/shops/[id]': 'Get shop by ID',
        'PUT /api/shops/[id]': 'Update shop',
        'DELETE /api/shops/[id]': 'Delete shop (admin only)'
      },
      products: {
        'GET /api/products': 'Get all products with pagination, search, and filtering',
        'POST /api/products': 'Create new product',
        'GET /api/products/[id]': 'Get product by ID',
        'PUT /api/products/[id]': 'Update product',
        'DELETE /api/products/[id]': 'Delete/deactivate product'
      },
      categories: {
        'GET /api/categories': 'Get all product categories with pagination and filtering',
        'POST /api/categories': 'Create new category (admin only)',
        'GET /api/categories/[id]': 'Get category by ID',
        'PUT /api/categories/[id]': 'Update category (admin only)',
        'DELETE /api/categories/[id]': 'Delete/deactivate category (admin only)'
      },
      orders: {
        'GET /api/orders': 'Get all orders with pagination and filtering',
        'POST /api/orders': 'Create new order',
        'GET /api/orders/[id]': 'Get order by ID',
        'PUT /api/orders/[id]': 'Update order status/details',
        'DELETE /api/orders/[id]': 'Cancel order'
      },
      customers: {
        'GET /api/customers': 'Get all customers with pagination and search',
        'POST /api/customers': 'Create new customer',
        'GET /api/customers/[id]': 'Get customer by ID with order history',
        'PUT /api/customers/[id]': 'Update customer details',
        'DELETE /api/customers/[id]': 'Deactivate customer'
      },
      analytics: {
        'GET /api/analytics/dashboard': 'Get dashboard metrics and KPIs',
        'GET /api/analytics/sales': 'Get detailed sales analytics and reports'
      },
      activityLogs: {
        'GET /api/activity-logs': 'Get activity logs with filtering',
        'POST /api/activity-logs': 'Create manual activity log',
        'GET /api/activity-logs/stats': 'Get activity log statistics'
      },
      notifications: {
        'GET /api/notifications': 'Get all notifications with pagination and filtering',
        'POST /api/notifications': 'Create new notification',
        'GET /api/notifications/[id]': 'Get notification by ID',
        'PUT /api/notifications/[id]': 'Update notification',
        'DELETE /api/notifications/[id]': 'Delete notification',
        'POST /api/notifications/send': 'Send notification to specific user',
        'POST /api/notifications/broadcast': 'Broadcast notification to all users',
        'POST /api/notifications/system-alert': 'Send system alert',
        'POST /api/notifications/email': 'Send email notification',
        'POST /api/notifications/sms': 'Send SMS notification'
      }
    },
    queryParameters: {
      pagination: {
        page: 'Page number (default: 1)',
        limit: 'Items per page (default: 10)'
      },
      filtering: {
        search: 'Text search query',
        branchId: 'Filter by branch ID',
        category: 'Filter by product category',
        status: 'Filter by status',
        startDate: 'Filter from date (ISO format)',
        endDate: 'Filter to date (ISO format)'
      },
      sorting: {
        sortBy: 'Field to sort by',
        sortOrder: 'Sort order (asc/desc)'
      }
    },
    responseFormat: {
      success: {
        success: true,
        data: 'Response data',
        pagination: 'Pagination info (for list endpoints)'
      },
      error: {
        success: false,
        error: 'Error message',
        details: 'Validation errors (optional)'
      }
    },
    statusCodes: {
      200: 'Success',
      201: 'Created',
      400: 'Bad Request - Invalid input',
      401: 'Unauthorized - Authentication required',
      403: 'Forbidden - Insufficient permissions',
      404: 'Not Found',
      429: 'Too Many Requests - Rate limited',
      500: 'Internal Server Error'
    },
    roles: {
      overall_admin: 'Full system access across all branches',
      branch_manager: 'Access limited to assigned branch'
    },
    features: {
      authentication: 'JWT-based authentication with refresh tokens',
      authorization: 'Role-based access control with branch restrictions',
      rateLimit: 'Rate limiting on sensitive endpoints',
      activityLogging: 'Comprehensive audit trail',
      pagination: 'Cursor-based pagination for large datasets',
      search: 'Full-text search capabilities',
      filtering: 'Advanced filtering options',
      analytics: 'Real-time analytics and reporting'
    }
  }

  return NextResponse.json(apiDocumentation, {
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
    }
  })
}

// Health check endpoint
export async function HEAD(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'X-API-Status': 'healthy',
      'X-API-Version': '1.0.0'
    }
  })
}
