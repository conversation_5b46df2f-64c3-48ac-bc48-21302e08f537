import { NextRequest, NextResponse } from 'next/server'
import { seedDatabase } from '@/lib/database/seed'

export async function POST(request: NextRequest) {
  try {
    // Only allow seeding in development environment
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { success: false, error: 'Seeding is not allowed in production' },
        { status: 403 }
      )
    }

    console.log('🌱 Starting database seeding via API...')
    
    const result = await seedDatabase()
    
    if (result.success) {
      console.log('✅ Database seeding completed successfully!')
      return NextResponse.json({ 
        success: true, 
        message: 'Database seeding completed successfully!' 
      })
    } else {
      console.error('❌ Database seeding failed:', result.error)
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('❌ Database seeding failed:', error)
    return NextResponse.json(
      { success: false, error: 'Database seeding failed' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Use POST method to seed the database',
    note: 'Only available in development environment'
  })
}
