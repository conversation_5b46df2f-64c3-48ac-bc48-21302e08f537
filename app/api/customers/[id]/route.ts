import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getCustomerById, updateCustomer, deactivateCustomer } from '@/services/backend'
import { z } from 'zod'

const addressSchema = z.object({
  street: z.string().min(1).max(200),
  city: z.string().min(1).max(50),
  region: z.string().min(1).max(50),
  country: z.string().min(1).max(50),
  postalCode: z.string().min(1).max(20)
})

const updateCustomerSchema = z.object({
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  email: z.string().email().optional(),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/).optional(),
  address: addressSchema.optional(),
  preferredBranch: z.string().optional(),
  loyaltyPoints: z.number().min(0).optional(),
  isActive: z.boolean().optional()
})

// GET /api/customers/[id] - Get customer by ID
export const GET = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const customerId = params.id
    const includeOrders = new URL(request.url).searchParams.get('includeOrders') === 'true'

    const result = await getCustomerById(customerId, includeOrders, user.role, user.branchId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Customer not found' ? 404 : 500 }
      )
    }

    // Check branch access if customer has preferred branch
    if (result.data!.preferredBranch) {
      const branchCheck = checkBranchAccess(user, result.data!.preferredBranch)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get customer API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch customer' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PUT /api/customers/[id] - Update customer
export const PUT = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const customerId = params.id
    const body = await request.json()
    const validation = updateCustomerSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    // Get current customer to check branch access
    const currentCustomer = await getCustomerById(customerId)
    if (!currentCustomer.success) {
      return NextResponse.json(
        { success: false, error: currentCustomer.error },
        { status: currentCustomer.error === 'Customer not found' ? 404 : 500 }
      )
    }

    // Check branch access
    if (currentCustomer.data!.preferredBranch) {
      const branchCheck = checkBranchAccess(user, currentCustomer.data!.preferredBranch)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    // Check branch access for new preferred branch if being updated
    if (data.preferredBranch) {
      const branchCheck = checkBranchAccess(user, data.preferredBranch)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await updateCustomer(customerId, data, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Update customer API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update customer' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// DELETE /api/customers/[id] - Deactivate customer
export const DELETE = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const customerId = params.id

    // Get current customer to check branch access
    const currentCustomer = await getCustomerById(customerId)
    if (!currentCustomer.success) {
      return NextResponse.json(
        { success: false, error: currentCustomer.error },
        { status: currentCustomer.error === 'Customer not found' ? 404 : 500 }
      )
    }

    // Check branch access
    if (currentCustomer.data!.preferredBranch) {
      const branchCheck = checkBranchAccess(user, currentCustomer.data!.preferredBranch)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await deactivateCustomer(customerId, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Delete customer API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to deactivate customer' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
