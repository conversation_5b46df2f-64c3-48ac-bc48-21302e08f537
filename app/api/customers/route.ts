import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { Customer, Order, connectDB, logActivity } from '@/lib/database'
import { z } from 'zod'

const addressSchema = z.object({
  street: z.string().min(1).max(200),
  city: z.string().min(1).max(50),
  region: z.string().min(1).max(50),
  country: z.string().min(1).max(50),
  postalCode: z.string().min(1).max(20)
})

const createCustomerSchema = z.object({
  firstName: z.string().min(1).max(50),
  lastName: z.string().min(1).max(50),
  email: z.string().email(),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/),
  address: addressSchema,
  preferredBranch: z.string().optional()
})

// GET /api/customers - Get all customers
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search')
    const preferredBranch = searchParams.get('preferredBranch')
    const isActive = searchParams.get('isActive')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Build filter query
    const filter: any = {}
    
    if (search) {
      filter.$text = { $search: search }
    }

    if (isActive !== null && isActive !== undefined) {
      filter.isActive = isActive === 'true'
    }

    // Branch access control
    if (user.role === 'branch_manager' && user.branchId) {
      filter.preferredBranch = user.branchId
    } else if (preferredBranch) {
      const branchCheck = checkBranchAccess(user, preferredBranch)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
      filter.preferredBranch = preferredBranch
    }

    const skip = (page - 1) * limit
    const sort: any = {}
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1

    const [customers, total] = await Promise.all([
      Customer.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Customer.countDocuments(filter)
    ])

    return NextResponse.json({
      success: true,
      data: customers,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get customers error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch customers' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/customers - Create new customer
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    await connectDB()

    const body = await request.json()
    const validation = createCustomerSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    // Check if customer already exists
    const existingCustomer = await Customer.findOne({
      $or: [
        { email: data.email },
        { phone: data.phone }
      ]
    })

    if (existingCustomer) {
      return NextResponse.json(
        {
          success: false,
          error: existingCustomer.email === data.email 
            ? 'Email already registered' 
            : 'Phone number already registered'
        },
        { status: 400 }
      )
    }

    // Check preferred branch access
    if (data.preferredBranch) {
      const branchCheck = checkBranchAccess(user, data.preferredBranch)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    // Create customer
    const customer = await Customer.create({
      ...data,
      totalOrders: 0,
      totalSpent: 0,
      loyaltyPoints: 0,
      isActive: true
    })

    // Log activity
    await logActivity({
      type: 'User',
      description: `New customer "${customer.firstName} ${customer.lastName}" created`,
      userId: user.userId,
      userName: user.username,
      branchId: data.preferredBranch,
      metadata: { 
        customerId: customer._id.toString(),
        customerName: `${customer.firstName} ${customer.lastName}`,
        email: customer.email
      }
    })

    return NextResponse.json({
      success: true,
      data: customer
    }, { status: 201 })
  } catch (error) {
    console.error('Create customer error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create customer' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
