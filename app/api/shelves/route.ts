import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import Shelf from '@/lib/database/models/Shelf'
import Warehouse from '@/lib/database/models/Warehouse'
import { z } from 'zod'

// Validation schemas
const createShelfSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name cannot exceed 100 characters'),
  warehouseId: z.string().min(1, 'Warehouse ID is required'),
  section: z.string().min(1, 'Section is required').max(10, 'Section cannot exceed 10 characters')
    .regex(/^[A-Z0-9]+$/, 'Section must contain only uppercase letters and numbers'),
  row: z.string().min(1, 'Row is required').max(10, 'Row cannot exceed 10 characters'),
  position: z.string().min(1, 'Position is required').max(10, 'Position cannot exceed 10 characters')
    .regex(/^[A-Z0-9]+$/, 'Position must contain only uppercase letters and numbers'),
  level: z.number().min(1, 'Level must be at least 1').max(10, 'Level cannot exceed 10'),
  description: z.string().max(300, 'Description cannot exceed 300 characters').optional(),
  capacity: z.number().min(1, 'Capacity must be greater than 0'),
  dimensions: z.object({
    width: z.number().min(0.1, 'Width must be greater than 0'),
    height: z.number().min(0.1, 'Height must be greater than 0'),
    depth: z.number().min(0.1, 'Depth must be greater than 0'),
    unit: z.enum(['cm', 'm']).default('cm')
  }),
  weightLimit: z.number().min(1, 'Weight limit must be greater than 0'),
  shelfType: z.enum(['standard', 'refrigerated', 'hazmat', 'fragile', 'bulk']).default('standard'),
  accessLevel: z.enum(['ground', 'ladder', 'forklift', 'crane']).default('ground'),
  notes: z.string().max(500, 'Notes cannot exceed 500 characters').optional(),
  barcode: z.string().max(50, 'Barcode cannot exceed 50 characters').optional(),
  qrCode: z.string().max(100, 'QR code cannot exceed 100 characters').optional()
})

const shelfFiltersSchema = z.object({
  search: z.string().optional(),
  warehouseId: z.string().optional(),
  section: z.string().optional(),
  shelfType: z.enum(['standard', 'refrigerated', 'hazmat', 'fragile', 'bulk']).optional(),
  accessLevel: z.enum(['ground', 'ladder', 'forklift', 'crane']).optional(),
  isActive: z.boolean().optional(),
  isReserved: z.boolean().optional(),
  availabilityStatus: z.enum(['available', 'nearly_full', 'full', 'reserved', 'inactive']).optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20)
})

// GET /api/shelves - Get all shelves with filtering
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)
    const validation = shelfFiltersSchema.safeParse({
      search: searchParams.get('search') || undefined,
      warehouseId: searchParams.get('warehouseId') || undefined,
      section: searchParams.get('section') || undefined,
      shelfType: searchParams.get('shelfType') || undefined,
      accessLevel: searchParams.get('accessLevel') || undefined,
      isActive: searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined,
      isReserved: searchParams.get('isReserved') ? searchParams.get('isReserved') === 'true' : undefined,
      availabilityStatus: searchParams.get('availabilityStatus') || undefined,
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20')
    })

    if (!validation.success) {
      return NextResponse.json(
        { success: false, error: 'Invalid query parameters', details: validation.error.errors },
        { status: 400 }
      )
    }

    const { search, warehouseId, section, shelfType, accessLevel, isActive, isReserved, availabilityStatus, page, limit } = validation.data

    // Build query based on user role and filters
    const query: any = {}

    // Role-based filtering - get accessible warehouses first
    if (user.role === 'branch_manager' && user.branchId) {
      const accessibleWarehouses = await Warehouse.find({
        $or: [
          { branchId: user.branchId },
          { type: { $in: ['main', 'distribution'] } }
        ],
        isActive: true
      }).select('_id').lean()

      const warehouseIds = accessibleWarehouses.map(w => w._id.toString())
      query.warehouseId = { $in: warehouseIds }
    }

    // Apply filters
    if (search) {
      query.$text = { $search: search }
    }

    if (warehouseId) {
      // Check warehouse access
      const warehouse = await Warehouse.findById(warehouseId)
      if (!warehouse) {
        return NextResponse.json(
          { success: false, error: 'Warehouse not found' },
          { status: 404 }
        )
      }

      if (user.role === 'branch_manager') {
        if (warehouse.branchId && warehouse.branchId !== user.branchId) {
          if (!['main', 'distribution'].includes(warehouse.type)) {
            return NextResponse.json(
              { success: false, error: 'Access denied to this warehouse' },
              { status: 403 }
            )
          }
        }
      }

      query.warehouseId = warehouseId
    }

    if (section) {
      query.section = section.toUpperCase()
    }

    if (shelfType) {
      query.shelfType = shelfType
    }

    if (accessLevel) {
      query.accessLevel = accessLevel
    }

    if (isActive !== undefined) {
      query.isActive = isActive
    }

    if (isReserved !== undefined) {
      query.isReserved = isReserved
    }

    // Handle availability status filter
    if (availabilityStatus) {
      switch (availabilityStatus) {
        case 'inactive':
          query.isActive = false
          break
        case 'reserved':
          query.isReserved = true
          query.isActive = true
          break
        case 'full':
          query.isActive = true
          query.isReserved = false
          query.$expr = { $gte: ['$currentOccupancy', '$capacity'] }
          break
        case 'nearly_full':
          query.isActive = true
          query.isReserved = false
          query.$expr = { 
            $and: [
              { $lt: ['$currentOccupancy', '$capacity'] },
              { $gt: ['$currentOccupancy', { $multiply: ['$capacity', 0.8] }] }
            ]
          }
          break
        case 'available':
          query.isActive = true
          query.isReserved = false
          query.$expr = { $lte: ['$currentOccupancy', { $multiply: ['$capacity', 0.8] }] }
          break
      }
    }

    // Execute query with pagination
    const skip = (page - 1) * limit
    const [shelves, total] = await Promise.all([
      Shelf.find(query)
        .sort({ warehouseCode: 1, section: 1, row: 1, position: 1, level: 1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Shelf.countDocuments(query)
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: shelves,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error('Get shelves API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch shelves' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/shelves - Create new shelf
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    await connectDB()

    const body = await request.json()
    console.log('📝 Shelf creation request body:', JSON.stringify(body, null, 2))

    const validation = createShelfSchema.safeParse(body)
    console.log('🔍 Shelf schema validation result:', validation.success)

    if (!validation.success) {
      console.log('❌ Shelf validation errors:', JSON.stringify(validation.error.errors, null, 2))
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: validation.error.errors },
        { status: 400 }
      )
    }

    const data = validation.data

    // Get warehouse and validate access
    const warehouse = await Warehouse.findById(data.warehouseId)
    if (!warehouse) {
      return NextResponse.json(
        { success: false, error: 'Warehouse not found' },
        { status: 404 }
      )
    }

    // Check warehouse access permissions
    if (user.role === 'branch_manager') {
      if (warehouse.branchId && warehouse.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Access denied to create shelves in this warehouse' },
          { status: 403 }
        )
      }
    }

    // Check for duplicate shelf position
    const existingShelf = await Shelf.findOne({
      warehouseId: data.warehouseId,
      section: data.section.toUpperCase(),
      row: data.row,
      position: data.position.toUpperCase(),
      level: data.level
    })

    if (existingShelf) {
      return NextResponse.json(
        { success: false, error: 'A shelf already exists at this position' },
        { status: 400 }
      )
    }

    // Generate shelf code
    const shelfCode = `${warehouse.code}-${data.section.toUpperCase()}${data.row}-${data.position.toUpperCase()}-L${data.level}`

    // Create shelf
    const shelf = new Shelf({
      ...data,
      code: shelfCode,
      section: data.section.toUpperCase(),
      position: data.position.toUpperCase(),
      warehouseName: warehouse.name,
      warehouseCode: warehouse.code,
      currentOccupancy: 0,
      currentWeight: 0
    })

    await shelf.save()

    return NextResponse.json({
      success: true,
      data: shelf,
      message: 'Shelf created successfully'
    }, { status: 201 })
  } catch (error) {
    console.error('Create shelf API error:', error)
    
    if (error.code === 11000) {
      return NextResponse.json(
        { success: false, error: 'Shelf code or position already exists' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create shelf' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
