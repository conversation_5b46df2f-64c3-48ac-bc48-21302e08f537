import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import She<PERSON> from '@/lib/database/models/Shelf'
import Warehouse from '@/lib/database/models/Warehouse'
import { z } from 'zod'

// Validation schema for updates
const updateShelfSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name cannot exceed 100 characters').optional(),
  description: z.string().max(300, 'Description cannot exceed 300 characters').optional(),
  capacity: z.number().min(1, 'Capacity must be greater than 0').optional(),
  currentOccupancy: z.number().min(0).optional(),
  dimensions: z.object({
    width: z.number().min(1, 'Width must be greater than 0'),
    height: z.number().min(1, 'Height must be greater than 0'),
    depth: z.number().min(1, 'Depth must be greater than 0')
  }).optional(),
  weightLimit: z.number().min(1, 'Weight limit must be greater than 0').optional(),
  currentWeight: z.number().min(0).optional(),
  shelfType: z.enum(['standard', 'refrigerated', 'hazmat', 'fragile', 'bulk']).optional(),
  accessLevel: z.enum(['ground', 'ladder', 'forklift', 'crane']).optional(),
  isActive: z.boolean().optional(),
  isReserved: z.boolean().optional(),
  reservedFor: z.string().max(100, 'Reserved for cannot exceed 100 characters').optional(),
  lastInventoryCheck: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  notes: z.string().max(500, 'Notes cannot exceed 500 characters').optional(),
  barcode: z.string().max(50, 'Barcode cannot exceed 50 characters').optional(),
  qrCode: z.string().max(100, 'QR code cannot exceed 100 characters').optional()
})

// GET /api/shelves/[id] - Get shelf by ID
export const GET = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const shelfId = params.id

    const shelf = await Shelf.findById(shelfId).lean()

    if (!shelf) {
      return NextResponse.json(
        { success: false, error: 'Shelf not found' },
        { status: 404 }
      )
    }

    // Check warehouse access permissions
    const warehouse = await Warehouse.findById(shelf.warehouseId)
    if (!warehouse) {
      return NextResponse.json(
        { success: false, error: 'Associated warehouse not found' },
        { status: 404 }
      )
    }

    if (user.role === 'branch_manager') {
      if (warehouse.branchId && warehouse.branchId !== user.branchId) {
        if (!['main', 'distribution'].includes(warehouse.type)) {
          return NextResponse.json(
            { success: false, error: 'Access denied to this shelf' },
            { status: 403 }
          )
        }
      }
    }

    // Calculate virtual fields
    const occupancyPercentage = shelf.capacity > 0 ? Math.round((shelf.currentOccupancy / shelf.capacity) * 100) : 0
    const weightPercentage = shelf.weightLimit > 0 ? Math.round((shelf.currentWeight / shelf.weightLimit) * 100) : 0
    
    let availabilityStatus = 'available'
    if (!shelf.isActive) availabilityStatus = 'inactive'
    else if (shelf.isReserved) availabilityStatus = 'reserved'
    else if (shelf.currentOccupancy >= shelf.capacity) availabilityStatus = 'full'
    else if (shelf.currentOccupancy > shelf.capacity * 0.8) availabilityStatus = 'nearly_full'

    return NextResponse.json({
      success: true,
      data: {
        ...shelf,
        occupancyPercentage,
        weightPercentage,
        availabilityStatus,
        warehouse: {
          id: warehouse._id,
          name: warehouse.name,
          code: warehouse.code,
          type: warehouse.type
        }
      }
    })
  } catch (error) {
    console.error('Get shelf API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch shelf' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PUT /api/shelves/[id] - Update shelf
export const PUT = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const shelfId = params.id
    const body = await request.json()
    const validation = updateShelfSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: validation.error.errors },
        { status: 400 }
      )
    }

    const updates = validation.data

    // Get current shelf
    const shelf = await Shelf.findById(shelfId)

    if (!shelf) {
      return NextResponse.json(
        { success: false, error: 'Shelf not found' },
        { status: 404 }
      )
    }

    // Check warehouse access permissions
    const warehouse = await Warehouse.findById(shelf.warehouseId)
    if (!warehouse) {
      return NextResponse.json(
        { success: false, error: 'Associated warehouse not found' },
        { status: 404 }
      )
    }

    if (user.role === 'branch_manager') {
      if (warehouse.branchId && warehouse.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Access denied to update this shelf' },
          { status: 403 }
        )
      }
    }

    // Validate capacity and occupancy
    if (updates.capacity && updates.capacity < shelf.currentOccupancy) {
      return NextResponse.json(
        { success: false, error: 'New capacity cannot be less than current occupancy' },
        { status: 400 }
      )
    }

    if (updates.currentOccupancy && updates.currentOccupancy > (updates.capacity || shelf.capacity)) {
      return NextResponse.json(
        { success: false, error: 'Current occupancy cannot exceed capacity' },
        { status: 400 }
      )
    }

    // Validate weight limit and current weight
    if (updates.weightLimit && updates.weightLimit < shelf.currentWeight) {
      return NextResponse.json(
        { success: false, error: 'New weight limit cannot be less than current weight' },
        { status: 400 }
      )
    }

    if (updates.currentWeight && updates.currentWeight > (updates.weightLimit || shelf.weightLimit)) {
      return NextResponse.json(
        { success: false, error: 'Current weight cannot exceed weight limit' },
        { status: 400 }
      )
    }

    // Update shelf
    Object.assign(shelf, updates)
    await shelf.save()

    return NextResponse.json({
      success: true,
      data: shelf,
      message: 'Shelf updated successfully'
    })
  } catch (error) {
    console.error('Update shelf API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update shelf' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// DELETE /api/shelves/[id] - Delete shelf
export const DELETE = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const shelfId = params.id

    const shelf = await Shelf.findById(shelfId)

    if (!shelf) {
      return NextResponse.json(
        { success: false, error: 'Shelf not found' },
        { status: 404 }
      )
    }

    // Check warehouse access permissions
    const warehouse = await Warehouse.findById(shelf.warehouseId)
    if (!warehouse) {
      return NextResponse.json(
        { success: false, error: 'Associated warehouse not found' },
        { status: 404 }
      )
    }

    if (user.role === 'branch_manager') {
      if (warehouse.branchId && warehouse.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Access denied to delete this shelf' },
          { status: 403 }
        )
      }
    }

    // Check if shelf has items
    if (shelf.currentOccupancy > 0) {
      return NextResponse.json(
        { success: false, error: `Cannot delete shelf with ${shelf.currentOccupancy} items. Please move items first.` },
        { status: 400 }
      )
    }

    // Soft delete by setting isActive to false
    shelf.isActive = false
    await shelf.save()

    return NextResponse.json({
      success: true,
      message: 'Shelf deactivated successfully'
    })
  } catch (error) {
    console.error('Delete shelf API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete shelf' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
