import { NextRequest, NextResponse } from 'next/server'
import { registerUser } from '@/lib/auth/services'
import { rateLimit, addCorsHeaders } from '@/lib/auth/middleware'
import { z } from 'zod'

const registerSchema = z.object({
  username: z.string().min(3).max(30),
  email: z.string().email(),
  password: z.string().min(6),
  name: z.string().min(1).max(100),
  role: z.enum(['overall_admin', 'branch_manager']),
  branchId: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    const rateCheck = rateLimit(`register:${clientIP}`, 3, 60 * 60 * 1000) // 3 attempts per hour
    
    if (!rateCheck.allowed) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Too many registration attempts. Please try again later.' 
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateCheck.resetTime.toString()
          }
        }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    
    const validation = registerSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    // Validate branch requirement for branch managers
    if (data.role === 'branch_manager' && !data.branchId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Branch ID is required for branch managers'
        },
        { status: 400 }
      )
    }

    // Attempt registration
    const result = await registerUser(data)

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error
        },
        { status: 400 }
      )
    }

    // Set HTTP-only cookie for refresh token
    const response = NextResponse.json({
      success: true,
      user: result.user,
      accessToken: result.tokens!.accessToken
    })

    // Set refresh token as HTTP-only cookie
    response.cookies.set('refreshToken', result.tokens!.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/'
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Register API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
