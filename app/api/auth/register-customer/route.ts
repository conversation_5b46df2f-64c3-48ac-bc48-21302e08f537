// app/api/auth/register-customer/route.ts - Customer registration endpoint

import { NextRequest, NextResponse } from 'next/server'
import { Customer, Shop, connectDB, logActivity } from '@/lib/database'
import { rateLimit, addCorsHeaders } from '@/lib/auth/middleware'
import { z } from 'zod'

const customerRegistrationSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50),
  lastName: z.string().min(1, 'Last name is required').max(50),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number'),
  address: z.object({
    street: z.string().min(1, 'Street address is required').max(200),
    city: z.string().min(1, 'City is required').max(50),
    region: z.string().min(1, 'Region is required').max(50),
    country: z.string().min(1, 'Country is required').max(50),
    postalCode: z.string().min(1, 'Postal code is required').max(20)
  }),
  preferredBranch: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    const rateCheck = rateLimit(`customer-register:${clientIP}`, 5, 60 * 60 * 1000) // 5 attempts per hour
    
    if (!rateCheck.allowed) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Too many registration attempts. Please try again later.' 
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateCheck.resetTime.toString()
          }
        }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    
    const validation = customerRegistrationSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    await connectDB()

    // Check if customer already exists
    const existingCustomer = await Customer.findOne({
      $or: [
        { email: data.email.toLowerCase() },
        { phone: data.phone }
      ]
    })

    if (existingCustomer) {
      return NextResponse.json(
        {
          success: false,
          error: existingCustomer.email === data.email.toLowerCase() 
            ? 'Email already registered' 
            : 'Phone number already registered'
        },
        { status: 400 }
      )
    }

    // Validate preferred branch if provided
    if (data.preferredBranch) {
      const branch = await Shop.findById(data.preferredBranch)
      if (!branch) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid preferred branch'
          },
          { status: 400 }
        )
      }
    }

    // Create new customer
    const newCustomer = await Customer.create({
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email.toLowerCase(),
      phone: data.phone,
      address: data.address,
      preferredBranch: data.preferredBranch,
      totalOrders: 0,
      totalSpent: 0,
      loyaltyPoints: 0,
      isActive: true
    })

    // Log registration activity
    await logActivity({
      type: 'User',
      description: `New customer ${newCustomer.firstName} ${newCustomer.lastName} registered`,
      userId: newCustomer._id.toString(),
      userName: `${newCustomer.firstName} ${newCustomer.lastName}`,
      branchId: data.preferredBranch,
      metadata: { 
        customerEmail: newCustomer.email,
        customerPhone: newCustomer.phone 
      }
    })

    // Return success response (without sensitive data)
    return NextResponse.json({
      success: true,
      message: 'Customer registration successful',
      customer: {
        id: newCustomer._id.toString(),
        firstName: newCustomer.firstName,
        lastName: newCustomer.lastName,
        email: newCustomer.email,
        phone: newCustomer.phone,
        preferredBranch: newCustomer.preferredBranch
      }
    })

  } catch (error) {
    console.error('Customer registration error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Registration failed. Please try again.'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
