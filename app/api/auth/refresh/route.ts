import { NextRequest, NextResponse } from 'next/server'
import { refreshUserToken } from '@/lib/auth/services'
import { addCorsHeaders } from '@/lib/auth/middleware'

export async function POST(request: NextRequest) {
  try {
    // Get refresh token from HTTP-only cookie
    const refreshToken = request.cookies.get('refresh-token')?.value

    if (!refreshToken) {
      return NextResponse.json(
        {
          success: false,
          error: 'No refresh token provided'
        },
        { status: 401 }
      )
    }

    // Attempt token refresh
    const result = await refreshUserToken(refreshToken)

    if (!result.success) {
      // Clear invalid refresh token cookie
      const response = NextResponse.json(
        {
          success: false,
          error: result.error
        },
        { status: 401 }
      )

      response.cookies.set('refresh-token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 0,
        path: '/'
      })

      return addCorsHeaders(response)
    }

    // Return new tokens
    const response = NextResponse.json({
      success: true,
      user: result.user,
      accessToken: result.tokens!.accessToken
    })

    // Update refresh token cookie
    response.cookies.set('refresh-token', result.tokens!.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      path: '/'
    })

    // Update access token cookie
    response.cookies.set('auth-token', result.tokens!.accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      path: '/'
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Refresh token API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
