import { NextRequest, NextResponse } from 'next/server'
import { loginUser } from '@/lib/auth/services'
import { rateLimit, addCorsHeaders } from '@/lib/auth/middleware'
import { createSession, getUserActiveSessions, parseUserAgent, getLocationInfo } from '@/lib/auth/session-utils'
import { socketManager } from '@/lib/socket/server'

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    const rateCheck = rateLimit(`login:${clientIP}`, 5, 15 * 60 * 1000) // 5 attempts per 15 minutes
    
    if (!rateCheck.allowed) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Too many login attempts. Please try again later.' 
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateCheck.resetTime.toString()
          }
        }
      )
    }

    // Parse request body
    const body = await request.json()
    const { username, email, password } = body

    // Validate input
    if ((!username && !email) || !password) {
      return NextResponse.json(
        {
          success: false,
          error: 'Username/email and password are required'
        },
        { status: 400 }
      )
    }

    // Determine if we have email or username
    const loginEmail = email || (username?.includes('@') ? username : null)

    if (!loginEmail) {
      // If username doesn't contain @, we need to look up the user by username
      // For now, we'll return an error since our system primarily uses email
      return NextResponse.json(
        {
          success: false,
          error: 'Please use your email address to login'
        },
        { status: 400 }
      )
    }

    // Attempt login
    const result = await loginUser({ email: loginEmail, password })

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error
        },
        { status: 401 }
      )
    }

    // Parse device and location information
    const userAgent = request.headers.get('user-agent') || 'Unknown'
    const deviceInfo = parseUserAgent(userAgent)
    const locationInfo = getLocationInfo(request)

    // Check for existing active sessions
    const existingSessions = await getUserActiveSessions(result.user!.id)

    // Create new session record
    const session = await createSession({
      userId: result.user!.id,
      email: result.user!.email,
      username: result.user!.username,
      role: result.user!.role,
      branchId: result.user!.branchId,
      deviceInfo,
      location: locationInfo
    })

    // Prepare response with session information
    const responseData = {
      success: true,
      user: result.user,
      accessToken: session.sessionToken,
      sessionInfo: {
        sessionId: session._id.toString(),
        deviceDescription: session.getDeviceDescription(),
        loginTime: session.loginTime,
        existingSessions: existingSessions.map(s => ({
          deviceDescription: s.deviceDescription,
          locationDescription: s.locationDescription,
          loginTime: s.loginTime,
          lastActivity: s.lastActivity
        }))
      }
    }

    // Set HTTP-only cookie for refresh token
    const response = NextResponse.json(responseData)

    // Set access token cookie for middleware
    response.cookies.set('auth-token', session.sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      path: '/'
    })

    // Set refresh token as HTTP-only cookie
    response.cookies.set('refresh-token', session.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      path: '/'
    })

    // Notify Socket.IO about new session
    try {
      socketManager.notifySessionCreated(
        session.sessionId,
        result.user.id,
        {
          username: result.user.username,
          email: result.user.email,
          role: result.user.role,
          deviceInfo: session.deviceInfo,
          location: session.location
        }
      )

      // Update session statistics
      const activeSessions = await getUserActiveSessions(result.user.id)
      socketManager.updateSessionStats({
        totalSessions: activeSessions.length + 1,
        activeSessions: activeSessions.length + 1,
        onlineUsers: socketManager.getConnectedUsers().length
      })
    } catch (socketError) {
      console.warn('Socket.IO notification failed:', socketError)
      // Don't fail the login if Socket.IO fails
    }

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Login API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
