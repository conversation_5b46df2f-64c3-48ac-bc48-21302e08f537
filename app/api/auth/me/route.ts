import { NextRequest, NextResponse } from 'next/server'
import { withAuth } from '@/lib/auth/middleware'
import { getUserProfile } from '@/lib/auth/services'

export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const result = await getUserProfile(user.userId)

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      user: result.user
    })
  } catch (error) {
    console.error('Get profile API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
})

export const PUT = withAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const { name, avatar } = body

    // Validate input
    const updates: { name?: string; avatar?: string } = {}
    if (name && typeof name === 'string' && name.trim().length > 0) {
      updates.name = name.trim()
    }
    if (avatar && typeof avatar === 'string') {
      updates.avatar = avatar
    }

    if (Object.keys(updates).length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'No valid updates provided'
        },
        { status: 400 }
      )
    }

    const { updateUserProfile } = await import('@/lib/auth/services')
    const result = await updateUserProfile(user.userId, updates)

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error
        },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      user: result.user
    })
  } catch (error) {
    console.error('Update profile API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
})
