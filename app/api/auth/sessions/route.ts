// app/api/auth/sessions/route.ts - Session Management API

import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken } from '@/lib/auth/jwt'
import { getUserActiveSessions, terminateSession, terminateOtherSessions } from '@/lib/auth/session-utils'
import Session from '@/lib/database/models/Session'
import { addCorsHeaders } from '@/lib/auth/middleware'

// GET /api/auth/sessions - Get user's active sessions
export async function GET(request: NextRequest) {
  try {
    // Get auth token from cookies
    const authToken = request.cookies.get('auth-token')?.value
    
    if (!authToken) {
      return NextResponse.json(
        { success: false, error: 'No authentication token' },
        { status: 401 }
      )
    }

    // Verify token
    const decoded = verifyAccessToken(authToken)
    const userId = decoded.userId

    // Get current session ID from token
    const currentSession = await Session.findByToken(authToken)
    const currentSessionId = currentSession?._id.toString()

    // Get all active sessions for the user
    const sessions = await getUserActiveSessions(userId, currentSessionId)

    const response = NextResponse.json({
      success: true,
      sessions,
      currentSessionId
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Get sessions error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve sessions' },
      { status: 500 }
    )
  }
}

// DELETE /api/auth/sessions - Terminate sessions
export async function DELETE(request: NextRequest) {
  try {
    // Get auth token from cookies
    const authToken = request.cookies.get('auth-token')?.value
    
    if (!authToken) {
      return NextResponse.json(
        { success: false, error: 'No authentication token' },
        { status: 401 }
      )
    }

    // Verify token
    const decoded = verifyAccessToken(authToken)
    const userId = decoded.userId

    // Get request body
    const body = await request.json()
    const { sessionId, terminateAll } = body

    let terminatedCount = 0

    if (terminateAll) {
      // Terminate all other sessions except current
      const currentSession = await Session.findByToken(authToken)
      if (currentSession) {
        terminatedCount = await terminateOtherSessions(
          userId, 
          currentSession._id.toString(),
          'manual'
        )
      }
    } else if (sessionId) {
      // Terminate specific session
      const success = await terminateSession(sessionId, 'manual')
      terminatedCount = success ? 1 : 0
    } else {
      return NextResponse.json(
        { success: false, error: 'Either sessionId or terminateAll must be specified' },
        { status: 400 }
      )
    }

    const response = NextResponse.json({
      success: true,
      message: `${terminatedCount} session(s) terminated`,
      terminatedCount
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Terminate sessions error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to terminate sessions' },
      { status: 500 }
    )
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'GET, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
