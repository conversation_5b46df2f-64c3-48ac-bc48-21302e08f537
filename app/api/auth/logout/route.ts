import { NextRequest, NextResponse } from 'next/server'
import { addCorsHeaders } from '@/lib/auth/middleware'
import { verifyAccessToken } from '@/lib/auth/jwt'
import Session from '@/lib/database/models/Session'

export async function POST(request: NextRequest) {
  try {
    // Get the auth token from cookies
    const authToken = request.cookies.get('auth-token')?.value

    if (authToken) {
      try {
        // Verify and decode the token to get session info
        const decoded = verifyAccessToken(authToken)

        // Find and terminate the session
        const session = await Session.findByToken(authToken)
        if (session) {
          await session.terminate('manual')
        }
      } catch (tokenError) {
        // Token might be invalid, but we still want to clear cookies
        console.warn('Token verification failed during logout:', tokenError)
      }
    }

    // Clear the refresh token cookie
    const response = NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    })

    // Clear both auth and refresh token cookies
    response.cookies.set('auth-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    })

    response.cookies.set('refresh-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/'
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Logout API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
