import { NextRequest, NextResponse } from 'next/server'
import { requestPasswordReset, confirmPasswordReset } from '@/lib/auth/services'
import { rateLimit, addCorsHeaders } from '@/lib/auth/middleware'
import { z } from 'zod'

const resetRequestSchema = z.object({
  email: z.string().email('Invalid email address')
})

const resetConfirmSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  newPassword: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string().min(1, 'Password confirmation is required')
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

// Request password reset
export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    const rateCheck = rateLimit(`reset-request:${clientIP}`, 3, 60 * 60 * 1000) // 3 attempts per hour
    
    if (!rateCheck.allowed) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Too many password reset attempts. Please try again later.' 
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': rateCheck.resetTime.toString()
          }
        }
      )
    }

    const body = await request.json()
    
    const validation = resetRequestSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email address'
        },
        { status: 400 }
      )
    }

    const result = await requestPasswordReset(validation.data)

    // Always return success to prevent email enumeration
    const response = NextResponse.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.',
      // In development, return the token for testing
      ...(process.env.NODE_ENV === 'development' && result.token && { token: result.token })
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Password reset request API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// Confirm password reset
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    const validation = resetConfirmSchema.safeParse(body)
    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const { token, newPassword } = validation.data

    const result = await confirmPasswordReset({ token, newPassword })

    if (!result.success) {
      return NextResponse.json(
        {
          success: false,
          error: result.error
        },
        { status: 400 }
      )
    }

    const response = NextResponse.json({
      success: true,
      message: 'Password reset successfully'
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Password reset confirm API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'POST, PUT, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
