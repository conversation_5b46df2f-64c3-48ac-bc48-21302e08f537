import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/database'
import { downloadFromGridFS, getFileInfo } from '@/lib/utils/gridfs'

/**
 * GET /api/images/[id] - Retrieve image from GridFS
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('🖼️ IMAGE SERVE REQUEST STARTED')
    await connectDB()

    const imageId = (await params).id
    console.log('🆔 Image ID requested:', imageId)

    if (!imageId) {
      console.log('❌ No image ID provided')
      return NextResponse.json(
        { success: false, error: 'Image ID is required' },
        { status: 400 }
      )
    }

    // Get file info first to check if it exists and get metadata
    let fileInfo
    try {
      console.log('🔍 Looking for file info...')
      fileInfo = await getFileInfo(imageId)
      console.log('✅ File found:', fileInfo.filename, 'Size:', fileInfo.length)
    } catch (error) {
      console.log('❌ File not found:', error)
      return NextResponse.json(
        { success: false, error: 'Image not found' },
        { status: 404 }
      )
    }

    // Get download stream
    const downloadStream = downloadFromGridFS(imageId)

    // Set appropriate headers
    const headers = new Headers()
    headers.set('Content-Type', fileInfo.metadata?.mimeType || 'image/jpeg')
    headers.set('Content-Length', fileInfo.length.toString())
    headers.set('Cache-Control', 'public, max-age=31536000') // Cache for 1 year
    headers.set('ETag', imageId)

    // Handle conditional requests
    const ifNoneMatch = request.headers.get('if-none-match')
    if (ifNoneMatch === imageId) {
      return new NextResponse(null, { status: 304, headers })
    }

    // Convert stream to response
    const readable = new ReadableStream({
      start(controller) {
        downloadStream.on('data', (chunk) => {
          controller.enqueue(chunk)
        })

        downloadStream.on('end', () => {
          controller.close()
        })

        downloadStream.on('error', (error) => {
          console.error('Stream error:', error)
          controller.error(error)
        })
      }
    })

    console.log('📤 Sending image response with headers:', Object.fromEntries(headers.entries()))
    return new NextResponse(readable, { headers })

  } catch (error) {
    console.error('❌ Image retrieval error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve image' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/images/[id] - Delete image from GridFS
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    // Verify authentication
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const imageId = (await params).id

    if (!imageId) {
      return NextResponse.json(
        { success: false, error: 'Image ID is required' },
        { status: 400 }
      )
    }

    // Check if file exists
    try {
      await getFileInfo(imageId)
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Image not found' },
        { status: 404 }
      )
    }

    // Delete the file
    const { deleteFromGridFS } = await import('@/lib/utils/gridfs')
    await deleteFromGridFS(imageId)

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    })

  } catch (error) {
    console.error('Image deletion error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete image' },
      { status: 500 }
    )
  }
}
