// app/api/images/upload/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import { uploadToGridFS, validateImageFile, generateUniqueFilename } from '@/lib/utils/gridfs'

/**
 * POST /api/images/upload - Upload images to GridFS
 */
export async function POST(request: NextRequest) {
  try {
    // Connect to database
    await connectDB()

    // Verify authentication
    const authResult = await authMiddleware(request, ['overall_admin', 'branch_manager'])
    if (!authResult.success) {
      return authResult.response || NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { user } = authResult

    // Ensure user is defined
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User information not available' },
        { status: 401 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    const category = formData.get('category') as string || 'general'
    const productId = formData.get('productId') as string

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No files provided' },
        { status: 400 }
      )
    }

    // Validate files
    const validationErrors: string[] = []
    for (const file of files) {
      const validation = validateImageFile(file)
      if (!validation.valid) {
        validationErrors.push(`${file.name}: ${validation.error}`)
      }
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'File validation failed',
          details: validationErrors
        },
        { status: 400 }
      )
    }

    // Upload files
    const uploadResults = []
    for (const file of files) {
      try {
        const filename = generateUniqueFilename(file.name, category)
        const result = await uploadToGridFS(file, filename, {
          category,
          productId,
          uploadedBy: user.userId,
          branchId: user.branchId
        })
        uploadResults.push(result)
      } catch (error) {
        console.error(`Failed to upload ${file.name}:`, error)
        validationErrors.push(`${file.name}: Upload failed`)
      }
    }

    if (uploadResults.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'All uploads failed',
          details: validationErrors
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: uploadResults, // Frontend expects array directly
      meta: {
        totalUploaded: uploadResults.length,
        totalRequested: files.length,
        errors: validationErrors.length > 0 ? validationErrors : undefined
      }
    })

  } catch (error) {
    console.error('Image upload error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/images/upload - Get upload guidelines
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    data: {
      allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],
      maxFileSize: '10MB',
      maxFiles: 10,
      guidelines: [
        'Use high-quality images for better product presentation',
        'Recommended resolution: 1200x1200 pixels or higher',
        'Use consistent lighting and background',
        'Include multiple angles for products with variants'
      ]
    }
  })
}
