import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getSalesAnalytics } from '@/services/backend'

// GET /api/analytics/sales - Get detailed sales analytics
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const branchId = searchParams.get('branchId') || undefined
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined
    const groupBy = searchParams.get('groupBy') || 'day'
    const category = searchParams.get('category') || undefined

    // Check branch access if branchId is specified
    if (branchId) {
      const branchCheck = checkBranchAccess(user, branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await getSalesAnalytics(
      branchId,
      startDate,
      endDate,
      groupBy,
      category,
      user.role,
      user.branchId
    )

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Sales analytics API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch sales analytics' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})




