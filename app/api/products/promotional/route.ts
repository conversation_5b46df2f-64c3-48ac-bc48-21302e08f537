import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/database'
import Product from '@/lib/database/models/Product'

// GET /api/products/promotional - Get promotional and on-sale products (public access)
export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '6')

    console.log('🎯 Promotional products API called with limit:', limit)

    // Get current date for date validation
    const now = new Date()

    // Build filter for promotional products
    const filter: any = {
      isActive: true,
      $or: [
        // Products that are promoted
        { isPromoted: true },
        // Products that are on sale with active dates
        {
          isOnSale: true,
          $or: [
            // No sale dates specified (always on sale)
            { saleStartDate: { $exists: false }, saleEndDate: { $exists: false } },
            // Sale dates specified and currently active
            {
              $and: [
                { $or: [{ saleStartDate: { $exists: false } }, { saleStartDate: { $lte: now } }] },
                { $or: [{ saleEndDate: { $exists: false } }, { saleEndDate: { $gte: now } }] }
              ]
            }
          ]
        }
      ]
    }

    console.log('📋 Promotional filter:', JSON.stringify(filter, null, 2))

    // Fetch promotional products
    const products = await Product.find(filter)
      .sort({ 
        // Prioritize promoted products, then by creation date
        isPromoted: -1, 
        isOnSale: -1, 
        createdAt: -1 
      })
      .limit(limit)
      .lean()

    console.log('📊 Promotional products found:', products.length)

    // Transform products to match frontend interface
    const transformedProducts = products.map((product: any) => ({
      _id: product._id.toString(),
      id: product._id.toString(),
      name: product.name,
      sku: product.sku,
      categoryId: product.categoryId,
      categoryName: product.categoryName,
      price: product.price,
      originalPrice: product.originalPrice,
      currency: product.currency,
      stock: product.stock,
      minStockLevel: product.minStockLevel,
      images: product.images || [],
      featuredImage: product.featuredImage,
      description: product.description,
      specifications: product.specifications || [],
      branchId: product.branchId,
      brand: product.brand,
      model: product.model,
      warranty: product.warranty,
      weight: product.weight,
      dimensions: product.dimensions,
      tags: product.tags || [],
      variants: product.variants || [],
      hasVariants: product.hasVariants || false,
      isFeatured: product.isFeatured,
      isActive: product.isActive,
      isPromoted: product.isPromoted,
      isOnSale: product.isOnSale,
      salePrice: product.salePrice,
      saleStartDate: product.saleStartDate,
      saleEndDate: product.saleEndDate,
      promotionDescription: product.promotionDescription,
      status: product.status,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      createdBy: product.createdBy,
      updatedBy: product.updatedBy
    }))

    console.log('✅ Returning promotional products:', transformedProducts.length)

    return NextResponse.json({
      success: true,
      data: transformedProducts,
      total: transformedProducts.length
    })
  } catch (error) {
    console.error('Get promotional products API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch promotional products' },
      { status: 500 }
    )
  }
}
