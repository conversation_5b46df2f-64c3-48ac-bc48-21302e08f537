import { NextRequest, NextResponse } from 'next/server'
import { getProducts, serviceUtils } from '@/services/backend'
import type { ProductFilters } from '@/services/backend/productService'

// GET /api/products/public - Get all products (public access for customers)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    console.log('🔍 Public products API called with params:', Object.fromEntries(searchParams.entries()))

    const pagination = serviceUtils.validatePagination(
      parseInt(searchParams.get('page') || '1'),
      parseInt(searchParams.get('limit') || '20')
    )

    const filters: ProductFilters = {
      search: serviceUtils.sanitizeSearchQuery(searchParams.get('search') || undefined),
      category: searchParams.get('category') || undefined,
      brand: searchParams.get('brand') || undefined,
      branchId: searchParams.get('branchId') || undefined,
      country: searchParams.get('country') || undefined,
      region: searchParams.get('region') || undefined,
      minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,
      featured: searchParams.get('featured') === 'true'
    }

    console.log('📋 Filters applied:', JSON.stringify(filters, null, 2))
    console.log('📄 Pagination:', JSON.stringify(pagination, null, 2))

    // For public access, only show active products from all branches
    const result = await getProducts(filters, pagination, 'customer', undefined)

    console.log('📊 Backend result:', {
      success: result.success,
      dataLength: result.data?.length || 0,
      total: result.pagination?.total || 0,
      error: result.error
    })

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get public products API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch products' },
      { status: 500 }
    )
  }
}
