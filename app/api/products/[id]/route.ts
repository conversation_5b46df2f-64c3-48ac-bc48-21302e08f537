import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { Product, Inventory, connectDB, logActivity } from '@/lib/database'
import { z } from 'zod'

const updateProductSchema = z.object({
  name: z.string().min(1).max(200).optional(),
  sku: z.string().min(1).max(50).optional(),
  categoryId: z.string().min(1).optional(),
  categoryName: z.string().min(1).optional(),
  category: z.enum([
    'Desktops', 'Laptops', 'TV Sets', 'Server Systems', 'Amplifiers',
    'Network Devices', 'Security Software', 'Application Software',
    'Mobile Devices', 'Audio Equipment', 'Gaming', 'Accessories'
  ]).optional(),
  price: z.number().min(0).optional(),
  originalPrice: z.number().min(0).optional(),
  currency: z.enum(['MWK', 'ZMW', 'TZS', 'ZAR', 'USD']).optional(),
  stock: z.number().min(0).optional(),
  minStockLevel: z.number().min(0).optional(),
  featuredImage: z.string().optional(),
  images: z.array(z.string()).optional(),
  description: z.string().min(1).max(1000).optional(),
  specifications: z.array(z.string()).optional(),
  branchId: z.string().min(1).optional(),
  brand: z.string().min(1).max(50).optional(),
  model: z.string().min(1).max(100).optional(),
  warranty: z.string().min(1).max(100).optional(),
  weight: z.number().min(0).optional(),
  dimensions: z.object({
    length: z.number().min(0),
    width: z.number().min(0),
    height: z.number().min(0)
  }).optional(),
  tags: z.array(z.string()).optional(),
  variants: z.array(z.any()).optional(),
  hasVariants: z.boolean().optional(),
  isFeatured: z.boolean().optional(),
  isPromoted: z.boolean().optional(),
  isOnSale: z.boolean().optional(),
  salePrice: z.number().min(0).max(100000000, 'Sale price cannot exceed 100 million').optional(),
  saleStartDate: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  saleEndDate: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  promotionDescription: z.string().max(200, 'Promotion description cannot exceed 200 characters').optional(),
  isActive: z.boolean().optional()
})

// GET /api/products/[id] - Get product by ID
export const GET = withAuth(async (request: NextRequest, user, context) => {
  try {
    await connectDB()

    // Extract params from context
    const { params } = context
    const resolvedParams = await params
    const productId = resolvedParams.id

    const product = await Product.findById(productId).lean()

    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    // Check branch access
    const branchCheck = checkBranchAccess(user, product.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      data: product
    })
  } catch (error) {
    console.error('Get product error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch product' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PUT /api/products/[id] - Update product
export const PUT = withAuth(async (request: NextRequest, user, context) => {
  try {
    console.log('🔄 UPDATE product API called')

    // Extract params from context
    const { params } = context
    const resolvedParams = await params
    const productId = resolvedParams.id

    console.log('📝 Product ID:', productId)
    console.log('👤 User:', { userId: user.userId, role: user.role, branchId: user.branchId })

    await connectDB()

    console.log('🔍 Finding existing product...')
    const existingProduct = await Product.findById(productId)
    if (!existingProduct) {
      console.log('❌ Product not found')
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    console.log('✅ Product found:', { id: existingProduct._id, name: existingProduct.name, branchId: existingProduct.branchId })

    // Check branch access
    console.log('🔐 Checking branch access...')
    const branchCheck = checkBranchAccess(user, existingProduct.branchId)
    if (!branchCheck.hasAccess) {
      console.log('❌ Branch access denied:', branchCheck.error)
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    console.log('✅ Branch access granted')

    console.log('📥 Reading request body...')
    const body = await request.json()
    console.log('📦 Received update data:', JSON.stringify(body, null, 2))

    console.log('🔍 Validating update data...')
    const validation = updateProductSchema.safeParse(body)
    console.log('📋 Validation result:', {
      success: validation.success,
      errors: validation.success ? null : validation.error.errors
    })

    if (!validation.success) {
      console.log('❌ Validation failed:', validation.error.errors)
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const updates = validation.data

    const product = await Product.findByIdAndUpdate(
      productId,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: false }
    )

    // Update inventory if stock changed
    if (updates.stock !== undefined) {
      await Inventory.findOneAndUpdate(
        { productId, branchId: product!.branchId },
        { 
          stock: updates.stock,
          lastRestocked: new Date()
        }
      )
    }

    // Log activity
    await logActivity({
      type: 'Product',
      description: `Product "${product!.name}" updated`,
      userId: user.userId,
      userName: user.username,
      branchId: product!.branchId,
      metadata: { 
        productId: product!._id.toString(),
        productName: product!.name,
        updates 
      }
    })

    return NextResponse.json({
      success: true,
      data: product
    })
  } catch (error) {
    console.error('Update product error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update product' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// DELETE /api/products/[id] - Delete product
export const DELETE = withAuth(async (request: NextRequest, user, context) => {
  try {
    console.log('🗑️ DELETE product API called')

    // Extract params from context
    const { params } = context
    const resolvedParams = await params
    const productId = resolvedParams.id

    console.log('📝 Product ID:', productId)
    console.log('👤 User:', { userId: user.userId, role: user.role, branchId: user.branchId })

    await connectDB()

    console.log('🔍 Finding product...')
    const product = await Product.findById(productId)
    if (!product) {
      console.log('❌ Product not found')
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    console.log('✅ Product found:', { id: product._id, name: product.name, branchId: product.branchId })

    // Check branch access
    console.log('🔐 Checking branch access...')
    const branchCheck = checkBranchAccess(user, product.branchId)
    if (!branchCheck.hasAccess) {
      console.log('❌ Branch access denied:', branchCheck.error)
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    console.log('✅ Branch access granted')

    // Soft delete - set isActive to false
    console.log('🔄 Soft deleting product...')
    await Product.findByIdAndUpdate(productId, {
      isActive: false,
      updatedAt: new Date(),
      updatedBy: user.userId
    })

    console.log('✅ Product soft deleted')

    // Also update inventory
    console.log('📦 Updating inventory...')
    const inventoryUpdate = await Inventory.findOneAndUpdate(
      { productId, branchId: product.branchId },
      { stock: 0 }
    )

    console.log('📦 Inventory update result:', inventoryUpdate ? 'Updated' : 'Not found')

    // Log activity
    console.log('📝 Logging activity...')
    try {
      await logActivity({
        type: 'Product',
        description: `Product "${product.name}" deleted`,
        userId: user.userId,
        userName: user.username,
        branchId: product.branchId,
        metadata: {
          productId: product._id.toString(),
          productName: product.name
        }
      })
      console.log('✅ Activity logged')
    } catch (logError) {
      console.error('⚠️ Failed to log activity:', logError)
      // Don't fail the whole operation if logging fails
    }

    console.log('✅ Product deletion completed successfully')
    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully'
    })
  } catch (error) {
    console.error('❌ Delete product error:', error)
    console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return NextResponse.json(
      { success: false, error: 'Failed to delete product' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
