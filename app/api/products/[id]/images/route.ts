import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import Product from '@/lib/database/models/Product'
import { uploadToGridFS, validateImageFile, generateUniqueFilename } from '@/lib/utils/gridfs'

/**
 * POST /api/products/[id]/images - Upload images for a specific product
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Connect to database
    await connectDB()

    // Verify authentication
    const authResult = await authMiddleware(request, ['overall_admin', 'branch_manager'])
    if (!authResult.success) {
      return authResult.response || NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { user } = authResult
    const productId = (await params).id

    // Check if product exists
    const product = await Product.findById(productId)
    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const files = formData.getAll('images') as File[]
    const setAsFeatured = formData.get('setAsFeatured') === 'true'

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No images provided' },
        { status: 400 }
      )
    }

    // Validate files
    const validationErrors: string[] = []
    for (const file of files) {
      const validation = validateImageFile(file)
      if (!validation.valid) {
        validationErrors.push(`${file.name}: ${validation.error}`)
      }
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'File validation failed',
          details: validationErrors
        },
        { status: 400 }
      )
    }

    // Upload files
    const uploadResults = []
    for (const file of files) {
      try {
        const filename = generateUniqueFilename(file.name, 'product')
        const result = await uploadToGridFS(file, filename, {
          category: 'product',
          productId: productId,
          productName: product.name,
          uploadedBy: user.userId,
          branchId: user.branchId
        })
        uploadResults.push(result)
      } catch (error) {
        console.error(`Failed to upload ${file.name}:`, error)
        validationErrors.push(`${file.name}: Upload failed`)
      }
    }

    if (uploadResults.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'All uploads failed',
          details: validationErrors
        },
        { status: 500 }
      )
    }

    // Update product with new image URLs
    const newImageUrls = uploadResults.map(result => result.url)
    const updatedImages = [...(product.images || []), ...newImageUrls]

    // Set featured image if requested or if no featured image exists
    let featuredImage = product.featuredImage
    if (setAsFeatured || !featuredImage) {
      featuredImage = newImageUrls[0]
    }

    // Update product
    await Product.findByIdAndUpdate(productId, {
      images: updatedImages,
      featuredImage: featuredImage,
      updatedBy: user.userId,
      updatedAt: new Date()
    })

    return NextResponse.json({
      success: true,
      data: {
        uploadedImages: uploadResults,
        totalUploaded: uploadResults.length,
        totalRequested: files.length,
        featuredImage: featuredImage,
        errors: validationErrors.length > 0 ? validationErrors : undefined
      }
    })

  } catch (error) {
    console.error('Product image upload error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/products/[id]/images - Get all images for a product
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()

    const productId = (await params).id

    // Get product
    const product = await Product.findById(productId).select('images featuredImage name')
    if (!product) {
      return NextResponse.json(
        { success: false, error: 'Product not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        images: product.images || [],
        featuredImage: product.featuredImage,
        productName: product.name
      }
    })

  } catch (error) {
    console.error('Get product images error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve product images' },
      { status: 500 }
    )
  }
}
