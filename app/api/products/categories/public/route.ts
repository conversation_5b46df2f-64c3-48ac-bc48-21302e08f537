import { NextRequest, NextResponse } from 'next/server'
import { connectDB } from '@/lib/database'
import ProductCategory from '@/lib/database/models/ProductCategory'

// GET /api/products/categories/public - Get all active product categories (public access)
export async function GET(request: NextRequest) {
  try {
    await connectDB()

    const categories = await ProductCategory.find({
      isActive: true
    }).select('name description slug productCount featuredImage icon iconType iconName').sort({ name: 1 })

    return NextResponse.json({
      success: true,
      data: categories
    })
  } catch (error) {
    console.error('Get public categories API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch categories' },
      { status: 500 }
    )
  }
}
