import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getProducts, createProduct, serviceUtils } from '@/services/backend'
import ProductCategory from '@/lib/database/models/ProductCategory'
import { z } from 'zod'

const productVariantSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1).max(100),
  sku: z.string().min(1).max(50),
  price: z.number().min(0).max(100000000, 'Price cannot exceed 100 million'),
  originalPrice: z.number().min(0).max(100000000, 'Original price cannot exceed 100 million').optional(),
  currency: z.enum(['MWK', 'ZMW', 'TZS', 'ZAR', 'USD']).default('MWK'),
  stock: z.number().min(0).default(0),
  attributes: z.record(z.string()).default({}),
  images: z.array(z.string()).default([]),
  isActive: z.boolean().default(true)
})

const createProductSchema = z.object({
  name: z.string().min(1).max(200),
  sku: z.string().min(1).max(50),
  categoryId: z.string().min(1),
  categoryName: z.string().min(1),
  price: z.number().min(0.01, 'Price must be greater than 0').max(100000000, 'Price cannot exceed 100 million'),
  originalPrice: z.number().min(0).max(100000000, 'Original price cannot exceed 100 million').optional(),
  currency: z.enum(['MWK', 'ZMW', 'TZS', 'ZAR', 'USD']).default('MWK'),
  stock: z.number().min(0).default(0),
  minStockLevel: z.number().min(0).default(5),
  featuredImage: z.string().min(1),
  images: z.array(z.string()).default([]),
  description: z.string().min(1).max(1000),
  specifications: z.array(z.string()).default([]),
  branchId: z.string().min(1),
  brand: z.string().min(1).max(50),
  model: z.string().min(1).max(100),
  warranty: z.string().min(1).max(100),
  weight: z.number().min(0).optional(),
  dimensions: z.object({
    length: z.number().min(0),
    width: z.number().min(0),
    height: z.number().min(0)
  }).optional(),
  tags: z.array(z.string()).default([]),
  variants: z.array(productVariantSchema).default([]),
  hasVariants: z.boolean().default(false),
  isFeatured: z.boolean().default(false),
  isPromoted: z.boolean().default(false),
  isOnSale: z.boolean().default(false),
  salePrice: z.number().min(0).max(100000000, 'Sale price cannot exceed 100 million').optional(),
  saleStartDate: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  saleEndDate: z.string().transform(str => str ? new Date(str) : undefined).optional(),
  promotionDescription: z.string().max(200, 'Promotion description cannot exceed 200 characters').optional()
})

// GET /api/products - Get all products
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const pagination = serviceUtils.validatePagination(
      parseInt(searchParams.get('page') || '1'),
      parseInt(searchParams.get('limit') || '10')
    )

    const filters = {
      search: serviceUtils.sanitizeSearchQuery(searchParams.get('search') || undefined),
      category: searchParams.get('category') || undefined,
      brand: searchParams.get('brand') || undefined,
      status: searchParams.get('status') as any || undefined,
      branchId: searchParams.get('branchId') || undefined,
      minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,
      featured: searchParams.get('featured') === 'true'
    }

    // Check branch access if branchId is specified
    if (filters.branchId) {
      const branchCheck = checkBranchAccess(user, filters.branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await getProducts(filters, pagination, user.role, user.branchId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get products API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch products' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/products - Create new product
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    console.log('🔥 CREATE PRODUCT API STARTED')
    const body = await request.json()
    console.log('📦 Request body received:', JSON.stringify(body, null, 2))

    const validation = createProductSchema.safeParse(body)
    console.log('✅ Validation result:', validation.success)

    if (!validation.success) {
      console.log('❌ Validation failed:', validation.error.errors)
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data
    console.log('📋 Validated data:', JSON.stringify(data, null, 2))

    // Check branch access
    const branchCheck = checkBranchAccess(user, data.branchId)
    console.log('🏢 Branch access check:', branchCheck)
    if (!branchCheck.hasAccess) {
      console.log('❌ Branch access denied')
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    // Validate category exists and is active
    console.log('🔍 Looking for category with ID:', data.categoryId)
    const category = await ProductCategory.findById(data.categoryId)
    console.log('📂 Category found:', category ? { id: category._id, name: category.name, isActive: category.isActive } : 'null')

    if (!category || !category.isActive) {
      console.log('❌ Category validation failed')
      return NextResponse.json(
        { success: false, error: 'Invalid or inactive category' },
        { status: 400 }
      )
    }

    // Ensure category name matches
    if (category.name !== data.categoryName) {
      console.log('🔄 Updating category name from', data.categoryName, 'to', category.name)
      data.categoryName = category.name
    }

    // Add audit fields
    const productData = {
      ...data,
      createdBy: user.userId,
      updatedBy: user.userId
    }
    console.log('🚀 Final product data:', JSON.stringify(productData, null, 2))

    console.log('📞 Calling createProduct service...')
    const result = await createProduct(productData, user.userId, user.username)
    console.log('📊 CreateProduct result:', JSON.stringify(result, null, 2))

    if (!result.success) {
      console.log('❌ CreateProduct failed:', result.error)
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    console.log('✅ Product created successfully!')
    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Create product API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create product' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
