import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import Warehouse from '@/lib/database/models/Warehouse'
import Shelf from '@/lib/database/models/Shelf'
import { z } from 'zod'

// Validation schema for updates
const updateWarehouseSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name cannot exceed 100 characters').optional(),
  description: z.string().max(500, 'Description cannot exceed 500 characters').optional(),
  address: z.string().min(1, 'Address is required').max(200, 'Address cannot exceed 200 characters').optional(),
  capacity: z.number().min(1, 'Capacity must be greater than 0').optional(),
  currentUtilization: z.number().min(0).max(100).optional(),
  isActive: z.boolean().optional(),
  manager: z.string().max(100, 'Manager name cannot exceed 100 characters').optional(),
  managerId: z.string().optional(),
  contactInfo: z.object({
    phone: z.string().max(20, 'Phone cannot exceed 20 characters').optional(),
    email: z.string().email('Invalid email format').max(100, 'Email cannot exceed 100 characters').optional()
  }).optional(),
  operatingHours: z.object({
    open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
    close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
    timezone: z.string().default('Africa/Blantyre')
  }).optional(),
  coordinates: z.object({
    lat: z.number().min(-90).max(90),
    lng: z.number().min(-180).max(180)
  }).optional(),
  features: z.array(z.enum([
    'climate_controlled',
    'security_cameras', 
    'loading_dock',
    'fire_suppression',
    'backup_power',
    'refrigerated',
    'hazmat_certified',
    'automated_systems'
  ])).optional()
})

// GET /api/warehouses/[id] - Get warehouse by ID
export const GET = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const warehouseId = params.id

    const warehouse = await Warehouse.findById(warehouseId).lean()

    if (!warehouse) {
      return NextResponse.json(
        { success: false, error: 'Warehouse not found' },
        { status: 404 }
      )
    }

    // Check access permissions
    if (user.role === 'branch_manager') {
      // Branch managers can only access their branch warehouses or main/distribution warehouses
      if (warehouse.branchId && warehouse.branchId !== user.branchId) {
        if (!['main', 'distribution'].includes(warehouse.type)) {
          return NextResponse.json(
            { success: false, error: 'Access denied to this warehouse' },
            { status: 403 }
          )
        }
      }
    }

    // Get shelf count for this warehouse
    const shelfCount = await Shelf.countDocuments({ warehouseId: warehouseId, isActive: true })

    return NextResponse.json({
      success: true,
      data: {
        ...warehouse,
        shelfCount
      }
    })
  } catch (error) {
    console.error('Get warehouse API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch warehouse' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PUT /api/warehouses/[id] - Update warehouse
export const PUT = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const warehouseId = params.id
    const body = await request.json()
    const validation = updateWarehouseSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: validation.error.errors },
        { status: 400 }
      )
    }

    const updates = validation.data

    // Get current warehouse
    const warehouse = await Warehouse.findById(warehouseId)

    if (!warehouse) {
      return NextResponse.json(
        { success: false, error: 'Warehouse not found' },
        { status: 404 }
      )
    }

    // Check permissions
    if (user.role === 'branch_manager') {
      // Branch managers can only update their own branch warehouses
      if (warehouse.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Access denied to update this warehouse' },
          { status: 403 }
        )
      }
      
      // Branch managers cannot change certain fields
      const restrictedFields = ['type', 'branchId', 'capacity']
      const hasRestrictedUpdates = restrictedFields.some(field => field in updates)
      if (hasRestrictedUpdates) {
        return NextResponse.json(
          { success: false, error: 'Branch managers cannot modify warehouse type, branch assignment, or capacity' },
          { status: 403 }
        )
      }
    }

    // Update warehouse
    Object.assign(warehouse, updates)
    await warehouse.save()

    return NextResponse.json({
      success: true,
      data: warehouse,
      message: 'Warehouse updated successfully'
    })
  } catch (error) {
    console.error('Update warehouse API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update warehouse' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// DELETE /api/warehouses/[id] - Delete warehouse
export const DELETE = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const warehouseId = params.id

    const warehouse = await Warehouse.findById(warehouseId)

    if (!warehouse) {
      return NextResponse.json(
        { success: false, error: 'Warehouse not found' },
        { status: 404 }
      )
    }

    // Branch managers can only delete warehouses in their branch
    if (user.role === 'branch_manager') {
      if (!warehouse.branchId || warehouse.branchId !== user.branchId) {
        return NextResponse.json(
          { success: false, error: 'Branch managers can only delete warehouses in their assigned branch' },
          { status: 403 }
        )
      }
    }

    // Check if warehouse has active shelves
    const activeShelvesCount = await Shelf.countDocuments({ 
      warehouseId: warehouseId, 
      isActive: true 
    })

    if (activeShelvesCount > 0) {
      return NextResponse.json(
        { success: false, error: `Cannot delete warehouse with ${activeShelvesCount} active shelves. Please deactivate or remove shelves first.` },
        { status: 400 }
      )
    }

    // Soft delete by setting isActive to false
    warehouse.isActive = false
    await warehouse.save()

    // Also deactivate all shelves in this warehouse
    await Shelf.updateMany(
      { warehouseId: warehouseId },
      { isActive: false }
    )

    return NextResponse.json({
      success: true,
      message: 'Warehouse deactivated successfully'
    })
  } catch (error) {
    console.error('Delete warehouse API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete warehouse' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
