import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import Warehouse from '@/lib/database/models/Warehouse'
import { z } from 'zod'

// Validation schemas
const createWarehouseSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name cannot exceed 100 characters'),
  code: z.string().min(1, 'Code is required').max(20, 'Code cannot exceed 20 characters')
    .regex(/^[A-Z0-9]+$/, 'Code must contain only uppercase letters and numbers'),
  description: z.string().max(500, 'Description cannot exceed 500 characters').optional(),
  type: z.enum(['main', 'branch', 'storage', 'distribution']),
  branchId: z.string().optional(),
  address: z.string().min(1, 'Address is required').max(200, 'Address cannot exceed 200 characters'),
  capacity: z.number().min(1, 'Capacity must be greater than 0'),
  manager: z.string().max(100, 'Manager name cannot exceed 100 characters').optional(),
  managerId: z.string().optional(),
  contactInfo: z.object({
    phone: z.string().max(20, 'Phone cannot exceed 20 characters').optional(),
    email: z.string().email('Invalid email format').max(100, 'Email cannot exceed 100 characters').optional()
  }).optional(),
  operatingHours: z.object({
    open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
    close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
    timezone: z.string().default('Africa/Blantyre')
  }),
  coordinates: z.object({
    lat: z.number().min(-90).max(90),
    lng: z.number().min(-180).max(180)
  }).optional(),
  features: z.array(z.enum([
    'climate_controlled',
    'security_cameras', 
    'loading_dock',
    'fire_suppression',
    'backup_power',
    'refrigerated',
    'hazmat_certified',
    'automated_systems'
  ])).optional()
})

const warehouseFiltersSchema = z.object({
  search: z.string().optional(),
  type: z.enum(['main', 'branch', 'storage', 'distribution']).optional(),
  branchId: z.string().optional(),
  isActive: z.boolean().optional(),
  managerId: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20)
})

// GET /api/warehouses - Get all warehouses with filtering
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    await connectDB()

    const { searchParams } = new URL(request.url)
    const validation = warehouseFiltersSchema.safeParse({
      search: searchParams.get('search') || undefined,
      type: searchParams.get('type') || undefined,
      branchId: searchParams.get('branchId') || undefined,
      isActive: searchParams.get('isActive') ? searchParams.get('isActive') === 'true' : undefined,
      managerId: searchParams.get('managerId') || undefined,
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20')
    })

    if (!validation.success) {
      return NextResponse.json(
        { success: false, error: 'Invalid query parameters', details: validation.error.errors },
        { status: 400 }
      )
    }

    const { search, type, branchId, isActive, managerId, page, limit } = validation.data

    // Build query based on user role and filters
    const query: any = {}

    // Role-based filtering
    if (user.role === 'branch_manager' && user.branchId) {
      query.$or = [
        { branchId: user.branchId },
        { type: { $in: ['main', 'distribution'] } } // Branch managers can see main/distribution warehouses
      ]
    }

    // Apply filters
    if (search) {
      query.$text = { $search: search }
    }

    if (type) {
      query.type = type
    }

    if (branchId) {
      // Check branch access if branchId is specified
      const branchCheck = checkBranchAccess(user, branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
      query.branchId = branchId
    }

    if (isActive !== undefined) {
      query.isActive = isActive
    }

    if (managerId) {
      query.managerId = managerId
    }

    // Execute query with pagination
    const skip = (page - 1) * limit
    const [warehouses, total] = await Promise.all([
      Warehouse.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      Warehouse.countDocuments(query)
    ])

    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      success: true,
      data: warehouses,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error('Get warehouses API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch warehouses' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/warehouses - Create new warehouse
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    await connectDB()

    const body = await request.json()
    const validation = createWarehouseSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { success: false, error: 'Invalid input data', details: validation.error.errors },
        { status: 400 }
      )
    }

    const data = validation.data

    // Debug logging for branch manager permissions
    console.log('🔍 Warehouse creation debug:')
    console.log('  User role:', user.role)
    console.log('  User branchId:', user.branchId)
    console.log('  Data branchId:', data.branchId)
    console.log('  Data type:', data.type)

    // Branch managers can only create warehouses for their own branch
    if (user.role === 'branch_manager') {
      if (!data.branchId || data.branchId !== user.branchId) {
        console.log('❌ Branch manager permission check failed:')
        console.log('  data.branchId exists:', !!data.branchId)
        console.log('  branchId match:', data.branchId === user.branchId)
        return NextResponse.json(
          { success: false, error: 'Branch managers can only create warehouses for their assigned branch' },
          { status: 403 }
        )
      }
      console.log('✅ Branch manager permission check passed')
    }

    // Validate branch warehouse requirements
    if (data.type === 'branch') {
      if (!data.branchId) {
        return NextResponse.json(
          { success: false, error: 'Branch ID is required for branch warehouses' },
          { status: 400 }
        )
      }

      // Check if branch exists and user has access
      const branchCheck = checkBranchAccess(user, data.branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    // Check for duplicate warehouse code
    const existingWarehouse = await Warehouse.findOne({ code: data.code.toUpperCase() })
    if (existingWarehouse) {
      return NextResponse.json(
        { success: false, error: 'Warehouse code already exists' },
        { status: 400 }
      )
    }

    // Create warehouse
    const warehouse = new Warehouse({
      ...data,
      code: data.code.toUpperCase(),
      currentUtilization: 0
    })

    await warehouse.save()

    return NextResponse.json({
      success: true,
      data: warehouse,
      message: 'Warehouse created successfully'
    }, { status: 201 })
  } catch (error) {
    console.error('Create warehouse API error:', error)
    
    if (error.code === 11000) {
      return NextResponse.json(
        { success: false, error: 'Warehouse code already exists' },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { success: false, error: 'Failed to create warehouse' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
