import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getShopById, updateShop, deleteShop } from '@/services/backend'
import { z } from 'zod'

const updateShopSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  location: z.string().min(1).max(100).optional(),
  country: z.string().min(1).max(50).optional(),
  region: z.string().min(1).max(50).optional(),
  description: z.string().min(1).max(500).optional(),
  address: z.string().min(1).max(200).optional(),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/).optional(),
  email: z.string().email().optional(),
  coordinates: z.object({
    lat: z.number().min(-90).max(90),
    lng: z.number().min(-180).max(180)
  }).optional(),
  operatingHours: z.object({
    open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    timezone: z.string()
  }).optional(),
  image: z.string().url().optional(),
  status: z.enum(['Active', 'Inactive', 'Opening Soon', 'Maintenance']).optional()
})

// GET /api/shops/[id] - Get shop by ID
export const GET = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    const shopId = params.id

    // Check branch access
    const branchCheck = checkBranchAccess(user, shopId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    const result = await getShopById(shopId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Shop not found' ? 404 : 500 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get shop API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch shop' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PUT /api/shops/[id] - Update shop
export const PUT = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const shopId = params.id

    // Check branch access
    const branchCheck = checkBranchAccess(user, shopId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validation = updateShopSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const updates = validation.data

    const shop = await Shop.findByIdAndUpdate(
      shopId,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    )

    if (!shop) {
      return NextResponse.json(
        { success: false, error: 'Shop not found' },
        { status: 404 }
      )
    }

    // Log activity
    await logActivity({
      type: 'Product',
      description: `Shop "${shop.name}" updated`,
      userId: user.userId,
      userName: user.username,
      branchId: shopId,
      branchName: shop.name,
      metadata: { updates }
    })

    return NextResponse.json({
      success: true,
      data: shop
    })
  } catch (error) {
    console.error('Update shop error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update shop' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// DELETE /api/shops/[id] - Delete shop
export const DELETE = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const shopId = params.id

    const shop = await Shop.findById(shopId)

    if (!shop) {
      return NextResponse.json(
        { success: false, error: 'Shop not found' },
        { status: 404 }
      )
    }

    // Check if shop has products or orders (you might want to prevent deletion)
    // This is a business logic decision

    await Shop.findByIdAndDelete(shopId)

    // Log activity
    await logActivity({
      type: 'Product',
      description: `Shop "${shop.name}" deleted`,
      userId: user.userId,
      userName: user.username,
      metadata: { shopId, shopName: shop.name }
    })

    return NextResponse.json({
      success: true,
      message: 'Shop deleted successfully'
    })
  } catch (error) {
    console.error('Delete shop error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete shop' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin']
})
