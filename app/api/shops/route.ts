import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getShops, createShop, serviceUtils } from '@/services/backend'
import { z } from 'zod'

const createShopSchema = z.object({
  name: z.string().min(1).max(100),
  location: z.string().min(1).max(100),
  country: z.string().min(1).max(50),
  region: z.string().min(1).max(50),
  managerId: z.string().min(1),
  description: z.string().min(1).max(500),
  address: z.string().min(1).max(200),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/),
  email: z.string().email(),
  coordinates: z.object({
    lat: z.number().min(-90).max(90),
    lng: z.number().min(-180).max(180)
  }).optional(),
  operatingHours: z.object({
    open: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    close: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/),
    timezone: z.string().default('Africa/Blantyre')
  }),
  image: z.string().url()
})

// GET /api/shops - Get all shops
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const pagination = serviceUtils.validatePagination(
      parseInt(searchParams.get('page') || '1'),
      parseInt(searchParams.get('limit') || '10')
    )

    const filters = {
      search: serviceUtils.sanitizeSearchQuery(searchParams.get('search')),
      country: searchParams.get('country') || undefined,
      region: searchParams.get('region') || undefined,
      status: searchParams.get('status') || undefined,
      branchId: searchParams.get('branchId') || undefined
    }

    // Check branch access if branchId is specified
    if (filters.branchId) {
      const branchCheck = checkBranchAccess(user, filters.branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await getShops(filters, pagination, user.role, user.branchId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get shops API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch shops' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/shops - Create new shop
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const validation = createShopSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const result = await createShop(validation.data, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Create shop API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create shop' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin']
})
