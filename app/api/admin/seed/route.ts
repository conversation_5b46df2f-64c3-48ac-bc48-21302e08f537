// app/api/admin/seed/route.ts - Database seeding API endpoint

import { NextRequest, NextResponse } from 'next/server'
import { seedDatabase, clearDatabase, resetDatabase } from '@/lib/database/seed'

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json()

    // Only allow seeding in development environment for safety
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        {
          success: false,
          error: 'Database seeding is not allowed in production'
        },
        { status: 403 }
      )
    }

    let result
    switch (action) {
      case 'seed':
        result = await seedDatabase()
        break
      case 'clear':
        result = await clearDatabase()
        break
      case 'reset':
        result = await resetDatabase()
        break
      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid action. Use "seed", "clear", or "reset"'
          },
          { status: 400 }
        )
    }

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: `Database ${action} completed successfully`
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: `Database ${action} failed`,
          details: result.error
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Seed API error:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Database seeding API',
    endpoints: {
      'POST /api/admin/seed': 'Seed, clear, or reset database',
    },
    actions: {
      seed: 'Add initial data to database',
      clear: 'Remove all data from database',
      reset: 'Clear and reseed database'
    },
    usage: {
      seed: 'POST /api/admin/seed with body: {"action": "seed"}',
      clear: 'POST /api/admin/seed with body: {"action": "clear"}',
      reset: 'POST /api/admin/seed with body: {"action": "reset"}'
    },
    note: 'Only available in development environment'
  })
}
