// app/api/admin/sessions/stats/route.ts - Admin Session Statistics API

import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken } from '@/lib/auth/jwt'
import { addCorsHeaders } from '@/lib/auth/middleware'
import Session from '@/lib/database/models/Session'
import User from '@/lib/database/models/User'

// Helper function to verify admin access
async function verifyAdminAccess(request: NextRequest) {
  const authToken = request.cookies.get('auth-token')?.value
  
  if (!authToken) {
    return { error: 'No authentication token', status: 401 }
  }

  try {
    const decoded = verifyAccessToken(authToken)

    if (decoded.role !== 'overall_admin') {
      return { error: 'Insufficient permissions', status: 403 }
    }

    return { success: true, adminId: decoded.userId }
  } catch (error) {
    return { error: 'Invalid token', status: 401 }
  }
}

// GET /api/admin/sessions/stats - Get session statistics
export async function GET(request: NextRequest) {
  try {
    const adminAuth = await verifyAdminAccess(request)
    if (adminAuth.error) {
      return NextResponse.json(
        { success: false, error: adminAuth.error },
        { status: adminAuth.status }
      )
    }

    const { searchParams } = new URL(request.url)
    const timeframe = searchParams.get('timeframe') || '7d' // 1d, 7d, 30d, 90d

    // Calculate date range based on timeframe
    const now = new Date()
    let startDate: Date
    
    switch (timeframe) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    }

    // Get overall session statistics
    const [
      totalSessions,
      activeSessions,
      totalUsers,
      activeUsers,
      sessionsByDevice,
      sessionsByOS,
      recentLogins,
      topActiveUsers
    ] = await Promise.all([
      // Total sessions in timeframe
      Session.countDocuments({
        loginTime: { $gte: startDate }
      }),

      // Currently active sessions
      Session.countDocuments({
        isActive: true,
        expiresAt: { $gt: now }
      }),

      // Total users
      User.countDocuments(),

      // Users with active sessions
      Session.distinct('userId', {
        isActive: true,
        expiresAt: { $gt: now }
      }).then(userIds => userIds.length),

      // Sessions by device type
      Session.aggregate([
        { $match: { loginTime: { $gte: startDate } } },
        {
          $group: {
            _id: '$deviceInfo.deviceType',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ]),

      // Sessions by operating system
      Session.aggregate([
        { $match: { loginTime: { $gte: startDate } } },
        {
          $group: {
            _id: '$deviceInfo.os',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),

      // Recent login activity (last 24 hours)
      Session.aggregate([
        { 
          $match: { 
            loginTime: { $gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) }
          } 
        },
        {
          $lookup: {
            from: 'users',
            localField: 'userId',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: '$user' },
        {
          $project: {
            loginTime: 1,
            'deviceInfo.deviceType': 1,
            'deviceInfo.browser': 1,
            'deviceInfo.os': 1,
            'location.ipAddress': 1,
            'location.country': 1,
            'user.username': 1,
            'user.name': 1,
            'user.role': 1
          }
        },
        { $sort: { loginTime: -1 } },
        { $limit: 20 }
      ]),

      // Top users by session count
      Session.aggregate([
        { $match: { loginTime: { $gte: startDate } } },
        {
          $group: {
            _id: '$userId',
            sessionCount: { $sum: 1 },
            lastLogin: { $max: '$loginTime' },
            activeSessionCount: {
              $sum: {
                $cond: [
                  {
                    $and: [
                      { $eq: ['$isActive', true] },
                      { $gt: ['$expiresAt', now] }
                    ]
                  },
                  1,
                  0
                ]
              }
            }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'user'
          }
        },
        { $unwind: '$user' },
        {
          $project: {
            sessionCount: 1,
            lastLogin: 1,
            activeSessionCount: 1,
            'user.username': 1,
            'user.name': 1,
            'user.email': 1,
            'user.role': 1
          }
        },
        { $sort: { sessionCount: -1 } },
        { $limit: 10 }
      ])
    ])

    // Calculate session trends (daily login counts for the timeframe)
    const sessionTrends = await Session.aggregate([
      { $match: { loginTime: { $gte: startDate } } },
      {
        $group: {
          _id: {
            year: { $year: '$loginTime' },
            month: { $month: '$loginTime' },
            day: { $dayOfMonth: '$loginTime' }
          },
          count: { $sum: 1 },
          uniqueUsers: { $addToSet: '$userId' }
        }
      },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            }
          },
          loginCount: '$count',
          uniqueUserCount: { $size: '$uniqueUsers' }
        }
      },
      { $sort: { date: 1 } }
    ])

    // Security alerts (suspicious activity)
    const securityAlerts = await Session.aggregate([
      { $match: { loginTime: { $gte: startDate } } },
      {
        $group: {
          _id: '$userId',
          uniqueIPs: { $addToSet: '$location.ipAddress' },
          uniqueDevices: { $addToSet: '$deviceInfo.device' },
          sessionCount: { $sum: 1 }
        }
      },
      {
        $match: {
          $or: [
            { $expr: { $gt: [{ $size: '$uniqueIPs' }, 3] } }, // More than 3 IPs
            { $expr: { $gt: [{ $size: '$uniqueDevices' }, 5] } }, // More than 5 devices
            { sessionCount: { $gt: 20 } } // More than 20 sessions
          ]
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          'user.username': 1,
          'user.name': 1,
          'user.email': 1,
          uniqueIPCount: { $size: '$uniqueIPs' },
          uniqueDeviceCount: { $size: '$uniqueDevices' },
          sessionCount: 1,
          riskLevel: {
            $switch: {
              branches: [
                { case: { $gt: ['$sessionCount', 50] }, then: 'high' },
                { case: { $gt: [{ $size: '$uniqueIPs' }, 5] }, then: 'high' },
                { case: { $gt: [{ $size: '$uniqueDevices' }, 8] }, then: 'medium' },
                { case: { $gt: ['$sessionCount', 20] }, then: 'medium' }
              ],
              default: 'low'
            }
          }
        }
      },
      { $sort: { sessionCount: -1 } },
      { $limit: 10 }
    ])

    const response = NextResponse.json({
      success: true,
      stats: {
        overview: {
          totalSessions,
          activeSessions,
          totalUsers,
          activeUsers,
          timeframe
        },
        deviceBreakdown: sessionsByDevice,
        osBreakdown: sessionsByOS,
        sessionTrends,
        recentActivity: recentLogins,
        topUsers: topActiveUsers,
        securityAlerts
      }
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Admin session stats error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve session statistics' },
      { status: 500 }
    )
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
