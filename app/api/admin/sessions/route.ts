// app/api/admin/sessions/route.ts - Admin Session Management API

import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken } from '@/lib/auth/jwt'
import { addCorsHeaders } from '@/lib/auth/middleware'
import Session from '@/lib/database/models/Session'
import User from '@/lib/database/models/User'
import AuditLog from '@/lib/database/models/AuditLog'
import { logActivity } from '@/lib/database'
import mongoose from 'mongoose'

// Helper function to verify admin access
async function verifyAdminAccess(request: NextRequest) {
  const authToken = request.cookies.get('auth-token')?.value
  
  if (!authToken) {
    return { error: 'No authentication token', status: 401 }
  }

  try {
    const decoded = verifyAccessToken(authToken)

    if (decoded.role !== 'overall_admin') {
      return { error: 'Insufficient permissions', status: 403 }
    }

    return { success: true, adminId: decoded.userId, adminRole: decoded.role }
  } catch (error) {
    return { error: 'Invalid token', status: 401 }
  }
}

// GET /api/admin/sessions - Get all user sessions with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const adminAuth = await verifyAdminAccess(request)
    if (adminAuth.error) {
      return NextResponse.json(
        { success: false, error: adminAuth.error },
        { status: adminAuth.status }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const userId = searchParams.get('userId')
    const isActive = searchParams.get('isActive')
    const deviceType = searchParams.get('deviceType')
    const sortBy = searchParams.get('sortBy') || 'loginTime'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Build filter query
    const filter: any = {}
    
    if (userId) {
      filter.userId = new mongoose.Types.ObjectId(userId)
    }
    
    if (isActive !== null && isActive !== undefined) {
      filter.isActive = isActive === 'true'
      if (filter.isActive) {
        filter.expiresAt = { $gt: new Date() }
      }
    }
    
    if (deviceType) {
      filter['deviceInfo.deviceType'] = deviceType
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Get sessions with user information
    const sessions = await Session.aggregate([
      { $match: filter },
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          _id: 1,
          userId: 1,
          sessionToken: 1,
          deviceInfo: 1,
          location: 1,
          loginTime: 1,
          lastActivity: 1,
          expiresAt: 1,
          isActive: 1,
          logoutTime: 1,
          logoutReason: 1,
          'user.username': 1,
          'user.email': 1,
          'user.name': 1,
          'user.role': 1
        }
      },
      { $sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 } },
      { $skip: skip },
      { $limit: limit }
    ])

    // Get total count for pagination
    const totalCount = await Session.countDocuments(filter)
    const totalPages = Math.ceil(totalCount / limit)

    // Log admin session viewing activity
    await AuditLog.logSessionAction({
      action: 'session_view',
      adminId: adminAuth.adminId!,
      adminUsername: 'admin',
      adminName: 'Admin',
      description: `Viewed ${sessions.length} sessions (page ${page} of ${totalPages})`,
      metadata: {
        page,
        limit,
        totalCount,
        filters: { userId, isActive, deviceType }
      },
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    })

    const response = NextResponse.json({
      success: true,
      sessions,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        limit,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Admin get sessions error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve sessions' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/sessions - Force terminate sessions
export async function DELETE(request: NextRequest) {
  try {
    const adminAuth = await verifyAdminAccess(request)
    if (adminAuth.error) {
      return NextResponse.json(
        { success: false, error: adminAuth.error },
        { status: adminAuth.status }
      )
    }

    const body = await request.json()
    const { sessionIds, userId, reason = 'admin_forced' } = body

    let terminatedCount = 0
    let affectedUsers: string[] = []

    if (sessionIds && Array.isArray(sessionIds)) {
      // Terminate specific sessions
      const sessions = await Session.find({ 
        _id: { $in: sessionIds.map(id => new mongoose.Types.ObjectId(id)) },
        isActive: true 
      })

      for (const session of sessions) {
        await session.terminate('forced')
        terminatedCount++
        
        if (!affectedUsers.includes(session.userId.toString())) {
          affectedUsers.push(session.userId.toString())
        }
      }
    } else if (userId) {
      // Terminate all sessions for a specific user
      const result = await Session.updateMany(
        { 
          userId: new mongoose.Types.ObjectId(userId), 
          isActive: true 
        },
        { 
          isActive: false, 
          logoutTime: new Date(), 
          logoutReason: 'forced' 
        }
      )
      
      terminatedCount = result.modifiedCount || 0
      affectedUsers = [userId]
    } else {
      return NextResponse.json(
        { success: false, error: 'Either sessionIds or userId must be provided' },
        { status: 400 }
      )
    }

    // Log admin activity for each affected user
    for (const affectedUserId of affectedUsers) {
      const user = await User.findById(affectedUserId)
      if (user) {
        // Log to activity log
        await logActivity({
          type: 'Admin',
          description: `Admin force terminated ${terminatedCount} session(s) for user ${user.username}`,
          userId: adminAuth.adminId!,
          userName: 'Admin',
          metadata: {
            action: 'force_terminate_sessions',
            targetUserId: affectedUserId,
            targetUsername: user.username,
            sessionCount: terminatedCount,
            reason
          }
        })

        // Log to audit log for security compliance
        await AuditLog.logSessionAction({
          action: sessionIds ? 'session_terminate' : 'session_terminate_all',
          adminId: adminAuth.adminId!,
          adminUsername: 'admin', // You might want to get actual admin username
          adminName: 'Admin',
          targetUserId: affectedUserId,
          targetUsername: user.username,
          targetUserEmail: user.email,
          description: `Force terminated ${terminatedCount} session(s) for user ${user.username}. Reason: ${reason}`,
          metadata: {
            sessionCount: terminatedCount,
            reason,
            sessionIds: sessionIds || 'all'
          },
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown'
        })
      }
    }

    const response = NextResponse.json({
      success: true,
      message: `${terminatedCount} session(s) terminated`,
      terminatedCount,
      affectedUsers: affectedUsers.length
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Admin terminate sessions error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to terminate sessions' },
      { status: 500 }
    )
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'GET, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
