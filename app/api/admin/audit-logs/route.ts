// app/api/admin/audit-logs/route.ts - Admin Audit Log API

import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken } from '@/lib/auth/jwt'
import { addCorsHeaders } from '@/lib/auth/middleware'
import AuditLog from '@/lib/database/models/AuditLog'

// Helper function to verify admin access
async function verifyAdminAccess(request: NextRequest) {
  const authToken = request.cookies.get('auth-token')?.value
  
  if (!authToken) {
    return { error: 'No authentication token', status: 401 }
  }

  try {
    const decoded = verifyAccessToken(authToken)

    if (decoded.role !== 'overall_admin') {
      return { error: 'Insufficient permissions', status: 403 }
    }

    return { success: true, adminId: decoded.userId }
  } catch (error) {
    return { error: 'Invalid token', status: 401 }
  }
}

// GET /api/admin/audit-logs - Get audit logs with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const adminAuth = await verifyAdminAccess(request)
    if (adminAuth.error) {
      return NextResponse.json(
        { success: false, error: adminAuth.error },
        { status: adminAuth.status }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const category = searchParams.get('category')
    const severity = searchParams.get('severity')
    const action = searchParams.get('action')
    const adminId = searchParams.get('adminId')
    const targetUserId = searchParams.get('targetUserId')
    const timeframe = searchParams.get('timeframe') || '7d'
    const success = searchParams.get('success')

    // Calculate date range
    const now = new Date()
    let startDate: Date
    
    switch (timeframe) {
      case '1d':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    }

    // Build filter query
    const filter: any = {
      timestamp: { $gte: startDate }
    }
    
    if (category) filter.category = category
    if (severity) filter.severity = severity
    if (action) filter.action = action
    if (adminId) filter.adminId = adminId
    if (targetUserId) filter.targetUserId = targetUserId
    if (success !== null && success !== undefined) {
      filter.success = success === 'true'
    }

    // Calculate pagination
    const skip = (page - 1) * limit

    // Get audit logs
    const logs = await AuditLog.find(filter)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limit)
      .populate('adminId', 'username name email')
      .populate('targetUserId', 'username name email')

    // Get total count for pagination
    const totalCount = await AuditLog.countDocuments(filter)
    const totalPages = Math.ceil(totalCount / limit)

    // Get activity statistics
    const stats = await AuditLog.getActivityStats(
      timeframe === '1d' ? 1 : 
      timeframe === '7d' ? 7 : 
      timeframe === '30d' ? 30 : 90
    )

    const response = NextResponse.json({
      success: true,
      logs,
      stats: stats[0] || {},
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        limit,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Admin audit logs error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve audit logs' },
      { status: 500 }
    )
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
