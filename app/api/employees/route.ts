import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getEmployees, createEmployee, serviceUtils } from '@/services/backend'
import { z } from 'zod'

const addressSchema = z.object({
  street: z.string().min(1).max(200),
  city: z.string().min(1).max(50),
  region: z.string().min(1).max(50),
  country: z.string().min(1).max(50),
  postalCode: z.string().min(1).max(20)
})

const emergencyContactSchema = z.object({
  name: z.string().min(1).max(100),
  relationship: z.string().min(1).max(50),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/)
})

const createEmployeeSchema = z.object({
  firstName: z.string().min(1).max(50),
  lastName: z.string().min(1).max(50),
  email: z.string().email(),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/),
  position: z.string().min(1).max(100),
  department: z.string().min(1).max(100),
  branchId: z.string().min(1),
  salary: z.number().min(0),
  hireDate: z.string().transform(str => new Date(str)),
  address: addressSchema,
  emergencyContact: emergencyContactSchema,
  userId: z.string().optional()
})

// GET /api/employees - Get all employees
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const pagination = serviceUtils.validatePagination(
      parseInt(searchParams.get('page') || '1'),
      parseInt(searchParams.get('limit') || '10')
    )

    const filters = {
      search: serviceUtils.sanitizeSearchQuery(searchParams.get('search') || undefined),
      branchId: searchParams.get('branchId') || undefined,
      department: searchParams.get('department') || undefined,
      position: searchParams.get('position') || undefined,
      isActive: searchParams.get('isActive') === 'true' ? true : searchParams.get('isActive') === 'false' ? false : undefined,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc'
    }

    // Check branch access if branchId is specified
    if (filters.branchId) {
      const branchCheck = checkBranchAccess(user, filters.branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await getEmployees(filters, pagination, user.role, user.branchId)

    console.log('API: Backend service result:', {
      success: result.success,
      dataLength: result.data?.length || 0,
      pagination: result.pagination,
      error: result.error,
      actualData: JSON.stringify(result.data, null, 2)
    })

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get employees API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch employees' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/employees - Create new employee
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    console.log('API: Received employee data:', body)

    const validation = createEmployeeSchema.safeParse(body)

    if (!validation.success) {
      console.log('API: Validation failed:', validation.error.errors)
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data
    console.log('API: Validated data:', data)

    // Check branch access
    const branchCheck = checkBranchAccess(user, data.branchId)
    if (!branchCheck.hasAccess) {
      console.log('API: Branch access denied:', branchCheck.error)
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    console.log('=== API ROUTE CREATE EMPLOYEE START ===')
    console.log('API: Creating employee with user:', user.userId, user.username)
    console.log('API: Final validated data being sent to service:', JSON.stringify(data, null, 2))

    const result = await createEmployee(data, user.userId, user.username)

    console.log('API: Employee creation result:', JSON.stringify(result, null, 2))
    console.log('API: Result success:', result.success)
    console.log('API: Result data:', result.data)
    console.log('API: Result error:', result.error)

    if (!result.success) {
      console.log('API: Employee creation failed:', result.error)
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    console.log('API: Employee created successfully')
    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Create employee API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create employee' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
