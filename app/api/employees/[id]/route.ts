import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getEmployeeById, updateEmployee, deleteEmployee } from '@/services/backend'
import { z } from 'zod'

const addressSchema = z.object({
  street: z.string().min(1).max(200),
  city: z.string().min(1).max(50),
  region: z.string().min(1).max(50),
  country: z.string().min(1).max(50),
  postalCode: z.string().min(1).max(20)
})

const emergencyContactSchema = z.object({
  name: z.string().min(1).max(100),
  relationship: z.string().min(1).max(50),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/)
})

const updateEmployeeSchema = z.object({
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  email: z.string().email().optional(),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/).optional(),
  position: z.string().min(1).max(100).optional(),
  department: z.string().min(1).max(100).optional(),
  branchId: z.string().min(1).optional(),
  salary: z.number().min(0).optional(),
  address: addressSchema.optional(),
  emergencyContact: emergencyContactSchema.optional(),
  isActive: z.boolean().optional()
})

// GET /api/employees/[id] - Get employee by ID
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    // Extract employee ID from URL path
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const employeeId = pathSegments[pathSegments.length - 1]

    if (!employeeId || employeeId === 'employees') {
      return NextResponse.json(
        { success: false, error: 'Employee ID is required' },
        { status: 400 }
      )
    }

    const result = await getEmployeeById(employeeId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: result.error === 'Employee not found' ? 404 : 500 }
      )
    }

    // Check branch access
    const branchCheck = checkBranchAccess(user, result.data!.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get employee API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch employee' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PUT /api/employees/[id] - Update employee
export const PUT = withAuth(async (request: NextRequest, user) => {
  try {
    // Extract employee ID from URL path
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const employeeId = pathSegments[pathSegments.length - 1]

    if (!employeeId || employeeId === 'employees') {
      return NextResponse.json(
        { success: false, error: 'Employee ID is required' },
        { status: 400 }
      )
    }
    const body = await request.json()
    const validation = updateEmployeeSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    // Get current employee to check branch access
    const currentEmployee = await getEmployeeById(employeeId)
    if (!currentEmployee.success) {
      return NextResponse.json(
        { success: false, error: currentEmployee.error },
        { status: currentEmployee.error === 'Employee not found' ? 404 : 500 }
      )
    }

    // Check branch access for current employee
    const currentBranchCheck = checkBranchAccess(user, currentEmployee.data!.branchId)
    if (!currentBranchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: currentBranchCheck.error },
        { status: 403 }
      )
    }

    // Check branch access for new branch if being updated
    if (data.branchId) {
      const newBranchCheck = checkBranchAccess(user, data.branchId)
      if (!newBranchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: newBranchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await updateEmployee(employeeId, data, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Update employee API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update employee' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// DELETE /api/employees/[id] - Delete employee (soft delete)
export const DELETE = withAuth(async (request: NextRequest, user) => {
  try {
    // Extract employee ID from URL path
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/')
    const employeeId = pathSegments[pathSegments.length - 1]

    if (!employeeId || employeeId === 'employees') {
      return NextResponse.json(
        { success: false, error: 'Employee ID is required' },
        { status: 400 }
      )
    }

    // Get current employee to check branch access
    const currentEmployee = await getEmployeeById(employeeId)
    if (!currentEmployee.success) {
      return NextResponse.json(
        { success: false, error: currentEmployee.error },
        { status: currentEmployee.error === 'Employee not found' ? 404 : 500 }
      )
    }

    // Check branch access
    const branchCheck = checkBranchAccess(user, currentEmployee.data!.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    const result = await deleteEmployee(employeeId, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Delete employee API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete employee' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
