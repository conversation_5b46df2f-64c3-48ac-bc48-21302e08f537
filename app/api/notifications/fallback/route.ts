import { NextRequest, NextResponse } from 'next/server'
import { authMiddleware } from '@/lib/auth/middleware'

/**
 * GET /api/notifications/fallback - Fallback notification endpoint for HTTP polling
 * Used when Socket.io is unavailable
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await authMiddleware(request)
    if (!authResult.success) {
      return authResult.response || NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { user } = authResult

    // For now, return empty notifications array
    // In a real implementation, you would fetch notifications from database
    const notifications = [
      {
        id: `fallback-${Date.now()}`,
        type: 'info',
        title: 'System Status',
        message: 'Real-time notifications are using fallback mode',
        timestamp: new Date().toISOString(),
        userId: user.id
      }
    ]

    return NextResponse.json({
      success: true,
      notifications,
      fallback: true,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Fallback notifications error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch notifications',
        fallback: true 
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/notifications/fallback - Send notification via HTTP fallback
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const authResult = await verifyAuth(request)
    if (!authResult.success) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { event, data } = body

    // Log the fallback event (in real implementation, you'd process it)
    console.log('Fallback notification event:', { event, data })

    return NextResponse.json({
      success: true,
      message: 'Event processed via fallback',
      fallback: true,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Fallback notification POST error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process notification',
        fallback: true 
      },
      { status: 500 }
    )
  }
}
