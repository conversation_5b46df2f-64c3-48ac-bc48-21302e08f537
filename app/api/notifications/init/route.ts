import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken } from '@/lib/auth/jwt'
import { addCorsHeaders } from '@/lib/auth/middleware'
import { NotificationInitService } from '@/lib/services/notification-init'
import { NotificationHelpers } from '@/lib/services/notification-helpers'

// Helper function to verify authentication
async function verifyAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader?.startsWith('Bearer ')) {
    return { error: 'Missing or invalid authorization header', status: 401 }
  }

  const token = authHeader.substring(7)
  
  try {
    const decoded = verifyAccessToken(token)
    return { success: true, user: decoded }
  } catch (error) {
    return { error: 'Invalid token', status: 401 }
  }
}

/**
 * POST /api/notifications/init - Initialize notification system for user
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuth(request)
    if (!auth.success) {
      return NextResponse.json(
        { success: false, error: auth.error },
        { status: auth.status }
      )
    }

    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'init_preferences':
        // Initialize default preferences for the user
        const preferences = await NotificationInitService.createDefaultPreferences(
          auth.user.userId,
          auth.user.role
        )

        const response = NextResponse.json({
          success: true,
          message: 'Notification preferences initialized',
          preferences: {
            userId: preferences.userId,
            channels: preferences.channels,
            categories: preferences.categories,
            frequency: preferences.frequency,
            settings: preferences.settings
          }
        })

        return addCorsHeaders(response)

      case 'send_welcome':
        // Send welcome notification
        await NotificationInitService.sendWelcomeNotification(auth.user.userId, {
          username: auth.user.username,
          email: auth.user.email,
          role: auth.user.role
        })

        return addCorsHeaders(NextResponse.json({
          success: true,
          message: 'Welcome notification sent'
        }))

      case 'create_samples':
        // Create sample notifications for testing
        if (auth.user.role !== 'overall_admin') {
          return NextResponse.json(
            { success: false, error: 'Only admins can create sample notifications' },
            { status: 403 }
          )
        }

        const samples = await NotificationInitService.createSampleNotifications(auth.user.userId)

        return addCorsHeaders(NextResponse.json({
          success: true,
          message: 'Sample notifications created',
          count: samples.length
        }))

      case 'init_all_users':
        // Initialize preferences for all users (admin only)
        if (auth.user.role !== 'overall_admin') {
          return NextResponse.json(
            { success: false, error: 'Only admins can initialize all users' },
            { status: 403 }
          )
        }

        const results = await NotificationInitService.initializeAllUsers()
        const successCount = results.filter(r => r.success).length
        const failureCount = results.filter(r => !r.success).length

        return addCorsHeaders(NextResponse.json({
          success: true,
          message: 'User initialization completed',
          results: {
            total: results.length,
            success: successCount,
            failures: failureCount
          }
        }))

      case 'test_business_events':
        // Test business event notifications
        if (auth.user.role !== 'overall_admin') {
          return NextResponse.json(
            { success: false, error: 'Only admins can test business events' },
            { status: 403 }
          )
        }

        // Create various business event notifications
        const testNotifications = await Promise.all([
          // Low stock alert
          NotificationHelpers.notifyLowStock({
            productId: 'test_prod_001',
            productName: 'Test Product - iPhone 15',
            sku: 'TEST-IPH15-001',
            currentStock: 2,
            minimumStock: 10,
            branchId: 'test_branch_001',
            branchName: 'Test Branch'
          }, auth.user.userId),

          // New order
          NotificationHelpers.notifyOrderPlaced({
            orderId: 'test_ord_001',
            orderNumber: 'TEST-ORD-001',
            customerId: 'test_cust_001',
            customerName: 'Test Customer',
            customerEmail: '<EMAIL>',
            totalAmount: 999.99,
            currency: 'USD',
            items: [
              { productId: 'test_prod_001', productName: 'Test Product', quantity: 1, price: 999.99 }
            ],
            branchId: 'test_branch_001'
          }, auth.user.userId),

          // Security alert
          NotificationHelpers.notifySecurityAlert({
            userId: auth.user.userId,
            username: auth.user.username,
            alertType: 'Multiple Login Attempts',
            ipAddress: '*************',
            location: 'Test Location',
            device: 'Test Device',
            riskLevel: 'medium',
            details: 'Multiple failed login attempts detected from unusual location'
          }, auth.user.userId),

          // Campaign launch
          NotificationHelpers.notifyCampaignLaunch({
            campaignId: 'test_camp_001',
            campaignName: 'Test Flash Sale',
            campaignType: 'sale',
            discountPercentage: 25,
            validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
            promoCode: 'TEST25',
            targetAudience: ['customer']
          }, auth.user.userId),

          // Payment received
          NotificationHelpers.notifyPaymentReceived({
            transactionId: 'test_txn_001',
            amount: 999.99,
            currency: 'USD',
            customerId: 'test_cust_001',
            customerName: 'Test Customer',
            orderId: 'test_ord_001',
            paymentMethod: 'Credit Card'
          }, auth.user.userId)
        ])

        return addCorsHeaders(NextResponse.json({
          success: true,
          message: 'Test business event notifications created',
          count: testNotifications.length,
          notifications: testNotifications.map(n => ({
            id: n.id,
            category: n.category,
            type: n.type,
            title: n.title
          }))
        }))

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Notification init error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to initialize notifications' },
      { status: 500 }
    )
  }
}

/**
 * GET /api/notifications/init - Get initialization status
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuth(request)
    if (!auth.success) {
      return NextResponse.json(
        { success: false, error: auth.error },
        { status: auth.status }
      )
    }

    // Check if user has preferences initialized
    const { NotificationService } = await import('@/lib/services/notification-service')
    const { notifications, total } = await NotificationService.getUserNotifications(auth.user.userId, { limit: 1 })
    
    const response = NextResponse.json({
      success: true,
      status: {
        hasNotifications: total > 0,
        notificationCount: total,
        userId: auth.user.userId,
        userRole: auth.user.role
      }
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Get init status error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get initialization status' },
      { status: 500 }
    )
  }
}
