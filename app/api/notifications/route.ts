// app/api/notifications/route.ts - Comprehensive Notifications API routes

import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken } from '@/lib/auth/jwt'
import { addCorsHeaders } from '@/lib/auth/middleware'
import { NotificationService } from '@/lib/services/notification-service'
import type { CreateNotificationOptions, NotificationFilters } from '@/lib/services/notification-service'

// Helper function to verify authentication
async function verifyAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader?.startsWith('Bearer ')) {
    return { error: 'Missing or invalid authorization header', status: 401 }
  }

  const token = authHeader.substring(7)

  try {
    const decoded = verifyAccessToken(token)
    return { success: true, user: decoded }
  } catch (error) {
    return { error: 'Invalid token', status: 401 }
  }
}

/**
 * GET /api/notifications - Get user notifications
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuth(request)
    if (!auth.success) {
      return NextResponse.json(
        { success: false, error: auth.error },
        { status: auth.status }
      )
    }

    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const filters: NotificationFilters = {
      category: searchParams.get('category') as any || undefined,
      type: searchParams.get('type') || undefined,
      priority: searchParams.get('priority') as any || undefined,
      status: searchParams.get('status') || undefined,
      limit: parseInt(searchParams.get('limit') || '50'),
      skip: parseInt(searchParams.get('skip') || '0')
    }

    // Parse date filters
    if (searchParams.get('dateFrom')) {
      filters.dateFrom = new Date(searchParams.get('dateFrom')!)
    }
    if (searchParams.get('dateTo')) {
      filters.dateTo = new Date(searchParams.get('dateTo')!)
    }

    // Get notifications for the authenticated user
    const result = await NotificationService.getUserNotifications(auth.user.userId, filters)

    const response = NextResponse.json({
      success: true,
      notifications: result.notifications,
      total: result.total,
      pagination: {
        limit: filters.limit,
        skip: filters.skip,
        hasMore: (filters.skip || 0) + (filters.limit || 50) < result.total
      }
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Get notifications error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get notifications' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/notifications - Create a new notification (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuth(request)
    if (!auth.success) {
      return NextResponse.json(
        { success: false, error: auth.error },
        { status: auth.status }
      )
    }

    // Check if user has permission to create notifications
    if (auth.user.role !== 'overall_admin' && auth.user.role !== 'branch_manager') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body = await request.json()

    // Validate required fields
    const requiredFields = ['category', 'type', 'title', 'message', 'target']
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Create notification options
    const options: CreateNotificationOptions = {
      category: body.category,
      type: body.type,
      priority: body.priority || 'medium',
      title: body.title,
      message: body.message,
      description: body.description,
      icon: body.icon,
      image: body.image,
      color: body.color,
      badge: body.badge,
      target: body.target,
      data: body.data,
      actions: body.actions,
      scheduledFor: body.scheduledFor ? new Date(body.scheduledFor) : undefined,
      expiresAt: body.expiresAt ? new Date(body.expiresAt) : undefined,
      channels: body.channels || ['in_app'],
      createdBy: auth.user.userId,
      source: body.source || 'admin'
    }

    // Create the notification
    const notification = await NotificationService.createNotification(options)

    const response = NextResponse.json({
      success: true,
      notification: {
        id: notification.id,
        category: notification.category,
        type: notification.type,
        priority: notification.priority,
        title: notification.title,
        message: notification.message,
        createdAt: notification.createdAt,
        scheduledFor: notification.scheduledFor,
        expiresAt: notification.expiresAt
      }
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Create notification error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create notification' },
      { status: 500 }
    )
  }
}

/**
 * PATCH /api/notifications - Mark notifications as read
 */
export async function PATCH(request: NextRequest) {
  try {
    const auth = await verifyAuth(request)
    if (!auth.success) {
      return NextResponse.json(
        { success: false, error: auth.error },
        { status: auth.status }
      )
    }

    const body = await request.json()

    if (body.action === 'mark_read') {
      if (body.notificationIds && Array.isArray(body.notificationIds)) {
        // Mark multiple notifications as read
        await NotificationService.markMultipleAsRead(body.notificationIds, auth.user.userId)
      } else if (body.notificationId) {
        // Mark single notification as read
        await NotificationService.markAsRead(body.notificationId, auth.user.userId)
      } else {
        return NextResponse.json(
          { success: false, error: 'Missing notificationId or notificationIds' },
          { status: 400 }
        )
      }

      const response = NextResponse.json({
        success: true,
        message: 'Notifications marked as read'
      })

      return addCorsHeaders(response)
    } else {
      return NextResponse.json(
        { success: false, error: 'Invalid action' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Update notifications error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update notifications' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/notifications - Delete notifications (admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    const auth = await verifyAuth(request)
    if (!auth.success) {
      return NextResponse.json(
        { success: false, error: auth.error },
        { status: auth.status }
      )
    }

    // Check if user has permission to delete notifications
    if (auth.user.role !== 'overall_admin') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body = await request.json()

    if (!body.notificationIds || !Array.isArray(body.notificationIds)) {
      return NextResponse.json(
        { success: false, error: 'Missing notificationIds array' },
        { status: 400 }
      )
    }

    // TODO: Implement notification deletion
    // For now, we'll mark them as archived instead of deleting
    // This preserves audit trail and analytics data

    const response = NextResponse.json({
      success: true,
      message: 'Notifications deleted'
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Delete notifications error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete notifications' },
      { status: 500 }
    )
  }
}
