// app/api/notifications/sms/route.ts - SMS notification API route

import { NextRequest, NextResponse } from 'next/server'
import { sendSMSNotification } from '@/services/backend/notificationsService'

/**
 * POST /api/notifications/sms - Send SMS notification
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.to || !body.message) {
      return NextResponse.json(
        { success: false, error: 'To and message are required' },
        { status: 400 }
      )
    }

    // Send SMS notification
    const success = await sendSMSNotification(body)

    if (success) {
      return NextResponse.json({
        success: true,
        data: { message: 'SMS notification sent successfully' }
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send SMS notification' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('POST /api/notifications/sms error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to send SMS notification' },
      { status: 500 }
    )
  }
}
