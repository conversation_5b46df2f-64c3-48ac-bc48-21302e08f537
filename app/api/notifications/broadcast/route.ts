// app/api/notifications/broadcast/route.ts - Broadcast notification API route

import { NextRequest, NextResponse } from 'next/server'
import { broadcastRealtimeNotification } from '@/services/backend/notificationsService'

/**
 * POST /api/notifications/broadcast - Broadcast notification to all users or specific users
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userIds, ...notificationData } = body
    
    // Validate required fields
    if (!notificationData.title || !notificationData.message) {
      return NextResponse.json(
        { success: false, error: 'Title and message are required' },
        { status: 400 }
      )
    }

    // Broadcast notification
    const success = await broadcastRealtimeNotification(notificationData, userIds)

    if (success) {
      return NextResponse.json({
        success: true,
        data: { message: 'Notification broadcasted successfully' }
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to broadcast notification' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('POST /api/notifications/broadcast error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to broadcast notification' },
      { status: 500 }
    )
  }
}
