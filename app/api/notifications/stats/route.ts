import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken } from '@/lib/auth/jwt'
import { addCorsHeaders } from '@/lib/auth/middleware'
import { NotificationService } from '@/lib/services/notification-service'
import type { NotificationFilters } from '@/lib/services/notification-service'

// Helper function to verify authentication
async function verifyAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader?.startsWith('Bearer ')) {
    return { error: 'Missing or invalid authorization header', status: 401 }
  }

  const token = authHeader.substring(7)
  
  try {
    const decoded = verifyAccessToken(token)
    return { success: true, user: decoded }
  } catch (error) {
    return { error: 'Invalid token', status: 401 }
  }
}

/**
 * GET /api/notifications/stats - Get notification statistics
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuth(request)
    if (!auth.success) {
      return NextResponse.json(
        { success: false, error: auth.error },
        { status: auth.status }
      )
    }

    // Check if user has permission to view stats (admin only)
    if (auth.user.role !== 'overall_admin' && auth.user.role !== 'branch_manager') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    
    // Parse query parameters
    const filters: NotificationFilters = {
      category: searchParams.get('category') as any || undefined,
      type: searchParams.get('type') || undefined,
      priority: searchParams.get('priority') as any || undefined
    }

    // Parse date filters
    if (searchParams.get('dateFrom')) {
      filters.dateFrom = new Date(searchParams.get('dateFrom')!)
    }
    if (searchParams.get('dateTo')) {
      filters.dateTo = new Date(searchParams.get('dateTo')!)
    }

    // Get notification statistics
    const stats = await NotificationService.getNotificationStats(filters)

    // Calculate additional metrics
    const deliveryRate = stats.total > 0 ? (stats.delivered / stats.total) * 100 : 0
    const openRate = stats.delivered > 0 ? (stats.opened / stats.delivered) * 100 : 0
    const clickRate = stats.opened > 0 ? (stats.clicked / stats.opened) * 100 : 0

    // Process category breakdown
    const categoryBreakdown = {}
    const priorityBreakdown = {}
    const statusBreakdown = {}

    stats.byCategory.forEach(item => {
      // Category breakdown
      if (!categoryBreakdown[item.category]) {
        categoryBreakdown[item.category] = { total: 0, unread: 0, read: 0 }
      }
      categoryBreakdown[item.category].total += 1
      if (item.status === 'unread') categoryBreakdown[item.category].unread += 1
      if (item.status === 'read') categoryBreakdown[item.category].read += 1

      // Priority breakdown
      if (!priorityBreakdown[item.priority]) {
        priorityBreakdown[item.priority] = 0
      }
      priorityBreakdown[item.priority] += 1

      // Status breakdown
      if (!statusBreakdown[item.status]) {
        statusBreakdown[item.status] = 0
      }
      statusBreakdown[item.status] += 1
    })

    const response = NextResponse.json({
      success: true,
      stats: {
        overview: {
          total: stats.total,
          unread: stats.unread,
          read: stats.read,
          delivered: stats.delivered,
          opened: stats.opened,
          clicked: stats.clicked
        },
        metrics: {
          deliveryRate: Math.round(deliveryRate * 100) / 100,
          openRate: Math.round(openRate * 100) / 100,
          clickRate: Math.round(clickRate * 100) / 100
        },
        breakdown: {
          byCategory: categoryBreakdown,
          byPriority: priorityBreakdown,
          byStatus: statusBreakdown
        },
        filters: filters
      }
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Get notification stats error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get notification statistics' },
      { status: 500 }
    )
  }
}
