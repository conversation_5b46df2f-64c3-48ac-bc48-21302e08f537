// app/api/notifications/send/route.ts - Send notification API route

import { NextRequest, NextResponse } from 'next/server'
import { sendRealtimeNotification } from '@/services/backend/notificationsService'

/**
 * POST /api/notifications/send - Send notification to specific user
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { recipientId, ...notificationData } = body
    
    // Validate required fields
    if (!recipientId) {
      return NextResponse.json(
        { success: false, error: 'Recipient ID is required' },
        { status: 400 }
      )
    }

    if (!notificationData.title || !notificationData.message) {
      return NextResponse.json(
        { success: false, error: 'Title and message are required' },
        { status: 400 }
      )
    }

    // Send real-time notification
    const success = await sendRealtimeNotification(recipientId, notificationData)

    if (success) {
      return NextResponse.json({
        success: true,
        data: { message: 'Notification sent successfully' }
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send notification' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('POST /api/notifications/send error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to send notification' },
      { status: 500 }
    )
  }
}
