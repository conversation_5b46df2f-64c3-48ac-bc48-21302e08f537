import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken } from '@/lib/auth/jwt'
import { addCorsHeaders } from '@/lib/auth/middleware'
import { connectDB } from '@/lib/database'
import NotificationPreferences from '@/lib/database/models/NotificationPreferences'

// Helper function to verify authentication
async function verifyAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader?.startsWith('Bearer ')) {
    return { error: 'Missing or invalid authorization header', status: 401 }
  }

  const token = authHeader.substring(7)
  
  try {
    const decoded = verifyAccessToken(token)
    return { success: true, user: decoded }
  } catch (error) {
    return { error: 'Invalid token', status: 401 }
  }
}

/**
 * GET /api/notifications/preferences - Get user notification preferences
 */
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuth(request)
    if (!auth.success) {
      return NextResponse.json(
        { success: false, error: auth.error },
        { status: auth.status }
      )
    }

    await connectDB()

    // Get or create preferences for the user
    const preferences = await NotificationPreferences.getOrCreateForUser(auth.user.userId)

    const response = NextResponse.json({
      success: true,
      preferences: {
        userId: preferences.userId,
        channels: preferences.channels,
        categories: preferences.categories,
        frequency: preferences.frequency,
        settings: preferences.settings,
        updatedAt: preferences.updatedAt
      }
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Get notification preferences error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to get notification preferences' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/notifications/preferences - Update user notification preferences
 */
export async function PUT(request: NextRequest) {
  try {
    const auth = await verifyAuth(request)
    if (!auth.success) {
      return NextResponse.json(
        { success: false, error: auth.error },
        { status: auth.status }
      )
    }

    const body = await request.json()
    
    await connectDB()

    // Get or create preferences for the user
    let preferences = await NotificationPreferences.getOrCreateForUser(auth.user.userId)

    // Update preferences
    if (body.channels) {
      preferences.channels = { ...preferences.channels, ...body.channels }
    }

    if (body.categories) {
      Object.keys(body.categories).forEach(category => {
        if (preferences.categories[category]) {
          preferences.categories[category] = {
            ...preferences.categories[category],
            ...body.categories[category]
          }
        }
      })
    }

    if (body.frequency) {
      preferences.frequency = { ...preferences.frequency, ...body.frequency }
    }

    if (body.settings) {
      preferences.settings = { ...preferences.settings, ...body.settings }
    }

    // Save updated preferences
    await preferences.save()

    const response = NextResponse.json({
      success: true,
      preferences: {
        userId: preferences.userId,
        channels: preferences.channels,
        categories: preferences.categories,
        frequency: preferences.frequency,
        settings: preferences.settings,
        updatedAt: preferences.updatedAt
      }
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Update notification preferences error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update notification preferences' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/notifications/preferences/reset - Reset preferences to defaults
 */
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuth(request)
    if (!auth.success) {
      return NextResponse.json(
        { success: false, error: auth.error },
        { status: auth.status }
      )
    }

    await connectDB()

    // Delete existing preferences to trigger default creation
    await NotificationPreferences.deleteOne({ userId: auth.user.userId })

    // Create new default preferences
    const preferences = await NotificationPreferences.createDefault(auth.user.userId)

    const response = NextResponse.json({
      success: true,
      message: 'Notification preferences reset to defaults',
      preferences: {
        userId: preferences.userId,
        channels: preferences.channels,
        categories: preferences.categories,
        frequency: preferences.frequency,
        settings: preferences.settings,
        updatedAt: preferences.updatedAt
      }
    })

    return addCorsHeaders(response)
  } catch (error) {
    console.error('Reset notification preferences error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to reset notification preferences' },
      { status: 500 }
    )
  }
}
