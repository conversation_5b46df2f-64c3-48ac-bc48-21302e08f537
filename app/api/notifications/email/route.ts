// app/api/notifications/email/route.ts - Email notification API route

import { NextRequest, NextResponse } from 'next/server'
import { sendEmailNotification } from '@/services/backend/notificationsService'

/**
 * POST /api/notifications/email - Send email notification
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.to || !body.subject || !body.body) {
      return NextResponse.json(
        { success: false, error: 'To, subject, and body are required' },
        { status: 400 }
      )
    }

    // Send email notification
    const success = await sendEmailNotification(body)

    if (success) {
      return NextResponse.json({
        success: true,
        data: { message: 'Email notification sent successfully' }
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send email notification' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('POST /api/notifications/email error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to send email notification' },
      { status: 500 }
    )
  }
}
