// app/api/notifications/[id]/route.ts - Individual notification API routes

import { NextRequest, NextResponse } from 'next/server'
import type { Notification } from '@/types/frontend'

// Mock database - replace with actual database
const mockNotifications: Notification[] = []

/**
 * GET /api/notifications/[id] - Get notification by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const notificationId = params.id
    
    const notification = mockNotifications.find(n => n._id === notificationId)
    
    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: notification
    })
  } catch (error) {
    console.error('GET /api/notifications/[id] error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch notification' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/notifications/[id] - Update notification
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const notificationId = params.id
    const body = await request.json()
    
    const notificationIndex = mockNotifications.findIndex(n => n._id === notificationId)
    
    if (notificationIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      )
    }

    // Update notification
    const updatedNotification = {
      ...mockNotifications[notificationIndex],
      ...body,
      updatedAt: new Date().toISOString()
    }

    mockNotifications[notificationIndex] = updatedNotification

    return NextResponse.json({
      success: true,
      data: updatedNotification
    })
  } catch (error) {
    console.error('PUT /api/notifications/[id] error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update notification' },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/notifications/[id] - Delete notification
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const notificationId = params.id
    
    const notificationIndex = mockNotifications.findIndex(n => n._id === notificationId)
    
    if (notificationIndex === -1) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      )
    }

    // Remove notification
    mockNotifications.splice(notificationIndex, 1)

    return NextResponse.json({
      success: true,
      data: true
    })
  } catch (error) {
    console.error('DELETE /api/notifications/[id] error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete notification' },
      { status: 500 }
    )
  }
}
