// app/api/notifications/poll/route.ts - Polling endpoint for real-time notifications

import { NextRequest, NextResponse } from 'next/server'

// Mock notification storage - replace with actual database
let mockNotifications: any[] = []
let lastPollTimestamp: { [userId: string]: number } = {}

/**
 * GET /api/notifications/poll - Poll for new notifications
 * This provides a fallback for real-time notifications when Socket.IO is not available
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const since = searchParams.get('since') // timestamp
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      )
    }

    const sinceTimestamp = since ? parseInt(since) : (lastPollTimestamp[userId] || 0)
    const currentTimestamp = Date.now()

    // Filter notifications for this user since last poll
    const newNotifications = mockNotifications.filter(notification => {
      const notificationTime = new Date(notification.createdAt).getTime()
      return (
        (notification.recipientId === userId || notification.recipientId === 'all') &&
        notificationTime > sinceTimestamp
      )
    }).slice(0, limit)

    // Update last poll timestamp
    lastPollTimestamp[userId] = currentTimestamp

    return NextResponse.json({
      success: true,
      data: {
        notifications: newNotifications,
        hasMore: newNotifications.length === limit,
        timestamp: currentTimestamp,
        pollingInterval: 5000 // Suggest 5 second polling interval
      }
    })
  } catch (error) {
    console.error('Notification polling error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to poll notifications' },
      { status: 500 }
    )
  }
}

/**
 * POST /api/notifications/poll - Add notification to polling queue
 * This allows other parts of the system to add notifications for polling
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { notification } = body

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification data is required' },
        { status: 400 }
      )
    }

    // Add notification to mock storage
    const newNotification = {
      _id: `poll_${Date.now()}`,
      ...notification,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    mockNotifications.unshift(newNotification)

    // Keep only last 1000 notifications to prevent memory issues
    if (mockNotifications.length > 1000) {
      mockNotifications = mockNotifications.slice(0, 1000)
    }

    return NextResponse.json({
      success: true,
      data: newNotification
    })
  } catch (error) {
    console.error('Add notification to polling queue error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to add notification to polling queue' },
      { status: 500 }
    )
  }
}
