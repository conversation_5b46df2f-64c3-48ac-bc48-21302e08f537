// app/api/notifications/system-alert/route.ts - System alert API route

import { NextRequest, NextResponse } from 'next/server'
import { sendSystemAlert } from '@/services/backend/notificationsService'

/**
 * POST /api/notifications/system-alert - Send system alert
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.title || !body.message) {
      return NextResponse.json(
        { success: false, error: 'Title and message are required' },
        { status: 400 }
      )
    }

    // Send system alert
    const success = await sendSystemAlert(body)

    if (success) {
      return NextResponse.json({
        success: true,
        data: { message: 'System alert sent successfully' }
      })
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to send system alert' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('POST /api/notifications/system-alert error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to send system alert' },
      { status: 500 }
    )
  }
}
