import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { Order, connectDB, logActivity } from '@/lib/database'
import { z } from 'zod'

const updateOrderSchema = z.object({
  status: z.enum(['Pending', 'Confirmed', 'Processing', 'Shipped', 'Delivered', 'Cancelled', 'Refunded']).optional(),
  paymentStatus: z.enum(['Pending', 'Paid', 'Failed', 'Refunded']).optional(),
  trackingNumber: z.string().max(50).optional(),
  estimatedDelivery: z.string().datetime().optional(),
  notes: z.string().max(500).optional()
})

// GET /api/orders/[id] - Get order by ID
export const GET = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const orderId = params.id

    const order = await Order.findById(orderId).lean()

    if (!order) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      )
    }

    // Check branch access
    const branchCheck = checkBranchAccess(user, order.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      data: order
    })
  } catch (error) {
    console.error('Get order error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch order' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// PUT /api/orders/[id] - Update order
export const PUT = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const orderId = params.id

    const existingOrder = await Order.findById(orderId)
    if (!existingOrder) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      )
    }

    // Check branch access
    const branchCheck = checkBranchAccess(user, existingOrder.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validation = updateOrderSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const updates = validation.data

    // Convert estimatedDelivery string to Date if provided
    if (updates.estimatedDelivery) {
      updates.estimatedDelivery = new Date(updates.estimatedDelivery) as any
    }

    const order = await Order.findByIdAndUpdate(
      orderId,
      { ...updates, updatedAt: new Date() },
      { new: true, runValidators: true }
    )

    // Log activity based on what was updated
    let activityDescription = `Order ${order!.orderNumber} updated`
    if (updates.status) {
      activityDescription = `Order ${order!.orderNumber} status changed to ${updates.status}`
    } else if (updates.paymentStatus) {
      activityDescription = `Order ${order!.orderNumber} payment status changed to ${updates.paymentStatus}`
    } else if (updates.trackingNumber) {
      activityDescription = `Tracking number ${updates.trackingNumber} added to order ${order!.orderNumber}`
    }

    await logActivity({
      type: 'Order',
      description: activityDescription,
      userId: user.userId,
      userName: user.username,
      branchId: order!.branchId,
      metadata: { 
        orderId: order!._id.toString(),
        orderNumber: order!.orderNumber,
        updates 
      }
    })

    return NextResponse.json({
      success: true,
      data: order
    })
  } catch (error) {
    console.error('Update order error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update order' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// DELETE /api/orders/[id] - Cancel order
export const DELETE = withAuth(async (request: NextRequest, user, { params }) => {
  try {
    await connectDB()

    const orderId = params.id

    const order = await Order.findById(orderId)
    if (!order) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      )
    }

    // Check branch access
    const branchCheck = checkBranchAccess(user, order.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    // Check if order can be cancelled
    if (['Shipped', 'Delivered'].includes(order.status)) {
      return NextResponse.json(
        { success: false, error: 'Cannot cancel shipped or delivered orders' },
        { status: 400 }
      )
    }

    // Update order status to cancelled
    await Order.findByIdAndUpdate(orderId, { 
      status: 'Cancelled',
      updatedAt: new Date()
    })

    // TODO: Restore product stock if needed

    // Log activity
    await logActivity({
      type: 'Order',
      description: `Order ${order.orderNumber} cancelled`,
      userId: user.userId,
      userName: user.username,
      branchId: order.branchId,
      metadata: { 
        orderId: order._id.toString(),
        orderNumber: order.orderNumber
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Order cancelled successfully'
    })
  } catch (error) {
    console.error('Cancel order error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to cancel order' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})
