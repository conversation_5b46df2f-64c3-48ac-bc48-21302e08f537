import { NextRequest, NextResponse } from 'next/server'
import { withAuth, checkBranchAccess } from '@/lib/auth/middleware'
import { getOrders, createOrder, serviceUtils } from '@/services/backend'
import { z } from 'zod'

const orderItemSchema = z.object({
  productId: z.string().min(1),
  quantity: z.number().min(1)
})

const addressSchema = z.object({
  street: z.string().min(1).max(200),
  city: z.string().min(1).max(50),
  region: z.string().min(1).max(50),
  country: z.string().min(1).max(50),
  postalCode: z.string().min(1).max(20)
})

const createOrderSchema = z.object({
  customerId: z.string().min(1),
  items: z.array(orderItemSchema).min(1),
  branchId: z.string().min(1),
  paymentMethod: z.enum(['credit-card', 'debit-card', 'mobile-money', 'bank-transfer', 'cash']),
  shippingAddress: addressSchema,
  billingAddress: addressSchema,
  notes: z.string().max(500).optional(),
  discount: z.number().min(0).default(0)
})

// GET /api/orders - Get all orders
export const GET = withAuth(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const pagination = serviceUtils.validatePagination(
      parseInt(searchParams.get('page') || '1'),
      parseInt(searchParams.get('limit') || '10')
    )

    const filters = {
      status: searchParams.get('status') as any || undefined,
      paymentStatus: searchParams.get('paymentStatus') as any || undefined,
      branchId: searchParams.get('branchId') || undefined,
      customerId: searchParams.get('customerId') || undefined,
      startDate: searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined,
      endDate: searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined
    }

    // Check branch access if branchId is specified
    if (filters.branchId) {
      const branchCheck = checkBranchAccess(user, filters.branchId)
      if (!branchCheck.hasAccess) {
        return NextResponse.json(
          { success: false, error: branchCheck.error },
          { status: 403 }
        )
      }
    }

    const result = await getOrders(filters, pagination, user.role, user.branchId)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }

    return NextResponse.json(result)
  } catch (error) {
    console.error('Get orders API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch orders' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})

// POST /api/orders - Create new order
export const POST = withAuth(async (request: NextRequest, user) => {
  try {
    const body = await request.json()
    const validation = createOrderSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid input data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const data = validation.data

    // Check branch access
    const branchCheck = checkBranchAccess(user, data.branchId)
    if (!branchCheck.hasAccess) {
      return NextResponse.json(
        { success: false, error: branchCheck.error },
        { status: 403 }
      )
    }

    const result = await createOrder(data, user.userId, user.username)

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Create order API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create order' },
      { status: 500 }
    )
  }
}, {
  requiredRoles: ['overall_admin', 'branch_manager']
})


