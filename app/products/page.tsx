"use client"

import { Suspense, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import ProductGrid from "@/components/landing/product-grid"
import ProductFilters from "@/components/landing/product-filters"
import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"
import { usePublicProducts } from "@/hooks/use-public-products"

// Component that uses useSearchParams - must be wrapped in Suspense
function ProductsContent() {
  const searchParams = useSearchParams()

  // Get initial filters from URL parameters
  const initialFilters = {
    search: searchParams.get('search') || undefined,
    category: searchParams.get('category') || undefined,
    brand: searchParams.get('brand') || undefined,
    branchId: searchParams.get('branchId') || undefined,
  }

  // Fetch all products with pagination and URL filters
  const { products, isLoading, error, setFilters } = usePublicProducts({
    filters: initialFilters,
    limit: 20,
    autoFetch: true
  })

  // Update filters when URL parameters change
  useEffect(() => {
    const newFilters = {
      search: searchParams.get('search') || undefined,
      category: searchParams.get('category') || undefined,
      brand: searchParams.get('brand') || undefined,
      branchId: searchParams.get('branchId') || undefined,
    }
    setFilters(newFilters)
  }, [searchParams, setFilters])

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
          {initialFilters.search
            ? `Search Results for "${initialFilters.search}"`
            : initialFilters.category
            ? `${initialFilters.category} Products`
            : "Our Products"
          }
        </h1>
        <p className="text-lg text-muted-foreground">
          {initialFilters.search
            ? `Found products matching "${initialFilters.search}"`
            : initialFilters.category
            ? `Browse our ${initialFilters.category.toLowerCase()} collection`
            : "Discover our complete range of electronics and technology products available across all our branches."
          }
        </p>
      </div>

      <div className="grid gap-8 lg:grid-cols-[300px_1fr]">
        <aside className="space-y-6">
          <ProductFilters onFiltersChange={setFilters} />
        </aside>

        <div>
          <ProductGrid products={products} isLoading={isLoading} error={error} />
        </div>
      </div>
    </div>
  )
}

export default function CustomerProductsPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1">
        <Suspense fallback={
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                <p className="text-muted-foreground">Loading products...</p>
              </div>
            </div>
          </div>
        }>
          <ProductsContent />
        </Suspense>
      </main>
      <Footer />
    </div>
  )
}
