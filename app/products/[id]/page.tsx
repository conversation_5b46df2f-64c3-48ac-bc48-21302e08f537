"use client"

import { use } from "react"
import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"
import ProductImageGallery from "@/components/product-detail/product-image-gallery"
import ProductInfo from "@/components/product-detail/product-info"
import RelatedProducts from "@/components/product-detail/related-products"
import ProductReviews from "@/components/product-detail/product-reviews"
import { Separator } from "@/components/ui/separator"
import { usePublicProduct, usePublicProducts } from "@/hooks/use-public-products"
import { Skeleton } from "@/components/ui/skeleton"

// Mock data for a single product and related products
const mockProduct = {
  id: "prod-001",
  name: "Fathahitech ProBook X1 Ultra",
  price: 1899.99,
  availability: "In Stock" as "In Stock" | "Low Stock" | "Out of Stock",
  description:
    "The Fathahitech ProBook X1 Ultra is a powerhouse laptop designed for professionals and creatives. Featuring a stunning 16-inch Retina display, the latest Intel Core i9 processor, and 32GB of RAM, it handles demanding tasks with ease. Its sleek aluminum unibody design and all-day battery life make it the perfect companion for on-the-go productivity.",
  specifications: [
    "Processor: Intel Core i9-13900H",
    "RAM: 32GB DDR5",
    "Storage: 1TB NVMe SSD",
    "Display: 16-inch Liquid Retina XDR (3072x1920)",
    "Graphics: NVIDIA GeForce RTX 4070",
    "Operating System: Windows 11 Pro",
    "Battery Life: Up to 15 hours",
  ],
  images: [
    "/images/product-detail-1.png",
    "/images/product-detail-2.png",
    "/images/product-detail-3.png",
    "/images/product-laptop.png",
  ],
  rating: 4.5,
  reviewsCount: 78,
}

const mockRelatedProducts = [
  {
    id: "rel-001",
    name: "Fathahitech Ergonomic Wireless Mouse",
    image: "/images/related-product-1.png",
    price: 49.99,
  },
  {
    id: "rel-002",
    name: "Fathahitech 2TB Portable SSD",
    image: "/images/related-product-2.png",
    price: 129.0,
  },
  {
    id: "rel-003",
    name: "Fathahitech Noise-Cancelling Headphones",
    image: "/images/related-product-3.png",
    price: 199.5,
  },
]

const mockReviews = [
  {
    id: "rev-001",
    author: "Alice M.",
    rating: 5,
    date: "2024-07-28",
    comment:
      "Absolutely blown away by the performance of this laptop! It handles all my video editing tasks without breaking a sweat. The display is gorgeous, and the battery life is surprisingly good for such a powerful machine. Highly recommend!",
  },
  {
    id: "rev-002",
    author: "John D.",
    rating: 4,
    date: "2024-07-25",
    comment:
      "Great laptop for gaming and coding. The keyboard is comfortable for long sessions. My only minor gripe is that it can get a bit warm under heavy load, but nothing a cooling pad can't fix. Overall, very satisfied.",
  },
  {
    id: "rev-003",
    author: "Sarah K.",
    rating: 5,
    date: "2024-07-20",
    comment:
      "As a graphic designer, I need a reliable and fast machine, and the ProBook X1 Ultra delivers! The color accuracy on the screen is fantastic, and it renders complex designs in seconds. Worth every penny!",
  },
]

export default function ProductDetailPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params using React.use()
  const { id } = use(params)

  // Fetch product data based on id
  const { product, isLoading, error } = usePublicProduct(id)

  // Fetch related products (same category)
  const { products: relatedProducts } = usePublicProducts({
    filters: { category: product?.categoryName },
    limit: 4,
    autoFetch: !!product?.categoryName
  })

  // Mock reviews for now (can be replaced with dynamic data later)
  const reviews = mockReviews
  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length || 0
  const totalReviews = reviews.length

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 py-12 md:py-20">
          <div className="container px-4 md:px-6 grid lg:grid-cols-2 gap-12">
            <Skeleton className="h-96 w-full" />
            <div className="space-y-4">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-6 w-1/2" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  // Error state
  if (error || !product) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 py-12 md:py-20">
          <div className="container px-4 md:px-6 text-center">
            <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
            <p className="text-muted-foreground">{error || "The product you're looking for doesn't exist."}</p>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 py-12 md:py-20">
        <div className="container px-4 md:px-6 grid lg:grid-cols-2 gap-12">
          <ProductImageGallery
            images={product.images?.length > 0 ? product.images : [product.featuredImage]}
            alt={product.name}
          />
          <ProductInfo
            product={{
              id: product._id || product.id,
              name: product.name,
              price: product.price,
              originalPrice: product.originalPrice,
              salePrice: product.salePrice,
              currency: product.currency,
              availability: product.status as "In Stock" | "Low Stock" | "Out of Stock",
              description: product.description,
              specifications: product.specifications || [],
              rating: 4.2, // Mock rating for now
              reviewsCount: totalReviews,
              category: product.categoryName,
              branchId: product.branchId,
              branchName: undefined, // Will be resolved from branch data
              sku: product.sku,
              stock: product.stock,
              isOnSale: product.isOnSale,
              isPromoted: product.isPromoted
            }}
          />
        </div>
        <Separator className="my-16" />
        <RelatedProducts products={relatedProducts.filter(p => p._id !== product._id)} />
        <ProductReviews reviews={reviews} averageRating={averageRating} totalReviews={totalReviews} />
      </main>
      <Footer />
    </div>
  )
}
