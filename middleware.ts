import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Define protected routes that require authentication (dashboard routes)
const protectedRoutes = [
  '/dashboard',
  '/inventory',
  '/admin-products',
  '/admin-categories',
  '/shops',
  '/sales',
  '/delivery',
  '/customers',
  '/analytics',
  '/sessions'
]

// Define public routes that don't require authentication
const publicRoutes = [
  '/',
  '/login',
  '/register',
  '/products',
  '/checkout'
]

// Define API routes that should be excluded from middleware
const apiRoutes = [
  '/api'
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Skip middleware for API routes, static files, and Next.js internals
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.includes('.')
  ) {
    return NextResponse.next()
  }

  // Check if the current path is explicitly public
  const isPublicRoute = publicRoutes.some(route =>
    pathname === route || pathname.startsWith(route + '/')
  )

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname === route || pathname.startsWith(route + '/')
  )

  // Get authentication token from cookies
  const authToken = request.cookies.get('auth-token')?.value
  let isAuthenticated = false
  let userRole: string | null = null

  // Debug: Log token presence
  console.log('Middleware - Auth token present:', !!authToken)
  if (authToken) {
    console.log('Middleware - Token length:', authToken.length)
    console.log('Middleware - Token starts with:', authToken.substring(0, 20) + '...')
  }

  // Validate token if present
  if (authToken) {
    try {
      // SECURE: Use Web Crypto API for Edge Runtime compatibility
      const parts = authToken.split('.')
      if (parts.length !== 3) {
        throw new Error('Invalid token format')
      }

      // Decode header and payload
      const header = JSON.parse(atob(parts[0]))
      const payload = JSON.parse(atob(parts[1]))

      // Basic validation checks
      if (!payload.exp || !payload.iat || !payload.userId || !payload.role) {
        throw new Error('Invalid token payload')
      }

      // Check expiration
      const now = Math.floor(Date.now() / 1000)
      if (payload.exp <= now) {
        throw new Error('Token expired')
      }

      // Check issuer and audience
      if (payload.iss !== 'fathahitech-api' || payload.aud !== 'fathahitech-app') {
        throw new Error('Invalid token issuer or audience')
      }

      // SECURITY NOTE: Edge Runtime doesn't support crypto modules for signature verification
      // Full cryptographic verification is performed in API routes via withAuth middleware
      // This middleware provides basic token structure and expiration validation for routing
      isAuthenticated = true
      userRole = payload.role
      console.log('Middleware - Token validated successfully, role:', userRole)
    } catch (error) {
      console.log('Middleware - Token validation failed:', error)

      // Clear invalid token
      const response = NextResponse.next()
      response.cookies.set('auth-token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 0,
        path: '/'
      })

      // If trying to access protected route with invalid token, redirect to login
      if (isProtectedRoute) {
        const loginUrl = new URL('/login', request.url)
        loginUrl.searchParams.set('redirect', pathname)
        return NextResponse.redirect(loginUrl)
      }

      return response
    }
  }

  // Redirect authenticated users away from auth pages with role-based routing
  if (isAuthenticated && (pathname === '/login' || pathname === '/register')) {
    let redirectUrl = '/dashboard' // default

    switch (userRole) {
      case 'overall_admin':
        redirectUrl = '/dashboard' // Unified dashboard for admins
        break
      case 'branch_manager':
        redirectUrl = '/dashboard' // Unified dashboard for branch managers
        break
      case 'sales_person':
        redirectUrl = '/dashboard' // Unified dashboard for sales person
        break
      case 'customer':
        redirectUrl = '/dashboard' // Unified dashboard for customers
        break
    }

    return NextResponse.redirect(new URL(redirectUrl, request.url))
  }

  // Allow public routes to pass through without any authentication checks
  if (isPublicRoute && !isProtectedRoute) {
    return NextResponse.next()
  }

  // Only check authentication for protected routes
  if (isProtectedRoute) {
    // If accessing a protected route without authentication, redirect to login
    if (!isAuthenticated) {
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }
  }

  return NextResponse.next()
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\..*|sw.js).*)',
  ],
}
