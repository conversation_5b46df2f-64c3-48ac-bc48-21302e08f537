// types/frontend.ts - Comprehensive frontend types matching backend interfaces

// ============================================================================
// API Response Types
// ============================================================================

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  pagination?: PaginationInfo
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
}

export interface PaginationParams {
  page: number
  limit: number
}

// ============================================================================
// User Types (matching IUser from backend)
// ============================================================================

export interface User {
  _id: string
  username: string
  email: string
  role: UserRole
  branchId?: string
  branchName?: string
  firstName: string
  lastName: string
  phone?: string
  isActive: boolean
  lastLogin?: string
  createdAt: string
  updatedAt: string
}

export interface CreateUserData {
  username: string
  email: string
  password: string
  role: UserRole
  branchId?: string
  firstName: string
  lastName: string
  phone?: string
}

export interface UpdateUserData {
  username?: string
  email?: string
  role?: UserRole
  branchId?: string
  firstName?: string
  lastName?: string
  phone?: string
  isActive?: boolean
}

export interface UserFilters {
  search?: string
  role?: string
  branchId?: string
  isActive?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// ============================================================================
// Shop/Branch Types (matching IShop from backend)
// ============================================================================

export interface Shop {
  _id: string
  name: string
  location: string
  country: string
  region: string
  manager: string
  managerId: string
  description: string
  address: string
  phone: string
  email: string
  coordinates?: {
    lat: number
    lng: number
  }
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  image: string
  totalProducts: number
  totalSales: number
  status: ShopStatus
  createdAt: string
  updatedAt: string
}

export type ShopStatus = 'Active' | 'Inactive' | 'Opening Soon' | 'Maintenance'

export interface CreateShopData {
  name: string
  location: string
  country: string
  region: string
  managerId: string
  description: string
  address: string
  phone: string
  email: string
  coordinates?: {
    lat: number
    lng: number
  }
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  image: string
}

export interface UpdateShopData {
  name?: string
  location?: string
  country?: string
  region?: string
  managerId?: string
  description?: string
  address?: string
  phone?: string
  email?: string
  coordinates?: {
    lat: number
    lng: number
  }
  operatingHours?: {
    open: string
    close: string
    timezone: string
  }
  image?: string
  status?: ShopStatus
}

export interface ShopFilters {
  search?: string
  country?: string
  region?: string
  status?: ShopStatus
  managerId?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// ============================================================================
// Product Types (matching IProduct from backend)
// ============================================================================

export interface ProductVariant {
  id: string
  name: string
  sku: string
  price: number
  originalPrice?: number
  stock: number
  attributes: Record<string, string>
  images: string[]
  isActive: boolean
}

export interface Product {
  _id: string
  id: string
  name: string
  sku: string
  categoryId: string
  categoryName: string
  price: number
  originalPrice?: number
  currency: string
  stock: number
  minStockLevel: number
  images: string[]
  featuredImage: string
  description: string
  specifications: string[]
  branchId: string
  brand: string
  model: string
  warranty: string
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  tags: string[]
  variants: ProductVariant[]
  hasVariants: boolean
  isFeatured: boolean
  isActive: boolean
  isPromoted: boolean
  isOnSale: boolean
  salePrice?: number
  saleStartDate?: string
  saleEndDate?: string
  promotionDescription?: string
  status: ProductStatus
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

export interface ProductCategory {
  id: string
  name: string
  description: string
  slug: string
  isActive: boolean
  productCount: number
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

export type ProductStatus = 'Available' | 'Out of Stock' | 'Discontinued' | 'Coming Soon'

export interface CreateProductData {
  name: string
  sku: string
  categoryId: string
  categoryName: string
  price: number
  originalPrice?: number
  stock: number
  minStockLevel: number
  featuredImage: string
  images: string[]
  description: string
  specifications: string[]
  branchId: string
  brand: string
  model: string
  warranty: string
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  tags: string[]
  variants: ProductVariant[]
  hasVariants: boolean
  isFeatured?: boolean
  isActive?: boolean
}

export interface UpdateProductData {
  id: string
  name?: string
  sku?: string
  categoryId?: string
  categoryName?: string
  price?: number
  originalPrice?: number
  stock?: number
  minStockLevel?: number
  featuredImage?: string
  images?: string[]
  description?: string
  specifications?: string[]
  brand?: string
  model?: string
  warranty?: string
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  tags?: string[]
  variants?: ProductVariant[]
  hasVariants?: boolean
  isFeatured?: boolean
  isActive?: boolean
  status?: ProductStatus
}

export interface ProductFilters {
  search?: string
  categoryId?: string
  categoryName?: string
  brand?: string
  status?: ProductStatus
  branchId?: string
  hasVariants?: boolean
  isFeatured?: boolean
  minPrice?: number
  maxPrice?: number
  featured?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// ============================================================================
// Customer Types (matching ICustomer from backend)
// ============================================================================

export interface Customer {
  _id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  preferredBranch?: string
  totalOrders: number
  totalSpent: number
  loyaltyPoints: number
  isActive: boolean
  lastOrderDate?: string
  createdAt: string
  updatedAt: string
}

export interface CustomerWithOrders extends Customer {
  orders?: Order[]
}

export interface CreateCustomerData {
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  preferredBranch?: string
}

export interface UpdateCustomerData {
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  address?: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  preferredBranch?: string
  isActive?: boolean
}

export interface CustomerFilters {
  search?: string
  preferredBranch?: string
  isActive?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// ============================================================================
// Order Types (matching IOrder from backend)
// ============================================================================

export interface Order {
  _id: string
  orderNumber: string
  customerId: string
  customerName: string
  customerEmail: string
  customerPhone: string
  items: OrderItem[]
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  status: OrderStatus
  paymentStatus: PaymentStatus
  paymentMethod: PaymentMethod
  branchId: string
  branchName: string
  shippingAddress: Address
  billingAddress: Address
  notes?: string
  trackingNumber?: string
  estimatedDelivery?: string
  createdAt: string
  updatedAt: string
}

export interface OrderItem {
  id: string
  productId: string
  productName: string
  sku: string
  price: number
  quantity: number
  subtotal: number
  image?: string
}

export interface Address {
  street: string
  city: string
  region: string
  country: string
  postalCode: string
}

export type OrderStatus = 'Pending' | 'Processing' | 'Shipped' | 'Delivered' | 'Cancelled' | 'Returned'
export type PaymentStatus = 'Pending' | 'Paid' | 'Failed' | 'Refunded' | 'Partially Refunded'
export type PaymentMethod = 'Cash' | 'Card' | 'Mobile Money' | 'Bank Transfer' | 'Credit'

export interface CreateOrderData {
  customerId: string
  items: {
    productId: string
    quantity: number
  }[]
  branchId: string
  paymentMethod: PaymentMethod
  shippingAddress: Address
  billingAddress: Address
  notes?: string
  discount?: number
}

export interface UpdateOrderData {
  status?: OrderStatus
  paymentStatus?: PaymentStatus
  trackingNumber?: string
  estimatedDelivery?: string
  notes?: string
}

export interface OrderFilters {
  status?: OrderStatus
  paymentStatus?: PaymentStatus
  branchId?: string
  customerId?: string
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// ============================================================================
// Employee Types (matching IEmployee from backend)
// ============================================================================

export interface Employee {
  _id: string
  employeeId: string
  firstName: string
  lastName: string
  email: string
  phone: string
  position: string
  department: string
  branchId: string
  branchName: string
  salary: number
  hireDate: string
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  emergencyContact: {
    name: string
    relationship: string
    phone: string
  }
  userId?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateEmployeeData {
  firstName: string
  lastName: string
  email: string
  phone: string
  position: string
  department: string
  branchId: string
  salary: number
  hireDate: string
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  emergencyContact: {
    name: string
    relationship: string
    phone: string
  }
  userId?: string
}

export interface UpdateEmployeeData {
  firstName?: string
  lastName?: string
  email?: string
  phone?: string
  position?: string
  department?: string
  branchId?: string
  salary?: number
  address?: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  emergencyContact?: {
    name: string
    relationship: string
    phone: string
  }
  isActive?: boolean
}

export interface EmployeeFilters {
  search?: string
  branchId?: string
  department?: string
  position?: string
  isActive?: boolean
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// ============================================================================
// Inventory Types (matching IInventory from backend)
// ============================================================================

export interface Inventory {
  _id: string
  productId: string
  productName: string
  sku: string
  branchId: string
  branchName: string
  stock: number
  minStockLevel: number
  maxStockLevel: number
  cost: number
  location: string
  // Enhanced location fields
  warehouseId?: string
  warehouseName?: string
  warehouseCode?: string
  shelfId?: string
  shelfCode?: string
  shelfName?: string
  supplier?: string
  batchNumber?: string
  expiryDate?: string
  lastRestocked: string
  createdAt: string
  updatedAt: string
}

export interface CreateInventoryData {
  productId: string
  productName: string
  sku: string
  branchId: string
  stock: number
  minStockLevel: number
  maxStockLevel: number
  cost: number
  location: string
  // Enhanced location fields
  warehouseId?: string
  warehouseName?: string
  warehouseCode?: string
  shelfId?: string
  shelfCode?: string
  shelfName?: string
  supplier?: string
  batchNumber?: string
  expiryDate?: string
  lastRestocked: string
}

export interface UpdateInventoryData {
  stock?: number
  minStockLevel?: number
  maxStockLevel?: number
  cost?: number
  location?: string
  supplier?: string
  batchNumber?: string
  expiryDate?: string
  lastRestocked?: string
}

export interface StockMovement {
  type: 'in' | 'out' | 'adjustment'
  quantity: number
  reason: string
  reference?: string
  cost?: number
}

// ============================================================================
// Warehouse Types
// ============================================================================

export interface Warehouse {
  _id: string
  name: string
  code: string
  description?: string
  type: 'main' | 'branch' | 'storage' | 'distribution'
  branchId?: string
  branchName?: string
  address: string
  capacity: number
  currentUtilization: number
  isActive: boolean
  manager?: string
  managerId?: string
  contactInfo: {
    phone?: string
    email?: string
  }
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  coordinates?: {
    lat: number
    lng: number
  }
  features: string[]
  shelfCount?: number
  createdAt: string
  updatedAt: string
}

export interface CreateWarehouseData {
  name: string
  code: string
  description?: string
  type: 'main' | 'branch' | 'storage' | 'distribution'
  branchId?: string
  address: string
  capacity: number
  manager?: string
  managerId?: string
  contactInfo?: {
    phone?: string
    email?: string
  }
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  coordinates?: {
    lat: number
    lng: number
  }
  features?: string[]
}

export interface UpdateWarehouseData {
  name?: string
  description?: string
  address?: string
  capacity?: number
  currentUtilization?: number
  isActive?: boolean
  manager?: string
  managerId?: string
  contactInfo?: {
    phone?: string
    email?: string
  }
  operatingHours?: {
    open: string
    close: string
    timezone: string
  }
  coordinates?: {
    lat: number
    lng: number
  }
  features?: string[]
}

export interface WarehouseFilters {
  search?: string
  type?: 'main' | 'branch' | 'storage' | 'distribution'
  branchId?: string
  isActive?: boolean
  managerId?: string
}

// ============================================================================
// Shelf Types
// ============================================================================

export interface Shelf {
  _id: string
  code: string
  name: string
  warehouseId: string
  warehouseName: string
  warehouseCode: string
  section: string
  row: string
  position: string
  level: number
  description?: string
  capacity: number
  currentOccupancy: number
  dimensions: {
    width: number
    height: number
    depth: number
  }
  weightLimit: number
  currentWeight: number
  shelfType: 'standard' | 'refrigerated' | 'hazmat' | 'fragile' | 'bulk'
  accessLevel: 'ground' | 'ladder' | 'forklift' | 'crane'
  isActive: boolean
  isReserved: boolean
  reservedFor?: string
  lastInventoryCheck?: string
  notes?: string
  barcode?: string
  qrCode?: string
  occupancyPercentage?: number
  weightPercentage?: number
  availabilityStatus?: 'available' | 'nearly_full' | 'full' | 'reserved' | 'inactive'
  warehouse?: {
    id: string
    name: string
    code: string
    type: string
  }
  createdAt: string
  updatedAt: string
}

export interface CreateShelfData {
  name: string
  warehouseId: string
  section: string
  row: string
  position: string
  level: number
  description?: string
  capacity: number
  dimensions: {
    width: number
    height: number
    depth: number
  }
  weightLimit: number
  shelfType: 'standard' | 'refrigerated' | 'hazmat' | 'fragile' | 'bulk'
  accessLevel: 'ground' | 'ladder' | 'forklift' | 'crane'
  notes?: string
  barcode?: string
  qrCode?: string
}

export interface UpdateShelfData {
  name?: string
  description?: string
  capacity?: number
  currentOccupancy?: number
  dimensions?: {
    width: number
    height: number
    depth: number
  }
  weightLimit?: number
  currentWeight?: number
  shelfType?: 'standard' | 'refrigerated' | 'hazmat' | 'fragile' | 'bulk'
  accessLevel?: 'ground' | 'ladder' | 'forklift' | 'crane'
  isActive?: boolean
  isReserved?: boolean
  reservedFor?: string
  lastInventoryCheck?: string
  notes?: string
  barcode?: string
  qrCode?: string
}

export interface ShelfFilters {
  search?: string
  warehouseId?: string
  section?: string
  shelfType?: 'standard' | 'refrigerated' | 'hazmat' | 'fragile' | 'bulk'
  accessLevel?: 'ground' | 'ladder' | 'forklift' | 'crane'
  isActive?: boolean
  isReserved?: boolean
  availabilityStatus?: 'available' | 'nearly_full' | 'full' | 'reserved' | 'inactive'
}

export interface InventoryFilters {
  search?: string
  branchId?: string
  productId?: string
  lowStock?: boolean
  outOfStock?: boolean
  location?: string
  supplier?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// ============================================================================
// Activity Log Types (matching IActivityLog from backend)
// ============================================================================

export interface ActivityLog {
  _id: string
  type: ActivityLogType
  description: string
  userId: string
  userName: string
  branchId?: string
  branchName?: string
  metadata?: Record<string, unknown>
  timestamp: string
  createdAt: string
  updatedAt: string
}

export type ActivityLogType = 'Order' | 'Inventory' | 'Sale' | 'Delivery' | 'Product' | 'User'

export interface CreateActivityLogData {
  type: ActivityLogType
  description: string
  metadata?: Record<string, unknown>
  branchId?: string
}

export interface ActivityLogFilters {
  type?: ActivityLogType
  userId?: string
  branchId?: string
  search?: string
  startDate?: string
  endDate?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface ActivityLogStats {
  summary: {
    totalActivities: number
    period: number
    startDate: string
    endDate: string
  }
  activitiesByType: Array<{
    type: string
    count: number
    percentage: number
  }>
  activitiesByUser: Array<{
    userId: string
    userName: string
    count: number
    percentage: number
  }>
  activityTrend: Array<{
    date: string
    count: number
  }>
  recentActivities: ActivityLog[]
  filters: {
    branchId?: string
    period: number
    startDate?: string
    endDate?: string
  }
}

// ============================================================================
// Analytics Types
// ============================================================================

export interface DashboardMetrics {
  overview: {
    totalSales: number
    totalOrders: number
    totalCustomers: number
    totalProducts: number
    salesGrowth: number
    ordersGrowth: number
  }
  recentOrders: Array<{
    _id: string
    orderNumber: string
    customerName: string
    total: number
    status: OrderStatus
    createdAt: string
  }>
  topProducts: Array<{
    _id: string
    productName: string
    totalQuantity: number
    totalRevenue: number
  }>
  salesByStatus: Array<{
    _id: OrderStatus
    count: number
    total: number
  }>
  salesTrend: Array<{
    date: string
    sales: number
    orders: number
  }>
  lowStockProducts: Product[]
  alerts: {
    lowStockCount: number
    pendingOrders: number
  }
}

export interface SalesAnalytics {
  summary: {
    totalRevenue: number
    totalOrders: number
    averageOrderValue: number
    totalTax: number
    totalShipping: number
    totalDiscount: number
    conversionRate: number
  }
  salesTrend: Array<{
    period: string
    totalSales: number
    totalOrders: number
    averageOrderValue: number
    totalTax: number
    totalShipping: number
    totalDiscount: number
  }>
  salesByPaymentMethod: Array<{
    _id: PaymentMethod
    totalSales: number
    orderCount: number
  }>
  salesByCategory: Array<{
    _id: ProductCategory
    totalSales: number
    totalQuantity: number
    orderCount: number
  }>
  topCustomers: Array<{
    _id: string
    customerName: string
    customerEmail: string
    totalSales: number
    orderCount: number
    averageOrderValue: number
  }>
  filters: {
    branchId?: string
    startDate?: string
    endDate?: string
    groupBy: string
    category?: string
  }
}

// ============================================================================
// Common Types
// ============================================================================

export type UserRole = 'overall_admin' | 'branch_manager'

// All types are already exported above
