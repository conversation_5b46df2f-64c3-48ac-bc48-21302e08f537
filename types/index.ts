// User and Authentication Types
export type UserRole = "overall_admin" | "branch_manager"

export interface User {
  id: string
  username: string
  role: UserRole
  branchId?: string
  name: string
  email?: string
  avatar?: string
}

// Shop and Branch Types
export interface Shop {
  id: string
  name: string
  location: string
  country: string
  region: string
  manager: string
  managerId: string
  totalProducts: number
  totalSales: number
  status: "Active" | "Inactive" | "Opening Soon" | "Maintenance"
  image: string
  description: string
  address: string
  phone: string
  email: string
  coordinates?: {
    lat: number
    lng: number
  }
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  createdAt: string
  updatedAt: string
}

// Product Types
export type ProductStatus = "In Stock" | "Low Stock" | "Out of Stock"

// Product Category (now dynamic from database)
export interface ProductCategory {
  id: string
  name: string
  description: string
  slug: string
  isActive: boolean
  productCount: number
  // Image fields
  featuredImage?: string | null
  icon?: string | null
  iconType?: 'image' | 'lucide'
  iconName?: string | null
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// Product Variant
export interface ProductVariant {
  id: string
  name: string
  sku: string
  price: number
  originalPrice?: number
  currency: string
  stock: number
  attributes: Record<string, string> // e.g., { color: 'red', size: 'large' }
  images: string[]
  isActive: boolean
}

// Image Upload
export interface ImageUpload {
  fileId: string
  filename: string
  url: string
  originalName?: string
  mimeType?: string
  size?: number
  uploadedAt?: string
}

export interface Product {
  id: string
  name: string
  sku: string
  categoryId: string // Reference to ProductCategory
  categoryName: string // Denormalized for performance
  price: number
  originalPrice?: number
  currency: string
  stock: number
  minStockLevel: number
  status: ProductStatus
  images: string[]
  featuredImage: string // Main product image
  description: string
  specifications: string[]
  branchId: string
  brand: string
  model: string
  warranty: string
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  tags: string[]
  variants: ProductVariant[]
  hasVariants: boolean
  isActive: boolean
  isFeatured: boolean
  isPromoted: boolean
  isOnSale: boolean
  salePrice?: number
  saleStartDate?: string
  saleEndDate?: string
  promotionDescription?: string
  createdAt: string
  updatedAt: string
  createdBy: string
  updatedBy: string
}

// Inventory Types
export interface InventoryItem {
  id: string
  productId: string
  productName: string
  sku: string
  branchId: string
  branchName: string
  stock: number
  minStockLevel: number
  maxStockLevel: number
  status: ProductStatus
  lastRestocked: string
  supplier?: string
  cost: number
  location: string // Warehouse location within branch
}

// Order Types
export type OrderStatus = 
  | "Pending" 
  | "Confirmed" 
  | "Processing" 
  | "Shipped" 
  | "Delivered" 
  | "Cancelled" 
  | "Refunded"

export type PaymentStatus = "Pending" | "Paid" | "Failed" | "Refunded"
export type PaymentMethod = "credit-card" | "debit-card" | "mobile-money" | "bank-transfer" | "cash"

export interface OrderItem {
  id: string
  productId: string
  productName: string
  sku: string
  price: number
  quantity: number
  subtotal: number
  image: string
}

export interface Order {
  id: string
  orderNumber: string
  customerId: string
  customerName: string
  customerEmail: string
  customerPhone: string
  items: OrderItem[]
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  status: OrderStatus
  paymentStatus: PaymentStatus
  paymentMethod: PaymentMethod
  branchId: string
  branchName: string
  shippingAddress: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  billingAddress: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  notes?: string
  createdAt: string
  updatedAt: string
  estimatedDelivery?: string
  trackingNumber?: string
}

// Customer Types
export interface Customer {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  preferredBranch?: string
  totalOrders: number
  totalSpent: number
  loyaltyPoints: number
  isActive: boolean
  createdAt: string
  lastOrderDate?: string
}

// Analytics Types
export interface SalesMetrics {
  totalSales: number
  totalOrders: number
  averageOrderValue: number
  topSellingProducts: Array<{
    productId: string
    productName: string
    quantitySold: number
    revenue: number
  }>
  salesByBranch: Array<{
    branchId: string
    branchName: string
    sales: number
    orders: number
  }>
  salesTrend: Array<{
    date: string
    sales: number
    orders: number
  }>
}

// Theme Types
export type ThemeMode = "light" | "dark"
export type ThemeColor = "sky" | "green" | "forest" | "red"

export interface ThemeConfig {
  mode: ThemeMode
  color: ThemeColor
}

// Filter and Search Types
export interface ProductFilters {
  categoryId?: string
  categoryName?: string
  branchId?: string
  country?: string
  region?: string
  priceRange?: {
    min: number
    max: number
  }
  availability?: ProductStatus
  brand?: string
  hasVariants?: boolean
  isFeatured?: boolean
  search?: string
}

export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: "asc" | "desc"
}

// API Response Types
export interface ApiResponse<T> {
  data: T
  message: string
  success: boolean
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Activity Log Types
export interface ActivityLog {
  id: string
  type: "Order" | "Inventory" | "Sale" | "Delivery" | "Product" | "User"
  description: string
  userId: string
  userName: string
  branchId?: string
  branchName?: string
  metadata?: Record<string, any>
  timestamp: string
}

// Form Data Types
export interface CreateProductCategoryData {
  name: string
  description: string
  isActive: boolean
  featuredImage?: string | null
  icon?: string | null
  iconType?: 'image' | 'lucide'
  iconName?: string | null
}

export interface CreateProductData {
  name: string
  sku: string
  categoryId: string
  categoryName: string
  price: number
  originalPrice?: number
  currency: string
  stock: number
  minStockLevel: number
  description: string
  specifications: string[]
  branchId: string
  brand: string
  model: string
  warranty: string
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  tags: string[]
  variants: ProductVariant[]
  hasVariants: boolean
  featuredImage: string
  images: string[]
  isActive: boolean
  isFeatured: boolean
  isPromoted: boolean
  isOnSale: boolean
  salePrice?: number
  saleStartDate?: string
  saleEndDate?: string
  promotionDescription?: string
}

export interface UpdateProductData extends Partial<CreateProductData> {
  id: string
}

// Image Upload Types
export interface ImageUploadResponse {
  uploadedFiles: ImageUpload[]
  totalUploaded: number
  totalRequested: number
  errors?: string[]
}

export interface ProductImageUploadResponse {
  uploadedImages: ImageUpload[]
  totalUploaded: number
  totalRequested: number
  featuredImage: string
  errors?: string[]
}

export interface CreateShopData {
  name: string
  location: string
  country: string
  region: string
  managerId: string
  description: string
  address: string
  phone: string
  email: string
  coordinates?: {
    lat: number
    lng: number
  }
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
}

export interface CreateBranchData {
  name: string
  location: string
  country: string
  region: string
  managerId: string
  description: string
  address: string
  phone: string
  email: string
  coordinates?: {
    lat: number
    lng: number
  }
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
}

// Employee Management Types
export interface Employee {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  role: "branch_manager" | "employee" | "cashier" | "sales_rep" | "inventory_manager"
  department: string
  branchId: string
  branchName: string
  managerId?: string
  managerName?: string
  hireDate: string
  salary: number
  status: "Active" | "Inactive" | "On Leave" | "Terminated"
  address: string
  emergencyContact: {
    name: string
    phone: string
    relationship: string
  }
  permissions: string[]
  avatar?: string
}

export interface CreateEmployeeData {
  firstName: string
  lastName: string
  email: string
  phone: string
  position: string
  department: string
  branchId: string
  hireDate: string
  salary: number
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  emergencyContact: {
    name: string
    phone: string
    relationship: string
  }
}

export interface CreateBranchManagerData {
  firstName: string
  lastName: string
  email: string
  phone: string
  position: string // Required by API
  department: string // Required by API
  role: string // Required by API
  branchId: string
  hireDate: string
  salary: number
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  emergencyContact: {
    name: string
    phone: string
    relationship: string
  }
  permissions: string[]
  managerialExperience: number
  qualifications: string[]
}

// Modal Props Types
export interface ModalProps {
  isOpen: boolean
  onClose: () => void
}

export interface FormModalProps<T> extends ModalProps {
  onSubmit: (data: T) => Promise<void>
  isLoading?: boolean
  initialData?: Partial<T>
  mode?: "create" | "edit"
}
