// Comprehensive notification system types and schemas

export type NotificationCategory = 
  | 'system'           // System-wide notifications
  | 'security'         // Security alerts and warnings
  | 'inventory'        // Stock, products, inventory management
  | 'orders'           // Order lifecycle notifications
  | 'delivery'         // Delivery status updates
  | 'campaigns'        // Sales, discounts, offers, competitions
  | 'users'            // User management notifications
  | 'financial'        // Payment, revenue, financial alerts
  | 'reports'          // Report generation and analytics
  | 'maintenance'      // System maintenance and updates

export type NotificationPriority = 'low' | 'medium' | 'high' | 'critical' | 'urgent'

export type NotificationStatus = 'unread' | 'read' | 'archived' | 'dismissed'

export type UserRole = 'overall_admin' | 'branch_manager' | 'employee' | 'customer'

// Notification targeting and routing
export interface NotificationTarget {
  // Role-based targeting
  roles?: UserRole[]
  
  // Specific user targeting
  userIds?: string[]
  
  // Branch-based targeting
  branchIds?: string[]
  
  // Geographic targeting
  locations?: string[]
  
  // Custom filters
  customFilters?: Record<string, any>
}

// Base notification interface
export interface BaseNotification {
  id: string
  category: NotificationCategory
  type: string
  priority: NotificationPriority
  status: NotificationStatus
  
  // Content
  title: string
  message: string
  description?: string
  
  // Rich content
  icon?: string
  image?: string
  color?: string
  badge?: string
  
  // Targeting
  target: NotificationTarget
  
  // Metadata
  data?: Record<string, any>
  actions?: NotificationAction[]
  
  // Timestamps
  createdAt: Date
  scheduledFor?: Date
  expiresAt?: Date
  readAt?: Date
  
  // Tracking
  createdBy: string
  source: string
  
  // Delivery
  channels: NotificationChannel[]
  deliveryStatus: DeliveryStatus
}

// Notification actions (buttons, links, etc.)
export interface NotificationAction {
  id: string
  label: string
  type: 'button' | 'link' | 'dismiss' | 'archive'
  variant?: 'primary' | 'secondary' | 'destructive' | 'outline'
  url?: string
  action?: string
  data?: Record<string, any>
}

// Delivery channels
export type NotificationChannel = 'in_app' | 'email' | 'sms' | 'push' | 'socket'

export interface DeliveryStatus {
  in_app?: 'pending' | 'delivered' | 'failed'
  email?: 'pending' | 'sent' | 'delivered' | 'failed'
  sms?: 'pending' | 'sent' | 'delivered' | 'failed'
  push?: 'pending' | 'sent' | 'delivered' | 'failed'
  socket?: 'pending' | 'sent' | 'delivered' | 'failed'
}

// Specific notification types

// INVENTORY NOTIFICATIONS
export interface InventoryNotification extends BaseNotification {
  category: 'inventory'
  type: 'stock_low' | 'stock_out' | 'product_added' | 'product_updated' | 'product_deleted' | 'restock_needed' | 'expiry_warning'
  data: {
    productId: string
    productName: string
    sku: string
    currentStock?: number
    minimumStock?: number
    branchId?: string
    branchName?: string
    expiryDate?: Date
    addedBy?: string
    updatedBy?: string
  }
}

// ORDER NOTIFICATIONS
export interface OrderNotification extends BaseNotification {
  category: 'orders'
  type: 'order_placed' | 'order_confirmed' | 'order_processing' | 'order_shipped' | 'order_delivered' | 'order_cancelled' | 'payment_received' | 'payment_failed'
  data: {
    orderId: string
    orderNumber: string
    customerId: string
    customerName: string
    customerEmail: string
    totalAmount: number
    currency: string
    items: Array<{
      productId: string
      productName: string
      quantity: number
      price: number
    }>
    branchId?: string
    branchName?: string
    deliveryAddress?: string
    estimatedDelivery?: Date
  }
}

// DELIVERY NOTIFICATIONS
export interface DeliveryNotification extends BaseNotification {
  category: 'delivery'
  type: 'delivery_assigned' | 'delivery_picked_up' | 'delivery_in_transit' | 'delivery_out_for_delivery' | 'delivery_delivered' | 'delivery_failed' | 'delivery_returned'
  data: {
    deliveryId: string
    orderId: string
    orderNumber: string
    customerId: string
    customerName: string
    driverId?: string
    driverName?: string
    driverPhone?: string
    trackingNumber: string
    currentLocation?: {
      lat: number
      lng: number
      address: string
    }
    estimatedDelivery?: Date
    deliveryAddress: string
    deliveryInstructions?: string
  }
}

// CAMPAIGN NOTIFICATIONS
export interface CampaignNotification extends BaseNotification {
  category: 'campaigns'
  type: 'sale_started' | 'discount_available' | 'offer_expiring' | 'competition_started' | 'winner_announced' | 'promotion_reminder'
  data: {
    campaignId: string
    campaignName: string
    campaignType: 'sale' | 'discount' | 'offer' | 'competition' | 'promotion'
    discountPercentage?: number
    discountAmount?: number
    minimumPurchase?: number
    validUntil?: Date
    promoCode?: string
    termsAndConditions?: string
    eligibleProducts?: string[]
    eligibleCategories?: string[]
    targetAudience?: string[]
  }
}

// SECURITY NOTIFICATIONS
export interface SecurityNotification extends BaseNotification {
  category: 'security'
  type: 'login_attempt' | 'password_changed' | 'account_locked' | 'suspicious_activity' | 'data_breach' | 'permission_changed'
  data: {
    userId?: string
    username?: string
    ipAddress?: string
    location?: string
    device?: string
    browser?: string
    timestamp: Date
    riskLevel: 'low' | 'medium' | 'high' | 'critical'
    details?: string
  }
}

// SYSTEM NOTIFICATIONS
export interface SystemNotification extends BaseNotification {
  category: 'system'
  type: 'maintenance_scheduled' | 'maintenance_started' | 'maintenance_completed' | 'system_update' | 'feature_released' | 'service_disruption'
  data: {
    maintenanceId?: string
    scheduledStart?: Date
    scheduledEnd?: Date
    affectedServices?: string[]
    updateVersion?: string
    features?: string[]
    impact?: 'low' | 'medium' | 'high'
    instructions?: string
  }
}

// USER NOTIFICATIONS
export interface UserNotification extends BaseNotification {
  category: 'users'
  type: 'user_registered' | 'user_activated' | 'user_deactivated' | 'role_changed' | 'profile_updated' | 'welcome_message'
  data: {
    userId: string
    username: string
    email: string
    role: UserRole
    previousRole?: UserRole
    branchId?: string
    branchName?: string
    registrationDate?: Date
    lastLogin?: Date
  }
}

// FINANCIAL NOTIFICATIONS
export interface FinancialNotification extends BaseNotification {
  category: 'financial'
  type: 'payment_received' | 'payment_failed' | 'refund_processed' | 'revenue_milestone' | 'expense_alert' | 'budget_exceeded'
  data: {
    transactionId?: string
    amount: number
    currency: string
    paymentMethod?: string
    customerId?: string
    customerName?: string
    orderId?: string
    reason?: string
    milestone?: string
    budgetCategory?: string
    budgetLimit?: number
    currentSpend?: number
  }
}

// REPORTS NOTIFICATIONS
export interface ReportNotification extends BaseNotification {
  category: 'reports'
  type: 'report_generated' | 'report_scheduled' | 'report_failed' | 'analytics_insight' | 'performance_alert'
  data: {
    reportId: string
    reportName: string
    reportType: string
    generatedBy: string
    fileUrl?: string
    fileSize?: number
    recordCount?: number
    dateRange?: {
      start: Date
      end: Date
    }
    insights?: string[]
    metrics?: Record<string, number>
  }
}

// Union type for all notification types
export type PlatformNotification = 
  | InventoryNotification
  | OrderNotification
  | DeliveryNotification
  | CampaignNotification
  | SecurityNotification
  | SystemNotification
  | UserNotification
  | FinancialNotification
  | ReportNotification

// Notification preferences
export interface NotificationPreferences {
  userId: string
  
  // Channel preferences
  channels: {
    in_app: boolean
    email: boolean
    sms: boolean
    push: boolean
  }
  
  // Category preferences
  categories: {
    [K in NotificationCategory]: {
      enabled: boolean
      priority: NotificationPriority[]
      channels: NotificationChannel[]
      quietHours?: {
        start: string // HH:mm format
        end: string   // HH:mm format
        timezone: string
      }
    }
  }
  
  // Frequency settings
  frequency: {
    immediate: NotificationCategory[]
    digest: NotificationCategory[]
    weekly: NotificationCategory[]
  }
  
  // Advanced settings
  settings: {
    groupSimilar: boolean
    autoArchive: boolean
    autoArchiveDays: number
    soundEnabled: boolean
    vibrationEnabled: boolean
  }
}

// Notification template system
export interface NotificationTemplate {
  id: string
  name: string
  category: NotificationCategory
  type: string
  
  // Template content
  title: string
  message: string
  description?: string
  
  // Template variables
  variables: string[]
  
  // Default settings
  defaultPriority: NotificationPriority
  defaultChannels: NotificationChannel[]
  defaultActions?: NotificationAction[]
  
  // Styling
  icon?: string
  color?: string
  
  // Metadata
  createdAt: Date
  updatedAt: Date
  createdBy: string
  isActive: boolean
}

// Notification analytics
export interface NotificationAnalytics {
  notificationId: string
  
  // Delivery metrics
  sent: number
  delivered: number
  failed: number
  
  // Engagement metrics
  opened: number
  clicked: number
  dismissed: number
  
  // Channel breakdown
  channelMetrics: {
    [K in NotificationChannel]: {
      sent: number
      delivered: number
      opened: number
      clicked: number
    }
  }
  
  // Time metrics
  averageOpenTime: number
  averageClickTime: number
  
  // Conversion metrics
  conversions: number
  conversionRate: number
  
  createdAt: Date
  updatedAt: Date
}
