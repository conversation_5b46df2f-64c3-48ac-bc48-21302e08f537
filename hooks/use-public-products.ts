"use client"

import { useState, useEffect, useCallback } from 'react'
import type { Product } from '@/types/frontend'

interface ProductFilters {
  search?: string
  category?: string
  brand?: string
  branchId?: string
  minPrice?: number
  maxPrice?: number
  featured?: boolean
}

interface UsePublicProductsOptions {
  filters?: ProductFilters
  page?: number
  limit?: number
  autoFetch?: boolean
}

interface UsePublicProductsReturn {
  products: Product[]
  isLoading: boolean
  error: string | null
  totalProducts: number
  totalPages: number
  currentPage: number
  refetch: () => Promise<void>
  setFilters: (filters: ProductFilters) => void
  setPage: (page: number) => void
}

export function usePublicProducts(options: UsePublicProductsOptions = {}): UsePublicProductsReturn {
  const {
    filters: initialFilters = {},
    page: initialPage = 1,
    limit = 20,
    autoFetch = true
  } = options

  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [totalProducts, setTotalProducts] = useState(0)
  const [totalPages, setTotalPages] = useState(0)
  const [currentPage, setCurrentPage] = useState(initialPage)
  const [filters, setFilters] = useState<ProductFilters>(initialFilters)

  const fetchProducts = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: limit.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.category && { category: filters.category }),
        ...(filters.brand && { brand: filters.brand }),
        ...(filters.branchId && { branchId: filters.branchId }),
        ...(filters.minPrice && { minPrice: filters.minPrice.toString() }),
        ...(filters.maxPrice && { maxPrice: filters.maxPrice.toString() }),
        ...(filters.featured && { featured: 'true' })
      })

      console.log('🚀 Fetching products from:', `/api/products/public?${params}`)

      const response = await fetch(`/api/products/public?${params}`)
      const result = await response.json()

      console.log('📥 Frontend received response:', {
        ok: response.ok,
        status: response.status,
        success: result.success,
        dataLength: result.data?.length || 0,
        total: result.pagination?.total || 0,
        error: result.error
      })

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to fetch products')
      }

      setProducts(result.data || [])
      setTotalProducts(result.pagination?.total || 0)
      setTotalPages(result.pagination?.totalPages || 0)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch products'
      setError(errorMessage)
      console.error('Error fetching products:', err)
    } finally {
      setIsLoading(false)
    }
  }, [currentPage, limit, filters])

  const setPage = useCallback((page: number) => {
    setCurrentPage(page)
  }, [])

  const updateFilters = useCallback((newFilters: ProductFilters) => {
    setFilters(newFilters)
    setCurrentPage(1) // Reset to first page when filters change
  }, [])

  useEffect(() => {
    if (autoFetch) {
      fetchProducts()
    }
  }, [fetchProducts, autoFetch])

  return {
    products,
    isLoading,
    error,
    totalProducts,
    totalPages,
    currentPage,
    refetch: fetchProducts,
    setFilters: updateFilters,
    setPage
  }
}

// Hook for fetching a single product by ID
export function usePublicProduct(productId: string | null) {
  const [product, setProduct] = useState<Product | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchProduct = useCallback(async () => {
    if (!productId) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/products/public/${productId}`)
      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to fetch product')
      }

      setProduct(result.data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch product'
      setError(errorMessage)
      console.error('Error fetching product:', err)
    } finally {
      setIsLoading(false)
    }
  }, [productId])

  useEffect(() => {
    fetchProduct()
  }, [fetchProduct])

  return {
    product,
    isLoading,
    error,
    refetch: fetchProduct
  }
}

// Hook for fetching product categories
export function usePublicCategories() {
  const [categories, setCategories] = useState<Array<{
    _id: string
    name: string
    description: string
    slug: string
    productCount: number
    // Enhanced image fields
    featuredImage?: string | null
    icon?: string | null
    iconType?: 'image' | 'lucide'
    iconName?: string | null
  }>>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchCategories = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/products/categories/public')
      const result = await response.json()

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to fetch categories')
      }

      setCategories(result.data || [])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch categories'
      setError(errorMessage)
      console.error('Error fetching categories:', err)
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchCategories()
  }, [fetchCategories])

  return {
    categories,
    isLoading,
    error,
    refetch: fetchCategories
  }
}
