"use client"

import { useState } from 'react'
import { whatsappService, type CheckoutData, type WhatsAppContact } from '@/services/frontend/whatsappService'

interface UseWhatsAppCheckoutOptions {
  onSuccess?: (whatsappUrl: string, contact: WhatsAppContact) => void
  onError?: (error: string) => void
}

interface UseWhatsAppCheckoutReturn {
  isLoading: boolean
  error: string | null
  contact: WhatsAppContact | null
  initiateCheckout: (checkoutData: CheckoutData, branchId?: string) => Promise<void>
  openWhatsApp: (whatsappUrl: string) => void
  clearError: () => void
}

export function useWhatsAppCheckout(options: UseWhatsAppCheckoutOptions = {}): UseWhatsAppCheckoutReturn {
  const { onSuccess, onError } = options
  
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [contact, setContact] = useState<WhatsAppContact | null>(null)

  const clearError = () => {
    setError(null)
  }

  const openWhatsApp = (whatsappUrl: string) => {
    try {
      // Open WhatsApp in a new window/tab
      window.open(whatsappUrl, '_blank', 'noopener,noreferrer')
    } catch (err) {
      console.error('Failed to open WhatsApp:', err)
      setError('Failed to open WhatsApp. Please try again.')
    }
  }

  const initiateCheckout = async (checkoutData: CheckoutData, branchId?: string) => {
    setIsLoading(true)
    setError(null)
    setContact(null)

    try {
      console.log('🚀 Initiating WhatsApp checkout...', {
        customerName: checkoutData.customerInfo.name,
        itemCount: checkoutData.items.length,
        total: checkoutData.summary.total,
        branchId
      })

      const result = await whatsappService.initiateWhatsAppCheckout(checkoutData, branchId)

      if (!result.success) {
        const errorMessage = result.error || 'Failed to initiate WhatsApp checkout'
        setError(errorMessage)
        onError?.(errorMessage)
        return
      }

      if (result.contact) {
        setContact(result.contact)
      }

      if (result.whatsappUrl) {
        console.log('✅ WhatsApp checkout URL generated successfully')
        onSuccess?.(result.whatsappUrl, result.contact!)
        
        // Automatically open WhatsApp
        openWhatsApp(result.whatsappUrl)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred'
      console.error('WhatsApp checkout error:', err)
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  return {
    isLoading,
    error,
    contact,
    initiateCheckout,
    openWhatsApp,
    clearError
  }
}
