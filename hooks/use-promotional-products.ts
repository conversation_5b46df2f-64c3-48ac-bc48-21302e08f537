"use client"

import { useState, useEffect, useCallback } from 'react'
import type { Product } from '@/types/frontend'

interface UsePromotionalProductsOptions {
  limit?: number
  autoFetch?: boolean
}

interface UsePromotionalProductsReturn {
  products: Product[]
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function usePromotionalProducts(options: UsePromotionalProductsOptions = {}): UsePromotionalProductsReturn {
  const {
    limit = 6,
    autoFetch = true
  } = options

  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchPromotionalProducts = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        limit: limit.toString()
      })

      console.log('🚀 Fetching promotional products from:', `/api/products/promotional?${params}`)

      const response = await fetch(`/api/products/promotional?${params}`)
      const result = await response.json()

      console.log('📥 Frontend received promotional products response:', {
        ok: response.ok,
        status: response.status,
        success: result.success,
        dataLength: result.data?.length || 0,
        total: result.total || 0,
        error: result.error
      })

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to fetch promotional products')
      }

      setProducts(result.data || [])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch promotional products'
      setError(errorMessage)
      console.error('Error fetching promotional products:', err)
    } finally {
      setIsLoading(false)
    }
  }, [limit])

  useEffect(() => {
    if (autoFetch) {
      fetchPromotionalProducts()
    }
  }, [fetchPromotionalProducts, autoFetch])

  return {
    products,
    isLoading,
    error,
    refetch: fetchPromotionalProducts
  }
}
