// hooks/use-category-stats.ts - Custom hook for fetching category statistics

import { useState, useEffect, useCallback } from 'react'
import { apiClient } from '@/services/frontend'
import type { ApiResponse } from '@/types/frontend'

export interface CategoryStats {
  overview: {
    totalCategories: number
    activeCategories: number
    categoriesWithProducts: number
    emptyCategories: number
    utilizationRate: number
  }
  topCategoriesBySales: Array<{
    name: string
    totalSales: number
    totalQuantity: number
    orderCount: number
    averageOrderValue: number
  }>
  topCategoriesByProducts: Array<{
    name: string
    productCount: number
    averagePrice: number
    totalStock: number
  }>
  categoriesWithLowStock: Array<{
    name: string
    lowStockCount: number
    totalProducts: number
    lowStockPercentage: number
  }>
  categoryPerformance: Array<{
    name: string
    description: string
    productCount: number
    averagePrice: number
    totalStock: number
    lowStockProducts: number
    stockHealthPercentage: number
  }>
  filters: {
    branchId?: string
    period: number
    startDate?: string
    endDate?: string
  }
}

interface UseCategoryStatsOptions {
  branchId?: string
  period?: number
  startDate?: string
  endDate?: string
  autoFetch?: boolean
  refreshInterval?: number
}

interface UseCategoryStatsReturn {
  data: CategoryStats | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  lastUpdated: Date | null
}

export function useCategoryStats(options: UseCategoryStatsOptions = {}): UseCategoryStatsReturn {
  const {
    branchId,
    period = 30,
    startDate,
    endDate,
    autoFetch = true,
    refreshInterval
  } = options

  const [data, setData] = useState<CategoryStats | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchCategoryStats = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Build query parameters
      const params = new URLSearchParams()
      if (branchId) params.append('branchId', branchId)
      if (period) params.append('period', period.toString())
      if (startDate) params.append('startDate', startDate)
      if (endDate) params.append('endDate', endDate)

      const response: ApiResponse<CategoryStats> = await apiClient.get(
        `categories/stats?${params.toString()}`
      )

      if (response.success && response.data) {
        setData(response.data)
        setLastUpdated(new Date())
      } else {
        setError(response.error || 'Failed to fetch category statistics')
      }
    } catch (err) {
      console.error('Category stats fetch error:', err)
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }, [branchId, period, startDate, endDate])

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchCategoryStats()
    }
  }, [fetchCategoryStats, autoFetch])

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      const interval = setInterval(fetchCategoryStats, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [fetchCategoryStats, refreshInterval])

  return {
    data,
    isLoading,
    error,
    refetch: fetchCategoryStats,
    lastUpdated
  }
}
