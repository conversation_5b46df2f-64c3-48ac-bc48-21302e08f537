// hooks/use-activity-logs.ts - Custom hook for fetching activity logs

import { useState, useEffect, useCallback } from 'react'
import { activityLogService } from '@/services/frontend'
import type { ActivityLog, ApiResponse } from '@/types/frontend'

interface UseActivityLogsOptions {
  limit?: number
  branchId?: string
  autoFetch?: boolean
  refreshInterval?: number
}

interface UseActivityLogsReturn {
  data: ActivityLog[]
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  lastUpdated: Date | null
}

export function useActivityLogs(options: UseActivityLogsOptions = {}): UseActivityLogsReturn {
  const {
    limit = 10,
    branchId,
    autoFetch = true,
    refreshInterval
  } = options

  const [data, setData] = useState<ActivityLog[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchActivityLogs = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response: ApiResponse<ActivityLog[]> = await activityLogService.getRecentActivityLogs(
        limit,
        branchId
      )

      if (response.success && response.data) {
        setData(response.data)
        setLastUpdated(new Date())
      } else {
        setError(response.error || 'Failed to fetch activity logs')
      }
    } catch (err) {
      console.error('Activity logs fetch error:', err)
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }, [limit, branchId])

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchActivityLogs()
    }
  }, [fetchActivityLogs, autoFetch])

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      const interval = setInterval(fetchActivityLogs, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [fetchActivityLogs, refreshInterval])

  return {
    data,
    isLoading,
    error,
    refetch: fetchActivityLogs,
    lastUpdated
  }
}
