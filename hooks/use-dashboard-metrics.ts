// hooks/use-dashboard-metrics.ts - Custom hook for fetching dashboard metrics

import { useState, useEffect, useCallback } from 'react'
import { analyticsService } from '@/services/frontend'
import type { DashboardMetrics, ApiResponse } from '@/types/frontend'

interface UseDashboardMetricsOptions {
  branchId?: string
  period?: number
  startDate?: string
  endDate?: string
  autoFetch?: boolean
  refreshInterval?: number
}

interface UseDashboardMetricsReturn {
  data: DashboardMetrics | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  lastUpdated: Date | null
}

export function useDashboardMetrics(options: UseDashboardMetricsOptions = {}): UseDashboardMetricsReturn {
  const {
    branchId,
    period = 30,
    startDate,
    endDate,
    autoFetch = true,
    refreshInterval
  } = options

  const [data, setData] = useState<DashboardMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchMetrics = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const response: ApiResponse<DashboardMetrics> = await analyticsService.getDashboardMetrics(
        branchId,
        period,
        startDate,
        endDate
      )

      if (response.success && response.data) {
        setData(response.data)
        setLastUpdated(new Date())
      } else {
        setError(response.error || 'Failed to fetch dashboard metrics')
      }
    } catch (err) {
      console.error('Dashboard metrics fetch error:', err)
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }, [branchId, period, startDate, endDate])

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchMetrics()
    }
  }, [fetchMetrics, autoFetch])

  // Set up refresh interval if specified
  useEffect(() => {
    if (refreshInterval && refreshInterval > 0) {
      const interval = setInterval(fetchMetrics, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [fetchMetrics, refreshInterval])

  return {
    data,
    isLoading,
    error,
    refetch: fetchMetrics,
    lastUpdated
  }
}
