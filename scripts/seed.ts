#!/usr/bin/env node

// scripts/seed.ts - Database seeding script

import { seedDatabase } from '../lib/database/seed'

async function runSeed() {
  console.log('🌱 Starting database seeding...')
  
  try {
    const result = await seedDatabase()
    
    if (result.success) {
      console.log('✅ Database seeding completed successfully!')
      process.exit(0)
    } else {
      console.error('❌ Database seeding failed:', result.error)
      process.exit(1)
    }
  } catch (error) {
    console.error('❌ Database seeding failed:', error)
    process.exit(1)
  }
}

runSeed()
