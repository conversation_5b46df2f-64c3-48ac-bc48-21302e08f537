#!/usr/bin/env node

// scripts/seed.js - Database seeding script

const path = require('path')

// Set up environment
process.env.NODE_ENV = process.env.NODE_ENV || 'development'

async function runSeed() {
  console.log('🌱 Starting database seeding...')

  try {
    // Import the seed function dynamically
    const { seedDatabase } = await import('../lib/database/seed.js')

    const result = await seedDatabase()

    if (result.success) {
      console.log('✅ Database seeding completed successfully!')
      process.exit(0)
    } else {
      console.error('❌ Database seeding failed:', result.error)
      process.exit(1)
    }
  } catch (error) {
    console.error('❌ Database seeding failed:', error)
    process.exit(1)
  }
}

runSeed()
