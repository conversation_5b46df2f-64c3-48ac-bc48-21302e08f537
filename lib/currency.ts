/**
 * Currency System for Multi-Country Support
 * Supports Malawi, Zambia, Tanzania, and South Africa
 */

export interface Currency {
  code: string
  name: string
  symbol: string
  country: string
  locale: string
  decimals: number
  isDefault?: boolean
}

export interface CountryConfig {
  code: string
  name: string
  defaultCurrency: string
  allowedCurrencies: string[]
}

// Supported currencies
export const CURRENCIES: Record<string, Currency> = {
  // Malawi Kwacha (Default)
  MWK: {
    code: 'MWK',
    name: 'Malawi Kwacha',
    symbol: 'MK',
    country: 'Malawi',
    locale: 'en-MW',
    decimals: 2,
    isDefault: true
  },
  
  // Zambian Kwacha
  ZMW: {
    code: 'ZMW',
    name: 'Zambian Kwacha',
    symbol: 'ZK',
    country: 'Zambia',
    locale: 'en-ZM',
    decimals: 2
  },
  
  // Tanzanian Shilling
  TZS: {
    code: 'TZS',
    name: 'Tanzanian Shilling',
    symbol: 'TSh',
    country: 'Tanzania',
    locale: 'en-TZ',
    decimals: 2
  },
  
  // South African Rand
  ZAR: {
    code: 'ZAR',
    name: 'South African Rand',
    symbol: 'R',
    country: 'South Africa',
    locale: 'en-ZA',
    decimals: 2
  },
  
  // US Dollar (for international)
  USD: {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    country: 'International',
    locale: 'en-US',
    decimals: 2
  }
}

// Country configurations
export const COUNTRIES: Record<string, CountryConfig> = {
  MW: {
    code: 'MW',
    name: 'Malawi',
    defaultCurrency: 'MWK',
    allowedCurrencies: ['MWK'] // Only MWK for Malawi
  },
  ZM: {
    code: 'ZM',
    name: 'Zambia',
    defaultCurrency: 'ZMW',
    allowedCurrencies: ['ZMW', 'USD'] // ZMW and USD for Zambia
  },
  TZ: {
    code: 'TZ',
    name: 'Tanzania',
    defaultCurrency: 'TZS',
    allowedCurrencies: ['TZS', 'USD'] // TZS and USD for Tanzania
  },
  ZA: {
    code: 'ZA',
    name: 'South Africa',
    defaultCurrency: 'ZAR',
    allowedCurrencies: ['ZAR', 'USD'] // ZAR and USD for South Africa
  }
}

/**
 * Get currency by code
 */
export function getCurrency(code: string): Currency | null {
  return CURRENCIES[code] || null
}

/**
 * Get default currency for a country
 */
export function getDefaultCurrency(countryCode: string): Currency {
  const country = COUNTRIES[countryCode]
  if (country) {
    return CURRENCIES[country.defaultCurrency]
  }
  // Default to MWK if country not found
  return CURRENCIES.MWK
}

/**
 * Get allowed currencies for a country
 */
export function getAllowedCurrencies(countryCode: string): Currency[] {
  const country = COUNTRIES[countryCode]
  if (country) {
    return country.allowedCurrencies.map(code => CURRENCIES[code]).filter(Boolean)
  }
  // Default to MWK only
  return [CURRENCIES.MWK]
}

/**
 * Format price with currency
 */
export function formatPrice(
  amount: number, 
  currencyCode: string = 'MWK',
  options: {
    showSymbol?: boolean
    showCode?: boolean
    minimumFractionDigits?: number
    maximumFractionDigits?: number
  } = {}
): string {
  const currency = getCurrency(currencyCode)
  if (!currency) {
    throw new Error(`Unsupported currency: ${currencyCode}`)
  }

  const {
    showSymbol = true,
    showCode = false,
    minimumFractionDigits = currency.decimals,
    maximumFractionDigits = currency.decimals
  } = options

  // Format the number
  const formatter = new Intl.NumberFormat(currency.locale, {
    minimumFractionDigits,
    maximumFractionDigits,
    useGrouping: true
  })

  const formattedAmount = formatter.format(amount)

  // Build the display string
  let result = ''
  
  if (showSymbol) {
    result = `${currency.symbol} ${formattedAmount}`
  } else {
    result = formattedAmount
  }
  
  if (showCode) {
    result += ` ${currency.code}`
  }

  return result
}

/**
 * Parse price string to number
 */
export function parsePrice(priceString: string): number {
  // Remove currency symbols and spaces
  const cleanString = priceString
    .replace(/[^\d.,]/g, '') // Remove non-numeric characters except . and ,
    .replace(/,/g, '') // Remove commas
  
  const parsed = parseFloat(cleanString)
  return isNaN(parsed) ? 0 : parsed
}

/**
 * Validate price amount
 */
export function validatePrice(
  amount: number,
  currencyCode: string = 'MWK',
  options: {
    min?: number
    max?: number
  } = {}
): {
  isValid: boolean
  error?: string
} {
  const { min = 0.01, max = 100000000 } = options

  if (isNaN(amount) || amount < 0) {
    return {
      isValid: false,
      error: 'Price must be a positive number'
    }
  }

  if (amount < min) {
    return {
      isValid: false,
      error: `Price must be at least ${formatPrice(min, currencyCode)}`
    }
  }

  if (amount > max) {
    return {
      isValid: false,
      error: `Price cannot exceed ${formatPrice(max, currencyCode)}`
    }
  }

  return { isValid: true }
}

/**
 * Get currency options for select dropdown
 */
export function getCurrencyOptions(countryCode?: string): Array<{
  value: string
  label: string
  symbol: string
}> {
  let currencies: Currency[]
  
  if (countryCode) {
    currencies = getAllowedCurrencies(countryCode)
  } else {
    currencies = Object.values(CURRENCIES)
  }

  return currencies.map(currency => ({
    value: currency.code,
    label: `${currency.name} (${currency.symbol})`,
    symbol: currency.symbol
  }))
}

/**
 * Detect country from branch or user location
 */
export function detectCountryFromBranch(branchId: string): string {
  // This would typically query the branch data
  // For now, we'll default to Malawi
  // TODO: Implement proper branch-to-country mapping
  return 'MW'
}

/**
 * Convert between currencies (placeholder for future exchange rate API)
 */
export function convertCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string
): number {
  // TODO: Implement real currency conversion with exchange rates
  // For now, return the same amount
  console.warn('Currency conversion not implemented yet')
  return amount
}
