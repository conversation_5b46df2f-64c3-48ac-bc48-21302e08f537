import { Server as HTTPServer } from 'http'
import { Server as SocketIOServer } from 'socket.io'
import { verifyAccessToken } from '@/lib/auth/jwt'
import { connectDB, logActivity } from '@/lib/database'
import Session from '@/lib/database/models/Session'
import User from '@/lib/database/models/User'
import type { UserRole } from '@/types'

export interface SocketUser {
  userId: string
  email: string
  username: string
  role: UserRole
  branchId?: string
}

export interface ServerToClientEvents {
  // Session events
  sessionCreated: (data: { sessionId: string; userId: string; userInfo: any }) => void
  sessionTerminated: (data: { sessionId: string; userId: string; reason: string }) => void
  sessionUpdated: (data: { sessionId: string; userId: string; updates: any }) => void

  // Security events
  securityAlert: (data: { type: string; message: string; severity: 'low' | 'medium' | 'high' | 'critical'; timestamp: Date }) => void
  suspiciousActivity: (data: { userId: string; activity: string; details: any; timestamp: Date }) => void

  // Admin notifications
  adminNotification: (data: { type: string; title: string; message: string; data?: any }) => void
  userStatusChanged: (data: { userId: string; status: 'online' | 'offline' | 'away'; timestamp: Date }) => void

  // Real-time updates
  sessionStatsUpdated: (data: { totalSessions: number; activeSessions: number; onlineUsers: number }) => void
  auditLogCreated: (data: { logId: string; action: string; userId: string; details: any }) => void

  // Notification events
  notification: (data: {
    id: string
    category: string
    type: string
    priority: string
    title: string
    message: string
    description?: string
    icon?: string
    image?: string
    color?: string
    badge?: string
    data?: Record<string, any>
    actions?: Array<{
      id: string
      label: string
      type: string
      variant?: string
      url?: string
      action?: string
      data?: any
    }>
    createdAt: Date
  }) => void
  notificationRead: (data: { notificationId: string; userId: string }) => void
  notificationDeleted: (data: { notificationId: string; userId: string }) => void
  notificationCountUpdated: (data: { userId: string; unreadCount: number; totalCount: number }) => void
}

export interface ClientToServerEvents {
  // Authentication
  authenticate: (token: string, callback: (success: boolean, user?: SocketUser) => void) => void

  // Session management
  joinSessionRoom: (sessionId: string) => void
  leaveSessionRoom: (sessionId: string) => void

  // Admin actions
  joinAdminRoom: () => void
  leaveAdminRoom: () => void
  terminateSession: (sessionId: string, reason: string) => void

  // User status
  updateStatus: (status: 'online' | 'offline' | 'away') => void
  heartbeat: () => void

  // Notification events
  markNotificationRead: (notificationId: string) => void
  markAllNotificationsRead: () => void
  joinNotificationRoom: () => void
  leaveNotificationRoom: () => void
}

export interface InterServerEvents {
  ping: () => void
}

export interface SocketData {
  user?: SocketUser
  sessionId?: string
  lastActivity?: Date
}

class SocketManager {
  private io: SocketIOServer<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData> | null = null
  private connectedUsers = new Map<string, string>() // userId -> socketId
  private userSessions = new Map<string, Set<string>>() // userId -> Set of sessionIds

  initialize(httpServer: HTTPServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.NODE_ENV === 'production' 
          ? ['https://fathahitech-shops.vercel.app'] 
          : ['http://localhost:3000', 'http://localhost:3001'],
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    this.setupEventHandlers()
    console.log('Socket.IO server initialized')
  }

  private setupEventHandlers() {
    if (!this.io) return

    this.io.on('connection', (socket) => {
      console.log(`Socket connected: ${socket.id}`)

      // Authentication handler
      socket.on('authenticate', async (token, callback) => {
        try {
          const decoded = verifyAccessToken(token)
          const user: SocketUser = {
            userId: decoded.userId,
            email: decoded.email,
            username: decoded.username,
            role: decoded.role,
            branchId: decoded.branchId
          }

          socket.data.user = user
          socket.data.lastActivity = new Date()

          // Track connected user
          this.connectedUsers.set(user.userId, socket.id)

          // Join user-specific room
          socket.join(`user:${user.userId}`)

          // Join role-based rooms
          socket.join(`role:${user.role}`)
          if (user.branchId) {
            socket.join(`branch:${user.branchId}`)
          }

          // Join admin room if admin
          if (user.role === 'overall_admin') {
            socket.join('admin')
          }

          // Log connection activity
          await this.logUserActivity(user.userId, 'socket_connected', {
            socketId: socket.id,
            timestamp: new Date()
          })

          // Notify admins of user connection
          this.notifyAdmins('userStatusChanged', {
            userId: user.userId,
            status: 'online',
            timestamp: new Date()
          })

          callback(true, user)
          console.log(`User authenticated: ${user.username} (${user.role})`)
        } catch (error) {
          console.error('Socket authentication failed:', error)
          callback(false)
        }
      })

      // Session room management
      socket.on('joinSessionRoom', (sessionId) => {
        if (!socket.data.user) return
        socket.join(`session:${sessionId}`)
        
        // Track user sessions
        const userSessions = this.userSessions.get(socket.data.user.userId) || new Set()
        userSessions.add(sessionId)
        this.userSessions.set(socket.data.user.userId, userSessions)
      })

      socket.on('leaveSessionRoom', (sessionId) => {
        if (!socket.data.user) return
        socket.leave(`session:${sessionId}`)
        
        // Remove from tracked sessions
        const userSessions = this.userSessions.get(socket.data.user.userId)
        if (userSessions) {
          userSessions.delete(sessionId)
        }
      })

      // Admin room management
      socket.on('joinAdminRoom', () => {
        if (!socket.data.user || socket.data.user.role !== 'overall_admin') return
        socket.join('admin')
      })

      socket.on('leaveAdminRoom', () => {
        socket.leave('admin')
      })

      // Session termination
      socket.on('terminateSession', async (sessionId, reason) => {
        if (!socket.data.user || socket.data.user.role !== 'overall_admin') return

        try {
          await connectDB()
          const session = await Session.findById(sessionId)
          if (session && session.isActive) {
            await session.terminate('admin_forced')
            
            // Log admin action
            await this.logUserActivity(socket.data.user.userId, 'session_terminated', {
              sessionId,
              reason,
              targetUserId: session.userId
            })

            // Notify all admins
            this.notifyAdmins('sessionTerminated', {
              sessionId,
              userId: session.userId.toString(),
              reason
            })

            // Notify the affected user
            this.io?.to(`user:${session.userId}`).emit('sessionTerminated', {
              sessionId,
              userId: session.userId.toString(),
              reason
            })
          }
        } catch (error) {
          console.error('Error terminating session:', error)
        }
      })

      // User status updates
      socket.on('updateStatus', async (status) => {
        if (!socket.data.user) return

        socket.data.lastActivity = new Date()
        
        // Notify admins of status change
        this.notifyAdmins('userStatusChanged', {
          userId: socket.data.user.userId,
          status,
          timestamp: new Date()
        })
      })

      // Heartbeat for keeping connection alive
      socket.on('heartbeat', () => {
        if (socket.data.user) {
          socket.data.lastActivity = new Date()
        }
      })

      // Notification handlers
      socket.on('markNotificationRead', async (notificationId) => {
        if (!socket.data.user) return

        try {
          // Import here to avoid circular dependency
          const { NotificationService } = await import('@/lib/services/notification-service')
          await NotificationService.markAsRead(notificationId, socket.data.user.userId)

          // Notify user of successful read
          socket.emit('notificationRead', { notificationId, userId: socket.data.user.userId })
        } catch (error) {
          console.error('Error marking notification as read:', error)
        }
      })

      socket.on('markAllNotificationsRead', async () => {
        if (!socket.data.user) return

        try {
          const { NotificationService } = await import('@/lib/services/notification-service')
          const { notifications } = await NotificationService.getUserNotifications(socket.data.user.userId, { status: 'unread' })
          const unreadIds = notifications.map(n => n.id)

          if (unreadIds.length > 0) {
            await NotificationService.markMultipleAsRead(unreadIds, socket.data.user.userId)

            // Update notification count
            socket.emit('notificationCountUpdated', {
              userId: socket.data.user.userId,
              unreadCount: 0,
              totalCount: notifications.length
            })
          }
        } catch (error) {
          console.error('Error marking all notifications as read:', error)
        }
      })

      socket.on('joinNotificationRoom', () => {
        if (!socket.data.user) return
        socket.join(`notifications:${socket.data.user.userId}`)
      })

      socket.on('leaveNotificationRoom', () => {
        if (!socket.data.user) return
        socket.leave(`notifications:${socket.data.user.userId}`)
      })

      // Disconnection handler
      socket.on('disconnect', async (reason) => {
        console.log(`Socket disconnected: ${socket.id}, reason: ${reason}`)

        if (socket.data.user) {
          // Remove from connected users
          this.connectedUsers.delete(socket.data.user.userId)
          
          // Clean up user sessions
          this.userSessions.delete(socket.data.user.userId)

          // Log disconnection
          await this.logUserActivity(socket.data.user.userId, 'socket_disconnected', {
            socketId: socket.id,
            reason,
            timestamp: new Date()
          })

          // Notify admins of user disconnection
          this.notifyAdmins('userStatusChanged', {
            userId: socket.data.user.userId,
            status: 'offline',
            timestamp: new Date()
          })
        }
      })
    })
  }

  // Helper methods
  private async logUserActivity(userId: string, action: string, details: any) {
    try {
      await logActivity(userId, action, details)
    } catch (error) {
      console.error('Error logging user activity:', error)
    }
  }

  private notifyAdmins(event: keyof ServerToClientEvents, data: any) {
    if (!this.io) return
    this.io.to('admin').emit(event as any, data)
  }

  // Public methods for external use
  public notifySessionCreated(sessionId: string, userId: string, userInfo: any) {
    if (!this.io) return
    
    this.io.to('admin').emit('sessionCreated', { sessionId, userId, userInfo })
    this.io.to(`user:${userId}`).emit('sessionCreated', { sessionId, userId, userInfo })
  }

  public notifySessionTerminated(sessionId: string, userId: string, reason: string) {
    if (!this.io) return
    
    this.io.to('admin').emit('sessionTerminated', { sessionId, userId, reason })
    this.io.to(`user:${userId}`).emit('sessionTerminated', { sessionId, userId, reason })
  }

  public sendSecurityAlert(alert: { type: string; message: string; severity: 'low' | 'medium' | 'high' | 'critical'; timestamp: Date }) {
    if (!this.io) return
    this.io.to('admin').emit('securityAlert', alert)
  }

  public updateSessionStats(stats: { totalSessions: number; activeSessions: number; onlineUsers: number }) {
    if (!this.io) return
    this.io.to('admin').emit('sessionStatsUpdated', stats)
  }

  public getConnectedUsers(): string[] {
    return Array.from(this.connectedUsers.keys())
  }

  public isUserConnected(userId: string): boolean {
    return this.connectedUsers.has(userId)
  }

  public getIO() {
    return this.io
  }

  // Notification methods
  public sendNotificationToUser(userId: string, notification: any) {
    if (!this.io) return
    this.io.to(`user:${userId}`).emit('notification', notification)
  }

  public sendNotificationToRole(role: string, notification: any) {
    if (!this.io) return
    this.io.to(`role:${role}`).emit('notification', notification)
  }

  public sendNotificationToBranch(branchId: string, notification: any) {
    if (!this.io) return
    this.io.to(`branch:${branchId}`).emit('notification', notification)
  }

  public broadcastNotification(notification: any) {
    if (!this.io) return
    this.io.emit('notification', notification)
  }

  public updateNotificationCount(userId: string, unreadCount: number, totalCount: number) {
    if (!this.io) return
    this.io.to(`user:${userId}`).emit('notificationCountUpdated', {
      userId,
      unreadCount,
      totalCount
    })
  }

  public notifyNotificationRead(userId: string, notificationId: string) {
    if (!this.io) return
    this.io.to(`user:${userId}`).emit('notificationRead', { notificationId, userId })
  }

  public notifyNotificationDeleted(userId: string, notificationId: string) {
    if (!this.io) return
    this.io.to(`user:${userId}`).emit('notificationDeleted', { notificationId, userId })
  }
}

// Singleton instance
export const socketManager = new SocketManager()
