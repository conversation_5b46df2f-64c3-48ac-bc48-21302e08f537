import { useEffect, useRef, useState } from 'react'
import { io, Socket } from 'socket.io-client'
import type { ServerToClientEvents, ClientToServerEvents, SocketUser } from './server'
import { isVercelEnvironment, isWebSocketSupported, getConnectionUrl, logEnvironmentInfo, shouldDisableSocketIO, getSocketConfig, handleSocketError } from '@/lib/utils/environment'

type SocketInstance = Socket<ServerToClientEvents, ClientToServerEvents>

interface UseSocketOptions {
  autoConnect?: boolean
  reconnection?: boolean
  reconnectionAttempts?: number
  reconnectionDelay?: number
}

interface SocketState {
  connected: boolean
  authenticated: boolean
  user: SocketUser | null
  connecting: boolean
  error: string | null
}

export function useSocket(options: UseSocketOptions = {}) {
  const {
    autoConnect = true,
    reconnection = true,
    reconnectionAttempts = 5,
    reconnectionDelay = 1000
  } = options

  const socketRef = useRef<SocketInstance | null>(null)
  const [state, setState] = useState<SocketState>({
    connected: false,
    authenticated: false,
    user: null,
    connecting: false,
    error: null
  })

  // Initialize socket connection
  const connect = () => {
    if (socketRef.current?.connected) return

    setState(prev => ({ ...prev, connecting: true, error: null }))

    // Log environment info for debugging
    if (process.env.NODE_ENV === 'development') {
      logEnvironmentInfo()
    }

    // Check if WebSocket is supported in current environment
    if (!isWebSocketSupported()) {
      console.log('WebSocket not supported in current environment - skipping connection')
      setState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: 'WebSocket not supported in current environment'
      }))
      return
    }

    const serverUrl = getConnectionUrl()

    // Check if Socket.io should be disabled entirely
    if (shouldDisableSocketIO()) {
      console.log('Socket.io disabled in this environment')
      setState(prev => ({
        ...prev,
        error: 'Real-time features unavailable in this environment',
        connecting: false
      }))
      return
    }

    // Use environment-specific configuration
    const socketConfig = getSocketConfig()

    socketRef.current = io(serverUrl, {
      ...socketConfig,
      reconnection,
      reconnectionAttempts: reconnectionAttempts || socketConfig.reconnectionAttempts,
      reconnectionDelay: reconnectionDelay || socketConfig.reconnectionDelay
    })

    const socket = socketRef.current

    // Connection events
    socket.on('connect', () => {
      console.log('Socket connected:', socket.id)
      setState(prev => ({ ...prev, connected: true, connecting: false, error: null }))
    })

    socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason)
      setState(prev => ({ 
        ...prev, 
        connected: false, 
        authenticated: false, 
        user: null,
        connecting: false 
      }))
    })

    socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error)

      // Use centralized error handling
      const errorInfo = handleSocketError(error)

      setState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: errorInfo.message
      }))

      // Gracefully handle connection errors without crashing
      if (socketRef.current) {
        try {
          socketRef.current.disconnect()
        } catch (e) {
          console.warn('Error disconnecting socket:', e)
        }
        socketRef.current = null
      }

      // Auto-retry with exponential backoff for recoverable errors
      if (errorInfo.shouldRetry && reconnectionAttempts > 0) {
        const retryDelay = Math.min(reconnectionDelay * Math.pow(2, 5 - reconnectionAttempts), 30000)
        console.log(`Retrying connection in ${retryDelay}ms...`)
        setTimeout(() => {
          if (!socketRef.current?.connected) {
            console.log('Attempting reconnection...')
            // Clear error state before retry
            setState(prev => ({ ...prev, error: null, connecting: true }))
            // Reinitialize socket with new instance
            try {
              initializeSocket()
            } catch (retryError) {
              console.error('Retry failed:', retryError)
              setState(prev => ({
                ...prev,
                error: 'Connection failed - real-time features unavailable',
                connecting: false
              }))
            }
          }
        }, retryDelay)
      }
    })

    socket.on('error', (error) => {
      console.error('Socket error:', error)
      setState(prev => ({
        ...prev,
        error: `Socket error: ${error.message || 'Unknown error'}`
      }))

      // Prevent error from propagating and crashing the app
      if (socketRef.current) {
        socketRef.current.disconnect()
        socketRef.current = null
      }
    })

    socket.connect()
  }

  // Authenticate with token
  const authenticate = (token: string): Promise<SocketUser> => {
    return new Promise((resolve, reject) => {
      if (!socketRef.current?.connected) {
        reject(new Error('Socket not connected'))
        return
      }

      socketRef.current.emit('authenticate', token, (success, user) => {
        if (success && user) {
          setState(prev => ({ ...prev, authenticated: true, user }))
          resolve(user)
        } else {
          setState(prev => ({ ...prev, error: 'Authentication failed' }))
          reject(new Error('Authentication failed'))
        }
      })
    })
  }

  // Disconnect socket
  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.disconnect()
      socketRef.current = null
    }
    setState({
      connected: false,
      authenticated: false,
      user: null,
      connecting: false,
      error: null
    })
  }

  // Session management
  const joinSessionRoom = (sessionId: string) => {
    socketRef.current?.emit('joinSessionRoom', sessionId)
  }

  const leaveSessionRoom = (sessionId: string) => {
    socketRef.current?.emit('leaveSessionRoom', sessionId)
  }

  // Admin functions
  const joinAdminRoom = () => {
    socketRef.current?.emit('joinAdminRoom')
  }

  const leaveAdminRoom = () => {
    socketRef.current?.emit('leaveAdminRoom')
  }

  const terminateSession = (sessionId: string, reason: string) => {
    socketRef.current?.emit('terminateSession', sessionId, reason)
  }

  // User status
  const updateStatus = (status: 'online' | 'offline' | 'away') => {
    socketRef.current?.emit('updateStatus', status)
  }

  const sendHeartbeat = () => {
    socketRef.current?.emit('heartbeat')
  }

  // Event listeners
  const on = <K extends keyof ServerToClientEvents>(
    event: K,
    listener: ServerToClientEvents[K]
  ) => {
    socketRef.current?.on(event, listener)
  }

  const off = <K extends keyof ServerToClientEvents>(
    event: K,
    listener?: ServerToClientEvents[K]
  ) => {
    socketRef.current?.off(event, listener)
  }

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [autoConnect])

  // Heartbeat interval
  useEffect(() => {
    if (state.authenticated) {
      const interval = setInterval(sendHeartbeat, 30000) // 30 seconds
      return () => clearInterval(interval)
    }
  }, [state.authenticated])

  return {
    // State
    ...state,
    socket: socketRef.current,

    // Connection methods
    connect,
    disconnect,
    authenticate,

    // Session methods
    joinSessionRoom,
    leaveSessionRoom,

    // Admin methods
    joinAdminRoom,
    leaveAdminRoom,
    terminateSession,

    // User methods
    updateStatus,
    sendHeartbeat,

    // Event methods
    on,
    off
  }
}

// Hook for session monitoring (admin use)
export function useSessionMonitoring() {
  const socket = useSocket({ autoConnect: false })
  const [sessions, setSessions] = useState<any[]>([])
  const [sessionStats, setSessionStats] = useState({
    totalSessions: 0,
    activeSessions: 0,
    onlineUsers: 0
  })
  const [securityAlerts, setSecurityAlerts] = useState<any[]>([])

  useEffect(() => {
    if (!socket.authenticated || socket.user?.role !== 'overall_admin') return

    // Join admin room
    socket.joinAdminRoom()

    // Listen for session events
    socket.on('sessionCreated', (data) => {
      setSessions(prev => [...prev, data])
    })

    socket.on('sessionTerminated', (data) => {
      setSessions(prev => prev.filter(s => s.sessionId !== data.sessionId))
    })

    socket.on('sessionStatsUpdated', (stats) => {
      setSessionStats(stats)
    })

    socket.on('securityAlert', (alert) => {
      setSecurityAlerts(prev => [alert, ...prev.slice(0, 49)]) // Keep last 50 alerts
    })

    return () => {
      socket.leaveAdminRoom()
      socket.off('sessionCreated')
      socket.off('sessionTerminated')
      socket.off('sessionStatsUpdated')
      socket.off('securityAlert')
    }
  }, [socket.authenticated, socket.user?.role])

  return {
    ...socket,
    sessions,
    sessionStats,
    securityAlerts,
    clearAlerts: () => setSecurityAlerts([])
  }
}

// Hook for user notifications
export function useNotifications() {
  const socket = useSocket({ autoConnect: false })
  const [notifications, setNotifications] = useState<any[]>([])

  useEffect(() => {
    if (!socket.authenticated) return

    socket.on('adminNotification', (notification) => {
      setNotifications(prev => [notification, ...prev.slice(0, 19)]) // Keep last 20
    })

    socket.on('sessionTerminated', (data) => {
      if (data.userId === socket.user?.userId) {
        setNotifications(prev => [{
          type: 'session_terminated',
          title: 'Session Terminated',
          message: `Your session was terminated: ${data.reason}`,
          timestamp: new Date()
        }, ...prev.slice(0, 19)])
      }
    })

    return () => {
      socket.off('adminNotification')
      socket.off('sessionTerminated')
    }
  }, [socket.authenticated, socket.user?.userId])

  const markAsRead = (index: number) => {
    setNotifications(prev => prev.filter((_, i) => i !== index))
  }

  const clearAll = () => {
    setNotifications([])
  }

  return {
    ...socket,
    notifications,
    markAsRead,
    clearAll
  }
}

// Simple socket client manager for notification store
class SocketClient {
  private socket: SocketInstance | null = null
  private isConnected = false

  connect() {
    if (this.socket?.connected) return

    // Check if WebSocket is supported in current environment
    if (!isWebSocketSupported()) {
      console.log('WebSocket not supported - skipping connection in SocketClient')
      this.isConnected = false
      return
    }

    const serverUrl = getConnectionUrl()

    this.socket = io(serverUrl, {
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      transports: ['websocket', 'polling'],
      timeout: 5000
    })

    this.socket.on('connect', () => {
      this.isConnected = true
    })

    this.socket.on('disconnect', () => {
      this.isConnected = false
    })

    this.socket.on('connect_error', (error) => {
      console.error('SocketClient connection error:', error)
      this.isConnected = false
      if (this.socket) {
        this.socket.disconnect()
        this.socket = null
      }
    })

    this.socket.on('error', (error) => {
      console.error('SocketClient error:', error)
      this.isConnected = false
      if (this.socket) {
        this.socket.disconnect()
        this.socket = null
      }
    })
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
      this.isConnected = false
    }
  }

  getSocket() {
    return this.socket
  }

  isSocketConnected() {
    return this.isConnected
  }
}

export const socketClient = new SocketClient()
