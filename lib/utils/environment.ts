// lib/utils/environment.ts - Environment detection utilities

/**
 * Detect if the application is running in a Vercel environment
 */
export function isVercelEnvironment(): boolean {
  // Check for Vercel environment variable
  if (process.env.VERCEL === '1') {
    return true
  }

  // Check for Vercel hostname in browser
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname
    if (hostname.includes('vercel.app') || hostname.includes('.vercel.app')) {
      return true
    }
  }

  // Check for Vercel URL in environment
  const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL
  if (socketUrl && socketUrl.includes('vercel.app')) {
    return true
  }

  return false
}

/**
 * Detect if WebSocket connections are supported in the current environment
 */
export function isWebSocketSupported(): boolean {
  // WebSocket is not supported in Vercel serverless environment
  if (isVercelEnvironment()) {
    return false
  }

  // Check if we're in a browser environment with WebSocket support
  if (typeof window !== 'undefined') {
    return 'WebSocket' in window
  }

  // For server-side, assume WebSocket is supported in non-Vercel environments
  return true
}

/**
 * Get the appropriate connection URL based on environment
 */
export function getConnectionUrl(): string {
  // In development, use localhost
  if (process.env.NODE_ENV === 'development') {
    return process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001'
  }

  // In production, prefer environment variable first
  if (process.env.NEXT_PUBLIC_SOCKET_URL) {
    return process.env.NEXT_PUBLIC_SOCKET_URL
  }

  // Fallback to current origin for HTTP fallback
  if (typeof window !== 'undefined') {
    return window.location.origin
  }

  // Final fallback
  return 'https://fathahitech-shops.vercel.app'
}

/**
 * Determine the best connection strategy for the current environment
 */
export function getConnectionStrategy(): 'websocket' | 'http' | 'hybrid' {
  if (isVercelEnvironment()) {
    return 'http' // Use HTTP fallback for Vercel
  }

  if (process.env.NODE_ENV === 'development') {
    return 'websocket' // Use WebSocket for development
  }

  return 'hybrid' // Try WebSocket first, fallback to HTTP
}

/**
 * Check if Socket.io should be completely disabled
 */
export function shouldDisableSocketIO(): boolean {
  // Disable in SSR environments
  if (typeof window === 'undefined') {
    return true
  }

  // For now, allow Socket.io with fallbacks
  // You can enable this to completely disable Socket.io in production:
  // return isVercelEnvironment() && process.env.NODE_ENV === 'production'

  return false
}

/**
 * Get Socket.io configuration based on environment
 */
export function getSocketConfig() {
  const isProduction = process.env.NODE_ENV === 'production'
  const isVercel = isVercelEnvironment()

  return {
    autoConnect: false,
    reconnection: true,
    reconnectionAttempts: isProduction ? 3 : 5,
    reconnectionDelay: isProduction ? 2000 : 1000,
    transports: isVercel ? ['polling'] : ['websocket', 'polling'],
    timeout: isProduction ? 10000 : 5000,
    forceNew: true,
    upgrade: !isVercel,
    rememberUpgrade: false,
    pingTimeout: 60000,
    pingInterval: 25000
  }
}

/**
 * Handle Socket.io errors gracefully
 */
export function handleSocketError(error: any): { message: string; shouldRetry: boolean; fallback: boolean } {
  const errorMessage = error?.message || error || 'Unknown error'

  if (errorMessage.includes('timeout')) {
    return {
      message: 'Connection timeout - using fallback mode',
      shouldRetry: true,
      fallback: true
    }
  }

  if (errorMessage.includes('ECONNREFUSED')) {
    return {
      message: 'Server unavailable - using offline mode',
      shouldRetry: false,
      fallback: true
    }
  }

  if (errorMessage.includes('CORS')) {
    return {
      message: 'Connection blocked - using fallback mode',
      shouldRetry: false,
      fallback: true
    }
  }

  if (errorMessage.includes('websocket')) {
    return {
      message: 'WebSocket failed - trying alternative connection',
      shouldRetry: true,
      fallback: false
    }
  }

  return {
    message: 'Connection failed - using fallback mode',
    shouldRetry: false,
    fallback: true
  }
}

/**
 * Log environment information for debugging
 */
export function logEnvironmentInfo(): void {
  console.log('Environment Info:', {
    isVercel: isVercelEnvironment(),
    webSocketSupported: isWebSocketSupported(),
    connectionUrl: getConnectionUrl(),
    strategy: getConnectionStrategy(),
    shouldDisable: shouldDisableSocketIO(),
    nodeEnv: process.env.NODE_ENV,
    vercelEnv: process.env.VERCEL,
    socketUrl: process.env.NEXT_PUBLIC_SOCKET_URL
  })
}
