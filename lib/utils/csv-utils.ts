// Utility functions for CSV handling

/**
 * Convert array of objects to CSV string
 */
export function arrayToCSV(data: any[]): string {
  if (data.length === 0) return ''

  const headers = Object.keys(data[0])
  const csvHeaders = headers.join(',')
  
  const csvRows = data.map(item => 
    headers.map(header => {
      const value = item[header]
      // Escape values that contain commas, quotes, or newlines
      if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`
      }
      return value ?? ''
    }).join(',')
  )

  return [csvHeaders, ...csvRows].join('\n')
}

/**
 * Parse CSV string to array of objects
 */
export function csvToArray(csvString: string): any[] {
  const lines = csvString.trim().split('\n')
  if (lines.length < 2) return []

  const headers = lines[0].split(',').map(h => h.trim())
  const data = []

  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i])
    if (values.length === headers.length) {
      const obj: any = {}
      headers.forEach((header, index) => {
        obj[header] = values[index]?.trim() || ''
      })
      data.push(obj)
    }
  }

  return data
}

/**
 * Parse a single CSV line handling quoted values
 */
function parseCSVLine(line: string): string[] {
  const result = []
  let current = ''
  let inQuotes = false
  let i = 0

  while (i < line.length) {
    const char = line[i]
    const nextChar = line[i + 1]

    if (char === '"') {
      if (inQuotes && nextChar === '"') {
        // Escaped quote
        current += '"'
        i += 2
      } else {
        // Toggle quote state
        inQuotes = !inQuotes
        i++
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      result.push(current)
      current = ''
      i++
    } else {
      current += char
      i++
    }
  }

  // Add the last field
  result.push(current)
  return result
}

/**
 * Download data as CSV file
 */
export function downloadCSV(data: any[], filename: string): void {
  const csvContent = arrayToCSV(data)
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

/**
 * Download data as JSON file
 */
export function downloadJSON(data: any[], filename: string): void {
  const jsonContent = JSON.stringify(data, null, 2)
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

/**
 * Validate CSV file structure
 */
export function validateCSVStructure(csvString: string, requiredFields: string[]): {
  valid: boolean
  errors: string[]
  headers: string[]
} {
  const errors: string[] = []
  const lines = csvString.trim().split('\n')
  
  if (lines.length < 1) {
    return {
      valid: false,
      errors: ['CSV file is empty'],
      headers: []
    }
  }

  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''))
  
  // Check for required fields
  const missingFields = requiredFields.filter(field => !headers.includes(field))
  if (missingFields.length > 0) {
    errors.push(`Missing required fields: ${missingFields.join(', ')}`)
  }

  // Check for duplicate headers
  const duplicateHeaders = headers.filter((header, index) => headers.indexOf(header) !== index)
  if (duplicateHeaders.length > 0) {
    errors.push(`Duplicate headers found: ${duplicateHeaders.join(', ')}`)
  }

  // Check if there's data
  if (lines.length < 2) {
    errors.push('CSV file contains headers but no data rows')
  }

  return {
    valid: errors.length === 0,
    errors,
    headers
  }
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase()
}

/**
 * Check if file is valid for import
 */
export function isValidImportFile(file: File): {
  valid: boolean
  error?: string
} {
  const validExtensions = ['csv', 'json']
  const maxSize = 10 * 1024 * 1024 // 10MB
  
  const extension = getFileExtension(file.name)
  
  if (!validExtensions.includes(extension)) {
    return {
      valid: false,
      error: `Invalid file type. Please use: ${validExtensions.join(', ')}`
    }
  }
  
  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File size too large. Maximum size is ${formatFileSize(maxSize)}`
    }
  }
  
  return { valid: true }
}
