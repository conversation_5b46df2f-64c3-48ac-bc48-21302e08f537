// lib/utils/gridfs.ts - GridFS Utilities for File Storage

import mongoose from 'mongoose'
import { GridFSBucket, GridFSBucketReadStream, GridFSBucketWriteStream, GridFSFile } from 'mongodb'
import { Readable } from 'stream'

let bucket: GridFSBucket | null = null

/**
 * Initialize GridFS bucket
 */
export function initGridFS(): GridFSBucket {
  if (!bucket) {
    if (!mongoose.connection.db) {
      throw new Error('MongoDB connection not established')
    }
    bucket = new GridFSBucket(mongoose.connection.db, {
      bucketName: 'images'
    })
  }
  return bucket
}

/**
 * Upload file to GridFS
 */
export async function uploadToGridFS(
  file: File,
  filename: string,
  metadata?: Record<string, any>
): Promise<{ fileId: string; filename: string; url: string }> {
  const gridfs = initGridFS()
  
  return new Promise(async (resolve, reject) => {
    try {
      const fileId = new mongoose.Types.ObjectId()
      const uploadStream: GridFSBucketWriteStream = gridfs.openUploadStreamWithId(
        fileId,
        filename,
        {
          metadata: {
            ...metadata,
            originalName: file.name,
            mimeType: file.type,
            size: file.size,
            uploadedAt: new Date()
          }
        }
      )

      // Convert File to stream
      const arrayBuffer = await file.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      const readable = Readable.from(buffer)

      uploadStream.on('error', (error: Error) => {
        reject(error)
      })

      uploadStream.on('finish', () => {
        resolve({
          fileId: fileId.toString(),
          filename,
          url: `/api/images/${fileId.toString()}`
        })
      })

      readable.pipe(uploadStream)
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * Download file from GridFS
 */
export function downloadFromGridFS(fileId: string): GridFSBucketReadStream {
  const gridfs = initGridFS()
  const objectId = new mongoose.Types.ObjectId(fileId)
  return gridfs.openDownloadStream(objectId)
}

/**
 * Delete file from GridFS
 */
export async function deleteFromGridFS(fileId: string): Promise<void> {
  const gridfs = initGridFS()
  const objectId = new mongoose.Types.ObjectId(fileId)
  await gridfs.delete(objectId)
}

/**
 * Get file info from GridFS
 */
export async function getFileInfo(fileId: string): Promise<GridFSFile> {
  const gridfs = initGridFS()
  const objectId = new mongoose.Types.ObjectId(fileId)

  try {
    const cursor = gridfs.find({ _id: objectId })
    const files = await cursor.toArray()

    if (!files || files.length === 0) {
      throw new Error('File not found')
    }

    return files[0]
  } catch (error) {
    throw error
  }
}

/**
 * List files in GridFS
 */
export async function listFiles(filter: Record<string, any> = {}): Promise<GridFSFile[]> {
  const gridfs = initGridFS()

  try {
    const cursor = gridfs.find(filter)
    const files = await cursor.toArray()
    return files || []
  } catch (error) {
    throw error
  }
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const allowedTypes = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/webp',
    'image/gif', 'image/bmp', 'image/tiff', 'image/svg+xml',
    'image/avif', 'image/heic', 'image/heif'
  ]
  const maxSize = 10 * 1024 * 1024 // 10MB

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `Invalid file type. Allowed types: JPG, PNG, WEBP, GIF, BMP, TIFF, SVG, AVIF, HEIC, HEIF`
    }
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File size too large. Maximum size is ${maxSize / (1024 * 1024)}MB`
    }
  }

  return { valid: true }
}

/**
 * Generate unique filename
 */
export function generateUniqueFilename(originalName: string, prefix?: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  const extension = originalName.split('.').pop()
  const baseName = originalName.split('.').slice(0, -1).join('.')
  
  const cleanBaseName = baseName.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()
  const prefixPart = prefix ? `${prefix}_` : ''
  
  return `${prefixPart}${cleanBaseName}_${timestamp}_${random}.${extension}`
}

/**
 * Resize image (placeholder for future implementation)
 * This would require a library like sharp for server-side image processing
 */
export function resizeImage(
  buffer: Buffer,
  _width: number,
  _height: number
): Promise<Buffer> {
  // TODO: Implement image resizing with sharp or similar library
  // For now, return the original buffer
  return Promise.resolve(buffer)
}
