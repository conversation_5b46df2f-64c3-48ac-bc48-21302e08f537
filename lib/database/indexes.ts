import mongoose from 'mongoose'
import User from './models/User'
import Shop from './models/Shop'
import Product from './models/Product'
import Order from './models/Order'
import Customer from './models/Customer'
import Employee from './models/Employee'
import Inventory from './models/Inventory'
import ActivityLog from './models/ActivityLog'

/**
 * Create additional database indexes for performance optimization
 * This function should be called after database connection is established
 */
export async function createDatabaseIndexes(): Promise<void> {
  try {
    console.log('Creating database indexes...')

    // User indexes
    await User.collection.createIndex({ email: 1 }, { unique: true })
    await User.collection.createIndex({ username: 1 }, { unique: true })
    await User.collection.createIndex({ branchId: 1 })
    await User.collection.createIndex({ role: 1 })
    await User.collection.createIndex({ isActive: 1 })
    await User.collection.createIndex({ lastLogin: -1 })

    // Shop indexes
    await Shop.collection.createIndex({ managerId: 1 }, { unique: true })
    await Shop.collection.createIndex({ location: 1 })
    await Shop.collection.createIndex({ country: 1, region: 1 })
    await Shop.collection.createIndex({ status: 1 })
    await Shop.collection.createIndex({ totalSales: -1 })
    await Shop.collection.createIndex({ 'coordinates.lat': 1, 'coordinates.lng': 1 })

    // Product indexes
    await Product.collection.createIndex({ sku: 1 }, { unique: true })
    await Product.collection.createIndex({ branchId: 1, category: 1 })
    await Product.collection.createIndex({ branchId: 1, status: 1 })
    await Product.collection.createIndex({ category: 1, status: 1 })
    await Product.collection.createIndex({ brand: 1 })
    await Product.collection.createIndex({ price: 1 })
    await Product.collection.createIndex({ isActive: 1, isFeatured: 1 })
    await Product.collection.createIndex({ stock: 1 })
    await Product.collection.createIndex({ minStockLevel: 1 })

    // Order indexes
    await Order.collection.createIndex({ orderNumber: 1 }, { unique: true })
    await Order.collection.createIndex({ customerId: 1, createdAt: -1 })
    await Order.collection.createIndex({ branchId: 1, createdAt: -1 })
    await Order.collection.createIndex({ status: 1, createdAt: -1 })
    await Order.collection.createIndex({ paymentStatus: 1 })
    await Order.collection.createIndex({ total: -1 })
    await Order.collection.createIndex({ estimatedDelivery: 1 })

    // Customer indexes
    await Customer.collection.createIndex({ email: 1 }, { unique: true })
    await Customer.collection.createIndex({ phone: 1 })
    await Customer.collection.createIndex({ preferredBranch: 1 })
    await Customer.collection.createIndex({ totalSpent: -1 })
    await Customer.collection.createIndex({ loyaltyPoints: -1 })
    await Customer.collection.createIndex({ lastOrderDate: -1 })
    await Customer.collection.createIndex({ isActive: 1, totalSpent: -1 })

    // Employee indexes
    await Employee.collection.createIndex({ email: 1 }, { unique: true })
    await Employee.collection.createIndex({ branchId: 1, role: 1 })
    await Employee.collection.createIndex({ managerId: 1 })
    await Employee.collection.createIndex({ department: 1 })
    await Employee.collection.createIndex({ status: 1 })
    await Employee.collection.createIndex({ hireDate: -1 })
    await Employee.collection.createIndex({ salary: -1 })

    // Inventory indexes
    await Inventory.collection.createIndex({ productId: 1, branchId: 1 }, { unique: true })
    await Inventory.collection.createIndex({ sku: 1 })
    await Inventory.collection.createIndex({ branchId: 1, status: 1 })
    await Inventory.collection.createIndex({ status: 1, stock: 1 })
    await Inventory.collection.createIndex({ lastRestocked: -1 })
    await Inventory.collection.createIndex({ supplier: 1 })

    // Activity Log indexes
    await ActivityLog.collection.createIndex({ type: 1, timestamp: -1 })
    await ActivityLog.collection.createIndex({ userId: 1, timestamp: -1 })
    await ActivityLog.collection.createIndex({ branchId: 1, timestamp: -1 })
    await ActivityLog.collection.createIndex({ timestamp: -1 })

    // Compound indexes for complex queries
    await Product.collection.createIndex({ 
      branchId: 1, 
      category: 1, 
      status: 1, 
      isActive: 1 
    })
    
    await Order.collection.createIndex({ 
      branchId: 1, 
      status: 1, 
      paymentStatus: 1 
    })
    
    await Customer.collection.createIndex({ 
      preferredBranch: 1, 
      isActive: 1, 
      totalSpent: -1 
    })

    // Text indexes for search functionality
    await Product.collection.createIndex({
      name: 'text',
      description: 'text',
      brand: 'text',
      model: 'text',
      tags: 'text'
    }, { name: 'product_text_search' })

    await Customer.collection.createIndex({
      firstName: 'text',
      lastName: 'text',
      email: 'text'
    }, { name: 'customer_text_search' })

    await Employee.collection.createIndex({
      firstName: 'text',
      lastName: 'text',
      email: 'text'
    }, { name: 'employee_text_search' })

    await ActivityLog.collection.createIndex({
      description: 'text',
      userName: 'text'
    }, { name: 'activity_text_search' })

    console.log('Database indexes created successfully')
  } catch (error) {
    console.error('Error creating database indexes:', error)
    throw error
  }
}

/**
 * Drop all custom indexes (useful for development/testing)
 */
export async function dropDatabaseIndexes(): Promise<void> {
  try {
    console.log('Dropping custom database indexes...')

    const collections = [
      User.collection,
      Shop.collection,
      Product.collection,
      Order.collection,
      Customer.collection,
      Employee.collection,
      Inventory.collection,
      ActivityLog.collection
    ]

    for (const collection of collections) {
      const indexes = await collection.indexes()
      for (const index of indexes) {
        // Don't drop the default _id index
        if (index.name !== '_id_') {
          try {
            await collection.dropIndex(index.name)
          } catch (error) {
            // Index might not exist, continue
            console.warn(`Could not drop index ${index.name}:`, error)
          }
        }
      }
    }

    console.log('Custom database indexes dropped successfully')
  } catch (error) {
    console.error('Error dropping database indexes:', error)
    throw error
  }
}

/**
 * Get index statistics for all collections
 */
export async function getIndexStats(): Promise<Record<string, any>> {
  try {
    const stats: Record<string, any> = {}

    const collections = [
      { name: 'users', collection: User.collection },
      { name: 'shops', collection: Shop.collection },
      { name: 'products', collection: Product.collection },
      { name: 'orders', collection: Order.collection },
      { name: 'customers', collection: Customer.collection },
      { name: 'employees', collection: Employee.collection },
      { name: 'inventories', collection: Inventory.collection },
      { name: 'activitylogs', collection: ActivityLog.collection }
    ]

    for (const { name, collection } of collections) {
      stats[name] = {
        indexes: await collection.indexes(),
        stats: await collection.stats()
      }
    }

    return stats
  } catch (error) {
    console.error('Error getting index statistics:', error)
    throw error
  }
}
