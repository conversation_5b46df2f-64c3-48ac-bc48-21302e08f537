// lib/database/seed.ts - Database seeding for initial data

import { connectDB, User, Shop, Employee } from '@/lib/database'
import bcrypt from 'bcryptjs'

/**
 * Seed the database with initial admin user, sample shops, and sample employees
 */
export async function seedDatabase() {
  try {
    await connectDB()
    console.log('🌱 Starting database seeding...')

    // Check if admin users already exist
    const existingAdmins = await User.find({
      email: { $in: ['<EMAIL>', '<EMAIL>'] }
    })

    const existingEmails = existingAdmins.map(admin => admin.email)

    // Create Winston admin if doesn't exist
    if (!existingEmails.includes('<EMAIL>')) {
      const winstonAdmin = await User.create({
        username: 'winston_admin',
        email: '<EMAIL>',
        password: '@Admin2020', // Will be hashed by the pre-save middleware
        name: '<PERSON>',
        role: 'overall_admin',
        isActive: true,
        emailVerified: true
      })

      console.log('✅ Winston admin user created:', {
        id: winstonAdmin._id,
        username: winstonAdmin.username,
        email: winstonAdmin.email,
        name: winstonAdmin.name,
        role: winstonAdmin.role
      })
    } else {
      console.log('✅ Winston admin user already exists')
    }

    // Create FathahiTech admin if doesn't exist
    if (!existingEmails.includes('<EMAIL>')) {
      const fathahitechAdmin = await User.create({
        username: 'fathahitech_admin',
        email: '<EMAIL>',
        password: 'FathahitechAdmin@2025', // Will be hashed by the pre-save middleware
        name: 'FathahiTech Administrator',
        role: 'overall_admin',
        isActive: true,
        emailVerified: true
      })

      console.log('✅ FathahiTech admin user created:', {
        id: fathahitechAdmin._id,
        username: fathahitechAdmin.username,
        email: fathahitechAdmin.email,
        name: fathahitechAdmin.name,
        role: fathahitechAdmin.role
      })
    } else {
      console.log('✅ FathahiTech admin user already exists')
    }

    // Get admin user for shop management
    const adminUser = await User.findOne({ email: '<EMAIL>' })
    if (!adminUser) {
      throw new Error('Admin user not found. Cannot create shops.')
    }

    // Check if shops already exist
    const existingShops = await Shop.countDocuments()
    if (existingShops > 0) {
      console.log('✅ Shops already exist, skipping shop creation')
    } else {
      // Create sample shops/branches
      const shops = [
        {
          name: 'FathahiTech Blantyre',
          location: 'Blantyre',
          country: 'Malawi',
          region: 'Southern',
          manager: adminUser.name,
          managerId: adminUser._id.toString(),
          description: 'Main branch in Blantyre offering comprehensive tech solutions',
          address: 'Ginnery Corner, Blantyre, Malawi',
          phone: '+265997358331',
          email: '<EMAIL>',
          coordinates: {
            lat: -15.7861,
            lng: 35.0058
          },
          operatingHours: {
            open: '08:00',
            close: '18:00',
            timezone: 'Africa/Blantyre'
          },
          image: '/images/shops/blantyre.jpg',
          totalProducts: 0,
          totalSales: 0,
          status: 'Active' as const
        },
        {
          name: 'FathahiTech Lilongwe',
          location: 'Lilongwe',
          country: 'Malawi',
          region: 'Central',
          manager: adminUser.name,
          managerId: adminUser._id.toString(),
          description: 'Capital city branch providing cutting-edge technology services',
          address: 'City Centre, Lilongwe, Malawi',
          phone: '+265997358332',
          email: '<EMAIL>',
          coordinates: {
            lat: -13.9626,
            lng: 33.7741
          },
          operatingHours: {
            open: '08:00',
            close: '18:00',
            timezone: 'Africa/Blantyre'
          },
          image: '/images/shops/lilongwe.jpg',
          totalProducts: 0,
          totalSales: 0,
          status: 'Active' as const
        },
        {
          name: 'FathahiTech Mzuzu',
          location: 'Mzuzu',
          country: 'Malawi',
          region: 'Northern',
          manager: adminUser.name,
          managerId: adminUser._id.toString(),
          description: 'Northern region branch serving the tech needs of Mzuzu and surrounding areas',
          address: 'Mzuzu City Centre, Mzuzu, Malawi',
          phone: '+265997358333',
          email: '<EMAIL>',
          coordinates: {
            lat: -11.4607,
            lng: 34.0158
          },
          operatingHours: {
            open: '08:00',
            close: '18:00',
            timezone: 'Africa/Blantyre'
          },
          image: '/images/shops/mzuzu.jpg',
          totalProducts: 0,
          totalSales: 0,
          status: 'Active' as const
        },
        {
          name: 'FathahiTech Lusaka',
          location: 'Lusaka',
          country: 'Zambia',
          region: 'Lusaka',
          manager: adminUser.name,
          managerId: adminUser._id.toString(),
          description: 'Zambian headquarters providing tech solutions across Lusaka',
          address: 'Cairo Road, Lusaka, Zambia',
          phone: '+260977358331',
          email: '<EMAIL>',
          coordinates: {
            lat: -15.3875,
            lng: 28.3228
          },
          operatingHours: {
            open: '08:00',
            close: '18:00',
            timezone: 'Africa/Lusaka'
          },
          image: '/images/shops/lusaka.jpg',
          totalProducts: 0,
          totalSales: 0,
          status: 'Active' as const
        }
      ]

      const createdShops = await Shop.insertMany(shops)
      console.log(`✅ Created ${createdShops.length} sample shops:`)
      createdShops.forEach(shop => {
        console.log(`   - ${shop.name} (${shop._id})`)
      })

      // Create sample employees for each shop
      await seedSampleEmployees(createdShops)
    }

    console.log('🎉 Database seeding completed successfully!')
    return { success: true }

  } catch (error) {
    console.error('❌ Database seeding failed:', error)
    return { success: false, error }
  }
}

/**
 * Seed sample employees for the shops
 */
async function seedSampleEmployees(shops: any[]) {
  try {
    // Check if employees already exist
    const existingEmployees = await Employee.countDocuments()
    if (existingEmployees > 0) {
      console.log('✅ Employees already exist, skipping employee creation')
      return
    }

    const sampleEmployees = []

    // Create employees for each shop
    for (const shop of shops) {
      // Branch Manager for each shop
      sampleEmployees.push({
        employeeId: `EMP${String(sampleEmployees.length + 1).padStart(4, '0')}`,
        firstName: getBranchManagerFirstName(shop.location),
        lastName: getBranchManagerLastName(shop.location),
        email: `manager.${shop.location.toLowerCase()}@fathahitech.com`,
        phone: getBranchManagerPhone(shop.location),
        position: 'Branch Manager',
        department: 'Management',
        branchId: shop._id.toString(),
        branchName: shop.name,
        hireDate: new Date('2023-01-15'),
        salary: 120000,
        address: {
          street: `Manager Residence Street`,
          city: shop.location,
          region: shop.region,
          country: shop.country,
          postalCode: getPostalCode(shop.location)
        },
        emergencyContact: {
          name: `${getBranchManagerFirstName(shop.location)} Emergency Contact`,
          relationship: 'spouse',
          phone: getEmergencyPhone(shop.location)
        },
        isActive: true
      })

      // Sales Representatives
      sampleEmployees.push({
        employeeId: `EMP${String(sampleEmployees.length + 1).padStart(4, '0')}`,
        firstName: getSalesRepFirstName(shop.location, 1),
        lastName: getSalesRepLastName(shop.location, 1),
        email: `sales1.${shop.location.toLowerCase()}@fathahitech.com`,
        phone: getSalesRepPhone(shop.location, 1),
        position: 'Sales Representative',
        department: 'Sales',
        branchId: shop._id.toString(),
        branchName: shop.name,
        hireDate: new Date('2023-03-01'),
        salary: 65000,
        address: {
          street: `Sales Rep 1 Street`,
          city: shop.location,
          region: shop.region,
          country: shop.country,
          postalCode: getPostalCode(shop.location)
        },
        emergencyContact: {
          name: `${getSalesRepFirstName(shop.location, 1)} Emergency`,
          relationship: 'parent',
          phone: getEmergencyPhone(shop.location, 1)
        },
        isActive: true
      })

      sampleEmployees.push({
        employeeId: `EMP${String(sampleEmployees.length + 1).padStart(4, '0')}`,
        firstName: getSalesRepFirstName(shop.location, 2),
        lastName: getSalesRepLastName(shop.location, 2),
        email: `sales2.${shop.location.toLowerCase()}@fathahitech.com`,
        phone: getSalesRepPhone(shop.location, 2),
        position: 'Sales Representative',
        department: 'Sales',
        branchId: shop._id.toString(),
        branchName: shop.name,
        hireDate: new Date('2023-04-15'),
        salary: 62000,
        address: {
          street: `Sales Rep 2 Street`,
          city: shop.location,
          region: shop.region,
          country: shop.country,
          postalCode: getPostalCode(shop.location)
        },
        emergencyContact: {
          name: `${getSalesRepFirstName(shop.location, 2)} Emergency`,
          relationship: 'sibling',
          phone: getEmergencyPhone(shop.location, 2)
        },
        isActive: true
      })

      // Cashier
      sampleEmployees.push({
        employeeId: `EMP${String(sampleEmployees.length + 1).padStart(4, '0')}`,
        firstName: getCashierFirstName(shop.location),
        lastName: getCashierLastName(shop.location),
        email: `cashier.${shop.location.toLowerCase()}@fathahitech.com`,
        phone: getCashierPhone(shop.location),
        position: 'Cashier',
        department: 'Finance',
        branchId: shop._id.toString(),
        branchName: shop.name,
        hireDate: new Date('2023-05-01'),
        salary: 55000,
        address: {
          street: `Cashier Street`,
          city: shop.location,
          region: shop.region,
          country: shop.country,
          postalCode: getPostalCode(shop.location)
        },
        emergencyContact: {
          name: `${getCashierFirstName(shop.location)} Emergency`,
          relationship: 'parent',
          phone: getEmergencyPhone(shop.location, 3)
        },
        isActive: true
      })

      // Inventory Manager
      sampleEmployees.push({
        employeeId: `EMP${String(sampleEmployees.length + 1).padStart(4, '0')}`,
        firstName: getInventoryManagerFirstName(shop.location),
        lastName: getInventoryManagerLastName(shop.location),
        email: `inventory.${shop.location.toLowerCase()}@fathahitech.com`,
        phone: getInventoryManagerPhone(shop.location),
        position: 'Inventory Manager',
        department: 'Operations',
        branchId: shop._id.toString(),
        branchName: shop.name,
        hireDate: new Date('2023-02-15'),
        salary: 75000,
        address: {
          street: `Inventory Manager Street`,
          city: shop.location,
          region: shop.region,
          country: shop.country,
          postalCode: getPostalCode(shop.location)
        },
        emergencyContact: {
          name: `${getInventoryManagerFirstName(shop.location)} Emergency`,
          relationship: 'spouse',
          phone: getEmergencyPhone(shop.location, 4)
        },
        isActive: true
      })

      // General Employee
      sampleEmployees.push({
        employeeId: `EMP${String(sampleEmployees.length + 1).padStart(4, '0')}`,
        firstName: getGeneralEmployeeFirstName(shop.location),
        lastName: getGeneralEmployeeLastName(shop.location),
        email: `employee.${shop.location.toLowerCase()}@fathahitech.com`,
        phone: getGeneralEmployeePhone(shop.location),
        position: 'Customer Service Representative',
        department: 'Customer Service',
        branchId: shop._id.toString(),
        branchName: shop.name,
        hireDate: new Date('2023-06-01'),
        salary: 50000,
        address: {
          street: `Employee Street`,
          city: shop.location,
          region: shop.region,
          country: shop.country,
          postalCode: getPostalCode(shop.location)
        },
        emergencyContact: {
          name: `${getGeneralEmployeeFirstName(shop.location)} Emergency`,
          relationship: 'parent',
          phone: getEmergencyPhone(shop.location, 5)
        },
        isActive: true
      })
    }

    const createdEmployees = await Employee.insertMany(sampleEmployees)
    console.log(`✅ Created ${createdEmployees.length} sample employees across all branches`)

  } catch (error) {
    console.error('❌ Failed to create sample employees:', error)
    throw error
  }
}

// Helper functions for generating employee data
function getBranchManagerFirstName(location: string): string {
  const names: { [key: string]: string } = {
    'Blantyre': 'James',
    'Lilongwe': 'Grace',
    'Mzuzu': 'Peter',
    'Lusaka': 'Mary'
  }
  return names[location] || 'Manager'
}

function getBranchManagerLastName(location: string): string {
  const names: { [key: string]: string } = {
    'Blantyre': 'Banda',
    'Lilongwe': 'Phiri',
    'Mzuzu': 'Mwale',
    'Lusaka': 'Tembo'
  }
  return names[location] || 'Manager'
}

function getBranchManagerPhone(location: string): string {
  const phones: { [key: string]: string } = {
    'Blantyre': '+265999123001',
    'Lilongwe': '+265999123002',
    'Mzuzu': '+265999123003',
    'Lusaka': '+260977123001'
  }
  return phones[location] || '+265999123000'
}

function getSalesRepFirstName(location: string, index: number): string {
  const names: { [key: string]: string[] } = {
    'Blantyre': ['Alice', 'John'],
    'Lilongwe': ['Sarah', 'Michael'],
    'Mzuzu': ['Faith', 'David'],
    'Lusaka': ['Joyce', 'Patrick']
  }
  return names[location]?.[index - 1] || `Sales${index}`
}

function getSalesRepLastName(location: string, index: number): string {
  const names: { [key: string]: string[] } = {
    'Blantyre': ['Chirwa', 'Kachale'],
    'Lilongwe': ['Mvula', 'Gondwe'],
    'Mzuzu': ['Nyirenda', 'Sakala'],
    'Lusaka': ['Mulenga', 'Zulu']
  }
  return names[location]?.[index - 1] || `Rep${index}`
}

function getSalesRepPhone(location: string, index: number): string {
  const basePhones: { [key: string]: string } = {
    'Blantyre': '+265999124',
    'Lilongwe': '+265999125',
    'Mzuzu': '+265999126',
    'Lusaka': '+260977124'
  }
  return `${basePhones[location] || '+265999124'}00${index}`
}

function getCashierFirstName(location: string): string {
  const names: { [key: string]: string } = {
    'Blantyre': 'Mercy',
    'Lilongwe': 'Emmanuel',
    'Mzuzu': 'Esther',
    'Lusaka': 'Joseph'
  }
  return names[location] || 'Cashier'
}

function getCashierLastName(location: string): string {
  const names: { [key: string]: string } = {
    'Blantyre': 'Mbewe',
    'Lilongwe': 'Chisale',
    'Mzuzu': 'Munthali',
    'Lusaka': 'Banda'
  }
  return names[location] || 'Cashier'
}

function getCashierPhone(location: string): string {
  const phones: { [key: string]: string } = {
    'Blantyre': '+265999127001',
    'Lilongwe': '+265999127002',
    'Mzuzu': '+265999127003',
    'Lusaka': '+260977127001'
  }
  return phones[location] || '+265999127000'
}

function getInventoryManagerFirstName(location: string): string {
  const names: { [key: string]: string } = {
    'Blantyre': 'Robert',
    'Lilongwe': 'Beatrice',
    'Mzuzu': 'Francis',
    'Lusaka': 'Catherine'
  }
  return names[location] || 'Inventory'
}

function getInventoryManagerLastName(location: string): string {
  const names: { [key: string]: string } = {
    'Blantyre': 'Msiska',
    'Lilongwe': 'Kamanga',
    'Mzuzu': 'Jere',
    'Lusaka': 'Phiri'
  }
  return names[location] || 'Manager'
}

function getInventoryManagerPhone(location: string): string {
  const phones: { [key: string]: string } = {
    'Blantyre': '+265999128001',
    'Lilongwe': '+265999128002',
    'Mzuzu': '+265999128003',
    'Lusaka': '+260977128001'
  }
  return phones[location] || '+265999128000'
}

function getGeneralEmployeeFirstName(location: string): string {
  const names: { [key: string]: string } = {
    'Blantyre': 'Precious',
    'Lilongwe': 'Daniel',
    'Mzuzu': 'Ruth',
    'Lusaka': 'Samuel'
  }
  return names[location] || 'Employee'
}

function getGeneralEmployeeLastName(location: string): string {
  const names: { [key: string]: string } = {
    'Blantyre': 'Chikwawa',
    'Lilongwe': 'Mwanza',
    'Mzuzu': 'Zimba',
    'Lusaka': 'Mwila'
  }
  return names[location] || 'Employee'
}

function getGeneralEmployeePhone(location: string): string {
  const phones: { [key: string]: string } = {
    'Blantyre': '+265999129001',
    'Lilongwe': '+265999129002',
    'Mzuzu': '+265999129003',
    'Lusaka': '+260977129001'
  }
  return phones[location] || '+265999129000'
}

function getEmergencyPhone(location: string, index: number = 0): string {
  const basePhones: { [key: string]: string } = {
    'Blantyre': '+265888',
    'Lilongwe': '+265888',
    'Mzuzu': '+265888',
    'Lusaka': '+260966'
  }
  return `${basePhones[location] || '+265888'}${String(index).padStart(3, '0')}${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`
}

function getPostalCode(location: string): string {
  const postalCodes: { [key: string]: string } = {
    'Blantyre': 'BT101',
    'Lilongwe': 'LL201',
    'Mzuzu': 'MZ301',
    'Lusaka': 'LK401'
  }
  return postalCodes[location] || 'PC001'
}

/**
 * Clear all data from the database (use with caution!)
 */
export async function clearDatabase() {
  try {
    await connectDB()
    console.log('🧹 Clearing database...')

    await User.deleteMany({})
    await Shop.deleteMany({})
    // Add other models as needed

    console.log('✅ Database cleared successfully!')
    return { success: true }

  } catch (error) {
    console.error('❌ Database clearing failed:', error)
    return { success: false, error }
  }
}

/**
 * Reset database (clear and reseed)
 */
export async function resetDatabase() {
  try {
    console.log('🔄 Resetting database...')
    
    const clearResult = await clearDatabase()
    if (!clearResult.success) {
      throw new Error('Failed to clear database')
    }

    const seedResult = await seedDatabase()
    if (!seedResult.success) {
      throw new Error('Failed to seed database')
    }

    console.log('🎉 Database reset completed successfully!')
    return { success: true }

  } catch (error) {
    console.error('❌ Database reset failed:', error)
    return { success: false, error }
  }
}
