import Product from './models/Product'
import Inventory from './models/Inventory'
import ActivityLog from './models/ActivityLog'
import type { IOrderItem } from './models/Order'

/**
 * Generate a unique order number
 */
export function generateOrderNumber(): string {
  const timestamp = Date.now().toString()
  const random = Math.random().toString(36).substring(2, 8).toUpperCase()
  return `ORD-${timestamp.slice(-8)}-${random}`
}

/**
 * Generate a unique SKU for a product
 */
export function generateSKU(category: string, brand: string): string {
  const categoryCode = category.substring(0, 3).toUpperCase()
  const brandCode = brand.substring(0, 3).toUpperCase()
  const timestamp = Date.now().toString().slice(-6)
  const random = Math.random().toString(36).substring(2, 4).toUpperCase()
  
  return `${categoryCode}-${brandCode}-${timestamp}-${random}`
}

/**
 * Calculate order total including tax and shipping
 */
export function calculateOrderTotal(
  items: IOrderItem[],
  taxRate: number = 0.18, // 18% VAT for Malawi
  shippingCost: number = 0,
  discountAmount: number = 0
): {
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
} {
  const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0)
  const tax = subtotal * taxRate
  const shipping = shippingCost
  const discount = discountAmount
  const total = subtotal + tax + shipping - discount

  return {
    subtotal: Math.round(subtotal * 100) / 100,
    tax: Math.round(tax * 100) / 100,
    shipping: Math.round(shipping * 100) / 100,
    discount: Math.round(discount * 100) / 100,
    total: Math.round(total * 100) / 100
  }
}

/**
 * Update product stock levels across product and inventory collections
 */
export async function updateProductStock(
  productId: string,
  branchId: string,
  quantityChange: number, // Positive for restock, negative for sale
  userId: string,
  userName: string
): Promise<void> {
  try {
    // Update product stock
    const product = await Product.findById(productId)
    if (!product) {
      throw new Error('Product not found')
    }

    const newStock = product.stock + quantityChange
    if (newStock < 0) {
      throw new Error('Insufficient stock')
    }

    product.stock = newStock
    await product.save()

    // Update inventory stock
    const inventory = await Inventory.findOne({ productId, branchId })
    if (inventory) {
      inventory.stock = newStock
      if (quantityChange > 0) {
        inventory.lastRestocked = new Date()
      }
      await inventory.save()
    }

    // Log the activity
    await logActivity({
      type: 'Inventory',
      description: `Stock ${quantityChange > 0 ? 'increased' : 'decreased'} by ${Math.abs(quantityChange)} for ${product.name}`,
      userId,
      userName,
      branchId,
      metadata: {
        productId,
        productName: product.name,
        sku: product.sku,
        quantityChange,
        newStock
      }
    })
  } catch (error) {
    console.error('Error updating product stock:', error)
    throw error
  }
}

/**
 * Log an activity to the activity log
 */
export async function logActivity(data: {
  type: 'Order' | 'Inventory' | 'Sale' | 'Delivery' | 'Product' | 'User'
  description: string
  userId: string
  userName: string
  branchId?: string
  branchName?: string
  metadata?: Record<string, any>
}): Promise<void> {
  try {
    await ActivityLog.create({
      ...data,
      timestamp: new Date()
    })
  } catch (error) {
    console.error('Error logging activity:', error)
    // Don't throw error for logging failures to avoid breaking main operations
  }
}

/**
 * Generate a random password for new users
 */
export function generateRandomPassword(length: number = 12): string {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
  let password = ''
  
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length))
  }
  
  return password
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number, currency: string = 'MWK'): string {
  return new Intl.NumberFormat('en-MW', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount)
}

/**
 * Calculate distance between two coordinates (in kilometers)
 */
export function calculateDistance(
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number
): number {
  const R = 6371 // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/
  return emailRegex.test(email)
}

/**
 * Validate phone number format
 */
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone)
}

/**
 * Sanitize string for database storage
 */
export function sanitizeString(str: string): string {
  return str.trim().replace(/[<>]/g, '')
}
