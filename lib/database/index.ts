// Database connection
export { default as connectDB } from './connection'

// Models
export { default as User } from './models/User'
export { default as Shop } from './models/Shop'
export { default as Product } from './models/Product'
export { default as ProductCategory } from './models/ProductCategory'
export { default as Order } from './models/Order'
export { default as Customer } from './models/Customer'
export { default as Employee } from './models/Employee'
export { default as Inventory } from './models/Inventory'
export { default as Warehouse } from './models/Warehouse'
export { default as Shelf } from './models/Shelf'
export { default as ActivityLog } from './models/ActivityLog'

// Model interfaces
export type { IUser } from './models/User'
export type { IShop } from './models/Shop'
export type { IProduct } from './models/Product'
export type { IProductCategory } from './models/ProductCategory'
export type { IOrder, IOrderItem } from './models/Order'
export type { ICustomer } from './models/Customer'
export type { IEmployee } from './models/Employee'
export type { IInventory } from './models/Inventory'
export type { IWarehouse } from './models/Warehouse'
export type { IShelf } from './models/Shelf'
export type { IActivityLog } from './models/ActivityLog'

// Database utilities
export {
  createDatabaseIndexes,
  dropDatabaseIndexes,
  getIndexStats
} from './indexes'

export {
  generateOrderNumber,
  generateSKU,
  calculateOrderTotal,
  updateProductStock,
  logActivity
} from './utils'

// Re-export types from the main types file for convenience
export type {
  // User and Authentication Types
  UserRole,
  User as UserType,
  
  // Shop and Branch Types
  Shop as ShopType,
  
  // Product Types
  ProductStatus,
  ProductCategory,
  Product as ProductType,
  
  // Inventory Types
  InventoryItem,
  
  // Order Types
  OrderStatus,
  PaymentStatus,
  PaymentMethod,
  OrderItem,
  Order as OrderType,
  
  // Customer Types
  Customer as CustomerType,
  
  // Employee Types
  Employee as EmployeeType,
  
  // Analytics Types
  SalesMetrics,
  
  // Activity Log Types
  ActivityLog as ActivityLogType,
  
  // Form Data Types
  CreateProductCategoryData,
  CreateProductData,
  CreateShopData,
  CreateBranchData,
  CreateEmployeeData,
  CreateBranchManagerData,
  
  // Filter and Search Types
  ProductFilters,
  PaginationParams,
  
  // API Response Types
  ApiResponse
} from '@/types'
