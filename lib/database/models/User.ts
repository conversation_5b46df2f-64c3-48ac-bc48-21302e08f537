import mongoose, { Document, Schema } from 'mongoose'
import bcrypt from 'bcryptjs'
import type { UserRole } from '@/types'

export interface IUser extends Document {
  _id: string
  username: string
  email: string
  password: string
  role: UserRole
  branchId?: string
  name: string
  phone?: string
  avatar?: string
  isActive: boolean
  lastLogin?: Date
  passwordResetToken?: string
  passwordResetExpires?: Date
  refreshTokens: string[] // Store multiple refresh tokens for multiple devices
  sessionCount: number // Track active sessions
  accountLocked: boolean
  lockUntil?: Date
  loginAttempts: number
  twoFactorEnabled: boolean
  twoFactorSecret?: string
  emailVerified: boolean
  emailVerificationToken?: string
  emailVerificationExpires?: Date
  createdAt: Date
  updatedAt: Date

  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>
  generatePasswordResetToken(): string
  addRefreshToken(token: string): Promise<void>
  removeRefreshToken(token: string): Promise<void>
  clearAllRefreshTokens(): Promise<void>
  incrementLoginAttempts(): Promise<void>
  resetLoginAttempts(): Promise<void>
  lockAccount(): Promise<void>
  unlockAccount(): Promise<void>
}

const UserSchema = new Schema<IUser>({
  username: {
    type: String,
    required: [true, 'Username is required'],
    unique: true,
    trim: true,
    minlength: [3, 'Username must be at least 3 characters long'],
    maxlength: [30, 'Username cannot exceed 30 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters long'],
    select: false // Don't include password in queries by default
  },
  role: {
    type: String,
    enum: ['overall_admin', 'branch_manager'],
    required: [true, 'User role is required'],
    default: 'branch_manager'
  },
  branchId: {
    type: String,
    required: function(this: IUser) {
      return this.role === 'branch_manager'
    }
  },
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [50, 'Name cannot exceed 50 characters']
  },
  phone: {
    type: String,
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
  },
  avatar: {
    type: String,
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date,
    default: null
  },
  passwordResetToken: {
    type: String,
    default: null
  },
  passwordResetExpires: {
    type: Date,
    default: null
  },
  refreshTokens: [{
    type: String,
    select: false // Don't include in queries by default
  }],
  sessionCount: {
    type: Number,
    default: 0,
    min: 0
  },
  accountLocked: {
    type: Boolean,
    default: false
  },
  lockUntil: {
    type: Date,
    default: null
  },
  loginAttempts: {
    type: Number,
    default: 0,
    min: 0
  },
  twoFactorEnabled: {
    type: Boolean,
    default: false
  },
  twoFactorSecret: {
    type: String,
    default: null,
    select: false
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: {
    type: String,
    default: null,
    select: false
  },
  emailVerificationExpires: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      delete ret.password
      delete ret.passwordResetToken
      delete ret.passwordResetExpires
      return ret
    }
  }
})

// Index for performance (email and username already indexed via unique: true)
UserSchema.index({ branchId: 1 })
UserSchema.index({ role: 1 })

// Hash password before saving
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next()
  
  try {
    const salt = await bcrypt.genSalt(12)
    this.password = await bcrypt.hash(this.password, salt)
    next()
  } catch (error) {
    next(error as Error)
  }
})

// Compare password method
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password)
}

// Generate password reset token
UserSchema.methods.generatePasswordResetToken = function(): string {
  const resetToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)

  this.passwordResetToken = resetToken
  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes

  return resetToken
}

// Add refresh token
UserSchema.methods.addRefreshToken = async function(token: string): Promise<void> {
  // Limit to 5 active sessions per user
  if (this.refreshTokens.length >= 5) {
    this.refreshTokens.shift() // Remove oldest token
  }
  this.refreshTokens.push(token)
  this.sessionCount = this.refreshTokens.length
  await this.save()
}

// Remove refresh token
UserSchema.methods.removeRefreshToken = async function(token: string): Promise<void> {
  this.refreshTokens = this.refreshTokens.filter((t: string) => t !== token)
  this.sessionCount = this.refreshTokens.length
  await this.save()
}

// Clear all refresh tokens (logout from all devices)
UserSchema.methods.clearAllRefreshTokens = async function(): Promise<void> {
  this.refreshTokens = []
  this.sessionCount = 0
  await this.save()
}

// Increment login attempts
UserSchema.methods.incrementLoginAttempts = async function(): Promise<void> {
  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < new Date()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 }
    })
  }

  const updates: any = { $inc: { loginAttempts: 1 } }

  // If we have max attempts and it's not locked yet, lock the account
  if (this.loginAttempts + 1 >= 5 && !this.accountLocked) {
    updates.$set = {
      accountLocked: true,
      lockUntil: new Date(Date.now() + 30 * 60 * 1000) // Lock for 30 minutes
    }
  }

  await this.updateOne(updates)
}

// Reset login attempts
UserSchema.methods.resetLoginAttempts = async function(): Promise<void> {
  await this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
    $set: { accountLocked: false }
  })
}

// Lock account
UserSchema.methods.lockAccount = async function(): Promise<void> {
  await this.updateOne({
    $set: {
      accountLocked: true,
      lockUntil: new Date(Date.now() + 30 * 60 * 1000) // Lock for 30 minutes
    }
  })
}

// Unlock account
UserSchema.methods.unlockAccount = async function(): Promise<void> {
  await this.updateOne({
    $unset: { lockUntil: 1, loginAttempts: 1 },
    $set: { accountLocked: false }
  })
}

const User = mongoose.models.User || mongoose.model<IUser>('User', UserSchema)

export default User
