import mongoose, { Document, Schema } from 'mongoose'
import type { OrderStatus, PaymentStatus, PaymentMethod } from '@/types'

export interface IOrderItem {
  id: string
  productId: string
  productName: string
  sku: string
  price: number
  quantity: number
  subtotal: number
  image: string
}

export interface IOrder extends Document {
  _id: string
  orderNumber: string
  customerId: string
  customerName: string
  customerEmail: string
  customerPhone: string
  items: IOrderItem[]
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  status: OrderStatus
  paymentStatus: PaymentStatus
  paymentMethod: PaymentMethod
  branchId: string
  branchName: string
  shippingAddress: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  billingAddress: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  notes?: string
  estimatedDelivery?: Date
  trackingNumber?: string
  createdAt: Date
  updatedAt: Date
}

const OrderItemSchema = new Schema<IOrderItem>({
  id: {
    type: String,
    required: true
  },
  productId: {
    type: String,
    required: [true, 'Product ID is required'],
    ref: 'Product'
  },
  productName: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true
  },
  sku: {
    type: String,
    required: [true, 'SKU is required'],
    trim: true,
    uppercase: true
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price cannot be negative']
  },
  quantity: {
    type: Number,
    required: [true, 'Quantity is required'],
    min: [1, 'Quantity must be at least 1']
  },
  subtotal: {
    type: Number,
    required: [true, 'Subtotal is required'],
    min: [0, 'Subtotal cannot be negative']
  },
  image: {
    type: String,
    required: [true, 'Product image is required']
  }
}, { _id: false })

const AddressSchema = new Schema({
  street: {
    type: String,
    required: [true, 'Street address is required'],
    trim: true,
    maxlength: [200, 'Street address cannot exceed 200 characters']
  },
  city: {
    type: String,
    required: [true, 'City is required'],
    trim: true,
    maxlength: [50, 'City cannot exceed 50 characters']
  },
  region: {
    type: String,
    required: [true, 'Region is required'],
    trim: true,
    maxlength: [50, 'Region cannot exceed 50 characters']
  },
  country: {
    type: String,
    required: [true, 'Country is required'],
    trim: true,
    maxlength: [50, 'Country cannot exceed 50 characters']
  },
  postalCode: {
    type: String,
    required: [true, 'Postal code is required'],
    trim: true,
    maxlength: [20, 'Postal code cannot exceed 20 characters']
  }
}, { _id: false })

const OrderSchema = new Schema<IOrder>({
  orderNumber: {
    type: String,
    required: [true, 'Order number is required'],
    unique: true,
    trim: true,
    uppercase: true
  },
  customerId: {
    type: String,
    required: [true, 'Customer ID is required'],
    ref: 'Customer'
  },
  customerName: {
    type: String,
    required: [true, 'Customer name is required'],
    trim: true,
    maxlength: [100, 'Customer name cannot exceed 100 characters']
  },
  customerEmail: {
    type: String,
    required: [true, 'Customer email is required'],
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  customerPhone: {
    type: String,
    required: [true, 'Customer phone is required'],
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
  },
  items: {
    type: [OrderItemSchema],
    required: [true, 'Order items are required'],
    validate: {
      validator: function(items: IOrderItem[]) {
        return items && items.length > 0
      },
      message: 'Order must have at least one item'
    }
  },
  subtotal: {
    type: Number,
    required: [true, 'Subtotal is required'],
    min: [0, 'Subtotal cannot be negative']
  },
  tax: {
    type: Number,
    required: [true, 'Tax amount is required'],
    min: [0, 'Tax cannot be negative'],
    default: 0
  },
  shipping: {
    type: Number,
    required: [true, 'Shipping amount is required'],
    min: [0, 'Shipping cannot be negative'],
    default: 0
  },
  discount: {
    type: Number,
    min: [0, 'Discount cannot be negative'],
    default: 0
  },
  total: {
    type: Number,
    required: [true, 'Total amount is required'],
    min: [0, 'Total cannot be negative']
  },
  status: {
    type: String,
    enum: ['Pending', 'Confirmed', 'Processing', 'Shipped', 'Delivered', 'Cancelled', 'Refunded'],
    default: 'Pending',
    required: [true, 'Order status is required']
  },
  paymentStatus: {
    type: String,
    enum: ['Pending', 'Paid', 'Failed', 'Refunded'],
    default: 'Pending',
    required: [true, 'Payment status is required']
  },
  paymentMethod: {
    type: String,
    enum: ['credit-card', 'debit-card', 'mobile-money', 'bank-transfer', 'cash'],
    required: [true, 'Payment method is required']
  },
  branchId: {
    type: String,
    required: [true, 'Branch ID is required'],
    ref: 'Shop'
  },
  branchName: {
    type: String,
    required: [true, 'Branch name is required'],
    trim: true
  },
  shippingAddress: {
    type: AddressSchema,
    required: [true, 'Shipping address is required']
  },
  billingAddress: {
    type: AddressSchema,
    required: [true, 'Billing address is required']
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [500, 'Notes cannot exceed 500 characters']
  },
  estimatedDelivery: {
    type: Date
  },
  trackingNumber: {
    type: String,
    trim: true,
    uppercase: true,
    maxlength: [50, 'Tracking number cannot exceed 50 characters']
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Pre-save middleware to calculate total
OrderSchema.pre('save', function(next) {
  this.total = this.subtotal + this.tax + this.shipping - this.discount
  next()
})

// Indexes for performance (orderNumber already indexed via unique: true)
OrderSchema.index({ customerId: 1 })
OrderSchema.index({ branchId: 1 })
OrderSchema.index({ status: 1 })
OrderSchema.index({ paymentStatus: 1 })
OrderSchema.index({ createdAt: -1 })

// Compound indexes for common queries
OrderSchema.index({ branchId: 1, status: 1 })
OrderSchema.index({ customerId: 1, status: 1 })
OrderSchema.index({ branchId: 1, createdAt: -1 })

const Order = mongoose.models.Order || mongoose.model<IOrder>('Order', OrderSchema)

export default Order
