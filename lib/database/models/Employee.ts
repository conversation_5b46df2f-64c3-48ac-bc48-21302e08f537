import mongoose, { Document, Schema } from 'mongoose'

export interface IEmployee extends Document {
  _id: string
  employeeId: string
  firstName: string
  lastName: string
  email: string
  phone: string
  position: string
  department: string
  branchId: string
  branchName: string
  salary: number
  hireDate: Date
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  emergencyContact: {
    name: string
    relationship: string
    phone: string
  }
  userId?: string
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

const AddressSchema = new Schema({
  street: {
    type: String,
    required: [true, 'Street address is required'],
    trim: true,
    maxlength: [200, 'Street address cannot exceed 200 characters']
  },
  city: {
    type: String,
    required: [true, 'City is required'],
    trim: true,
    maxlength: [50, 'City cannot exceed 50 characters']
  },
  region: {
    type: String,
    required: [true, 'Region is required'],
    trim: true,
    maxlength: [50, 'Region cannot exceed 50 characters']
  },
  country: {
    type: String,
    required: [true, 'Country is required'],
    trim: true,
    maxlength: [50, 'Country cannot exceed 50 characters']
  },
  postalCode: {
    type: String,
    required: [true, 'Postal code is required'],
    trim: true,
    maxlength: [20, 'Postal code cannot exceed 20 characters']
  }
}, { _id: false })

const EmergencyContactSchema = new Schema({
  name: {
    type: String,
    required: [true, 'Emergency contact name is required'],
    trim: true,
    maxlength: [100, 'Emergency contact name cannot exceed 100 characters']
  },
  relationship: {
    type: String,
    required: [true, 'Relationship is required'],
    trim: true,
    maxlength: [50, 'Relationship cannot exceed 50 characters']
  },
  phone: {
    type: String,
    required: [true, 'Emergency contact phone is required'],
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
  }
}, { _id: false })

const EmployeeSchema = new Schema<IEmployee>({
  employeeId: {
    type: String,
    unique: true,
    required: [true, 'Employee ID is required']
  },
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
  },
  position: {
    type: String,
    required: [true, 'Position is required'],
    trim: true,
    maxlength: [100, 'Position cannot exceed 100 characters']
  },
  department: {
    type: String,
    required: [true, 'Department is required'],
    trim: true,
    maxlength: [100, 'Department cannot exceed 100 characters']
  },
  branchId: {
    type: String,
    required: [true, 'Branch ID is required'],
    ref: 'Shop'
  },
  branchName: {
    type: String,
    required: [true, 'Branch name is required'],
    trim: true,
    maxlength: [100, 'Branch name cannot exceed 100 characters']
  },
  salary: {
    type: Number,
    required: [true, 'Salary is required'],
    min: [0, 'Salary cannot be negative']
  },
  hireDate: {
    type: Date,
    required: [true, 'Hire date is required'],
    validate: {
      validator: function(value: Date) {
        return value <= new Date()
      },
      message: 'Hire date cannot be in the future'
    }
  },
  address: {
    type: AddressSchema,
    required: [true, 'Address is required']
  },
  emergencyContact: {
    type: EmergencyContactSchema,
    required: [true, 'Emergency contact is required']
  },
  userId: {
    type: String,
    ref: 'User'
  },
  isActive: {
    type: Boolean,
    default: true,
    required: [true, 'Active status is required']
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Virtual for full name
EmployeeSchema.virtual('fullName').get(function(this: IEmployee) {
  return `${this.firstName} ${this.lastName}`
})

// Indexes for performance (employeeId and email already indexed via unique: true)
EmployeeSchema.index({ branchId: 1 })
EmployeeSchema.index({ position: 1 })
EmployeeSchema.index({ department: 1 })
EmployeeSchema.index({ isActive: 1 })
EmployeeSchema.index({ hireDate: -1 })

// Compound indexes for common queries
EmployeeSchema.index({ branchId: 1, position: 1 })
EmployeeSchema.index({ branchId: 1, isActive: 1 })
EmployeeSchema.index({ position: 1, isActive: 1 })

// Text index for search functionality
EmployeeSchema.index({
  firstName: 'text',
  lastName: 'text',
  email: 'text',
  position: 'text'
})

const Employee = mongoose.models.Employee || mongoose.model<IEmployee>('Employee', EmployeeSchema)

export default Employee
