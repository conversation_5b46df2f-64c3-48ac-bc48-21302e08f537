import mongoose, { Document, Schema } from 'mongoose'
import type { ProductStatus } from '@/types'

export interface IProductVariant {
  id: string
  name: string
  sku: string
  price: number
  originalPrice?: number
  stock: number
  attributes: Record<string, string> // e.g., { color: 'red', size: 'large' }
  images: string[]
  isActive: boolean
}

export interface IProduct extends Document {
  _id: string
  name: string
  sku: string
  categoryId: string // Reference to ProductCategory
  categoryName: string // Denormalized for performance
  price: number
  originalPrice?: number
  currency: string // Currency code (MWK, ZMW, TZS, ZAR, USD)
  stock: number
  minStockLevel: number
  status: ProductStatus
  images: string[]
  featuredImage: string // Main product image
  description: string
  specifications: string[]
  branchId: string
  brand: string
  model: string
  warranty: string
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  tags: string[]
  variants: IProductVariant[]
  hasVariants: boolean
  isActive: boolean
  isFeatured: boolean
  isPromoted: boolean
  isOnSale: boolean
  salePrice?: number
  saleStartDate?: Date
  saleEndDate?: Date
  promotionDescription?: string
  createdAt: Date
  updatedAt: Date
  createdBy: string
  updatedBy: string
}

// Product Variant Schema
const ProductVariantSchema = new Schema<IProductVariant>({
  id: {
    type: String,
    required: true,
    default: () => new mongoose.Types.ObjectId().toString()
  },
  name: {
    type: String,
    required: [true, 'Variant name is required'],
    trim: true,
    maxlength: [100, 'Variant name cannot exceed 100 characters']
  },
  sku: {
    type: String,
    required: [true, 'Variant SKU is required'],
    trim: true,
    uppercase: true,
    maxlength: [50, 'Variant SKU cannot exceed 50 characters']
  },
  price: {
    type: Number,
    required: [true, 'Variant price is required'],
    min: [0, 'Price cannot be negative'],
    max: [100000000, 'Price cannot exceed 100 million']
  },
  originalPrice: {
    type: Number,
    min: [0, 'Original price cannot be negative'],
    max: [100000000, 'Original price cannot exceed 100 million']
  },
  currency: {
    type: String,
    required: [true, 'Currency is required'],
    enum: ['MWK', 'ZMW', 'TZS', 'ZAR', 'USD'],
    default: 'MWK'
  },
  stock: {
    type: Number,
    required: [true, 'Variant stock is required'],
    min: [0, 'Stock cannot be negative'],
    default: 0
  },
  attributes: {
    type: Map,
    of: String,
    default: new Map()
  },
  images: [{
    type: String
  }],
  isActive: {
    type: Boolean,
    default: true
  }
}, { _id: false })

const ProductSchema = new Schema<IProduct>({
  name: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [200, 'Product name cannot exceed 200 characters']
  },
  sku: {
    type: String,
    required: [true, 'SKU is required'],
    unique: true,
    trim: true,
    uppercase: true,
    maxlength: [50, 'SKU cannot exceed 50 characters']
  },
  categoryId: {
    type: String,
    required: [true, 'Product category is required'],
    ref: 'ProductCategory'
  },
  categoryName: {
    type: String,
    required: [true, 'Category name is required'],
    trim: true
  },
  price: {
    type: Number,
    required: [true, 'Product price is required'],
    min: [0, 'Price cannot be negative'],
    max: [100000000, 'Price cannot exceed 100 million']
  },
  originalPrice: {
    type: Number,
    min: [0, 'Original price cannot be negative'],
    max: [100000000, 'Original price cannot exceed 100 million']
  },
  currency: {
    type: String,
    required: [true, 'Currency is required'],
    enum: ['MWK', 'ZMW', 'TZS', 'ZAR', 'USD'],
    default: 'MWK',
    uppercase: true
  },
  stock: {
    type: Number,
    required: [true, 'Stock quantity is required'],
    min: [0, 'Stock cannot be negative'],
    default: 0
  },
  minStockLevel: {
    type: Number,
    required: [true, 'Minimum stock level is required'],
    min: [0, 'Minimum stock level cannot be negative'],
    default: 5
  },
  status: {
    type: String,
    enum: ['In Stock', 'Low Stock', 'Out of Stock'],
    default: function(this: IProduct) {
      if (this.stock === 0) return 'Out of Stock'
      if (this.stock <= this.minStockLevel) return 'Low Stock'
      return 'In Stock'
    }
  },
  images: [{
    type: String
  }],
  featuredImage: {
    type: String,
    required: [true, 'Featured image is required']
  },
  description: {
    type: String,
    required: [true, 'Product description is required'],
    trim: true,
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  specifications: [{
    type: String,
    trim: true,
    maxlength: [200, 'Each specification cannot exceed 200 characters']
  }],
  branchId: {
    type: String,
    required: [true, 'Branch ID is required'],
    ref: 'Shop'
  },
  brand: {
    type: String,
    required: [true, 'Brand is required'],
    trim: true,
    maxlength: [50, 'Brand cannot exceed 50 characters']
  },
  model: {
    type: String,
    required: [true, 'Model is required'],
    trim: true,
    maxlength: [100, 'Model cannot exceed 100 characters']
  },
  warranty: {
    type: String,
    required: [true, 'Warranty information is required'],
    trim: true,
    maxlength: [100, 'Warranty cannot exceed 100 characters']
  },
  weight: {
    type: Number,
    min: [0, 'Weight cannot be negative']
  },
  dimensions: {
    length: {
      type: Number,
      min: [0, 'Length cannot be negative']
    },
    width: {
      type: Number,
      min: [0, 'Width cannot be negative']
    },
    height: {
      type: Number,
      min: [0, 'Height cannot be negative']
    }
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [30, 'Each tag cannot exceed 30 characters']
  }],
  variants: [ProductVariantSchema],
  hasVariants: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  isPromoted: {
    type: Boolean,
    default: false
  },
  isOnSale: {
    type: Boolean,
    default: false
  },
  salePrice: {
    type: Number,
    min: [0, 'Sale price cannot be negative'],
    max: [100000000, 'Sale price cannot exceed 100 million']
  },
  saleStartDate: {
    type: Date
  },
  saleEndDate: {
    type: Date
  },
  promotionDescription: {
    type: String,
    trim: true,
    maxlength: [200, 'Promotion description cannot exceed 200 characters']
  },
  createdBy: {
    type: String,
    required: [true, 'Created by is required']
  },
  updatedBy: {
    type: String,
    required: [true, 'Updated by is required']
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Update status based on stock level before saving
ProductSchema.pre('save', function(next) {
  // Calculate total stock including variants
  let totalStock = this.stock || 0
  if (this.hasVariants && this.variants.length > 0) {
    totalStock = this.variants.reduce((sum, variant) => sum + (variant.stock || 0), 0)
  }

  // Update status based on total stock
  if (totalStock === 0) {
    this.status = 'Out of Stock'
  } else if (totalStock <= this.minStockLevel) {
    this.status = 'Low Stock'
  } else {
    this.status = 'In Stock'
  }

  // Set hasVariants flag
  this.hasVariants = this.variants && this.variants.length > 0

  // Ensure featured image is set
  if (!this.featuredImage && this.images && this.images.length > 0) {
    this.featuredImage = this.images[0]
  }

  next()
})

// Indexes for performance (sku already indexed via unique: true)
ProductSchema.index({ categoryId: 1 })
ProductSchema.index({ categoryName: 1 })
ProductSchema.index({ branchId: 1 })
ProductSchema.index({ brand: 1 })
ProductSchema.index({ status: 1 })
ProductSchema.index({ isActive: 1 })
ProductSchema.index({ isFeatured: 1 })
ProductSchema.index({ hasVariants: 1 })
ProductSchema.index({ price: 1 })
ProductSchema.index({ currency: 1 })
ProductSchema.index({ branchId: 1, currency: 1 })
ProductSchema.index({ tags: 1 })
ProductSchema.index({ createdBy: 1 })
ProductSchema.index({ 'variants.sku': 1 })

// Compound indexes for common queries
ProductSchema.index({ branchId: 1, categoryId: 1 })
ProductSchema.index({ branchId: 1, status: 1 })
ProductSchema.index({ categoryId: 1, status: 1 })
ProductSchema.index({ isActive: 1, isFeatured: 1 })
ProductSchema.index({ isActive: 1, isPromoted: 1 })
ProductSchema.index({ isActive: 1, isOnSale: 1 })
ProductSchema.index({ isActive: 1, isFeatured: 1, isPromoted: 1, isOnSale: 1 })
ProductSchema.index({ branchId: 1, hasVariants: 1 })
ProductSchema.index({ saleStartDate: 1, saleEndDate: 1 })

// Text index for search functionality
ProductSchema.index({
  name: 'text',
  description: 'text',
  brand: 'text',
  model: 'text',
  categoryName: 'text',
  tags: 'text',
  'variants.name': 'text'
})

const Product = mongoose.models.Product || mongoose.model<IProduct>('Product', ProductSchema)

export default Product
