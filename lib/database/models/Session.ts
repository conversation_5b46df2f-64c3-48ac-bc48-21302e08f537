// lib/database/models/Session.ts - Session Management Model

import mongoose, { Schema, Document, Model } from 'mongoose'

// Session interface
export interface ISession extends Document {
  _id: mongoose.Types.ObjectId
  userId: mongoose.Types.ObjectId
  sessionToken: string
  refreshToken: string
  deviceInfo: {
    userAgent: string
    browser: string
    browserVersion: string
    os: string
    osVersion: string
    device: string
    deviceType: 'desktop' | 'mobile' | 'tablet'
    platform: string
  }
  location: {
    ipAddress: string
    country?: string
    city?: string
    timezone?: string
  }
  loginTime: Date
  lastActivity: Date
  expiresAt: Date
  isActive: boolean
  logoutTime?: Date
  logoutReason?: 'manual' | 'expired' | 'forced' | 'security'
  createdAt: Date
  updatedAt: Date
}

// Session schema
const SessionSchema = new Schema<ISession>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  sessionToken: {
    type: String,
    required: true,
    unique: true
  },
  refreshToken: {
    type: String,
    required: true,
    unique: true
  },
  deviceInfo: {
    userAgent: {
      type: String,
      required: true
    },
    browser: {
      type: String,
      required: true
    },
    browserVersion: {
      type: String,
      default: 'Unknown'
    },
    os: {
      type: String,
      required: true
    },
    osVersion: {
      type: String,
      default: 'Unknown'
    },
    device: {
      type: String,
      default: 'Unknown'
    },
    deviceType: {
      type: String,
      enum: ['desktop', 'mobile', 'tablet'],
      default: 'desktop'
    },
    platform: {
      type: String,
      default: 'Unknown'
    }
  },
  location: {
    ipAddress: {
      type: String,
      required: true
    },
    country: {
      type: String,
      default: null
    },
    city: {
      type: String,
      default: null
    },
    timezone: {
      type: String,
      default: null
    }
  },
  loginTime: {
    type: Date,
    required: true,
    default: Date.now
  },
  lastActivity: {
    type: Date,
    required: true,
    default: Date.now
  },
  expiresAt: {
    type: Date,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  logoutTime: {
    type: Date,
    default: null
  },
  logoutReason: {
    type: String,
    enum: ['manual', 'expired', 'forced', 'security'],
    default: null
  }
}, {
  timestamps: true,
  collection: 'sessions'
})

// Compound indexes for efficient queries (sessionToken, refreshToken already indexed via unique: true)
SessionSchema.index({ userId: 1 })
SessionSchema.index({ userId: 1, isActive: 1 })
SessionSchema.index({ userId: 1, loginTime: -1 })
SessionSchema.index({ isActive: 1 })
SessionSchema.index({ ipAddress: 1 })
// TTL index for automatic session expiration
SessionSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 })

// Instance methods
SessionSchema.methods.updateActivity = function(): Promise<ISession> {
  this.lastActivity = new Date()
  return this.save()
}

SessionSchema.methods.terminate = function(reason: 'manual' | 'expired' | 'forced' | 'security' = 'manual'): Promise<ISession> {
  this.isActive = false
  this.logoutTime = new Date()
  this.logoutReason = reason
  return this.save()
}

SessionSchema.methods.isExpired = function(): boolean {
  return new Date() > this.expiresAt
}

SessionSchema.methods.getDeviceDescription = function(): string {
  const { browser, browserVersion, os, osVersion, deviceType } = this.deviceInfo
  return `${browser} ${browserVersion} on ${os} ${osVersion} (${deviceType})`
}

SessionSchema.methods.getLocationDescription = function(): string {
  const { city, country, ipAddress } = this.location
  if (city && country) {
    return `${city}, ${country} (${ipAddress})`
  }
  return ipAddress
}

// Static methods
SessionSchema.statics.findActiveByUser = function(userId: string | mongoose.Types.ObjectId): Promise<ISession[]> {
  return this.find({ 
    userId: new mongoose.Types.ObjectId(userId), 
    isActive: true,
    expiresAt: { $gt: new Date() }
  }).sort({ lastActivity: -1 })
}

SessionSchema.statics.findByToken = function(sessionToken: string): Promise<ISession | null> {
  return this.findOne({ 
    sessionToken, 
    isActive: true,
    expiresAt: { $gt: new Date() }
  })
}

SessionSchema.statics.findByRefreshToken = function(refreshToken: string): Promise<ISession | null> {
  return this.findOne({ 
    refreshToken, 
    isActive: true,
    expiresAt: { $gt: new Date() }
  })
}

SessionSchema.statics.terminateAllUserSessions = function(
  userId: string | mongoose.Types.ObjectId, 
  reason: 'manual' | 'expired' | 'forced' | 'security' = 'forced'
): Promise<any> {
  return this.updateMany(
    { 
      userId: new mongoose.Types.ObjectId(userId), 
      isActive: true 
    },
    { 
      isActive: false, 
      logoutTime: new Date(), 
      logoutReason: reason 
    }
  )
}

SessionSchema.statics.cleanupExpiredSessions = function(): Promise<any> {
  return this.updateMany(
    { 
      isActive: true,
      expiresAt: { $lte: new Date() }
    },
    { 
      isActive: false, 
      logoutTime: new Date(), 
      logoutReason: 'expired' 
    }
  )
}

SessionSchema.statics.getUserSessionStats = function(userId: string | mongoose.Types.ObjectId): Promise<any> {
  return this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: null,
        totalSessions: { $sum: 1 },
        activeSessions: { 
          $sum: { 
            $cond: [
              { 
                $and: [
                  { $eq: ['$isActive', true] },
                  { $gt: ['$expiresAt', new Date()] }
                ]
              }, 
              1, 
              0
            ] 
          }
        },
        lastLogin: { $max: '$loginTime' },
        uniqueDevices: { $addToSet: '$deviceInfo.device' },
        uniqueIPs: { $addToSet: '$location.ipAddress' }
      }
    },
    {
      $project: {
        _id: 0,
        totalSessions: 1,
        activeSessions: 1,
        lastLogin: 1,
        uniqueDeviceCount: { $size: '$uniqueDevices' },
        uniqueIPCount: { $size: '$uniqueIPs' }
      }
    }
  ])
}

// Pre-save middleware
SessionSchema.pre('save', function(next) {
  if (this.isNew) {
    // Set expiration time (30 days from creation)
    if (!this.expiresAt) {
      this.expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    }
  }
  next()
})

// Export the model
const Session: Model<ISession> = mongoose.models.Session || mongoose.model<ISession>('Session', SessionSchema)

export default Session
