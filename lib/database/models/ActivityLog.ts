import mongoose, { Document, Schema } from 'mongoose'

export interface IActivityLog extends Document {
  _id: string
  type: 'Order' | 'Inventory' | 'Sale' | 'Delivery' | 'Product' | 'User'
  description: string
  userId: string
  userName: string
  branchId?: string
  branchName?: string
  metadata?: Record<string, any>
  timestamp: Date
  createdAt: Date
  updatedAt: Date
}

const ActivityLogSchema = new Schema<IActivityLog>({
  type: {
    type: String,
    enum: ['Order', 'Inventory', 'Sale', 'Delivery', 'Product', 'User'],
    required: [true, 'Activity type is required']
  },
  description: {
    type: String,
    required: [true, 'Activity description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  userId: {
    type: String,
    required: [true, 'User ID is required'],
    ref: 'User'
  },
  userName: {
    type: String,
    required: [true, 'User name is required'],
    trim: true,
    maxlength: [100, 'User name cannot exceed 100 characters']
  },
  branchId: {
    type: String,
    ref: 'Shop'
  },
  branchName: {
    type: String,
    trim: true,
    maxlength: [100, 'Branch name cannot exceed 100 characters']
  },
  metadata: {
    type: Schema.Types.Mixed,
    default: {}
  },
  timestamp: {
    type: Date,
    required: [true, 'Timestamp is required'],
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Indexes for performance
ActivityLogSchema.index({ type: 1 })
ActivityLogSchema.index({ userId: 1 })
ActivityLogSchema.index({ branchId: 1 })
ActivityLogSchema.index({ timestamp: -1 })
ActivityLogSchema.index({ createdAt: -1 })

// Compound indexes for common queries
ActivityLogSchema.index({ type: 1, timestamp: -1 })
ActivityLogSchema.index({ userId: 1, timestamp: -1 })
ActivityLogSchema.index({ branchId: 1, timestamp: -1 })
ActivityLogSchema.index({ branchId: 1, type: 1, timestamp: -1 })

// Text index for search functionality
ActivityLogSchema.index({
  description: 'text',
  userName: 'text'
})

const ActivityLog = mongoose.models.ActivityLog || mongoose.model<IActivityLog>('ActivityLog', ActivityLogSchema)

export default ActivityLog
