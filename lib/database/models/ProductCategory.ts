import { Schema, model, models, Document } from 'mongoose'

export interface IProductCategory extends Document {
  _id: string
  name: string
  description: string
  slug: string
  isActive: boolean
  productCount: number
  // Image fields
  featuredImage?: string // Background image URL
  icon?: string // Icon image URL
  iconType?: 'image' | 'lucide' // Type of icon (uploaded image or Lucide icon name)
  iconName?: string // Lucide icon name if iconType is 'lucide'
  createdAt: Date
  updatedAt: Date
  createdBy: string
  updatedBy: string
}

const ProductCategorySchema = new Schema<IProductCategory>({
  name: {
    type: String,
    required: [true, 'Category name is required'],
    trim: true,
    unique: true,
    maxlength: [50, 'Category name cannot exceed 50 characters'],
    minlength: [2, 'Category name must be at least 2 characters']
  },
  description: {
    type: String,
    required: [true, 'Category description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters'],
    minlength: [10, 'Description must be at least 10 characters']
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  productCount: {
    type: Number,
    default: 0,
    min: [0, 'Product count cannot be negative']
  },
  // Image fields
  featuredImage: {
    type: String,
    trim: true,
    default: null
  },
  icon: {
    type: String,
    trim: true,
    default: null
  },
  iconType: {
    type: String,
    enum: ['image', 'lucide'],
    default: 'lucide'
  },
  iconName: {
    type: String,
    trim: true,
    default: null
  },
  createdBy: {
    type: String,
    required: [true, 'Created by is required']
  },
  updatedBy: {
    type: String,
    required: [true, 'Updated by is required']
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Create slug from name before saving
ProductCategorySchema.pre('save', function(next) {
  if (this.isModified('name') || this.isNew) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }
  next()
})

// Also handle pre-create for when using Model.create()
ProductCategorySchema.pre('validate', function(next) {
  if (!this.slug && this.name) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
  }
  next()
})

// Transform _id to id when converting to JSON
ProductCategorySchema.set('toJSON', {
  transform: function(doc, ret) {
    ret.id = ret._id
    delete ret._id
    delete ret.__v
    return ret
  }
})

// Indexes for better performance (name and slug already indexed via unique: true)
ProductCategorySchema.index({ isActive: 1 })
ProductCategorySchema.index({ createdAt: -1 })

const ProductCategory = models.ProductCategory || model<IProductCategory>('ProductCategory', ProductCategorySchema)

export default ProductCategory
