// lib/database/models/AuditLog.ts - Audit Log Model for Security and Compliance

import mongoose, { Schema, Document, Model } from 'mongoose'

// Audit log interface
export interface IAuditLog extends Document {
  _id: mongoose.Types.ObjectId
  action: string
  category: 'session' | 'user' | 'admin' | 'security' | 'system'
  severity: 'low' | 'medium' | 'high' | 'critical'
  adminId: mongoose.Types.ObjectId
  adminUsername: string
  adminName: string
  targetUserId?: mongoose.Types.ObjectId
  targetUsername?: string
  targetUserEmail?: string
  sessionId?: mongoose.Types.ObjectId
  details: {
    description: string
    beforeState?: any
    afterState?: any
    metadata?: any
  }
  ipAddress: string
  userAgent: string
  timestamp: Date
  success: boolean
  errorMessage?: string
  createdAt: Date
  updatedAt: Date
}

// Audit log schema
const AuditLogSchema = new Schema<IAuditLog>({
  action: {
    type: String,
    required: true,
    enum: [
      // Session actions
      'session_terminate',
      'session_terminate_all',
      'session_view',
      'session_force_logout',
      
      // User actions
      'user_create',
      'user_update',
      'user_delete',
      'user_suspend',
      'user_activate',
      'user_role_change',
      
      // Admin actions
      'admin_login',
      'admin_logout',
      'admin_permission_change',
      
      // Security actions
      'security_alert_create',
      'security_alert_resolve',
      'suspicious_activity_detected',
      'failed_login_attempt',
      
      // System actions
      'system_backup',
      'system_restore',
      'system_maintenance',
      'data_export',
      'data_import'
    ]
  },
  category: {
    type: String,
    required: true,
    enum: ['session', 'user', 'admin', 'security', 'system'],
    index: true
  },
  severity: {
    type: String,
    required: true,
    enum: ['low', 'medium', 'high', 'critical'],
    index: true
  },
  adminId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  adminUsername: {
    type: String,
    required: true
  },
  adminName: {
    type: String,
    required: true
  },
  targetUserId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  targetUsername: {
    type: String,
    index: true
  },
  targetUserEmail: {
    type: String
  },
  sessionId: {
    type: Schema.Types.ObjectId,
    ref: 'Session',
    index: true
  },
  details: {
    description: {
      type: String,
      required: true
    },
    beforeState: {
      type: Schema.Types.Mixed
    },
    afterState: {
      type: Schema.Types.Mixed
    },
    metadata: {
      type: Schema.Types.Mixed
    }
  },
  ipAddress: {
    type: String,
    required: true,
    index: true
  },
  userAgent: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    required: true,
    default: Date.now,
    index: true
  },
  success: {
    type: Boolean,
    required: true,
    default: true,
    index: true
  },
  errorMessage: {
    type: String
  }
}, {
  timestamps: true,
  collection: 'audit_logs'
})

// Compound indexes for efficient queries
AuditLogSchema.index({ adminId: 1, timestamp: -1 })
AuditLogSchema.index({ targetUserId: 1, timestamp: -1 })
AuditLogSchema.index({ category: 1, severity: 1, timestamp: -1 })
AuditLogSchema.index({ action: 1, timestamp: -1 })
AuditLogSchema.index({ timestamp: -1 }) // For general time-based queries

// TTL index to automatically delete old logs (keep for 2 years)
AuditLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 2 * 365 * 24 * 60 * 60 })

// Static methods
AuditLogSchema.statics.logSessionAction = function(data: {
  action: string
  adminId: string
  adminUsername: string
  adminName: string
  targetUserId?: string
  targetUsername?: string
  targetUserEmail?: string
  sessionId?: string
  description: string
  metadata?: any
  ipAddress: string
  userAgent: string
  success?: boolean
  errorMessage?: string
}): Promise<IAuditLog> {
  return this.create({
    action: data.action,
    category: 'session',
    severity: data.action.includes('terminate') ? 'high' : 'medium',
    adminId: new mongoose.Types.ObjectId(data.adminId),
    adminUsername: data.adminUsername,
    adminName: data.adminName,
    targetUserId: data.targetUserId ? new mongoose.Types.ObjectId(data.targetUserId) : undefined,
    targetUsername: data.targetUsername,
    targetUserEmail: data.targetUserEmail,
    sessionId: data.sessionId ? new mongoose.Types.ObjectId(data.sessionId) : undefined,
    details: {
      description: data.description,
      metadata: data.metadata
    },
    ipAddress: data.ipAddress,
    userAgent: data.userAgent,
    success: data.success !== false,
    errorMessage: data.errorMessage
  })
}

AuditLogSchema.statics.getAdminActivity = function(
  adminId: string,
  timeframe: number = 30 // days
): Promise<IAuditLog[]> {
  const startDate = new Date(Date.now() - timeframe * 24 * 60 * 60 * 1000)
  
  return this.find({
    adminId: new mongoose.Types.ObjectId(adminId),
    timestamp: { $gte: startDate }
  }).sort({ timestamp: -1 }).limit(100)
}

AuditLogSchema.statics.getSecurityEvents = function(
  timeframe: number = 7 // days
): Promise<IAuditLog[]> {
  const startDate = new Date(Date.now() - timeframe * 24 * 60 * 60 * 1000)
  
  return this.find({
    $or: [
      { category: 'security' },
      { severity: { $in: ['high', 'critical'] } },
      { action: { $regex: /terminate|force|suspend/ } }
    ],
    timestamp: { $gte: startDate }
  }).sort({ timestamp: -1 }).limit(50)
}

AuditLogSchema.statics.getUserActivity = function(
  userId: string,
  timeframe: number = 30 // days
): Promise<IAuditLog[]> {
  const startDate = new Date(Date.now() - timeframe * 24 * 60 * 60 * 1000)
  
  return this.find({
    targetUserId: new mongoose.Types.ObjectId(userId),
    timestamp: { $gte: startDate }
  }).sort({ timestamp: -1 }).limit(50)
}

AuditLogSchema.statics.getActivityStats = function(timeframe: number = 30): Promise<any> {
  const startDate = new Date(Date.now() - timeframe * 24 * 60 * 60 * 1000)
  
  return this.aggregate([
    { $match: { timestamp: { $gte: startDate } } },
    {
      $group: {
        _id: null,
        totalActions: { $sum: 1 },
        successfulActions: { $sum: { $cond: ['$success', 1, 0] } },
        failedActions: { $sum: { $cond: ['$success', 0, 1] } },
        categoryCounts: {
          $push: '$category'
        },
        severityCounts: {
          $push: '$severity'
        },
        uniqueAdmins: { $addToSet: '$adminId' }
      }
    },
    {
      $project: {
        totalActions: 1,
        successfulActions: 1,
        failedActions: 1,
        successRate: { 
          $multiply: [
            { $divide: ['$successfulActions', '$totalActions'] },
            100
          ]
        },
        uniqueAdminCount: { $size: '$uniqueAdmins' },
        categoryCounts: 1,
        severityCounts: 1
      }
    }
  ])
}

// Instance methods
AuditLogSchema.methods.getSeverityColor = function(): string {
  switch (this.severity) {
    case 'critical':
      return 'red'
    case 'high':
      return 'orange'
    case 'medium':
      return 'yellow'
    case 'low':
      return 'green'
    default:
      return 'gray'
  }
}

AuditLogSchema.methods.getActionDescription = function(): string {
  const actionDescriptions: { [key: string]: string } = {
    session_terminate: 'Terminated user session',
    session_terminate_all: 'Terminated all user sessions',
    session_view: 'Viewed user sessions',
    session_force_logout: 'Forced user logout',
    user_create: 'Created new user',
    user_update: 'Updated user information',
    user_delete: 'Deleted user account',
    user_suspend: 'Suspended user account',
    user_activate: 'Activated user account',
    user_role_change: 'Changed user role',
    admin_login: 'Admin logged in',
    admin_logout: 'Admin logged out',
    security_alert_create: 'Created security alert',
    suspicious_activity_detected: 'Detected suspicious activity'
  }
  
  return actionDescriptions[this.action] || this.action.replace(/_/g, ' ')
}

// Export the model
const AuditLog: Model<IAuditLog> = mongoose.models.AuditLog || mongoose.model<IAuditLog>('AuditLog', AuditLogSchema)

export default AuditLog
