import mongoose, { Document, Schema } from 'mongoose'
import type { ProductStatus } from '@/types'

export interface IInventory extends Document {
  _id: string
  productId: string
  productName: string
  sku: string
  branchId: string
  branchName: string
  stock: number
  minStockLevel: number
  maxStockLevel: number
  status: ProductStatus
  lastRestocked: Date
  supplier?: string
  cost: number
  location: string // Warehouse location within branch
  // Enhanced location fields
  warehouseId?: string
  warehouseName?: string
  warehouseCode?: string
  shelfId?: string
  shelfCode?: string
  shelfName?: string
  batchNumber?: string
  expiryDate?: Date
  createdAt: Date
  updatedAt: Date
}

const InventorySchema = new Schema<IInventory>({
  productId: {
    type: String,
    required: [true, 'Product ID is required'],
    ref: 'Product'
  },
  productName: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [200, 'Product name cannot exceed 200 characters']
  },
  sku: {
    type: String,
    required: [true, 'SKU is required'],
    trim: true,
    uppercase: true,
    maxlength: [50, 'SKU cannot exceed 50 characters']
  },
  branchId: {
    type: String,
    required: [true, 'Branch ID is required'],
    ref: 'Shop'
  },
  branchName: {
    type: String,
    required: [true, 'Branch name is required'],
    trim: true,
    maxlength: [100, 'Branch name cannot exceed 100 characters']
  },
  stock: {
    type: Number,
    required: [true, 'Stock quantity is required'],
    min: [0, 'Stock cannot be negative'],
    default: 0
  },
  minStockLevel: {
    type: Number,
    required: [true, 'Minimum stock level is required'],
    min: [0, 'Minimum stock level cannot be negative'],
    default: 5
  },
  maxStockLevel: {
    type: Number,
    required: [true, 'Maximum stock level is required'],
    min: [0, 'Maximum stock level cannot be negative'],
    validate: {
      validator: function(this: IInventory, value: number) {
        return value >= this.minStockLevel
      },
      message: 'Maximum stock level must be greater than or equal to minimum stock level'
    }
  },
  status: {
    type: String,
    enum: ['In Stock', 'Low Stock', 'Out of Stock'],
    default: function(this: IInventory) {
      if (this.stock === 0) return 'Out of Stock'
      if (this.stock <= this.minStockLevel) return 'Low Stock'
      return 'In Stock'
    }
  },
  lastRestocked: {
    type: Date,
    required: [true, 'Last restocked date is required'],
    default: Date.now
  },
  supplier: {
    type: String,
    trim: true,
    maxlength: [100, 'Supplier name cannot exceed 100 characters']
  },
  cost: {
    type: Number,
    required: [true, 'Cost is required'],
    min: [0, 'Cost cannot be negative']
  },
  location: {
    type: String,
    required: [true, 'Warehouse location is required'],
    trim: true,
    maxlength: [100, 'Location cannot exceed 100 characters']
  },
  // Enhanced location fields
  warehouseId: {
    type: String,
    ref: 'Warehouse',
    trim: true
  },
  warehouseName: {
    type: String,
    trim: true,
    maxlength: [100, 'Warehouse name cannot exceed 100 characters']
  },
  warehouseCode: {
    type: String,
    trim: true,
    uppercase: true,
    maxlength: [20, 'Warehouse code cannot exceed 20 characters']
  },
  shelfId: {
    type: String,
    ref: 'Shelf',
    trim: true
  },
  shelfCode: {
    type: String,
    trim: true,
    uppercase: true,
    maxlength: [30, 'Shelf code cannot exceed 30 characters']
  },
  shelfName: {
    type: String,
    trim: true,
    maxlength: [100, 'Shelf name cannot exceed 100 characters']
  },
  batchNumber: {
    type: String,
    trim: true,
    maxlength: [50, 'Batch number cannot exceed 50 characters']
  },
  expiryDate: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Update status based on stock level before saving
InventorySchema.pre('save', function(next) {
  if (this.stock === 0) {
    this.status = 'Out of Stock'
  } else if (this.stock <= this.minStockLevel) {
    this.status = 'Low Stock'
  } else {
    this.status = 'In Stock'
  }
  next()
})

// Ensure unique combination of product and branch
InventorySchema.index({ productId: 1, branchId: 1 }, { unique: true })

// Other indexes for performance
InventorySchema.index({ sku: 1 })
InventorySchema.index({ branchId: 1 })
InventorySchema.index({ status: 1 })
InventorySchema.index({ stock: 1 })
InventorySchema.index({ lastRestocked: -1 })
InventorySchema.index({ supplier: 1 })

// Compound indexes for common queries
InventorySchema.index({ branchId: 1, status: 1 })
InventorySchema.index({ branchId: 1, stock: 1 })
InventorySchema.index({ status: 1, stock: 1 })
InventorySchema.index({ branchId: 1, lastRestocked: -1 })

// Text index for search functionality
InventorySchema.index({
  productName: 'text',
  sku: 'text',
  supplier: 'text',
  location: 'text'
})

const Inventory = mongoose.models.Inventory || mongoose.model<IInventory>('Inventory', InventorySchema)

export default Inventory
