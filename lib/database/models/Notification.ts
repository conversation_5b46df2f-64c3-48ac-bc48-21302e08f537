import mongoose, { Schema, Document } from 'mongoose'
import type { 
  PlatformNotification, 
  NotificationCategory, 
  NotificationPriority, 
  NotificationStatus,
  NotificationTarget,
  NotificationAction,
  NotificationChannel,
  DeliveryStatus
} from '@/types/notifications'

// Notification document interface
export interface INotification extends Document {
  category: NotificationCategory
  type: string
  priority: NotificationPriority
  status: NotificationStatus
  
  // Content
  title: string
  message: string
  description?: string
  
  // Rich content
  icon?: string
  image?: string
  color?: string
  badge?: string
  
  // Targeting
  target: NotificationTarget
  
  // Metadata
  data?: Record<string, any>
  actions?: NotificationAction[]
  
  // Timestamps
  createdAt: Date
  scheduledFor?: Date
  expiresAt?: Date
  readAt?: Date
  
  // Tracking
  createdBy: string
  source: string
  
  // Delivery
  channels: NotificationChannel[]
  deliveryStatus: DeliveryStatus
  
  // Analytics
  sentCount: number
  deliveredCount: number
  openedCount: number
  clickedCount: number
  
  // Methods
  markAsRead(): Promise<INotification>
  markAsDelivered(channel: NotificationChannel): Promise<INotification>
  incrementOpened(): Promise<INotification>
  incrementClicked(): Promise<INotification>
  isExpired(): boolean
  canBeDelivered(): boolean
}

// Notification target schema
const NotificationTargetSchema = new Schema({
  roles: [{ type: String, enum: ['overall_admin', 'branch_manager', 'employee', 'customer'] }],
  userIds: [{ type: String }],
  branchIds: [{ type: String }],
  locations: [{ type: String }],
  customFilters: { type: Schema.Types.Mixed }
}, { _id: false })

// Notification action schema
const NotificationActionSchema = new Schema({
  id: { type: String, required: true },
  label: { type: String, required: true },
  type: { type: String, enum: ['button', 'link', 'dismiss', 'archive'], required: true },
  variant: { type: String, enum: ['primary', 'secondary', 'destructive', 'outline'] },
  url: { type: String },
  action: { type: String },
  data: { type: Schema.Types.Mixed }
}, { _id: false })

// Delivery status schema
const DeliveryStatusSchema = new Schema({
  in_app: { type: String, enum: ['pending', 'delivered', 'failed'], default: 'pending' },
  email: { type: String, enum: ['pending', 'sent', 'delivered', 'failed'] },
  sms: { type: String, enum: ['pending', 'sent', 'delivered', 'failed'] },
  push: { type: String, enum: ['pending', 'sent', 'delivered', 'failed'] },
  socket: { type: String, enum: ['pending', 'sent', 'delivered', 'failed'] }
}, { _id: false })

// Main notification schema
const NotificationSchema = new Schema<INotification>({
  category: {
    type: String,
    enum: ['system', 'security', 'inventory', 'orders', 'delivery', 'campaigns', 'users', 'financial', 'reports', 'maintenance'],
    required: true
  },
  type: {
    type: String,
    required: true
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical', 'urgent'],
    required: true,
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['unread', 'read', 'archived', 'dismissed'],
    required: true,
    default: 'unread'
  },
  
  // Content
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  message: {
    type: String,
    required: true,
    maxlength: 1000
  },
  description: {
    type: String,
    maxlength: 2000
  },
  
  // Rich content
  icon: { type: String },
  image: { type: String },
  color: { type: String },
  badge: { type: String },
  
  // Targeting
  target: {
    type: NotificationTargetSchema,
    required: true
  },
  
  // Metadata
  data: { type: Schema.Types.Mixed },
  actions: [NotificationActionSchema],
  
  // Timestamps
  scheduledFor: { type: Date },
  expiresAt: { type: Date },
  readAt: { type: Date },

  // Tracking
  createdBy: {
    type: String,
    required: true
  },
  source: {
    type: String,
    required: true,
    default: 'system'
  },
  
  // Delivery
  channels: [{
    type: String,
    enum: ['in_app', 'email', 'sms', 'push', 'socket'],
    required: true
  }],
  deliveryStatus: {
    type: DeliveryStatusSchema,
    default: () => ({})
  },
  
  // Analytics
  sentCount: { type: Number, default: 0 },
  deliveredCount: { type: Number, default: 0 },
  openedCount: { type: Number, default: 0 },
  clickedCount: { type: Number, default: 0 }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes for performance
NotificationSchema.index({ createdAt: -1 })
NotificationSchema.index({ 'target.userIds': 1, status: 1, createdAt: -1 })
NotificationSchema.index({ 'target.roles': 1, status: 1, createdAt: -1 })
NotificationSchema.index({ 'target.branchIds': 1, status: 1, createdAt: -1 })
NotificationSchema.index({ category: 1, type: 1, createdAt: -1 })
NotificationSchema.index({ scheduledFor: 1, status: 1 })
NotificationSchema.index({ expiresAt: 1 })

// Virtual for checking if notification is read
NotificationSchema.virtual('isRead').get(function() {
  return this.status === 'read' || this.readAt !== undefined
})

// Virtual for checking if notification is expired (removed - using method instead)

// Instance methods
NotificationSchema.methods.markAsRead = function(): Promise<INotification> {
  this.status = 'read'
  this.readAt = new Date()
  return this.save()
}

NotificationSchema.methods.markAsDelivered = function(channel: NotificationChannel): Promise<INotification> {
  if (!this.deliveryStatus) {
    this.deliveryStatus = {}
  }
  this.deliveryStatus[channel] = 'delivered'
  this.deliveredCount += 1
  return this.save()
}

NotificationSchema.methods.incrementOpened = function(): Promise<INotification> {
  this.openedCount += 1
  return this.save()
}

NotificationSchema.methods.incrementClicked = function(): Promise<INotification> {
  this.clickedCount += 1
  return this.save()
}

NotificationSchema.methods.isExpired = function(): boolean {
  return this.expiresAt && this.expiresAt < new Date()
}

NotificationSchema.methods.canBeDelivered = function(): boolean {
  const now = new Date()
  
  // Check if scheduled for future
  if (this.scheduledFor && this.scheduledFor > now) {
    return false
  }
  
  // Check if expired
  if (this.expiresAt && this.expiresAt < now) {
    return false
  }
  
  // Check if already archived or dismissed
  if (this.status === 'archived' || this.status === 'dismissed') {
    return false
  }
  
  return true
}

// Static methods
NotificationSchema.statics.findForUser = function(userId: string, options: {
  category?: NotificationCategory
  status?: NotificationStatus
  limit?: number
  skip?: number
} = {}) {
  const query: any = {
    $or: [
      { 'target.userIds': userId },
      { 'target.roles': { $exists: true } } // Will be filtered by user role in application logic
    ]
  }
  
  if (options.category) {
    query.category = options.category
  }
  
  if (options.status) {
    query.status = options.status
  }
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0)
}

NotificationSchema.statics.findForRole = function(role: string, options: {
  category?: NotificationCategory
  status?: NotificationStatus
  limit?: number
  skip?: number
} = {}) {
  const query: any = {
    'target.roles': role
  }
  
  if (options.category) {
    query.category = options.category
  }
  
  if (options.status) {
    query.status = options.status
  }
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0)
}

NotificationSchema.statics.findScheduled = function() {
  const now = new Date()
  return this.find({
    scheduledFor: { $lte: now },
    status: { $nin: ['archived', 'dismissed'] },
    $or: [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: now } }
    ]
  })
}

NotificationSchema.statics.cleanupExpired = function() {
  const now = new Date()
  return this.updateMany(
    { expiresAt: { $lt: now }, status: { $ne: 'archived' } },
    { status: 'archived' }
  )
}

// Pre-save middleware
NotificationSchema.pre('save', function(next) {
  // Set default delivery status for enabled channels
  if (this.isNew && this.channels.length > 0) {
    this.channels.forEach(channel => {
      if (!this.deliveryStatus[channel]) {
        this.deliveryStatus[channel] = 'pending'
      }
    })
  }
  
  next()
})

// Indexes for performance
NotificationSchema.index({ category: 1 })
NotificationSchema.index({ type: 1 })
NotificationSchema.index({ priority: 1 })
NotificationSchema.index({ status: 1 })
NotificationSchema.index({ scheduledFor: 1 })
NotificationSchema.index({ expiresAt: 1 })
NotificationSchema.index({ createdBy: 1 })

// Compound indexes for common queries
NotificationSchema.index({ 'target.userId': 1, status: 1 })
NotificationSchema.index({ 'target.role': 1, status: 1 })
NotificationSchema.index({ 'target.branchId': 1, status: 1 })
NotificationSchema.index({ category: 1, priority: 1 })
NotificationSchema.index({ status: 1, createdAt: -1 })

// Export the model
const Notification = mongoose.models.Notification || mongoose.model<INotification>('Notification', NotificationSchema)

export default Notification
