import mongoose, { Document, Schema } from 'mongoose'

export interface IShop extends Document {
  _id: string
  name: string
  location: string
  country: string
  region: string
  manager: string
  managerId: string
  totalProducts: number
  totalSales: number
  status: 'Active' | 'Inactive' | 'Opening Soon' | 'Maintenance'
  image: string
  description: string
  address: string
  phone: string
  email: string
  coordinates?: {
    lat: number
    lng: number
  }
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  createdAt: Date
  updatedAt: Date
}

const ShopSchema = new Schema<IShop>({
  name: {
    type: String,
    required: [true, 'Shop name is required'],
    trim: true,
    maxlength: [100, 'Shop name cannot exceed 100 characters']
  },
  location: {
    type: String,
    required: [true, 'Location is required'],
    trim: true,
    maxlength: [100, 'Location cannot exceed 100 characters']
  },
  country: {
    type: String,
    required: [true, 'Country is required'],
    trim: true,
    maxlength: [50, 'Country cannot exceed 50 characters']
  },
  region: {
    type: String,
    required: [true, 'Region is required'],
    trim: true,
    maxlength: [50, 'Region cannot exceed 50 characters']
  },
  manager: {
    type: String,
    required: [true, 'Manager name is required'],
    trim: true,
    maxlength: [100, 'Manager name cannot exceed 100 characters']
  },
  managerId: {
    type: String,
    required: [true, 'Manager ID is required'],
    ref: 'User'
  },
  totalProducts: {
    type: Number,
    default: 0,
    min: [0, 'Total products cannot be negative']
  },
  totalSales: {
    type: Number,
    default: 0,
    min: [0, 'Total sales cannot be negative']
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Opening Soon', 'Maintenance'],
    default: 'Active',
    required: [true, 'Shop status is required']
  },
  image: {
    type: String,
    required: [true, 'Shop image is required']
  },
  description: {
    type: String,
    required: [true, 'Shop description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  address: {
    type: String,
    required: [true, 'Shop address is required'],
    trim: true,
    maxlength: [200, 'Address cannot exceed 200 characters']
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  coordinates: {
    lat: {
      type: Number,
      min: [-90, 'Latitude must be between -90 and 90'],
      max: [90, 'Latitude must be between -90 and 90']
    },
    lng: {
      type: Number,
      min: [-180, 'Longitude must be between -180 and 180'],
      max: [180, 'Longitude must be between -180 and 180']
    }
  },
  operatingHours: {
    open: {
      type: String,
      required: [true, 'Opening time is required'],
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please enter a valid time format (HH:MM)']
    },
    close: {
      type: String,
      required: [true, 'Closing time is required'],
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please enter a valid time format (HH:MM)']
    },
    timezone: {
      type: String,
      required: [true, 'Timezone is required'],
      default: 'Africa/Blantyre'
    }
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Indexes for performance
ShopSchema.index({ location: 1 })
ShopSchema.index({ country: 1 })
ShopSchema.index({ region: 1 })
ShopSchema.index({ managerId: 1 })
ShopSchema.index({ status: 1 })
ShopSchema.index({ 'coordinates.lat': 1, 'coordinates.lng': 1 })

// Compound index for location-based queries
ShopSchema.index({ country: 1, region: 1, location: 1 })

const Shop = mongoose.models.Shop || mongoose.model<IShop>('Shop', ShopSchema)

export default Shop
