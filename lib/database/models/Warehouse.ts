import mongoose, { Document, Schema } from 'mongoose'

export interface IWarehouse extends Document {
  _id: string
  name: string
  code: string // Unique warehouse code (e.g., WH001, WH002)
  description?: string
  type: 'main' | 'branch' | 'storage' | 'distribution'
  branchId?: string // Optional - for branch-specific warehouses
  branchName?: string
  address: string
  capacity: number // Total storage capacity
  currentUtilization: number // Current usage percentage
  isActive: boolean
  manager?: string
  managerId?: string
  contactInfo: {
    phone?: string
    email?: string
  }
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  coordinates?: {
    lat: number
    lng: number
  }
  features: string[] // e.g., ['climate_controlled', 'security_cameras', 'loading_dock']
  createdAt: Date
  updatedAt: Date
}

const WarehouseSchema = new Schema<IWarehouse>({
  name: {
    type: String,
    required: [true, 'Warehouse name is required'],
    trim: true,
    maxlength: [100, 'Warehouse name cannot exceed 100 characters']
  },
  code: {
    type: String,
    required: [true, 'Warehouse code is required'],
    unique: true,
    trim: true,
    uppercase: true,
    maxlength: [20, 'Warehouse code cannot exceed 20 characters'],
    match: [/^[A-Z0-9]+$/, 'Warehouse code must contain only uppercase letters and numbers']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  type: {
    type: String,
    enum: ['main', 'branch', 'storage', 'distribution'],
    required: [true, 'Warehouse type is required'],
    default: 'storage'
  },
  branchId: {
    type: String,
    ref: 'Shop',
    required: function(this: IWarehouse) {
      return this.type === 'branch'
    }
  },
  branchName: {
    type: String,
    trim: true,
    maxlength: [100, 'Branch name cannot exceed 100 characters'],
    required: function(this: IWarehouse) {
      return this.type === 'branch'
    }
  },
  address: {
    type: String,
    required: [true, 'Warehouse address is required'],
    trim: true,
    maxlength: [200, 'Address cannot exceed 200 characters']
  },
  capacity: {
    type: Number,
    required: [true, 'Warehouse capacity is required'],
    min: [1, 'Capacity must be greater than 0']
  },
  currentUtilization: {
    type: Number,
    default: 0,
    min: [0, 'Utilization cannot be negative'],
    max: [100, 'Utilization cannot exceed 100%']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  manager: {
    type: String,
    trim: true,
    maxlength: [100, 'Manager name cannot exceed 100 characters']
  },
  managerId: {
    type: String,
    ref: 'User'
  },
  contactInfo: {
    phone: {
      type: String,
      trim: true,
      maxlength: [20, 'Phone number cannot exceed 20 characters']
    },
    email: {
      type: String,
      trim: true,
      lowercase: true,
      maxlength: [100, 'Email cannot exceed 100 characters'],
      match: [/^\S+@\S+\.\S+$/, 'Please provide a valid email address']
    }
  },
  operatingHours: {
    open: {
      type: String,
      required: [true, 'Opening time is required'],
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please provide time in HH:MM format']
    },
    close: {
      type: String,
      required: [true, 'Closing time is required'],
      match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please provide time in HH:MM format']
    },
    timezone: {
      type: String,
      required: [true, 'Timezone is required'],
      default: 'Africa/Blantyre'
    }
  },
  coordinates: {
    lat: {
      type: Number,
      min: [-90, 'Latitude must be between -90 and 90'],
      max: [90, 'Latitude must be between -90 and 90']
    },
    lng: {
      type: Number,
      min: [-180, 'Longitude must be between -180 and 180'],
      max: [180, 'Longitude must be between -180 and 180']
    }
  },
  features: [{
    type: String,
    enum: [
      'climate_controlled',
      'security_cameras',
      'loading_dock',
      'fire_suppression',
      'backup_power',
      'refrigerated',
      'hazmat_certified',
      'automated_systems'
    ]
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Indexes for performance (code already indexed via unique: true)
WarehouseSchema.index({ branchId: 1 })
WarehouseSchema.index({ type: 1 })
WarehouseSchema.index({ isActive: 1 })
WarehouseSchema.index({ managerId: 1 })

// Compound indexes
WarehouseSchema.index({ type: 1, isActive: 1 })
WarehouseSchema.index({ branchId: 1, isActive: 1 })

// Text index for search
WarehouseSchema.index({
  name: 'text',
  code: 'text',
  description: 'text',
  address: 'text'
})

// Pre-save middleware to validate operating hours
WarehouseSchema.pre('save', function(next) {
  if (this.operatingHours.open >= this.operatingHours.close) {
    next(new Error('Opening time must be before closing time'))
  } else {
    next()
  }
})

const Warehouse = mongoose.models.Warehouse || mongoose.model<IWarehouse>('Warehouse', WarehouseSchema)

export default Warehouse
