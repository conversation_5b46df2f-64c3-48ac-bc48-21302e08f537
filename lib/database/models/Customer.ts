import mongoose, { Document, Schema } from 'mongoose'

export interface ICustomer extends Document {
  _id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  address: {
    street: string
    city: string
    region: string
    country: string
    postalCode: string
  }
  preferredBranch?: string
  totalOrders: number
  totalSpent: number
  loyaltyPoints: number
  isActive: boolean
  lastOrderDate?: Date
  createdAt: Date
  updatedAt: Date
}

const CustomerAddressSchema = new Schema({
  street: {
    type: String,
    required: [true, 'Street address is required'],
    trim: true,
    maxlength: [200, 'Street address cannot exceed 200 characters']
  },
  city: {
    type: String,
    required: [true, 'City is required'],
    trim: true,
    maxlength: [50, 'City cannot exceed 50 characters']
  },
  region: {
    type: String,
    required: [true, 'Region is required'],
    trim: true,
    maxlength: [50, 'Region cannot exceed 50 characters']
  },
  country: {
    type: String,
    required: [true, 'Country is required'],
    trim: true,
    maxlength: [50, 'Country cannot exceed 50 characters']
  },
  postalCode: {
    type: String,
    required: [true, 'Postal code is required'],
    trim: true,
    maxlength: [20, 'Postal code cannot exceed 20 characters']
  }
}, { _id: false })

const CustomerSchema = new Schema<ICustomer>({
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot exceed 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
  },
  address: {
    type: CustomerAddressSchema,
    required: [true, 'Address is required']
  },
  preferredBranch: {
    type: String,
    ref: 'Shop'
  },
  totalOrders: {
    type: Number,
    default: 0,
    min: [0, 'Total orders cannot be negative']
  },
  totalSpent: {
    type: Number,
    default: 0,
    min: [0, 'Total spent cannot be negative']
  },
  loyaltyPoints: {
    type: Number,
    default: 0,
    min: [0, 'Loyalty points cannot be negative']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastOrderDate: {
    type: Date,
    default: null
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Virtual for full name
CustomerSchema.virtual('fullName').get(function(this: ICustomer) {
  return `${this.firstName} ${this.lastName}`
})

// Indexes for performance (email already indexed via unique: true)
CustomerSchema.index({ phone: 1 })
CustomerSchema.index({ preferredBranch: 1 })
CustomerSchema.index({ isActive: 1 })
CustomerSchema.index({ totalSpent: -1 })
CustomerSchema.index({ loyaltyPoints: -1 })
CustomerSchema.index({ lastOrderDate: -1 })

// Compound indexes for common queries
CustomerSchema.index({ isActive: 1, totalSpent: -1 })
CustomerSchema.index({ preferredBranch: 1, isActive: 1 })

// Text index for search functionality
CustomerSchema.index({
  firstName: 'text',
  lastName: 'text',
  email: 'text'
})

const Customer = mongoose.models.Customer || mongoose.model<ICustomer>('Customer', CustomerSchema)

export default Customer
