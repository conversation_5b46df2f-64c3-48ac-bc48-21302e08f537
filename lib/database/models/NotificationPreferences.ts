import mongoose, { Schema, Document } from 'mongoose'
import type { 
  NotificationPreferences, 
  NotificationCategory, 
  NotificationPriority,
  NotificationChannel
} from '@/types/notifications'

// Notification preferences document interface
export interface INotificationPreferences extends Document {
  userId: string
  
  // Channel preferences
  channels: {
    in_app: boolean
    email: boolean
    sms: boolean
    push: boolean
  }
  
  // Category preferences
  categories: {
    [K in NotificationCategory]: {
      enabled: boolean
      priority: NotificationPriority[]
      channels: NotificationChannel[]
      quietHours?: {
        start: string
        end: string
        timezone: string
      }
    }
  }
  
  // Frequency settings
  frequency: {
    immediate: NotificationCategory[]
    digest: NotificationCategory[]
    weekly: NotificationCategory[]
  }
  
  // Advanced settings
  settings: {
    groupSimilar: boolean
    autoArchive: boolean
    autoArchiveDays: number
    soundEnabled: boolean
    vibrationEnabled: boolean
  }
  
  // Methods
  isChannelEnabled(channel: NotificationChannel): boolean
  isCategoryEnabled(category: NotificationCategory): boolean
  shouldReceiveNotification(category: NotificationCategory, priority: NotificationPriority, channel: NotificationChannel): boolean
  isInQuietHours(category: NotificationCategory): boolean
}

// Category preference schema
const CategoryPreferenceSchema = new Schema({
  enabled: { type: Boolean, default: true },
  priority: [{
    type: String,
    enum: ['low', 'medium', 'high', 'critical', 'urgent'],
    default: ['medium', 'high', 'critical', 'urgent']
  }],
  channels: [{
    type: String,
    enum: ['in_app', 'email', 'sms', 'push'],
    default: ['in_app']
  }],
  quietHours: {
    start: { type: String }, // HH:mm format
    end: { type: String },   // HH:mm format
    timezone: { type: String, default: 'UTC' }
  }
}, { _id: false })

// Main notification preferences schema
const NotificationPreferencesSchema = new Schema<INotificationPreferences>({
  userId: {
    type: String,
    required: true,
    unique: true
  },
  
  // Channel preferences
  channels: {
    in_app: { type: Boolean, default: true },
    email: { type: Boolean, default: true },
    sms: { type: Boolean, default: false },
    push: { type: Boolean, default: true }
  },
  
  // Category preferences with defaults
  categories: {
    system: {
      type: CategoryPreferenceSchema,
      default: {
        enabled: true,
        priority: ['medium', 'high', 'critical', 'urgent'],
        channels: ['in_app', 'email']
      }
    },
    security: {
      type: CategoryPreferenceSchema,
      default: {
        enabled: true,
        priority: ['high', 'critical', 'urgent'],
        channels: ['in_app', 'email', 'push']
      }
    },
    inventory: {
      type: CategoryPreferenceSchema,
      default: {
        enabled: true,
        priority: ['medium', 'high', 'critical'],
        channels: ['in_app']
      }
    },
    orders: {
      type: CategoryPreferenceSchema,
      default: {
        enabled: true,
        priority: ['low', 'medium', 'high'],
        channels: ['in_app', 'email']
      }
    },
    delivery: {
      type: CategoryPreferenceSchema,
      default: {
        enabled: true,
        priority: ['low', 'medium', 'high'],
        channels: ['in_app', 'push']
      }
    },
    campaigns: {
      type: CategoryPreferenceSchema,
      default: {
        enabled: true,
        priority: ['low', 'medium'],
        channels: ['in_app']
      }
    },
    users: {
      type: CategoryPreferenceSchema,
      default: {
        enabled: true,
        priority: ['medium', 'high'],
        channels: ['in_app']
      }
    },
    financial: {
      type: CategoryPreferenceSchema,
      default: {
        enabled: true,
        priority: ['medium', 'high', 'critical'],
        channels: ['in_app', 'email']
      }
    },
    reports: {
      type: CategoryPreferenceSchema,
      default: {
        enabled: true,
        priority: ['low', 'medium'],
        channels: ['in_app', 'email']
      }
    },
    maintenance: {
      type: CategoryPreferenceSchema,
      default: {
        enabled: true,
        priority: ['medium', 'high', 'critical'],
        channels: ['in_app', 'email']
      }
    }
  },
  
  // Frequency settings
  frequency: {
    immediate: [{
      type: String,
      enum: ['system', 'security', 'inventory', 'orders', 'delivery', 'campaigns', 'users', 'financial', 'reports', 'maintenance'],
      default: ['security', 'orders', 'delivery']
    }],
    digest: [{
      type: String,
      enum: ['system', 'security', 'inventory', 'orders', 'delivery', 'campaigns', 'users', 'financial', 'reports', 'maintenance'],
      default: ['inventory', 'users', 'reports']
    }],
    weekly: [{
      type: String,
      enum: ['system', 'security', 'inventory', 'orders', 'delivery', 'campaigns', 'users', 'financial', 'reports', 'maintenance'],
      default: ['campaigns', 'maintenance']
    }]
  },
  
  // Advanced settings
  settings: {
    groupSimilar: { type: Boolean, default: true },
    autoArchive: { type: Boolean, default: true },
    autoArchiveDays: { type: Number, default: 30, min: 1, max: 365 },
    soundEnabled: { type: Boolean, default: true },
    vibrationEnabled: { type: Boolean, default: true }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Instance methods
NotificationPreferencesSchema.methods.isChannelEnabled = function(channel: NotificationChannel): boolean {
  return this.channels[channel] === true
}

NotificationPreferencesSchema.methods.isCategoryEnabled = function(category: NotificationCategory): boolean {
  return this.categories[category]?.enabled === true
}

NotificationPreferencesSchema.methods.shouldReceiveNotification = function(
  category: NotificationCategory, 
  priority: NotificationPriority, 
  channel: NotificationChannel
): boolean {
  // Check if channel is globally enabled
  if (!this.isChannelEnabled(channel)) {
    return false
  }
  
  // Check if category is enabled
  if (!this.isCategoryEnabled(category)) {
    return false
  }
  
  const categoryPrefs = this.categories[category]
  
  // Check if priority is allowed for this category
  if (!categoryPrefs.priority.includes(priority)) {
    return false
  }
  
  // Check if channel is allowed for this category
  if (!categoryPrefs.channels.includes(channel)) {
    return false
  }
  
  // Check quiet hours
  if (this.isInQuietHours(category)) {
    // Only allow critical and urgent notifications during quiet hours
    return priority === 'critical' || priority === 'urgent'
  }
  
  return true
}

NotificationPreferencesSchema.methods.isInQuietHours = function(category: NotificationCategory): boolean {
  const categoryPrefs = this.categories[category]
  
  if (!categoryPrefs.quietHours || !categoryPrefs.quietHours.start || !categoryPrefs.quietHours.end) {
    return false
  }
  
  const now = new Date()
  const timezone = categoryPrefs.quietHours.timezone || 'UTC'
  
  // Convert current time to the specified timezone
  const currentTime = new Intl.DateTimeFormat('en-US', {
    timeZone: timezone,
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  }).format(now)
  
  const startTime = categoryPrefs.quietHours.start
  const endTime = categoryPrefs.quietHours.end
  
  // Handle quiet hours that span midnight
  if (startTime > endTime) {
    return currentTime >= startTime || currentTime <= endTime
  } else {
    return currentTime >= startTime && currentTime <= endTime
  }
}

// Static methods
NotificationPreferencesSchema.statics.findByUserId = function(userId: string) {
  return this.findOne({ userId })
}

NotificationPreferencesSchema.statics.createDefault = function(userId: string) {
  return this.create({ userId })
}

NotificationPreferencesSchema.statics.getOrCreateForUser = async function(userId: string) {
  let preferences = await this.findByUserId(userId)
  
  if (!preferences) {
    preferences = await this.createDefault(userId)
  }
  
  return preferences
}

// Pre-save middleware to ensure all categories have default values
NotificationPreferencesSchema.pre('save', function(next) {
  const categories: NotificationCategory[] = [
    'system', 'security', 'inventory', 'orders', 'delivery', 
    'campaigns', 'users', 'financial', 'reports', 'maintenance'
  ]
  
  categories.forEach(category => {
    if (!this.categories[category]) {
      // Set default values based on category type
      const isHighPriority = ['security', 'system', 'financial'].includes(category)
      const isUserFacing = ['orders', 'delivery', 'campaigns'].includes(category)
      
      this.categories[category] = {
        enabled: true,
        priority: isHighPriority 
          ? ['medium', 'high', 'critical', 'urgent']
          : ['low', 'medium', 'high'],
        channels: isUserFacing 
          ? ['in_app', 'push']
          : ['in_app']
      }
    }
  })
  
  next()
})

// Export the model
const NotificationPreferences = mongoose.models.NotificationPreferences ||
  mongoose.model<INotificationPreferences>('NotificationPreferences', NotificationPreferencesSchema)

export default NotificationPreferences
export type { INotificationPreferences }
