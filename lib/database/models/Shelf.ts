import mongoose, { Document, Schema } from 'mongoose'

export interface IShelf extends Document {
  _id: string
  code: string // Unique shelf code (e.g., A1-B2, WH001-A1-B2)
  name: string
  warehouseId: string
  warehouseName: string
  warehouseCode: string
  section: string // Section within warehouse (e.g., A, B, C)
  row: string // Row within section (e.g., 1, 2, 3)
  position: string // Position within row (e.g., A, B, C)
  level: number // Shelf level (1 = ground, 2 = second level, etc.)
  description?: string
  capacity: number // Maximum items this shelf can hold
  currentOccupancy: number // Current number of items
  dimensions: {
    width: number // in centimeters
    height: number // in centimeters
    depth: number // in centimeters
  }
  weightLimit: number // Maximum weight in kg
  currentWeight: number // Current weight in kg
  shelfType: 'standard' | 'refrigerated' | 'hazmat' | 'fragile' | 'bulk'
  accessLevel: 'ground' | 'ladder' | 'forklift' | 'crane'
  isActive: boolean
  isReserved: boolean
  reservedFor?: string // Product ID or purpose
  lastInventoryCheck?: Date
  notes?: string
  barcode?: string // For scanning systems
  qrCode?: string // For QR code systems
  createdAt: Date
  updatedAt: Date
}

const ShelfSchema = new Schema<IShelf>({
  code: {
    type: String,
    required: [true, 'Shelf code is required'],
    unique: true,
    trim: true,
    uppercase: true,
    maxlength: [30, 'Shelf code cannot exceed 30 characters'],
    match: [/^[A-Z0-9\-]+$/, 'Shelf code must contain only uppercase letters, numbers, and hyphens']
  },
  name: {
    type: String,
    required: [true, 'Shelf name is required'],
    trim: true,
    maxlength: [100, 'Shelf name cannot exceed 100 characters']
  },
  warehouseId: {
    type: String,
    required: [true, 'Warehouse ID is required'],
    ref: 'Warehouse'
  },
  warehouseName: {
    type: String,
    required: [true, 'Warehouse name is required'],
    trim: true,
    maxlength: [100, 'Warehouse name cannot exceed 100 characters']
  },
  warehouseCode: {
    type: String,
    required: [true, 'Warehouse code is required'],
    trim: true,
    uppercase: true,
    maxlength: [20, 'Warehouse code cannot exceed 20 characters']
  },
  section: {
    type: String,
    required: [true, 'Section is required'],
    trim: true,
    uppercase: true,
    maxlength: [10, 'Section cannot exceed 10 characters'],
    match: [/^[A-Z0-9]+$/, 'Section must contain only uppercase letters and numbers']
  },
  row: {
    type: String,
    required: [true, 'Row is required'],
    trim: true,
    maxlength: [10, 'Row cannot exceed 10 characters']
  },
  position: {
    type: String,
    required: [true, 'Position is required'],
    trim: true,
    uppercase: true,
    maxlength: [10, 'Position cannot exceed 10 characters'],
    match: [/^[A-Z0-9]+$/, 'Position must contain only uppercase letters and numbers']
  },
  level: {
    type: Number,
    required: [true, 'Level is required'],
    min: [1, 'Level must be at least 1'],
    max: [10, 'Level cannot exceed 10']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [300, 'Description cannot exceed 300 characters']
  },
  capacity: {
    type: Number,
    required: [true, 'Capacity is required'],
    min: [1, 'Capacity must be greater than 0']
  },
  currentOccupancy: {
    type: Number,
    default: 0,
    min: [0, 'Occupancy cannot be negative'],
    validate: {
      validator: function(this: IShelf, value: number) {
        return value <= this.capacity
      },
      message: 'Current occupancy cannot exceed capacity'
    }
  },
  dimensions: {
    width: {
      type: Number,
      required: [true, 'Width is required'],
      min: [1, 'Width must be greater than 0']
    },
    height: {
      type: Number,
      required: [true, 'Height is required'],
      min: [1, 'Height must be greater than 0']
    },
    depth: {
      type: Number,
      required: [true, 'Depth is required'],
      min: [1, 'Depth must be greater than 0']
    }
  },
  weightLimit: {
    type: Number,
    required: [true, 'Weight limit is required'],
    min: [1, 'Weight limit must be greater than 0']
  },
  currentWeight: {
    type: Number,
    default: 0,
    min: [0, 'Current weight cannot be negative'],
    validate: {
      validator: function(this: IShelf, value: number) {
        return value <= this.weightLimit
      },
      message: 'Current weight cannot exceed weight limit'
    }
  },
  shelfType: {
    type: String,
    enum: ['standard', 'refrigerated', 'hazmat', 'fragile', 'bulk'],
    required: [true, 'Shelf type is required'],
    default: 'standard'
  },
  accessLevel: {
    type: String,
    enum: ['ground', 'ladder', 'forklift', 'crane'],
    required: [true, 'Access level is required'],
    default: 'ground'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isReserved: {
    type: Boolean,
    default: false
  },
  reservedFor: {
    type: String,
    trim: true,
    maxlength: [100, 'Reserved for cannot exceed 100 characters']
  },
  lastInventoryCheck: {
    type: Date,
    default: null
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [500, 'Notes cannot exceed 500 characters']
  },
  barcode: {
    type: String,
    trim: true,
    maxlength: [50, 'Barcode cannot exceed 50 characters']
  },
  qrCode: {
    type: String,
    trim: true,
    maxlength: [100, 'QR code cannot exceed 100 characters']
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id
      delete ret._id
      delete ret.__v
      return ret
    }
  }
})

// Indexes for performance (code already indexed via unique: true)
ShelfSchema.index({ warehouseId: 1 })
ShelfSchema.index({ warehouseCode: 1 })
ShelfSchema.index({ section: 1 })
ShelfSchema.index({ isActive: 1 })
ShelfSchema.index({ isReserved: 1 })
ShelfSchema.index({ shelfType: 1 })

// Compound indexes
ShelfSchema.index({ warehouseId: 1, section: 1, row: 1, position: 1, level: 1 }, { unique: true })
ShelfSchema.index({ warehouseId: 1, isActive: 1 })
ShelfSchema.index({ warehouseId: 1, shelfType: 1 })
ShelfSchema.index({ section: 1, row: 1, position: 1 })

// Text index for search
ShelfSchema.index({
  code: 'text',
  name: 'text',
  description: 'text',
  notes: 'text'
})

// Pre-save middleware to generate shelf code if not provided
ShelfSchema.pre('save', function(next) {
  if (!this.code) {
    this.code = `${this.warehouseCode}-${this.section}${this.row}-${this.position}-L${this.level}`
  }
  next()
})

// Virtual for occupancy percentage
ShelfSchema.virtual('occupancyPercentage').get(function(this: IShelf) {
  return this.capacity > 0 ? Math.round((this.currentOccupancy / this.capacity) * 100) : 0
})

// Virtual for weight percentage
ShelfSchema.virtual('weightPercentage').get(function(this: IShelf) {
  return this.weightLimit > 0 ? Math.round((this.currentWeight / this.weightLimit) * 100) : 0
})

// Virtual for availability status
ShelfSchema.virtual('availabilityStatus').get(function(this: IShelf) {
  if (!this.isActive) return 'inactive'
  if (this.isReserved) return 'reserved'
  if (this.currentOccupancy >= this.capacity) return 'full'
  if (this.currentOccupancy > this.capacity * 0.8) return 'nearly_full'
  return 'available'
})

const Shelf = mongoose.models.Shelf || mongoose.model<IShelf>('Shelf', ShelfSchema)

export default Shelf
