import { NotificationService } from './notification-service'
import type {
  CreateNotificationOptions
} from '@/lib/services/notification-service'

/**
 * Helper functions to create business event notifications
 */
export class NotificationHelpers {

  // INVENTORY NOTIFICATIONS
  static async notifyLowStock(productData: {
    productId: string
    productName: string
    sku: string
    currentStock: number
    minimumStock: number
    branchId?: string
    branchName?: string
  }, createdBy: string) {
    const options: CreateNotificationOptions = {
      category: 'inventory',
      type: 'stock_low',
      priority: 'high',
      title: `⚠️ Low Stock Alert: ${productData.productName}`,
      message: `${productData.productName} is running low with only ${productData.currentStock} units remaining (minimum: ${productData.minimumStock})`,
      description: `Immediate restocking required for ${productData.sku} at ${productData.branchName || 'main warehouse'}`,
      icon: '📦',
      color: 'orange',
      target: {
        roles: ['overall_admin', 'branch_manager'],
        branchIds: productData.branchId ? [productData.branchId] : undefined
      },
      data: productData,
      actions: [
        {
          id: 'view_product',
          label: 'View Product',
          type: 'link',
          variant: 'outline',
          url: `/dashboard/inventory/products/${productData.productId}`
        },
        {
          id: 'reorder',
          label: 'Reorder Now',
          type: 'button',
          variant: 'primary',
          action: 'reorder_product',
          data: { productId: productData.productId }
        }
      ],
      channels: ['in_app', 'email'],
      createdBy,
      source: 'inventory_system'
    }

    return await NotificationService.createNotification(options)
  }

  static async notifyOutOfStock(productData: {
    productId: string
    productName: string
    sku: string
    branchId?: string
    branchName?: string
  }, createdBy: string) {
    const options: CreateNotificationOptions = {
      category: 'inventory',
      type: 'stock_out',
      priority: 'critical',
      title: `🚨 Out of Stock: ${productData.productName}`,
      message: `${productData.productName} is completely out of stock`,
      description: `Urgent restocking required for ${productData.sku}. Product is currently unavailable for sale.`,
      icon: '❌',
      color: 'red',
      target: {
        roles: ['overall_admin', 'branch_manager'],
        branchIds: productData.branchId ? [productData.branchId] : undefined
      },
      data: productData,
      actions: [
        {
          id: 'emergency_reorder',
          label: 'Emergency Reorder',
          type: 'button',
          variant: 'destructive',
          action: 'emergency_reorder',
          data: { productId: productData.productId }
        }
      ],
      channels: ['in_app', 'email', 'push'],
      createdBy,
      source: 'inventory_system'
    }

    return await NotificationService.createNotification(options)
  }

  static async notifyProductAdded(productData: {
    productId: string
    productName: string
    sku: string
    category: string
    addedBy: string
    branchId?: string
  }, createdBy: string) {
    const options: CreateNotificationOptions = {
      category: 'inventory',
      type: 'product_added',
      priority: 'medium',
      title: `✅ New Product Added: ${productData.productName}`,
      message: `${productData.productName} has been added to inventory`,
      description: `Product ${productData.sku} in category ${productData.category} is now available`,
      icon: '🆕',
      color: 'green',
      target: {
        roles: ['overall_admin', 'branch_manager', 'employee']
      },
      data: productData,
      actions: [
        {
          id: 'view_product',
          label: 'View Product',
          type: 'link',
          variant: 'outline',
          url: `/dashboard/inventory/products/${productData.productId}`
        }
      ],
      channels: ['in_app'],
      createdBy,
      source: 'inventory_system'
    }

    return await NotificationService.createNotification(options)
  }

  // ORDER NOTIFICATIONS
  static async notifyOrderPlaced(orderData: {
    orderId: string
    orderNumber: string
    customerId: string
    customerName: string
    customerEmail: string
    totalAmount: number
    currency: string
    items: Array<{
      productId: string
      productName: string
      quantity: number
      price: number
    }>
    branchId?: string
  }, createdBy: string) {
    const options: CreateNotificationOptions = {
      category: 'orders',
      type: 'order_placed',
      priority: 'medium',
      title: `🛒 New Order: #${orderData.orderNumber}`,
      message: `Order placed by ${orderData.customerName} for ${orderData.currency} ${orderData.totalAmount}`,
      description: `${orderData.items.length} items ordered. Customer: ${orderData.customerEmail}`,
      icon: '🛍️',
      color: 'blue',
      target: {
        roles: ['overall_admin', 'branch_manager'],
        branchIds: orderData.branchId ? [orderData.branchId] : undefined
      },
      data: orderData,
      actions: [
        {
          id: 'view_order',
          label: 'View Order',
          type: 'link',
          variant: 'primary',
          url: `/dashboard/orders/${orderData.orderId}`
        },
        {
          id: 'process_order',
          label: 'Process',
          type: 'button',
          variant: 'outline',
          action: 'process_order',
          data: { orderId: orderData.orderId }
        }
      ],
      channels: ['in_app', 'push'],
      createdBy,
      source: 'order_system'
    }

    return await NotificationService.createNotification(options)
  }

  static async notifyOrderStatusChange(orderData: {
    orderId: string
    orderNumber: string
    customerId: string
    customerName: string
    status: string
    previousStatus: string
  }, createdBy: string) {
    const statusEmojis = {
      confirmed: '✅',
      processing: '⚙️',
      shipped: '📦',
      delivered: '🎉',
      cancelled: '❌'
    }

    const options: CreateNotificationOptions = {
      category: 'orders',
      type: 'order_confirmed',
      priority: 'medium',
      title: `${statusEmojis[orderData.status] || '📋'} Order ${orderData.status}: #${orderData.orderNumber}`,
      message: `Order status changed from ${orderData.previousStatus} to ${orderData.status}`,
      description: `Customer ${orderData.customerName} will be notified of this status change`,
      target: {
        userIds: [orderData.customerId],
        roles: ['overall_admin', 'branch_manager']
      },
      data: orderData,
      actions: [
        {
          id: 'view_order',
          label: 'View Order',
          type: 'link',
          variant: 'outline',
          url: `/dashboard/orders/${orderData.orderId}`
        }
      ],
      channels: ['in_app', 'email'],
      createdBy,
      source: 'order_system'
    }

    return await NotificationService.createNotification(options)
  }

  // DELIVERY NOTIFICATIONS
  static async notifyDeliveryStatusChange(deliveryData: {
    deliveryId: string
    orderId: string
    orderNumber: string
    customerId: string
    customerName: string
    status: string
    trackingNumber: string
    estimatedDelivery?: Date
    currentLocation?: {
      lat: number
      lng: number
      address: string
    }
  }, createdBy: string) {
    const statusMessages = {
      assigned: 'Delivery has been assigned to a driver',
      picked_up: 'Package has been picked up',
      in_transit: 'Package is on the way',
      out_for_delivery: 'Package is out for delivery',
      delivered: 'Package has been delivered',
      failed: 'Delivery attempt failed'
    }

    const options: CreateNotificationOptions = {
      category: 'delivery',
      type: 'delivery_in_transit',
      priority: deliveryData.status === 'delivered' ? 'high' : 'medium',
      title: `🚚 Delivery Update: #${deliveryData.orderNumber}`,
      message: statusMessages[deliveryData.status] || `Delivery status: ${deliveryData.status}`,
      description: `Tracking: ${deliveryData.trackingNumber}${deliveryData.currentLocation ? ` | Location: ${deliveryData.currentLocation.address}` : ''}`,
      target: {
        userIds: [deliveryData.customerId],
        roles: ['overall_admin', 'branch_manager']
      },
      data: deliveryData,
      actions: [
        {
          id: 'track_delivery',
          label: 'Track Package',
          type: 'link',
          variant: 'primary',
          url: `/track/${deliveryData.trackingNumber}`
        }
      ],
      channels: ['in_app', 'push', 'sms'],
      createdBy,
      source: 'delivery_system'
    }

    return await NotificationService.createNotification(options)
  }

  // CAMPAIGN NOTIFICATIONS
  static async notifyCampaignLaunch(campaignData: {
    campaignId: string
    campaignName: string
    campaignType: 'sale' | 'discount' | 'offer' | 'competition'
    discountPercentage?: number
    validUntil?: Date
    promoCode?: string
    targetAudience: string[]
  }, createdBy: string) {
    const typeEmojis = {
      sale: '🔥',
      discount: '💰',
      offer: '🎁',
      competition: '🏆'
    }

    const options: CreateNotificationOptions = {
      category: 'campaigns',
      type: 'sale_started',
      priority: 'low',
      title: `${typeEmojis[campaignData.campaignType]} ${campaignData.campaignName}`,
      message: `New ${campaignData.campaignType} is now live!${campaignData.discountPercentage ? ` Save ${campaignData.discountPercentage}%` : ''}`,
      description: `${campaignData.promoCode ? `Use code: ${campaignData.promoCode}` : ''} ${campaignData.validUntil ? `Valid until ${campaignData.validUntil.toLocaleDateString()}` : ''}`,
      icon: '🎉',
      color: 'pink',
      target: {
        roles: campaignData.targetAudience.includes('all') ? ['customer'] : campaignData.targetAudience as any
      },
      data: campaignData,
      actions: [
        {
          id: 'shop_now',
          label: 'Shop Now',
          type: 'link',
          variant: 'primary',
          url: `/products?campaign=${campaignData.campaignId}`
        }
      ],
      channels: ['in_app', 'push'],
      expiresAt: campaignData.validUntil,
      createdBy,
      source: 'campaign_system'
    }

    return await NotificationService.createNotification(options)
  }

  // SECURITY NOTIFICATIONS
  static async notifySecurityAlert(securityData: {
    userId?: string
    username?: string
    alertType: string
    ipAddress?: string
    location?: string
    device?: string
    riskLevel: 'low' | 'medium' | 'high' | 'critical'
    details: string
  }, createdBy: string) {
    const riskEmojis = {
      low: '🔵',
      medium: '🟡',
      high: '🟠',
      critical: '🔴'
    }

    const options: CreateNotificationOptions = {
      category: 'security',
      type: 'suspicious_activity',
      priority: securityData.riskLevel === 'critical' ? 'urgent' : securityData.riskLevel as any,
      title: `${riskEmojis[securityData.riskLevel]} Security Alert: ${securityData.alertType}`,
      message: securityData.details,
      description: `${securityData.username ? `User: ${securityData.username}` : ''} ${securityData.ipAddress ? `IP: ${securityData.ipAddress}` : ''} ${securityData.location ? `Location: ${securityData.location}` : ''}`,
      target: {
        roles: ['overall_admin'],
        userIds: securityData.userId ? [securityData.userId] : undefined
      },
      data: securityData,
      actions: [
        {
          id: 'investigate',
          label: 'Investigate',
          type: 'link',
          variant: 'destructive',
          url: `/dashboard/security/alerts`
        }
      ],
      channels: ['in_app', 'email', 'push'],
      createdBy,
      source: 'security_system'
    }

    return await NotificationService.createNotification(options)
  }

  // USER NOTIFICATIONS
  static async notifyUserRegistration(userData: {
    userId: string
    username: string
    email: string
    role: string
    registrationDate: Date
  }, createdBy: string) {
    const options: CreateNotificationOptions = {
      category: 'users',
      type: 'user_registered',
      priority: 'medium',
      title: `👋 New User Registered: ${userData.username}`,
      message: `${userData.username} (${userData.email}) has registered as ${userData.role}`,
      target: {
        roles: ['overall_admin']
      },
      data: userData,
      actions: [
        {
          id: 'view_user',
          label: 'View Profile',
          type: 'link',
          variant: 'outline',
          url: `/dashboard/users/${userData.userId}`
        }
      ],
      channels: ['in_app'],
      createdBy,
      source: 'user_system'
    }

    return await NotificationService.createNotification(options)
  }

  // FINANCIAL NOTIFICATIONS
  static async notifyPaymentReceived(paymentData: {
    transactionId: string
    amount: number
    currency: string
    customerId: string
    customerName: string
    orderId?: string
    paymentMethod: string
  }, createdBy: string) {
    const options: CreateNotificationOptions = {
      category: 'financial',
      type: 'payment_received',
      priority: 'medium',
      title: `💰 Payment Received: ${paymentData.currency} ${paymentData.amount}`,
      message: `Payment from ${paymentData.customerName} via ${paymentData.paymentMethod}`,
      description: `Transaction ID: ${paymentData.transactionId}${paymentData.orderId ? ` | Order: ${paymentData.orderId}` : ''}`,
      target: {
        roles: ['overall_admin', 'branch_manager']
      },
      data: paymentData,
      actions: [
        {
          id: 'view_transaction',
          label: 'View Transaction',
          type: 'link',
          variant: 'outline',
          url: `/dashboard/financial/transactions/${paymentData.transactionId}`
        }
      ],
      channels: ['in_app'],
      createdBy,
      source: 'payment_system'
    }

    return await NotificationService.createNotification(options)
  }

  // SYSTEM NOTIFICATIONS
  static async notifySystemMaintenance(maintenanceData: {
    maintenanceId: string
    scheduledStart: Date
    scheduledEnd: Date
    affectedServices: string[]
    impact: 'low' | 'medium' | 'high'
  }, createdBy: string) {
    const options: CreateNotificationOptions = {
      category: 'maintenance',
      type: 'maintenance_scheduled',
      priority: maintenanceData.impact === 'high' ? 'high' : 'medium',
      title: `🔧 Scheduled Maintenance: ${maintenanceData.scheduledStart.toLocaleDateString()}`,
      message: `System maintenance scheduled from ${maintenanceData.scheduledStart.toLocaleString()} to ${maintenanceData.scheduledEnd.toLocaleString()}`,
      description: `Affected services: ${maintenanceData.affectedServices.join(', ')}`,
      target: {
        roles: ['overall_admin', 'branch_manager', 'employee', 'customer']
      },
      data: maintenanceData,
      channels: ['in_app', 'email'],
      scheduledFor: new Date(maintenanceData.scheduledStart.getTime() - 24 * 60 * 60 * 1000), // 24 hours before
      createdBy,
      source: 'system'
    }

    return await NotificationService.createNotification(options)
  }
}
