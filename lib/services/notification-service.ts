import { connectDB } from '@/lib/database'
import Notification, { type INotification } from '@/lib/database/models/Notification'
import NotificationPreferences, { type INotificationPreferences } from '@/lib/database/models/NotificationPreferences'
import User from '@/lib/database/models/User'
import { socketManager } from '@/lib/socket/server'
import type { 
  PlatformNotification, 
  NotificationCategory, 
  NotificationPriority,
  NotificationChannel,
  NotificationTarget,
  NotificationAction,
  UserRole
} from '@/types/notifications'

export interface CreateNotificationOptions {
  category: NotificationCategory
  type: string
  priority?: NotificationPriority
  title: string
  message: string
  description?: string
  
  // Rich content
  icon?: string
  image?: string
  color?: string
  badge?: string
  
  // Targeting
  target: NotificationTarget
  
  // Metadata
  data?: Record<string, any>
  actions?: NotificationAction[]
  
  // Scheduling
  scheduledFor?: Date
  expiresAt?: Date
  
  // Delivery
  channels?: NotificationChannel[]
  
  // Tracking
  createdBy: string
  source?: string
}

export interface NotificationFilters {
  userId?: string
  category?: NotificationCategory
  type?: string
  priority?: NotificationPriority
  status?: string
  dateFrom?: Date
  dateTo?: Date
  limit?: number
  skip?: number
}

export class NotificationService {
  
  /**
   * Create and send a notification
   */
  static async createNotification(options: CreateNotificationOptions): Promise<INotification> {
    await connectDB()
    
    // Set default values
    const notificationData = {
      ...options,
      priority: options.priority || 'medium',
      channels: options.channels || ['in_app'],
      source: options.source || 'system',
      deliveryStatus: {}
    }
    
    // Initialize delivery status for each channel
    notificationData.channels.forEach(channel => {
      notificationData.deliveryStatus[channel] = 'pending'
    })
    
    // Create the notification
    const notification = new Notification(notificationData)
    await notification.save()
    
    // Send the notification immediately if not scheduled
    if (!options.scheduledFor || options.scheduledFor <= new Date()) {
      await this.deliverNotification(notification)
    }
    
    return notification
  }
  
  /**
   * Create and send notifications for multiple users
   */
  static async createBulkNotifications(
    options: Omit<CreateNotificationOptions, 'target'>,
    targets: NotificationTarget[]
  ): Promise<INotification[]> {
    const notifications: INotification[] = []
    
    for (const target of targets) {
      const notification = await this.createNotification({
        ...options,
        target
      })
      notifications.push(notification)
    }
    
    return notifications
  }
  
  /**
   * Deliver a notification through all specified channels
   */
  static async deliverNotification(notification: INotification): Promise<void> {
    // Get target users
    const targetUsers = await this.resolveTargetUsers(notification.target)
    
    for (const user of targetUsers) {
      // Get user preferences
      const preferences = await NotificationPreferences.getOrCreateForUser(user.id)
      
      // Check if user should receive this notification
      for (const channel of notification.channels) {
        if (preferences.shouldReceiveNotification(notification.category, notification.priority, channel)) {
          await this.deliverToChannel(notification, user, channel)
        }
      }
    }
  }
  
  /**
   * Deliver notification to a specific channel
   */
  static async deliverToChannel(
    notification: INotification, 
    user: any, 
    channel: NotificationChannel
  ): Promise<void> {
    try {
      switch (channel) {
        case 'in_app':
          await this.deliverInApp(notification, user)
          break
        case 'socket':
          await this.deliverSocket(notification, user)
          break
        case 'email':
          await this.deliverEmail(notification, user)
          break
        case 'sms':
          await this.deliverSMS(notification, user)
          break
        case 'push':
          await this.deliverPush(notification, user)
          break
      }
      
      // Mark as delivered
      await notification.markAsDelivered(channel)
    } catch (error) {
      console.error(`Failed to deliver notification ${notification.id} via ${channel}:`, error)
      
      // Mark as failed
      notification.deliveryStatus[channel] = 'failed'
      await notification.save()
    }
  }
  
  /**
   * Deliver in-app notification
   */
  static async deliverInApp(notification: INotification, user: any): Promise<void> {
    // In-app notifications are stored in database and retrieved by UI
    // No additional action needed here
  }
  
  /**
   * Deliver real-time socket notification
   */
  static async deliverSocket(notification: INotification, user: any): Promise<void> {
    const socketData = {
      id: notification.id,
      category: notification.category,
      type: notification.type,
      priority: notification.priority,
      title: notification.title,
      message: notification.message,
      description: notification.description,
      icon: notification.icon,
      image: notification.image,
      color: notification.color,
      badge: notification.badge,
      data: notification.data,
      actions: notification.actions,
      createdAt: notification.createdAt
    }

    // Send via Socket.IO using the socket manager
    socketManager.sendNotificationToUser(user.id, socketData)

    // Also update notification count
    const { notifications } = await this.getUserNotifications(user.id, { status: 'unread' })
    socketManager.updateNotificationCount(user.id, notifications.length, notifications.length)
  }
  
  /**
   * Deliver email notification
   */
  static async deliverEmail(notification: INotification, user: any): Promise<void> {
    // TODO: Implement email delivery
    // This would integrate with your email service (SendGrid, AWS SES, etc.)
    console.log(`Email notification sent to ${user.email}:`, notification.title)
  }
  
  /**
   * Deliver SMS notification
   */
  static async deliverSMS(notification: INotification, user: any): Promise<void> {
    // TODO: Implement SMS delivery
    // This would integrate with your SMS service (Twilio, AWS SNS, etc.)
    console.log(`SMS notification sent to ${user.phone}:`, notification.title)
  }
  
  /**
   * Deliver push notification
   */
  static async deliverPush(notification: INotification, user: any): Promise<void> {
    // TODO: Implement push notification delivery
    // This would integrate with FCM, APNs, or web push service
    console.log(`Push notification sent to ${user.id}:`, notification.title)
  }
  
  /**
   * Resolve target users from notification target
   */
  static async resolveTargetUsers(target: NotificationTarget): Promise<any[]> {
    await connectDB()
    
    const users: any[] = []
    
    // Direct user targeting
    if (target.userIds && target.userIds.length > 0) {
      const directUsers = await User.find({ _id: { $in: target.userIds } })
      users.push(...directUsers)
    }
    
    // Role-based targeting
    if (target.roles && target.roles.length > 0) {
      const roleUsers = await User.find({ role: { $in: target.roles } })
      users.push(...roleUsers)
    }
    
    // Branch-based targeting
    if (target.branchIds && target.branchIds.length > 0) {
      const branchUsers = await User.find({ branchId: { $in: target.branchIds } })
      users.push(...branchUsers)
    }
    
    // Remove duplicates
    const uniqueUsers = users.filter((user, index, self) => 
      index === self.findIndex(u => u.id === user.id)
    )
    
    return uniqueUsers
  }
  
  /**
   * Get notifications for a user
   */
  static async getUserNotifications(
    userId: string, 
    filters: Omit<NotificationFilters, 'userId'> = {}
  ): Promise<{ notifications: INotification[], total: number }> {
    await connectDB()
    
    // Get user to determine role
    const user = await User.findById(userId)
    if (!user) {
      throw new Error('User not found')
    }
    
    // Build query
    const query: any = {
      $or: [
        { 'target.userIds': userId },
        { 'target.roles': user.role }
      ]
    }
    
    // Add branch filter if user has a branch
    if (user.branchId) {
      query.$or.push({ 'target.branchIds': user.branchId })
    }
    
    // Apply additional filters
    if (filters.category) query.category = filters.category
    if (filters.type) query.type = filters.type
    if (filters.priority) query.priority = filters.priority
    if (filters.status) query.status = filters.status
    
    if (filters.dateFrom || filters.dateTo) {
      query.createdAt = {}
      if (filters.dateFrom) query.createdAt.$gte = filters.dateFrom
      if (filters.dateTo) query.createdAt.$lte = filters.dateTo
    }
    
    // Get total count
    const total = await Notification.countDocuments(query)
    
    // Get notifications
    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .limit(filters.limit || 50)
      .skip(filters.skip || 0)
    
    return { notifications, total }
  }
  
  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string, userId: string): Promise<INotification> {
    await connectDB()
    
    const notification = await Notification.findById(notificationId)
    if (!notification) {
      throw new Error('Notification not found')
    }
    
    // Verify user has access to this notification
    const hasAccess = await this.userHasAccessToNotification(userId, notification)
    if (!hasAccess) {
      throw new Error('Access denied')
    }
    
    return await notification.markAsRead()
  }
  
  /**
   * Mark multiple notifications as read
   */
  static async markMultipleAsRead(notificationIds: string[], userId: string): Promise<void> {
    await connectDB()
    
    for (const id of notificationIds) {
      try {
        await this.markAsRead(id, userId)
      } catch (error) {
        console.error(`Failed to mark notification ${id} as read:`, error)
      }
    }
  }
  
  /**
   * Check if user has access to notification
   */
  static async userHasAccessToNotification(userId: string, notification: INotification): Promise<boolean> {
    // Check direct user targeting
    if (notification.target.userIds?.includes(userId)) {
      return true
    }
    
    // Check role-based targeting
    const user = await User.findById(userId)
    if (user && notification.target.roles?.includes(user.role)) {
      return true
    }
    
    // Check branch-based targeting
    if (user?.branchId && notification.target.branchIds?.includes(user.branchId)) {
      return true
    }
    
    return false
  }
  
  /**
   * Process scheduled notifications
   */
  static async processScheduledNotifications(): Promise<void> {
    await connectDB()
    
    const scheduledNotifications = await Notification.findScheduled()
    
    for (const notification of scheduledNotifications) {
      if (notification.canBeDelivered()) {
        await this.deliverNotification(notification)
      }
    }
  }
  
  /**
   * Cleanup expired notifications
   */
  static async cleanupExpiredNotifications(): Promise<void> {
    await connectDB()
    await Notification.cleanupExpired()
  }
  
  /**
   * Get notification statistics
   */
  static async getNotificationStats(filters: NotificationFilters = {}): Promise<any> {
    await connectDB()
    
    const pipeline: any[] = []
    
    // Match stage
    const matchStage: any = {}
    if (filters.category) matchStage.category = filters.category
    if (filters.type) matchStage.type = filters.type
    if (filters.dateFrom || filters.dateTo) {
      matchStage.createdAt = {}
      if (filters.dateFrom) matchStage.createdAt.$gte = filters.dateFrom
      if (filters.dateTo) matchStage.createdAt.$lte = filters.dateTo
    }
    
    if (Object.keys(matchStage).length > 0) {
      pipeline.push({ $match: matchStage })
    }
    
    // Group and calculate stats
    pipeline.push({
      $group: {
        _id: null,
        total: { $sum: 1 },
        unread: { $sum: { $cond: [{ $eq: ['$status', 'unread'] }, 1, 0] } },
        read: { $sum: { $cond: [{ $eq: ['$status', 'read'] }, 1, 0] } },
        delivered: { $sum: '$deliveredCount' },
        opened: { $sum: '$openedCount' },
        clicked: { $sum: '$clickedCount' },
        byCategory: {
          $push: {
            category: '$category',
            priority: '$priority',
            status: '$status'
          }
        }
      }
    })
    
    const result = await Notification.aggregate(pipeline)
    return result[0] || {
      total: 0,
      unread: 0,
      read: 0,
      delivered: 0,
      opened: 0,
      clicked: 0,
      byCategory: []
    }
  }
}
