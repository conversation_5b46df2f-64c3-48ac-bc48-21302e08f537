import { connectDB } from '@/lib/database'
import NotificationPreferences from '@/lib/database/models/NotificationPreferences'
import User from '@/lib/database/models/User'
import { NotificationHelpers } from './notification-helpers'
import { NotificationService } from './notification-service'
import type { UserRole } from '@/types/notifications'

/**
 * Initialize notification system for users and create default preferences
 */
export class NotificationInitService {

  /**
   * Create default notification preferences for a new user
   */
  static async createDefaultPreferences(userId: string, userRole: UserRole) {
    await connectDB()

    // Check if preferences already exist
    const existingPreferences = await NotificationPreferences.findOne({ userId })
    if (existingPreferences) {
      return existingPreferences
    }

    // Create role-based default preferences
    const defaultPreferences = this.getRoleBasedDefaults(userRole)
    
    const preferences = new NotificationPreferences({
      userId,
      ...defaultPreferences
    })

    await preferences.save()
    return preferences
  }

  /**
   * Get role-based default notification preferences
   */
  static getRoleBasedDefaults(role: UserRole) {
    const baseDefaults = {
      channels: {
        in_app: true,
        email: true,
        sms: false,
        push: true
      },
      frequency: {
        immediate: ['security', 'orders'],
        digest: ['inventory', 'users', 'reports'],
        weekly: ['campaigns', 'maintenance']
      },
      settings: {
        groupSimilar: true,
        autoArchive: true,
        autoArchiveDays: 30,
        soundEnabled: true,
        vibrationEnabled: true
      }
    }

    // Role-specific category preferences
    switch (role) {
      case 'overall_admin':
        return {
          ...baseDefaults,
          categories: {
            system: {
              enabled: true,
              priority: ['medium', 'high', 'critical', 'urgent'],
              channels: ['in_app', 'email', 'push']
            },
            security: {
              enabled: true,
              priority: ['high', 'critical', 'urgent'],
              channels: ['in_app', 'email', 'push', 'sms']
            },
            inventory: {
              enabled: true,
              priority: ['medium', 'high', 'critical'],
              channels: ['in_app', 'email']
            },
            orders: {
              enabled: true,
              priority: ['low', 'medium', 'high'],
              channels: ['in_app', 'push']
            },
            delivery: {
              enabled: true,
              priority: ['medium', 'high'],
              channels: ['in_app']
            },
            campaigns: {
              enabled: true,
              priority: ['low', 'medium'],
              channels: ['in_app']
            },
            users: {
              enabled: true,
              priority: ['medium', 'high'],
              channels: ['in_app', 'email']
            },
            financial: {
              enabled: true,
              priority: ['medium', 'high', 'critical'],
              channels: ['in_app', 'email']
            },
            reports: {
              enabled: true,
              priority: ['low', 'medium'],
              channels: ['in_app', 'email']
            },
            maintenance: {
              enabled: true,
              priority: ['medium', 'high', 'critical'],
              channels: ['in_app', 'email']
            }
          },
          frequency: {
            immediate: ['security', 'system', 'financial'],
            digest: ['inventory', 'orders', 'users', 'reports'],
            weekly: ['campaigns', 'maintenance']
          }
        }

      case 'branch_manager':
        return {
          ...baseDefaults,
          categories: {
            system: {
              enabled: true,
              priority: ['high', 'critical', 'urgent'],
              channels: ['in_app', 'email']
            },
            security: {
              enabled: true,
              priority: ['high', 'critical', 'urgent'],
              channels: ['in_app', 'email', 'push']
            },
            inventory: {
              enabled: true,
              priority: ['low', 'medium', 'high', 'critical'],
              channels: ['in_app', 'email', 'push']
            },
            orders: {
              enabled: true,
              priority: ['low', 'medium', 'high'],
              channels: ['in_app', 'push']
            },
            delivery: {
              enabled: true,
              priority: ['medium', 'high'],
              channels: ['in_app', 'push']
            },
            campaigns: {
              enabled: false,
              priority: [],
              channels: []
            },
            users: {
              enabled: true,
              priority: ['medium', 'high'],
              channels: ['in_app']
            },
            financial: {
              enabled: true,
              priority: ['medium', 'high'],
              channels: ['in_app', 'email']
            },
            reports: {
              enabled: true,
              priority: ['medium'],
              channels: ['in_app', 'email']
            },
            maintenance: {
              enabled: true,
              priority: ['medium', 'high'],
              channels: ['in_app', 'email']
            }
          },
          frequency: {
            immediate: ['security', 'inventory', 'orders'],
            digest: ['users', 'financial', 'reports'],
            weekly: ['maintenance']
          }
        }

      case 'employee':
        return {
          ...baseDefaults,
          channels: {
            in_app: true,
            email: false,
            sms: false,
            push: true
          },
          categories: {
            system: {
              enabled: true,
              priority: ['high', 'critical', 'urgent'],
              channels: ['in_app']
            },
            security: {
              enabled: true,
              priority: ['critical', 'urgent'],
              channels: ['in_app', 'push']
            },
            inventory: {
              enabled: true,
              priority: ['medium', 'high', 'critical'],
              channels: ['in_app', 'push']
            },
            orders: {
              enabled: true,
              priority: ['medium', 'high'],
              channels: ['in_app', 'push']
            },
            delivery: {
              enabled: true,
              priority: ['medium', 'high'],
              channels: ['in_app']
            },
            campaigns: {
              enabled: false,
              priority: [],
              channels: []
            },
            users: {
              enabled: false,
              priority: [],
              channels: []
            },
            financial: {
              enabled: false,
              priority: [],
              channels: []
            },
            reports: {
              enabled: false,
              priority: [],
              channels: []
            },
            maintenance: {
              enabled: true,
              priority: ['high', 'critical'],
              channels: ['in_app']
            }
          },
          frequency: {
            immediate: ['security', 'inventory', 'orders'],
            digest: ['delivery'],
            weekly: ['maintenance']
          }
        }

      case 'customer':
        return {
          ...baseDefaults,
          categories: {
            system: {
              enabled: true,
              priority: ['critical', 'urgent'],
              channels: ['in_app']
            },
            security: {
              enabled: true,
              priority: ['high', 'critical', 'urgent'],
              channels: ['in_app', 'email']
            },
            inventory: {
              enabled: false,
              priority: [],
              channels: []
            },
            orders: {
              enabled: true,
              priority: ['low', 'medium', 'high'],
              channels: ['in_app', 'email', 'push', 'sms']
            },
            delivery: {
              enabled: true,
              priority: ['low', 'medium', 'high'],
              channels: ['in_app', 'push', 'sms']
            },
            campaigns: {
              enabled: true,
              priority: ['low', 'medium'],
              channels: ['in_app', 'push']
            },
            users: {
              enabled: true,
              priority: ['medium'],
              channels: ['in_app', 'email']
            },
            financial: {
              enabled: true,
              priority: ['medium', 'high'],
              channels: ['in_app', 'email']
            },
            reports: {
              enabled: false,
              priority: [],
              channels: []
            },
            maintenance: {
              enabled: true,
              priority: ['high', 'critical'],
              channels: ['in_app', 'email']
            }
          },
          frequency: {
            immediate: ['orders', 'delivery', 'security'],
            digest: ['campaigns'],
            weekly: ['maintenance']
          }
        }

      default:
        return baseDefaults
    }
  }

  /**
   * Initialize notification preferences for all existing users
   */
  static async initializeAllUsers() {
    await connectDB()

    const users = await User.find({}, 'id role')
    const results = []

    for (const user of users) {
      try {
        const preferences = await this.createDefaultPreferences(user.id, user.role)
        results.push({ userId: user.id, success: true, preferences })
      } catch (error) {
        console.error(`Failed to initialize preferences for user ${user.id}:`, error)
        results.push({ userId: user.id, success: false, error: error.message })
      }
    }

    return results
  }

  /**
   * Send welcome notification to new user
   */
  static async sendWelcomeNotification(userId: string, userData: {
    username: string
    email: string
    role: UserRole
  }) {
    const welcomeMessages = {
      overall_admin: 'Welcome to the admin dashboard! You have full access to all system features.',
      branch_manager: 'Welcome to your branch management dashboard! Manage your branch operations efficiently.',
      employee: 'Welcome to the team! You can now access your work dashboard and receive important updates.',
      customer: 'Welcome to Fathahitech! Enjoy shopping with us and stay updated on your orders.'
    }

    await NotificationHelpers.notifyUserRegistration(
      {
        userId,
        username: userData.username,
        email: userData.email,
        role: userData.role,
        registrationDate: new Date()
      },
      'system'
    )

    // Send personalized welcome notification
    const welcomeNotification = {
      category: 'users' as const,
      type: 'welcome_message',
      priority: 'low' as const,
      title: `🎉 Welcome to Fathahitech, ${userData.username}!`,
      message: welcomeMessages[userData.role] || 'Welcome to our platform!',
      description: 'You can customize your notification preferences in the settings.',
      target: {
        userIds: [userId]
      },
      actions: [
        {
          id: 'explore_dashboard',
          label: 'Explore Dashboard',
          type: 'link' as const,
          variant: 'primary' as const,
          url: '/dashboard'
        },
        {
          id: 'notification_settings',
          label: 'Notification Settings',
          type: 'link' as const,
          variant: 'outline' as const,
          url: '/dashboard/settings/notifications'
        }
      ],
      channels: ['in_app' as const],
      createdBy: 'system',
      source: 'user_onboarding'
    }

    return await NotificationService.createNotification(welcomeNotification)
  }

  /**
   * Create sample notifications for testing
   */
  static async createSampleNotifications(userId: string) {
    const samples = [
      // Low stock notification
      NotificationHelpers.notifyLowStock({
        productId: 'prod_123',
        productName: 'iPhone 15 Pro',
        sku: 'IPH15P-256-BLK',
        currentStock: 3,
        minimumStock: 10,
        branchId: 'branch_1',
        branchName: 'Main Store'
      }, userId),

      // New order notification
      NotificationHelpers.notifyOrderPlaced({
        orderId: 'ord_456',
        orderNumber: 'ORD-2024-001',
        customerId: 'cust_789',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        totalAmount: 1299.99,
        currency: 'USD',
        items: [
          { productId: 'prod_123', productName: 'iPhone 15 Pro', quantity: 1, price: 1299.99 }
        ],
        branchId: 'branch_1'
      }, userId),

      // Campaign notification
      NotificationHelpers.notifyCampaignLaunch({
        campaignId: 'camp_001',
        campaignName: 'Black Friday Sale',
        campaignType: 'sale',
        discountPercentage: 50,
        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        promoCode: 'BLACKFRIDAY50',
        targetAudience: ['customer']
      }, userId)
    ]

    return await Promise.all(samples)
  }
}
