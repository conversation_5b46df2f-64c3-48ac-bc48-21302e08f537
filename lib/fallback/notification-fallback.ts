// Fallback notification system for when Socket.io is unavailable
// This provides basic notification functionality using HTTP polling

import React from 'react'

interface FallbackNotification {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  timestamp: Date
  read: boolean
}

class NotificationFallback {
  private notifications: FallbackNotification[] = []
  private listeners: ((notifications: FallbackNotification[]) => void)[] = []
  private pollingInterval: NodeJS.Timeout | null = null
  private isPolling = false

  constructor() {
    // Add some default notifications to show the system is working
    this.addNotification({
      type: 'info',
      title: 'System Status',
      message: 'Using fallback notification system - real-time features limited'
    })
  }

  /**
   * Start polling for notifications via HTTP
   */
  startPolling(intervalMs: number = 30000) {
    if (this.isPolling) return

    this.isPolling = true
    console.log('Starting notification fallback polling')

    this.pollingInterval = setInterval(async () => {
      try {
        await this.fetchNotifications()
      } catch (error) {
        console.warn('Notification polling failed:', error)
      }
    }, intervalMs)

    // Initial fetch
    this.fetchNotifications()
  }

  /**
   * Stop polling
   */
  stopPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
      this.pollingInterval = null
    }
    this.isPolling = false
  }

  /**
   * Fetch notifications from server via HTTP
   */
  private async fetchNotifications() {
    try {
      const response = await fetch('/api/notifications?fallback=true', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && Array.isArray(data.notifications)) {
          // Merge new notifications with existing ones
          const newNotifications = data.notifications.map((notif: any) => ({
            id: notif.id || `fallback-${Date.now()}-${Math.random()}`,
            type: notif.type || 'info',
            title: notif.title || 'Notification',
            message: notif.message || '',
            timestamp: new Date(notif.timestamp || Date.now()),
            read: false
          }))

          // Add only new notifications (avoid duplicates)
          const existingIds = new Set(this.notifications.map(n => n.id))
          const uniqueNew = newNotifications.filter((n: FallbackNotification) => !existingIds.has(n.id))
          
          if (uniqueNew.length > 0) {
            this.notifications = [...this.notifications, ...uniqueNew]
            this.notifyListeners()
          }
        }
      }
    } catch (error) {
      console.warn('Failed to fetch notifications:', error)
    }
  }

  /**
   * Add a notification manually
   */
  addNotification(notification: Omit<FallbackNotification, 'id' | 'timestamp' | 'read'>) {
    const newNotification: FallbackNotification = {
      id: `fallback-${Date.now()}-${Math.random()}`,
      timestamp: new Date(),
      read: false,
      ...notification
    }

    this.notifications.unshift(newNotification)
    
    // Keep only last 50 notifications
    if (this.notifications.length > 50) {
      this.notifications = this.notifications.slice(0, 50)
    }

    this.notifyListeners()
  }

  /**
   * Mark notification as read
   */
  markAsRead(id: string) {
    const notification = this.notifications.find(n => n.id === id)
    if (notification) {
      notification.read = true
      this.notifyListeners()
    }
  }

  /**
   * Mark all notifications as read
   */
  markAllAsRead() {
    this.notifications.forEach(n => n.read = true)
    this.notifyListeners()
  }

  /**
   * Remove a notification
   */
  removeNotification(id: string) {
    this.notifications = this.notifications.filter(n => n.id !== id)
    this.notifyListeners()
  }

  /**
   * Clear all notifications
   */
  clearAll() {
    this.notifications = []
    this.notifyListeners()
  }

  /**
   * Get all notifications
   */
  getNotifications(): FallbackNotification[] {
    return [...this.notifications]
  }

  /**
   * Get unread count
   */
  getUnreadCount(): number {
    return this.notifications.filter(n => !n.read).length
  }

  /**
   * Subscribe to notification changes
   */
  subscribe(listener: (notifications: FallbackNotification[]) => void) {
    this.listeners.push(listener)
    
    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  /**
   * Notify all listeners of changes
   */
  private notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener([...this.notifications])
      } catch (error) {
        console.error('Error in notification listener:', error)
      }
    })
  }

  /**
   * Simulate real-time notification for testing
   */
  simulateNotification() {
    this.addNotification({
      type: 'info',
      title: 'Test Notification',
      message: `Fallback notification at ${new Date().toLocaleTimeString()}`
    })
  }
}

// Export singleton instance
export const notificationFallback = new NotificationFallback()

// Export hook for React components
export function useFallbackNotifications() {
  const [notifications, setNotifications] = React.useState<FallbackNotification[]>([])
  const [unreadCount, setUnreadCount] = React.useState(0)

  React.useEffect(() => {
    const unsubscribe = notificationFallback.subscribe((newNotifications) => {
      setNotifications(newNotifications)
      setUnreadCount(notificationFallback.getUnreadCount())
    })

    // Initial load
    setNotifications(notificationFallback.getNotifications())
    setUnreadCount(notificationFallback.getUnreadCount())

    return unsubscribe
  }, [])

  return {
    notifications,
    unreadCount,
    markAsRead: notificationFallback.markAsRead.bind(notificationFallback),
    markAllAsRead: notificationFallback.markAllAsRead.bind(notificationFallback),
    removeNotification: notificationFallback.removeNotification.bind(notificationFallback),
    clearAll: notificationFallback.clearAll.bind(notificationFallback),
    startPolling: notificationFallback.startPolling.bind(notificationFallback),
    stopPolling: notificationFallback.stopPolling.bind(notificationFallback)
  }
}
