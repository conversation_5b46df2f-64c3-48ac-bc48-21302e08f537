// lib/auth/session-utils.ts - Session Management Utilities

import { NextRequest } from 'next/server'
import Session, { ISession } from '@/lib/database/models/Session'
import { generateTokenPair } from './jwt'
import mongoose from 'mongoose'

// Device information interface
export interface DeviceInfo {
  userAgent: string
  browser: string
  browserVersion: string
  os: string
  osVersion: string
  device: string
  deviceType: 'desktop' | 'mobile' | 'tablet'
  platform: string
}

// Location information interface
export interface LocationInfo {
  ipAddress: string
  country?: string
  city?: string
  timezone?: string
}

// Session creation data interface
export interface CreateSessionData {
  userId: string
  email: string
  username: string
  role: string
  branchId?: string
  deviceInfo: DeviceInfo
  location: LocationInfo
  expiresIn?: number // in milliseconds, defaults to 30 days
}

// Active session info for notifications
export interface ActiveSessionInfo {
  id: string
  deviceDescription: string
  locationDescription: string
  loginTime: Date
  lastActivity: Date
  isCurrentSession: boolean
}

/**
 * Parse user agent string to extract device information
 */
export function parseUserAgent(userAgent: string): DeviceInfo {
  // Basic user agent parsing - in production, consider using a library like 'ua-parser-js'
  const ua = userAgent.toLowerCase()
  
  // Detect browser
  let browser = 'Unknown'
  let browserVersion = 'Unknown'
  
  if (ua.includes('chrome') && !ua.includes('edg')) {
    browser = 'Chrome'
    const match = ua.match(/chrome\/([0-9.]+)/)
    browserVersion = match ? match[1] : 'Unknown'
  } else if (ua.includes('firefox')) {
    browser = 'Firefox'
    const match = ua.match(/firefox\/([0-9.]+)/)
    browserVersion = match ? match[1] : 'Unknown'
  } else if (ua.includes('safari') && !ua.includes('chrome')) {
    browser = 'Safari'
    const match = ua.match(/version\/([0-9.]+)/)
    browserVersion = match ? match[1] : 'Unknown'
  } else if (ua.includes('edg')) {
    browser = 'Edge'
    const match = ua.match(/edg\/([0-9.]+)/)
    browserVersion = match ? match[1] : 'Unknown'
  }
  
  // Detect OS
  let os = 'Unknown'
  let osVersion = 'Unknown'
  
  if (ua.includes('windows')) {
    os = 'Windows'
    if (ua.includes('windows nt 10.0')) osVersion = '10'
    else if (ua.includes('windows nt 6.3')) osVersion = '8.1'
    else if (ua.includes('windows nt 6.2')) osVersion = '8'
    else if (ua.includes('windows nt 6.1')) osVersion = '7'
  } else if (ua.includes('mac os x')) {
    os = 'macOS'
    const match = ua.match(/mac os x ([0-9_]+)/)
    osVersion = match ? match[1].replace(/_/g, '.') : 'Unknown'
  } else if (ua.includes('linux')) {
    os = 'Linux'
  } else if (ua.includes('android')) {
    os = 'Android'
    const match = ua.match(/android ([0-9.]+)/)
    osVersion = match ? match[1] : 'Unknown'
  } else if (ua.includes('iphone') || ua.includes('ipad')) {
    os = 'iOS'
    const match = ua.match(/os ([0-9_]+)/)
    osVersion = match ? match[1].replace(/_/g, '.') : 'Unknown'
  }
  
  // Detect device type
  let deviceType: 'desktop' | 'mobile' | 'tablet' = 'desktop'
  let device = 'Unknown'
  
  if (ua.includes('mobile') || ua.includes('iphone')) {
    deviceType = 'mobile'
    if (ua.includes('iphone')) device = 'iPhone'
    else if (ua.includes('android')) device = 'Android Phone'
  } else if (ua.includes('tablet') || ua.includes('ipad')) {
    deviceType = 'tablet'
    if (ua.includes('ipad')) device = 'iPad'
    else if (ua.includes('android')) device = 'Android Tablet'
  } else {
    deviceType = 'desktop'
    device = 'Desktop Computer'
  }
  
  // Detect platform
  let platform = 'Unknown'
  if (ua.includes('x11')) platform = 'X11'
  else if (ua.includes('win64') || ua.includes('wow64')) platform = 'Win64'
  else if (ua.includes('win32')) platform = 'Win32'
  else if (ua.includes('intel')) platform = 'Intel'
  else if (ua.includes('ppc')) platform = 'PowerPC'
  
  return {
    userAgent,
    browser,
    browserVersion,
    os,
    osVersion,
    device,
    deviceType,
    platform
  }
}

/**
 * Extract location information from request
 */
export function getLocationInfo(request: NextRequest): LocationInfo {
  // Get IP address from various headers
  const forwarded = request.headers.get('x-forwarded-for')
  const realIp = request.headers.get('x-real-ip')
  const cfConnectingIp = request.headers.get('cf-connecting-ip')
  
  let ipAddress = 'Unknown'
  
  if (forwarded) {
    ipAddress = forwarded.split(',')[0].trim()
  } else if (realIp) {
    ipAddress = realIp
  } else if (cfConnectingIp) {
    ipAddress = cfConnectingIp
  } else {
    // Fallback to connection remote address
    ipAddress = request.ip || 'Unknown'
  }
  
  // In production, you might want to use a geolocation service
  // For now, we'll just store the IP address
  return {
    ipAddress,
    // country: undefined, // Could be populated by geolocation service
    // city: undefined,    // Could be populated by geolocation service
    // timezone: undefined // Could be populated by geolocation service
  }
}

/**
 * Create a new session record
 */
export async function createSession(data: CreateSessionData): Promise<ISession> {
  const { userId, email, username, role, branchId, deviceInfo, location, expiresIn = 30 * 24 * 60 * 60 * 1000 } = data

  // Generate tokens with full user data
  const sessionId = new mongoose.Types.ObjectId().toString()
  const tokenPayload = {
    userId,
    email,
    username,
    role,
    branchId,
    sessionId
  }

  const { accessToken, refreshToken } = generateTokenPair(tokenPayload)
  
  // Create session record
  const session = new Session({
    userId: new mongoose.Types.ObjectId(userId),
    sessionToken: accessToken,
    refreshToken,
    deviceInfo,
    location,
    expiresAt: new Date(Date.now() + expiresIn)
  })
  
  return await session.save()
}

/**
 * Get active sessions for a user
 */
export async function getUserActiveSessions(
  userId: string, 
  currentSessionId?: string
): Promise<ActiveSessionInfo[]> {
  const sessions = await Session.findActiveByUser(userId)
  
  return sessions.map(session => ({
    id: session._id.toString(),
    deviceDescription: session.getDeviceDescription(),
    locationDescription: session.getLocationDescription(),
    loginTime: session.loginTime,
    lastActivity: session.lastActivity,
    isCurrentSession: currentSessionId ? session._id.toString() === currentSessionId : false
  }))
}

/**
 * Terminate a specific session
 */
export async function terminateSession(
  sessionId: string, 
  reason: 'manual' | 'expired' | 'forced' | 'security' = 'manual'
): Promise<boolean> {
  const session = await Session.findById(sessionId)
  if (!session || !session.isActive) {
    return false
  }
  
  await session.terminate(reason)
  return true
}

/**
 * Terminate all sessions for a user except the current one
 */
export async function terminateOtherSessions(
  userId: string, 
  currentSessionId: string,
  reason: 'manual' | 'expired' | 'forced' | 'security' = 'manual'
): Promise<number> {
  const result = await Session.updateMany(
    { 
      userId: new mongoose.Types.ObjectId(userId), 
      _id: { $ne: new mongoose.Types.ObjectId(currentSessionId) },
      isActive: true 
    },
    { 
      isActive: false, 
      logoutTime: new Date(), 
      logoutReason: reason 
    }
  )
  
  return result.modifiedCount || 0
}

/**
 * Update session activity
 */
export async function updateSessionActivity(sessionToken: string): Promise<boolean> {
  const session = await Session.findByToken(sessionToken)
  if (!session) {
    return false
  }
  
  await session.updateActivity()
  return true
}

/**
 * Validate and refresh session
 */
export async function refreshSession(refreshToken: string): Promise<{
  success: boolean
  session?: ISession
  tokens?: { accessToken: string; refreshToken: string }
  error?: string
}> {
  try {
    const session = await Session.findByRefreshToken(refreshToken)
    
    if (!session) {
      return { success: false, error: 'Invalid refresh token' }
    }
    
    if (session.isExpired()) {
      await session.terminate('expired')
      return { success: false, error: 'Session expired' }
    }
    
    // Get user data for token payload
    const { User } = await import('@/lib/database')
    const user = await User.findById(session.userId).lean()

    if (!user) {
      return { success: false, error: 'User not found' }
    }

    // Generate new tokens with full user data
    const tokenPayload = {
      userId: session.userId.toString(),
      email: user.email,
      username: user.username,
      role: user.role,
      branchId: user.branchId,
      sessionId: session._id.toString()
    }

    const { accessToken, refreshToken: newRefreshToken } = generateTokenPair(tokenPayload)
    
    // Update session with new tokens
    session.sessionToken = accessToken
    session.refreshToken = newRefreshToken
    await session.updateActivity()
    
    return {
      success: true,
      session,
      tokens: {
        accessToken,
        refreshToken: newRefreshToken
      }
    }
  } catch (error) {
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Session refresh failed' 
    }
  }
}

/**
 * Clean up expired sessions (should be run periodically)
 */
export async function cleanupExpiredSessions(): Promise<number> {
  const result = await Session.cleanupExpiredSessions()
  return result.modifiedCount || 0
}
