// JWT utilities
export {
  generateAccessToken,
  generateRefreshToken,
  generateTokenPair,
  verifyAccessToken,
  verifyRefreshToken,
  decodeToken,
  isTokenExpired,
  getTokenExpiration,
  extractTokenFromHeader,
  generateSecureToken,
  hashToken,
  validateJWTConfig
} from './jwt'

export type { JWTPayload, TokenPair } from './jwt'

// Middleware
export {
  authMiddleware,
  checkBranchAccess,
  checkPermissions,
  rateLimit,
  corsMiddleware,
  addCorsHeaders,
  withAuth
} from './middleware'

export type { AuthenticatedRequest } from './middleware'

// Services
export {
  loginUser,
  registerUser,
  refreshUserToken,
  getCurrentUser,
  getUserProfile,
  updateUserProfile,
  changePassword,
  requestPasswordReset,
  confirmPasswordReset
} from './services'

export type {
  LoginCredentials,
  RegisterData,
  AuthResult,
  PasswordResetRequest,
  PasswordResetConfirm
} from './services'

// Note: Auth context has been removed in favor of Zustand store
// Use useAuthStore from '@/stores/authStore' instead

// Guards and HOCs
export {
  AuthGuard,
  RoleGuard,
  BranchGuard,
  withAuth as with<PERSON><PERSON>H<PERSON>,
  withRole,
  withBranch,
  withAuthAndRole,
  usePermissions
} from './guards'

// Utility functions for client-side auth
export const authUtils = {
  /**
   * Check if user has required role
   */
  hasRole: (userRole: string, requiredRoles: string[]): boolean => {
    return requiredRoles.includes(userRole)
  },

  /**
   * Check if user can access branch
   */
  canAccessBranch: (userRole: string, userBranchId?: string, targetBranchId?: string): boolean => {
    if (userRole === 'overall_admin') return true
    if (userRole === 'branch_manager') {
      return !targetBranchId || userBranchId === targetBranchId
    }
    return false
  },

  /**
   * Get user display name
   */
  getUserDisplayName: (user: { name?: string; username?: string; email?: string }): string => {
    return user.name || user.username || user.email || 'Unknown User'
  },

  /**
   * Format user role for display
   */
  formatRole: (role: string): string => {
    return role.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  },

  /**
   * Check if token is close to expiring (within 5 minutes)
   */
  isTokenExpiringSoon: (token: string): boolean => {
    try {
      const decoded = decodeToken(token)
      if (!decoded || !decoded.exp) return true
      
      const currentTime = Math.floor(Date.now() / 1000)
      const timeUntilExpiry = decoded.exp - currentTime
      
      return timeUntilExpiry < 300 // 5 minutes
    } catch {
      return true
    }
  }
}

// Constants
export const AUTH_CONSTANTS = {
  ROLES: {
    OVERALL_ADMIN: 'overall_admin' as const,
    BRANCH_MANAGER: 'branch_manager' as const
  },
  
  STORAGE_KEYS: {
    ACCESS_TOKEN: 'accessToken',
    REFRESH_TOKEN: 'refreshToken',
    USER_DATA: 'userData'
  },
  
  API_ENDPOINTS: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    ME: '/api/auth/me',
    CHANGE_PASSWORD: '/api/auth/change-password',
    RESET_PASSWORD: '/api/auth/reset-password'
  },
  
  ROUTES: {
    LOGIN: '/login',
    REGISTER: '/register',
    DASHBOARD: '/dashboard',
    UNAUTHORIZED: '/unauthorized'
  }
} as const
