import bcrypt from 'bcryptjs'
import { User, Shop, connectDB, logActivity } from '@/lib/database'
import { generateTokenPair, verifyRefreshToken, generateSecureToken, hashToken } from './jwt'
import type { UserRole } from '@/types'

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  username: string
  email: string
  password: string
  name: string
  role: UserRole
  branchId?: string
}

export interface AuthResult {
  success: boolean
  user?: {
    id: string
    username: string
    email: string
    name: string
    role: UserRole
    branchId?: string
    avatar?: string
  }
  tokens?: {
    accessToken: string
    refreshToken: string
  }
  error?: string
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirm {
  token: string
  newPassword: string
}

/**
 * User login service
 */
export async function loginUser(credentials: LoginCredentials): Promise<AuthResult> {
  try {
    await connectDB()

    const { email, password } = credentials

    // Find user by email and include password for comparison
    const user = await User.findOne({ email: email.toLowerCase() })
      .select('+password')
      .lean()

    if (!user) {
      return {
        success: false,
        error: 'Invalid email or password'
      }
    }

    // Check if user is active
    if (!user.isActive) {
      return {
        success: false,
        error: 'Account is deactivated. Please contact administrator.'
      }
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      return {
        success: false,
        error: 'Invalid email or password'
      }
    }

    // Update last login
    await User.findByIdAndUpdate(user._id, { lastLogin: new Date() })

    // Generate tokens
    const tokenPayload = {
      userId: user._id.toString(),
      email: user.email,
      username: user.username,
      role: user.role,
      branchId: user.branchId
    }

    const tokens = generateTokenPair(tokenPayload)

    // Log login activity
    await logActivity({
      type: 'User',
      description: `User ${user.username} logged in`,
      userId: user._id.toString(),
      userName: user.name,
      branchId: user.branchId
    })

    return {
      success: true,
      user: {
        id: user._id.toString(),
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role,
        branchId: user.branchId,
        avatar: user.avatar
      },
      tokens
    }
  } catch (error) {
    console.error('Login error:', error)
    return {
      success: false,
      error: 'Login failed. Please try again.'
    }
  }
}

/**
 * User registration service
 */
export async function registerUser(data: RegisterData): Promise<AuthResult> {
  try {
    await connectDB()

    const { username, email, password, name, role, branchId } = data

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { email: email.toLowerCase() },
        { username: username.toLowerCase() }
      ]
    })

    if (existingUser) {
      return {
        success: false,
        error: existingUser.email === email.toLowerCase() 
          ? 'Email already registered' 
          : 'Username already taken'
      }
    }

    // Validate branch for branch managers
    if (role === 'branch_manager') {
      if (!branchId) {
        return {
          success: false,
          error: 'Branch ID is required for branch managers'
        }
      }

      // For now, we'll skip the branch validation since we don't have seeded data
      // In production, you would validate against actual shops in the database
      // TODO: Implement proper branch validation once shops are seeded

      // Check if branch already has a manager (using branchId as string)
      const existingManager = await User.findOne({ branchId, role: 'branch_manager' })
      if (existingManager) {
        return {
          success: false,
          error: 'This branch already has a manager'
        }
      }
    }

    // Create new user
    const newUser = await User.create({
      username: username.toLowerCase(),
      email: email.toLowerCase(),
      password, // Will be hashed by the pre-save middleware
      name,
      role,
      branchId: role === 'branch_manager' ? branchId : undefined,
      isActive: true
    })

    // Generate tokens
    const tokenPayload = {
      userId: newUser._id.toString(),
      email: newUser.email,
      username: newUser.username,
      role: newUser.role,
      branchId: newUser.branchId
    }

    const tokens = generateTokenPair(tokenPayload)

    // Log registration activity
    await logActivity({
      type: 'User',
      description: `New user ${newUser.username} registered with role ${newUser.role}`,
      userId: newUser._id.toString(),
      userName: newUser.name,
      branchId: newUser.branchId
    })

    return {
      success: true,
      user: {
        id: newUser._id.toString(),
        username: newUser.username,
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
        branchId: newUser.branchId,
        avatar: newUser.avatar
      },
      tokens
    }
  } catch (error) {
    console.error('Registration error:', error)
    return {
      success: false,
      error: 'Registration failed. Please try again.'
    }
  }
}

/**
 * Refresh token service
 */
export async function refreshUserToken(refreshToken: string): Promise<AuthResult> {
  try {
    await connectDB()

    // Verify refresh token
    const payload = verifyRefreshToken(refreshToken)

    // Check if user still exists and is active
    const user = await User.findById(payload.userId)
    if (!user || !user.isActive) {
      return {
        success: false,
        error: 'User not found or account deactivated'
      }
    }

    // Generate new tokens
    const tokenPayload = {
      userId: user._id.toString(),
      email: user.email,
      username: user.username,
      role: user.role,
      branchId: user.branchId
    }

    const tokens = generateTokenPair(tokenPayload)

    return {
      success: true,
      user: {
        id: user._id.toString(),
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role,
        branchId: user.branchId,
        avatar: user.avatar
      },
      tokens
    }
  } catch (error) {
    console.error('Token refresh error:', error)
    return {
      success: false,
      error: 'Token refresh failed'
    }
  }
}

/**
 * Get current user from server-side (for server components)
 */
export async function getCurrentUser(): Promise<AuthResult['user'] | null> {
  try {
    const { cookies } = await import('next/headers')
    const cookieStore = await cookies()
    const token = cookieStore.get('auth-token')?.value

    if (!token) {
      return null
    }

    const { verifyAccessToken } = await import('./jwt')

    try {
      const payload = await verifyAccessToken(token)

      if (!payload) {
        return null
      }

      const result = await getUserProfile(payload.userId)
      return result.success ? result.user : null
    } catch (tokenError) {
      // Handle token expiration specifically
      if (tokenError instanceof Error && tokenError.message === 'Access token expired') {
        console.log('Access token expired, user needs to re-authenticate')
        return null
      }

      // For other token errors, also return null
      console.error('Token verification error:', tokenError)
      return null
    }
  } catch (error) {
    console.error('getCurrentUser error:', error)
    return null
  }
}

/**
 * Get user profile service
 */
export async function getUserProfile(userId: string): Promise<AuthResult> {
  try {
    await connectDB()

    const user = await User.findById(userId)
    if (!user || !user.isActive) {
      return {
        success: false,
        error: 'User not found'
      }
    }

    return {
      success: true,
      user: {
        id: user._id.toString(),
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role,
        branchId: user.branchId,
        avatar: user.avatar
      }
    }
  } catch (error) {
    console.error('Get profile error:', error)
    return {
      success: false,
      error: 'Failed to get user profile'
    }
  }
}

/**
 * Update user profile service
 */
export async function updateUserProfile(
  userId: string,
  updates: Partial<{ name: string; avatar: string }>
): Promise<AuthResult> {
  try {
    await connectDB()

    const user = await User.findByIdAndUpdate(
      userId,
      { ...updates, updatedAt: new Date() },
      { new: true }
    )

    if (!user) {
      return {
        success: false,
        error: 'User not found'
      }
    }

    // Log profile update activity
    await logActivity({
      type: 'User',
      description: `User ${user.username} updated their profile`,
      userId: user._id.toString(),
      userName: user.name,
      branchId: user.branchId,
      metadata: { updates }
    })

    return {
      success: true,
      user: {
        id: user._id.toString(),
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role,
        branchId: user.branchId,
        avatar: user.avatar
      }
    }
  } catch (error) {
    console.error('Update profile error:', error)
    return {
      success: false,
      error: 'Failed to update profile'
    }
  }
}

/**
 * Change password service
 */
export async function changePassword(
  userId: string,
  currentPassword: string,
  newPassword: string
): Promise<{ success: boolean; error?: string }> {
  try {
    await connectDB()

    const user = await User.findById(userId).select('+password')
    if (!user) {
      return {
        success: false,
        error: 'User not found'
      }
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password)
    if (!isCurrentPasswordValid) {
      return {
        success: false,
        error: 'Current password is incorrect'
      }
    }

    // Update password (will be hashed by pre-save middleware)
    user.password = newPassword
    await user.save()

    // Log password change activity
    await logActivity({
      type: 'User',
      description: `User ${user.username} changed their password`,
      userId: user._id.toString(),
      userName: user.name,
      branchId: user.branchId
    })

    return { success: true }
  } catch (error) {
    console.error('Change password error:', error)
    return {
      success: false,
      error: 'Failed to change password'
    }
  }
}

/**
 * Request password reset service
 */
export async function requestPasswordReset(data: PasswordResetRequest): Promise<{ success: boolean; error?: string; token?: string }> {
  try {
    await connectDB()

    const { email } = data

    const user = await User.findOne({ email: email.toLowerCase() })
    if (!user) {
      // Don't reveal if email exists or not for security
      return {
        success: true // Always return success to prevent email enumeration
      }
    }

    if (!user.isActive) {
      return {
        success: false,
        error: 'Account is deactivated'
      }
    }

    // Generate reset token
    const resetToken = generateSecureToken(32)
    const hashedToken = hashToken(resetToken)

    // Save hashed token and expiration to user
    user.passwordResetToken = hashedToken
    user.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
    await user.save()

    // Log password reset request
    await logActivity({
      type: 'User',
      description: `Password reset requested for user ${user.username}`,
      userId: user._id.toString(),
      userName: user.name,
      branchId: user.branchId
    })

    return {
      success: true,
      token: resetToken // In production, send this via email instead of returning it
    }
  } catch (error) {
    console.error('Password reset request error:', error)
    return {
      success: false,
      error: 'Failed to process password reset request'
    }
  }
}

/**
 * Confirm password reset service
 */
export async function confirmPasswordReset(data: PasswordResetConfirm): Promise<{ success: boolean; error?: string }> {
  try {
    await connectDB()

    const { token, newPassword } = data

    // Hash the provided token to compare with stored hash
    const hashedToken = hashToken(token)

    // Find user with matching reset token that hasn't expired
    const user = await User.findOne({
      passwordResetToken: hashedToken,
      passwordResetExpires: { $gt: new Date() }
    }).select('+password')

    if (!user) {
      return {
        success: false,
        error: 'Invalid or expired reset token'
      }
    }

    // Update password and clear reset token
    user.password = newPassword // Will be hashed by pre-save middleware
    user.passwordResetToken = undefined
    user.passwordResetExpires = undefined
    await user.save()

    // Log password reset completion
    await logActivity({
      type: 'User',
      description: `Password reset completed for user ${user.username}`,
      userId: user._id.toString(),
      userName: user.name,
      branchId: user.branchId
    })

    return { success: true }
  } catch (error) {
    console.error('Password reset confirm error:', error)
    return {
      success: false,
      error: 'Failed to reset password'
    }
  }
}
