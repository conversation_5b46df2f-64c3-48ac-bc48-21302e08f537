import { NextRequest, NextResponse } from 'next/server'
import { verifyAccessToken, extractTokenFromHeader, type JWTPayload } from './jwt'
import { User } from '@/lib/database'
import type { UserRole } from '@/types'

export interface AuthenticatedRequest extends NextRequest {
  user?: JWTPayload
}

/**
 * Authentication middleware for API routes
 */
export async function authMiddleware(
  request: NextRequest,
  requiredRoles?: UserRole[]
): Promise<{ success: boolean; user?: JWTPayload; error?: string; response?: NextResponse }> {
  console.log('🔐 AUTH MIDDLEWARE STARTED')
  console.log('🔐 Required roles:', requiredRoles)
  try {
    // Extract token from Authorization header or cookies
    const authHeader = request.headers.get('authorization')
    let token = extractTokenFromHeader(authHeader)

    // If no token in header, check cookies
    if (!token) {
      token = request.cookies.get('auth-token')?.value || null
    }

    if (!token) {
      console.log('🔐 No token found in Authorization header or cookies')
      return {
        success: false,
        error: 'No authentication token provided',
        response: NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        )
      }
    }

    console.log('🔐 Token found:', token ? 'Yes' : 'No')
    console.log('🔐 Token source:', authHeader ? 'Authorization header' : 'Cookie')

    // Verify the token
    let payload: JWTPayload
    try {
      payload = verifyAccessToken(token)
      console.log('Middleware - Token verified successfully')
      console.log('Middleware - Token payload:', JSON.stringify(payload, null, 2))
    } catch (error) {
      console.log('Middleware - Token verification failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Token verification failed',
        response: NextResponse.json(
          { error: 'Invalid or expired token' },
          { status: 401 }
        )
      }
    }

    // Verify user still exists and is active
    const user = await User.findById(payload.userId).select('+isActive')
    if (!user || !user.isActive) {
      return {
        success: false,
        error: 'User not found or inactive',
        response: NextResponse.json(
          { error: 'User account not found or deactivated' },
          { status: 401 }
        )
      }
    }

    // Check role requirements
    if (requiredRoles && requiredRoles.length > 0) {
      console.log('Middleware - Role check:')
      console.log('  Required roles:', requiredRoles)
      console.log('  User role from token:', payload.role)
      console.log('  Role type:', typeof payload.role)
      console.log('  Role includes check:', requiredRoles.includes(payload.role))

      if (!requiredRoles.includes(payload.role)) {
        console.log('Middleware - ROLE CHECK FAILED')
        console.log('  User role:', JSON.stringify(payload.role))
        console.log('  Required roles:', JSON.stringify(requiredRoles))
        return {
          success: false,
          error: 'Insufficient permissions',
          response: NextResponse.json(
            { error: 'Insufficient permissions for this operation' },
            { status: 403 }
          )
        }
      }

      console.log('Middleware - Role check PASSED')
    }

    return {
      success: true,
      user: payload
    }
  } catch (error) {
    console.error('Auth middleware error:', error)
    return {
      success: false,
      error: 'Internal authentication error',
      response: NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

/**
 * Branch access middleware - ensures user can only access their branch data
 */
export function checkBranchAccess(
  userPayload: JWTPayload,
  requestedBranchId?: string
): { hasAccess: boolean; error?: string } {
  // Overall admins can access all branches
  if (userPayload.role === 'overall_admin') {
    return { hasAccess: true }
  }

  // Branch managers can only access their own branch
  if (userPayload.role === 'branch_manager') {
    if (!requestedBranchId) {
      // If no specific branch requested, allow access to their own branch
      return { hasAccess: true }
    }

    if (userPayload.branchId !== requestedBranchId) {
      return {
        hasAccess: false,
        error: 'Access denied: You can only access data from your assigned branch'
      }
    }

    return { hasAccess: true }
  }

  return {
    hasAccess: false,
    error: 'Access denied: Invalid user role'
  }
}

/**
 * Permission-based middleware
 */
export async function checkPermissions(
  userId: string,
  requiredPermissions: string[]
): Promise<{ hasPermission: boolean; error?: string }> {
  try {
    const user = await User.findById(userId)
    if (!user) {
      return {
        hasPermission: false,
        error: 'User not found'
      }
    }

    // Overall admins have all permissions
    if (user.role === 'overall_admin') {
      return { hasPermission: true }
    }

    // For other roles, check specific permissions
    // Note: You'll need to add a permissions field to your User model
    // or create a separate permissions system
    
    return { hasPermission: true } // Placeholder - implement based on your permission system
  } catch (error) {
    return {
      hasPermission: false,
      error: 'Error checking permissions'
    }
  }
}

/**
 * Rate limiting middleware (basic implementation)
 */
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function rateLimit(
  identifier: string,
  maxRequests: number = 100,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now()
  const windowStart = now - windowMs

  // Clean up old entries
  for (const [key, value] of rateLimitMap.entries()) {
    if (value.resetTime < now) {
      rateLimitMap.delete(key)
    }
  }

  const current = rateLimitMap.get(identifier)
  
  if (!current || current.resetTime < now) {
    // First request in window or window has reset
    rateLimitMap.set(identifier, {
      count: 1,
      resetTime: now + windowMs
    })
    return {
      allowed: true,
      remaining: maxRequests - 1,
      resetTime: now + windowMs
    }
  }

  if (current.count >= maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: current.resetTime
    }
  }

  current.count++
  return {
    allowed: true,
    remaining: maxRequests - current.count,
    resetTime: current.resetTime
  }
}

/**
 * CORS middleware for API routes
 */
export function corsMiddleware(request: NextRequest): NextResponse | null {
  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new NextResponse(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': process.env.FRONTEND_URL || '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400',
      },
    })
  }

  return null
}

/**
 * Add CORS headers to response
 */
export function addCorsHeaders(response: NextResponse): NextResponse {
  response.headers.set('Access-Control-Allow-Origin', process.env.FRONTEND_URL || '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  return response
}

/**
 * Wrapper function for protected API routes
 */
export function withAuth(
  handler: (request: NextRequest, user: JWTPayload, context?: any) => Promise<NextResponse>,
  options?: {
    requiredRoles?: UserRole[]
    requiredPermissions?: string[]
    rateLimitKey?: (request: NextRequest) => string
    maxRequests?: number
  }
) {
  return async (request: NextRequest, context?: any) => {
    try {
      // Handle CORS
      const corsResponse = corsMiddleware(request)
      if (corsResponse) return corsResponse

      // Rate limiting
      if (options?.rateLimitKey) {
        const identifier = options.rateLimitKey(request)
        const rateCheck = rateLimit(identifier, options.maxRequests)
        
        if (!rateCheck.allowed) {
          return NextResponse.json(
            { error: 'Too many requests' },
            { 
              status: 429,
              headers: {
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': rateCheck.resetTime.toString()
              }
            }
          )
        }
      }

      // Authentication
      const authResult = await authMiddleware(request, options?.requiredRoles)
      if (!authResult.success) {
        return authResult.response!
      }

      // Permission check
      if (options?.requiredPermissions && options.requiredPermissions.length > 0) {
        const permissionCheck = await checkPermissions(
          authResult.user!.userId,
          options.requiredPermissions
        )
        
        if (!permissionCheck.hasPermission) {
          return NextResponse.json(
            { error: permissionCheck.error || 'Insufficient permissions' },
            { status: 403 }
          )
        }
      }

      // Call the actual handler
      const response = await handler(request, authResult.user!, context)
      return addCorsHeaders(response)
    } catch (error) {
      console.error('Protected route error:', error)
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}
