'use client'

import React, { ComponentType, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/authStore'
import type { UserRole } from '@/types/frontend'

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
  </div>
)

// Unauthorized component
const UnauthorizedMessage = ({ message }: { message?: string }) => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
      <p className="text-gray-600 mb-4">
        {message || 'You do not have permission to access this page.'}
      </p>
      <button
        onClick={() => window.history.back()}
        className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
      >
        Go Back
      </button>
    </div>
  </div>
)

// Auth Guard Component
interface AuthGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
}

export function AuthGuard({ children, fallback, redirectTo = '/login' }: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push(redirectTo)
    }
  }, [isAuthenticated, isLoading, router, redirectTo])

  if (isLoading) {
    return fallback || <LoadingSpinner />
  }

  if (!isAuthenticated) {
    return null // Will redirect
  }

  return <>{children}</>
}

// Role Guard Component
interface RoleGuardProps {
  children: React.ReactNode
  requiredRoles: UserRole[]
  fallback?: React.ReactNode
  redirectTo?: string
  showUnauthorized?: boolean
}

export function RoleGuard({
  children,
  requiredRoles,
  fallback,
  redirectTo,
  showUnauthorized = true
}: RoleGuardProps) {
  const { user, isLoading, isAuthenticated } = useAuthStore()
  const router = useRouter()

  const hasRequiredRole = user && requiredRoles.includes(user.role)

  useEffect(() => {
    if (!isLoading && isAuthenticated && !hasRequiredRole && redirectTo) {
      router.push(redirectTo)
    }
  }, [isAuthenticated, isLoading, hasRequiredRole, router, redirectTo])

  if (isLoading) {
    return fallback || <LoadingSpinner />
  }

  if (!isAuthenticated) {
    return null // Should be handled by AuthGuard
  }

  if (!hasRequiredRole) {
    if (redirectTo) {
      return null // Will redirect
    }
    if (showUnauthorized) {
      return <UnauthorizedMessage message={`This page requires one of the following roles: ${requiredRoles.join(', ')}`} />
    }
    return fallback || null
  }

  return <>{children}</>
}

// Branch Guard Component (for branch-specific access)
interface BranchGuardProps {
  children: React.ReactNode
  allowedBranchId?: string
  fallback?: React.ReactNode
  showUnauthorized?: boolean
}

export function BranchGuard({
  children,
  allowedBranchId,
  fallback,
  showUnauthorized = true
}: BranchGuardProps) {
  const { user, isLoading, isAuthenticated } = useAuthStore()

  if (isLoading) {
    return fallback || <LoadingSpinner />
  }

  if (!isAuthenticated || !user) {
    return null // Should be handled by AuthGuard
  }

  // Overall admins can access all branches
  if (user.role === 'overall_admin') {
    return <>{children}</>
  }

  // Branch managers can only access their own branch
  if (user.role === 'branch_manager') {
    if (allowedBranchId && user.branchId !== allowedBranchId) {
      if (showUnauthorized) {
        return <UnauthorizedMessage message="You can only access data from your assigned branch." />
      }
      return fallback || null
    }
  }

  return <>{children}</>
}

// Higher-Order Component for Auth Protection
export function withAuth<P extends object>(
  Component: ComponentType<P>,
  options?: {
    redirectTo?: string
    fallback?: React.ReactNode
  }
) {
  const AuthenticatedComponent = (props: P) => {
    return (
      <AuthGuard redirectTo={options?.redirectTo} fallback={options?.fallback}>
        <Component {...props} />
      </AuthGuard>
    )
  }

  AuthenticatedComponent.displayName = `withAuth(${Component.displayName || Component.name})`
  return AuthenticatedComponent
}

// Higher-Order Component for Role Protection
export function withRole<P extends object>(
  Component: ComponentType<P>,
  requiredRoles: UserRole[],
  options?: {
    redirectTo?: string
    fallback?: React.ReactNode
    showUnauthorized?: boolean
  }
) {
  const RoleProtectedComponent = (props: P) => {
    return (
      <AuthGuard>
        <RoleGuard 
          requiredRoles={requiredRoles}
          redirectTo={options?.redirectTo}
          fallback={options?.fallback}
          showUnauthorized={options?.showUnauthorized}
        >
          <Component {...props} />
        </RoleGuard>
      </AuthGuard>
    )
  }

  RoleProtectedComponent.displayName = `withRole(${Component.displayName || Component.name})`
  return RoleProtectedComponent
}

// Higher-Order Component for Branch Protection
export function withBranch<P extends object>(
  Component: ComponentType<P>,
  options?: {
    allowedBranchId?: string
    fallback?: React.ReactNode
    showUnauthorized?: boolean
  }
) {
  const BranchProtectedComponent = (props: P) => {
    return (
      <AuthGuard>
        <BranchGuard 
          allowedBranchId={options?.allowedBranchId}
          fallback={options?.fallback}
          showUnauthorized={options?.showUnauthorized}
        >
          <Component {...props} />
        </BranchGuard>
      </AuthGuard>
    )
  }

  BranchProtectedComponent.displayName = `withBranch(${Component.displayName || Component.name})`
  return BranchProtectedComponent
}

// Combined HOC for Auth + Role + Branch protection
export function withAuthAndRole<P extends object>(
  Component: ComponentType<P>,
  requiredRoles: UserRole[],
  options?: {
    allowedBranchId?: string
    redirectTo?: string
    fallback?: React.ReactNode
    showUnauthorized?: boolean
  }
) {
  const ProtectedComponent = (props: P) => {
    return (
      <AuthGuard redirectTo={options?.redirectTo} fallback={options?.fallback}>
        <RoleGuard 
          requiredRoles={requiredRoles}
          redirectTo={options?.redirectTo}
          fallback={options?.fallback}
          showUnauthorized={options?.showUnauthorized}
        >
          <BranchGuard 
            allowedBranchId={options?.allowedBranchId}
            fallback={options?.fallback}
            showUnauthorized={options?.showUnauthorized}
          >
            <Component {...props} />
          </BranchGuard>
        </RoleGuard>
      </AuthGuard>
    )
  }

  ProtectedComponent.displayName = `withAuthAndRole(${Component.displayName || Component.name})`
  return ProtectedComponent
}

// Hook for conditional rendering based on permissions
export function usePermissions() {
  const { user } = useAuthStore()

  const hasRole = (roles: UserRole[]) => {
    return user ? roles.includes(user.role) : false
  }

  const canAccessBranch = (branchId?: string) => {
    if (!user) return false
    if (user.role === 'overall_admin') return true
    if (user.role === 'branch_manager') {
      return !branchId || user.branchId === branchId
    }
    return false
  }

  const isOverallAdmin = () => hasRole(['overall_admin'])
  const isBranchManager = () => hasRole(['branch_manager'])

  return {
    user,
    hasRole,
    canAccessBranch,
    isOverallAdmin,
    isBranchManager
  }
}
