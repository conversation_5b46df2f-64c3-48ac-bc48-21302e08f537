import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { ThemeMode, ThemeColor } from '@/types'

interface ThemeState {
  mode: ThemeMode
  color: ThemeColor
  systemTheme: ThemeMode
  
  // Actions
  setMode: (mode: ThemeMode) => void
  setColor: (color: ThemeColor) => void
  setSystemTheme: (theme: ThemeMode) => void
  toggleMode: () => void
  
  // Computed
  getEffectiveTheme: () => ThemeMode
}

// Theme color configurations
export const themeColors = {
  sky: {
    light: {
      primary: 'oklch(0.646 0.222 220.116)', // Sky blue
      primaryForeground: 'oklch(0.985 0 0)',
      secondary: 'oklch(0.96 0.013 220.116)',
      secondaryForeground: 'oklch(0.205 0 0)',
      accent: 'oklch(0.94 0.026 220.116)',
      accentForeground: 'oklch(0.205 0 0)',
      muted: 'oklch(0.97 0.006 220.116)',
      mutedForeground: 'oklch(0.556 0.013 220.116)',
      border: 'oklch(0.922 0.013 220.116)',
      input: 'oklch(0.922 0.013 220.116)',
      ring: 'oklch(0.646 0.222 220.116)',
    },
    dark: {
      primary: 'oklch(0.546 0.222 220.116)',
      primaryForeground: 'oklch(0.985 0 0)',
      secondary: 'oklch(0.16 0.013 220.116)',
      secondaryForeground: 'oklch(0.805 0 0)',
      accent: 'oklch(0.24 0.026 220.116)',
      accentForeground: 'oklch(0.805 0 0)',
      muted: 'oklch(0.17 0.006 220.116)',
      mutedForeground: 'oklch(0.656 0.013 220.116)',
      border: 'oklch(0.222 0.013 220.116)',
      input: 'oklch(0.222 0.013 220.116)',
      ring: 'oklch(0.546 0.222 220.116)',
    }
  },
  green: {
    light: {
      primary: 'oklch(0.546 0.222 142.116)', // Green
      primaryForeground: 'oklch(0.985 0 0)',
      secondary: 'oklch(0.96 0.013 142.116)',
      secondaryForeground: 'oklch(0.205 0 0)',
      accent: 'oklch(0.94 0.026 142.116)',
      accentForeground: 'oklch(0.205 0 0)',
      muted: 'oklch(0.97 0.006 142.116)',
      mutedForeground: 'oklch(0.556 0.013 142.116)',
      border: 'oklch(0.922 0.013 142.116)',
      input: 'oklch(0.922 0.013 142.116)',
      ring: 'oklch(0.546 0.222 142.116)',
    },
    dark: {
      primary: 'oklch(0.446 0.222 142.116)',
      primaryForeground: 'oklch(0.985 0 0)',
      secondary: 'oklch(0.16 0.013 142.116)',
      secondaryForeground: 'oklch(0.805 0 0)',
      accent: 'oklch(0.24 0.026 142.116)',
      accentForeground: 'oklch(0.805 0 0)',
      muted: 'oklch(0.17 0.006 142.116)',
      mutedForeground: 'oklch(0.656 0.013 142.116)',
      border: 'oklch(0.222 0.013 142.116)',
      input: 'oklch(0.222 0.013 142.116)',
      ring: 'oklch(0.446 0.222 142.116)',
    }
  },
  forest: {
    light: {
      primary: 'oklch(0.446 0.222 152.116)', // Forest green
      primaryForeground: 'oklch(0.985 0 0)',
      secondary: 'oklch(0.96 0.013 152.116)',
      secondaryForeground: 'oklch(0.205 0 0)',
      accent: 'oklch(0.94 0.026 152.116)',
      accentForeground: 'oklch(0.205 0 0)',
      muted: 'oklch(0.97 0.006 152.116)',
      mutedForeground: 'oklch(0.556 0.013 152.116)',
      border: 'oklch(0.922 0.013 152.116)',
      input: 'oklch(0.922 0.013 152.116)',
      ring: 'oklch(0.446 0.222 152.116)',
    },
    dark: {
      primary: 'oklch(0.346 0.222 152.116)',
      primaryForeground: 'oklch(0.985 0 0)',
      secondary: 'oklch(0.16 0.013 152.116)',
      secondaryForeground: 'oklch(0.805 0 0)',
      accent: 'oklch(0.24 0.026 152.116)',
      accentForeground: 'oklch(0.805 0 0)',
      muted: 'oklch(0.17 0.006 152.116)',
      mutedForeground: 'oklch(0.656 0.013 152.116)',
      border: 'oklch(0.222 0.013 152.116)',
      input: 'oklch(0.222 0.013 152.116)',
      ring: 'oklch(0.346 0.222 152.116)',
    }
  },
  red: {
    light: {
      primary: 'oklch(0.577 0.245 27.325)', // Red
      primaryForeground: 'oklch(0.985 0 0)',
      secondary: 'oklch(0.96 0.013 27.325)',
      secondaryForeground: 'oklch(0.205 0 0)',
      accent: 'oklch(0.94 0.026 27.325)',
      accentForeground: 'oklch(0.205 0 0)',
      muted: 'oklch(0.97 0.006 27.325)',
      mutedForeground: 'oklch(0.556 0.013 27.325)',
      border: 'oklch(0.922 0.013 27.325)',
      input: 'oklch(0.922 0.013 27.325)',
      ring: 'oklch(0.577 0.245 27.325)',
    },
    dark: {
      primary: 'oklch(0.477 0.245 27.325)',
      primaryForeground: 'oklch(0.985 0 0)',
      secondary: 'oklch(0.16 0.013 27.325)',
      secondaryForeground: 'oklch(0.805 0 0)',
      accent: 'oklch(0.24 0.026 27.325)',
      accentForeground: 'oklch(0.805 0 0)',
      muted: 'oklch(0.17 0.006 27.325)',
      mutedForeground: 'oklch(0.656 0.013 27.325)',
      border: 'oklch(0.222 0.013 27.325)',
      input: 'oklch(0.222 0.013 27.325)',
      ring: 'oklch(0.477 0.245 27.325)',
    }
  }
} as const

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      mode: 'light',
      color: 'sky',
      systemTheme: 'light',
      
      setMode: (mode) => {
        set({ mode })
        applyTheme(mode, get().color)
      },
      
      setColor: (color) => {
        set({ color })
        applyTheme(get().getEffectiveTheme(), color)
      },
      
      setSystemTheme: (theme) => {
        set({ systemTheme: theme })
        if (get().mode === 'system') {
          applyTheme(theme, get().color)
        }
      },
      
      toggleMode: () => {
        const currentMode = get().mode
        const newMode = currentMode === 'light' ? 'dark' : 'light'
        get().setMode(newMode)
      },
      
      getEffectiveTheme: () => {
        const { mode, systemTheme } = get()
        return mode === 'system' ? systemTheme : mode
      },
    }),
    {
      name: 'fathahitech-theme-store',
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Apply theme on hydration
          applyTheme(state.getEffectiveTheme(), state.color)
          
          // Set up system theme detection
          if (typeof window !== 'undefined') {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
            state.setSystemTheme(mediaQuery.matches ? 'dark' : 'light')
            
            mediaQuery.addEventListener('change', (e) => {
              state.setSystemTheme(e.matches ? 'dark' : 'light')
            })
          }
        }
      },
    }
  )
)

// Helper function to apply theme to document
function applyTheme(mode: ThemeMode, color: ThemeColor) {
  if (typeof window === 'undefined') return
  
  const root = document.documentElement
  const colorConfig = themeColors[color][mode]
  
  // Apply theme mode class
  root.classList.remove('light', 'dark')
  root.classList.add(mode)
  
  // Apply color scheme CSS variables
  Object.entries(colorConfig).forEach(([key, value]) => {
    const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`
    root.style.setProperty(cssVar, value)
  })
  
  // Apply theme-color meta tag for mobile browsers
  const themeColorMeta = document.querySelector('meta[name="theme-color"]')
  if (themeColorMeta) {
    themeColorMeta.setAttribute('content', colorConfig.primary)
  } else {
    const meta = document.createElement('meta')
    meta.name = 'theme-color'
    meta.content = colorConfig.primary
    document.head.appendChild(meta)
  }
}
