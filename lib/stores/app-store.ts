import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { User, Shop, Product, Order, Customer, ActivityLog } from '@/types'

interface AppState {
  // User state
  currentUser: User | null
  
  // Shops state
  shops: Shop[]
  selectedShop: Shop | null
  
  // Products state
  products: Product[]
  selectedProduct: Product | null
  
  // Orders state
  orders: Order[]
  selectedOrder: Order | null
  
  // Customers state
  customers: Customer[]
  selectedCustomer: Customer | null
  
  // Activity logs
  activityLogs: ActivityLog[]
  
  // UI state
  isLoading: boolean
  error: string | null
  
  // Sidebar state
  sidebarCollapsed: boolean
  
  // Actions
  setCurrentUser: (user: User | null) => void
  
  // Shop actions
  setShops: (shops: Shop[]) => void
  setSelectedShop: (shop: Shop | null) => void
  addShop: (shop: Shop) => void
  updateShop: (shopId: string, updates: Partial<Shop>) => void
  removeShop: (shopId: string) => void
  
  // Product actions
  setProducts: (products: Product[]) => void
  setSelectedProduct: (product: Product | null) => void
  addProduct: (product: Product) => void
  updateProduct: (productId: string, updates: Partial<Product>) => void
  removeProduct: (productId: string) => void
  
  // Order actions
  setOrders: (orders: Order[]) => void
  setSelectedOrder: (order: Order | null) => void
  addOrder: (order: Order) => void
  updateOrder: (orderId: string, updates: Partial<Order>) => void
  
  // Customer actions
  setCustomers: (customers: Customer[]) => void
  setSelectedCustomer: (customer: Customer | null) => void
  addCustomer: (customer: Customer) => void
  updateCustomer: (customerId: string, updates: Partial<Customer>) => void
  
  // Activity log actions
  addActivityLog: (log: ActivityLog) => void
  setActivityLogs: (logs: ActivityLog[]) => void
  
  // UI actions
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setSidebarCollapsed: (collapsed: boolean) => void
  
  // Utility actions
  reset: () => void
}

const initialState = {
  currentUser: null,
  shops: [],
  selectedShop: null,
  products: [],
  selectedProduct: null,
  orders: [],
  selectedOrder: null,
  customers: [],
  selectedCustomer: null,
  activityLogs: [],
  isLoading: false,
  error: null,
  sidebarCollapsed: false,
}

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,
        
        // User actions
        setCurrentUser: (user) => set({ currentUser: user }),
        
        // Shop actions
        setShops: (shops) => set({ shops }),
        setSelectedShop: (shop) => set({ selectedShop: shop }),
        addShop: (shop) => set((state) => ({ shops: [...state.shops, shop] })),
        updateShop: (shopId, updates) => set((state) => ({
          shops: state.shops.map(shop => 
            shop.id === shopId ? { ...shop, ...updates } : shop
          ),
          selectedShop: state.selectedShop?.id === shopId 
            ? { ...state.selectedShop, ...updates } 
            : state.selectedShop
        })),
        removeShop: (shopId) => set((state) => ({
          shops: state.shops.filter(shop => shop.id !== shopId),
          selectedShop: state.selectedShop?.id === shopId ? null : state.selectedShop
        })),
        
        // Product actions
        setProducts: (products) => set({ products }),
        setSelectedProduct: (product) => set({ selectedProduct: product }),
        addProduct: (product) => set((state) => ({ products: [...state.products, product] })),
        updateProduct: (productId, updates) => set((state) => ({
          products: state.products.map(product => 
            product.id === productId ? { ...product, ...updates } : product
          ),
          selectedProduct: state.selectedProduct?.id === productId 
            ? { ...state.selectedProduct, ...updates } 
            : state.selectedProduct
        })),
        removeProduct: (productId) => set((state) => ({
          products: state.products.filter(product => product.id !== productId),
          selectedProduct: state.selectedProduct?.id === productId ? null : state.selectedProduct
        })),
        
        // Order actions
        setOrders: (orders) => set({ orders }),
        setSelectedOrder: (order) => set({ selectedOrder: order }),
        addOrder: (order) => set((state) => ({ orders: [...state.orders, order] })),
        updateOrder: (orderId, updates) => set((state) => ({
          orders: state.orders.map(order => 
            order.id === orderId ? { ...order, ...updates } : order
          ),
          selectedOrder: state.selectedOrder?.id === orderId 
            ? { ...state.selectedOrder, ...updates } 
            : state.selectedOrder
        })),
        
        // Customer actions
        setCustomers: (customers) => set({ customers }),
        setSelectedCustomer: (customer) => set({ selectedCustomer: customer }),
        addCustomer: (customer) => set((state) => ({ customers: [...state.customers, customer] })),
        updateCustomer: (customerId, updates) => set((state) => ({
          customers: state.customers.map(customer => 
            customer.id === customerId ? { ...customer, ...updates } : customer
          ),
          selectedCustomer: state.selectedCustomer?.id === customerId 
            ? { ...state.selectedCustomer, ...updates } 
            : state.selectedCustomer
        })),
        
        // Activity log actions
        addActivityLog: (log) => set((state) => ({ 
          activityLogs: [log, ...state.activityLogs].slice(0, 100) // Keep only last 100 logs
        })),
        setActivityLogs: (logs) => set({ activityLogs: logs }),
        
        // UI actions
        setLoading: (loading) => set({ isLoading: loading }),
        setError: (error) => set({ error }),
        setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
        
        // Utility actions
        reset: () => set(initialState),
      }),
      {
        name: 'fathahitech-app-store',
        partialize: (state) => ({
          sidebarCollapsed: state.sidebarCollapsed,
          // Don't persist sensitive data like currentUser
        }),
      }
    ),
    {
      name: 'fathahitech-app-store',
    }
  )
)
