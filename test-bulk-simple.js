// Simple test script using Node.js built-in modules
const fs = require('fs');
const http = require('http');

// First, let's test the login to get a token
function testLogin() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      email: '<EMAIL>',
      password: '@Admin2020'
    });

    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('✅ Login Response:', result);
          
          if (result.success && result.accessToken) {
            resolve(result.accessToken);
          } else {
            reject(new Error('Login failed: ' + (result.error || 'Unknown error')));
          }
        } catch (error) {
          reject(new Error('Failed to parse login response: ' + error.message));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error('Login request failed: ' + error.message));
    });

    req.write(postData);
    req.end();
  });
}

// Test the bulk import with a token
function testBulkImport(token) {
  return new Promise((resolve, reject) => {
    // Read the CSV file
    let csvContent;
    try {
      csvContent = fs.readFileSync('./test-inventory-5-items.csv', 'utf8');
      console.log('📄 CSV Content loaded:');
      console.log(csvContent);
    } catch (error) {
      reject(new Error('Failed to read CSV file: ' + error.message));
      return;
    }

    // Create multipart form data manually
    const boundary = '----formdata-boundary-' + Math.random().toString(36);
    const formData = [
      `--${boundary}`,
      'Content-Disposition: form-data; name="file"; filename="test-inventory-5-items.csv"',
      'Content-Type: text/csv',
      '',
      csvContent,
      `--${boundary}--`
    ].join('\r\n');

    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/inventory/bulk-import',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': `multipart/form-data; boundary=${boundary}`,
        'Content-Length': Buffer.byteLength(formData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          console.log('\n📊 Bulk Import Response:');
          console.log('Status:', res.statusCode);
          console.log('Result:', JSON.stringify(result, null, 2));
          
          if (result.success) {
            console.log('\n🎉 BULK IMPORT SUCCESSFUL!');
            console.log(`📊 Total: ${result.data.total}`);
            console.log(`✅ Imported: ${result.data.imported}`);
            console.log(`❌ Failed: ${result.data.failed}`);
            
            if (result.data.errors && result.data.errors.length > 0) {
              console.log('\n⚠️  Errors:');
              result.data.errors.forEach(error => {
                console.log(`  Row ${error.row}: ${error.message}`);
              });
            }
          } else {
            console.log('\n❌ BULK IMPORT FAILED:', result.error);
          }
          
          resolve(result);
        } catch (error) {
          reject(new Error('Failed to parse bulk import response: ' + error.message));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error('Bulk import request failed: ' + error.message));
    });

    req.write(formData);
    req.end();
  });
}

// Main test function
async function runTest() {
  try {
    console.log('🚀 Starting Bulk Import Test...');
    console.log('=' .repeat(50));
    
    console.log('🔐 Step 1: Authenticating...');
    const token = await testLogin();
    console.log('✅ Authentication successful!');
    console.log('🔑 Token length:', token.length);
    
    console.log('\n📤 Step 2: Testing bulk import...');
    const result = await testBulkImport(token);
    
    console.log('\n✨ Test completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
runTest();
