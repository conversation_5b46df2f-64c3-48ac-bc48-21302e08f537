import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { apiClient } from '@/services/frontend'
import type { ApiResponse } from '@/types/frontend'

// Branch Types
export interface Branch {
  _id: string
  name: string
  location: string
  country: string
  region: string
  manager: string
  managerId: string
  status: 'Active' | 'Inactive' | 'Opening Soon' | 'Maintenance'
  address: string
  phone: string
  email: string
  description: string
  image?: string
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  coordinates?: {
    lat: number
    lng: number
  }
  totalProducts: number
  totalSales: number
  createdAt: string
  updatedAt: string
}

export interface CreateBranchData {
  name: string
  location: string
  country: string
  region: string
  managerId: string
  description: string
  address: string
  phone: string
  email: string
  operatingHours: {
    open: string
    close: string
    timezone: string
  }
  coordinates?: {
    lat: number
    lng: number
  }
  image?: string
}

export interface UpdateBranchData {
  name?: string
  location?: string
  country?: string
  region?: string
  managerId?: string
  description?: string
  address?: string
  phone?: string
  email?: string
  status?: 'Active' | 'Inactive' | 'Opening Soon' | 'Maintenance'
  operatingHours?: {
    open: string
    close: string
    timezone: string
  }
  coordinates?: {
    lat: number
    lng: number
  }
  image?: string
}

export interface BranchFilters {
  search?: string
  country?: string
  region?: string
  status?: string
  managerId?: string
}

// Branch Store State
export interface BranchState {
  // State
  branches: Branch[]
  selectedBranch: Branch | null
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean
  error: string | null

  // Pagination
  currentPage: number
  totalPages: number
  totalItems: number

  // Actions
  fetchBranches: (filters?: BranchFilters) => Promise<void>
  fetchBranchById: (branchId: string) => Promise<Branch | null>
  createBranch: (branchData: CreateBranchData) => Promise<Branch | null>
  updateBranch: (branchId: string, updates: UpdateBranchData) => Promise<Branch | null>
  deleteBranch: (branchId: string) => Promise<boolean>
  
  // Utility actions
  clearError: () => void
  setSelectedBranch: (branch: Branch | null) => void
  
  // Computed getters
  getActiveBranches: () => Branch[]
  getBranchesByRegion: (region: string) => Branch[]
  getBranchByManager: (managerId: string) => Branch | undefined
}

// Helper function to ensure auth token is set
const ensureAuthToken = (): boolean => {
  if (typeof window === 'undefined') return false

  const currentToken = apiClient.getAuthToken()
  if (currentToken) {
    console.log('Branch Store: Auth token already set')
    return true
  }

  // Try to get token from auth store first
  try {
    const { useAuthStore } = require('./authStore')
    const authState = useAuthStore.getState()

    if (authState.isAuthenticated && authState.token) {
      console.log('Branch Store: Setting auth token from auth store')
      apiClient.setAuthToken(authState.token)
      return true
    }
  } catch (error) {
    console.warn('Branch Store: Could not access auth store:', error)
  }

  // Fallback to localStorage
  const storedToken = localStorage.getItem('accessToken')
  if (storedToken) {
    console.log('Branch Store: Setting auth token from localStorage')
    apiClient.setAuthToken(storedToken)
    return true
  }

  console.log('Branch Store: No auth token available')
  return false
}

export const useBranchStore = create<BranchState>()(
  persist(
    immer((set, get) => ({
      // Initial state
      branches: [],
      selectedBranch: null,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      error: null,
      currentPage: 1,
      totalPages: 0,
      totalItems: 0,

      // CRUD Operations
      fetchBranches: async (filters = {}) => {
        console.log("=== BRANCH STORE FETCH START ===")

        set((state) => {
          state.isLoading = true
          state.error = null
        })

        try {
          // Ensure auth token is available
          if (!ensureAuthToken()) {
            // Try to refresh token if not available
            try {
              const { useAuthStore } = require('./authStore')
              const authStore = useAuthStore.getState()

              if (authStore.isAuthenticated) {
                const refreshResult = await authStore.refreshToken()
                if (!refreshResult.success) {
                  throw new Error('Authentication required - please login again')
                }
              } else {
                throw new Error('Authentication required - please login')
              }
            } catch (authError) {
              set((state) => {
                state.error = authError instanceof Error ? authError.message : 'Authentication required'
                state.isLoading = false
              })
              return
            }
          }

          // Build query parameters
          const params = new URLSearchParams()
          Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
              params.append(key, String(value))
            }
          })

          const queryString = params.toString()
          const url = queryString ? `/api/shops?${queryString}` : '/api/shops'

          console.log("Branch Store: Calling URL:", url)
          const response = await apiClient.get<Branch[]>(url)
          console.log("Branch Store: API response:", response)

          if (response.success && response.data) {
            set((state) => {
              state.branches = response.data!
              state.currentPage = response.pagination?.page || 1
              state.totalPages = response.pagination?.totalPages || 0
              state.totalItems = response.pagination?.total || 0
              state.isLoading = false
              state.error = null
            })
            console.log("=== BRANCH STORE FETCH SUCCESS ===")
          } else {
            set((state) => {
              state.error = response.error || 'Failed to fetch branches'
              state.isLoading = false
            })
            console.log("=== BRANCH STORE FETCH FAILED ===", response.error)
          }
        } catch (error) {
          console.error("=== BRANCH STORE FETCH EXCEPTION ===")
          console.error("Branch Store: Exception during fetch:", error)
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch branches'
            state.isLoading = false
          })
        }
      },

      fetchBranchById: async (branchId: string) => {
        console.log("=== BRANCH STORE FETCH BY ID START ===")
        
        if (!ensureAuthToken()) {
          set((state) => {
            state.error = 'Authentication required'
            state.isLoading = false
          })
          return null
        }

        set((state) => {
          state.isLoading = true
          state.error = null
        })

        try {
          const response = await apiClient.get<Branch>(`/api/shops/${branchId}`)
          
          if (response.success && response.data) {
            set((state) => {
              state.selectedBranch = response.data!
              state.isLoading = false
            })
            console.log("=== BRANCH STORE FETCH BY ID SUCCESS ===")
            return response.data
          } else {
            set((state) => {
              state.error = response.error || 'Failed to fetch branch'
              state.isLoading = false
            })
            console.log("=== BRANCH STORE FETCH BY ID FAILED ===")
            return null
          }
        } catch (error) {
          console.error("=== BRANCH STORE FETCH BY ID EXCEPTION ===")
          console.error("Branch Store: Exception during fetch by ID:", error)
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to fetch branch'
            state.isLoading = false
          })
          return null
        }
      },

      createBranch: async (branchData: CreateBranchData) => {
        console.log("=== BRANCH STORE CREATE START ===")
        
        if (!ensureAuthToken()) {
          set((state) => {
            state.error = 'Authentication required'
            state.isCreating = false
          })
          return null
        }

        set((state) => {
          state.isCreating = true
          state.error = null
        })

        try {
          const response = await apiClient.post<Branch>('/api/shops', branchData)
          
          if (response.success && response.data) {
            set((state) => {
              state.branches.push(response.data!)
              state.totalItems += 1
              state.isCreating = false
            })
            console.log("=== BRANCH STORE CREATE SUCCESS ===")
            return response.data
          } else {
            set((state) => {
              state.error = response.error || 'Failed to create branch'
              state.isCreating = false
            })
            console.log("=== BRANCH STORE CREATE FAILED ===")
            return null
          }
        } catch (error) {
          console.error("=== BRANCH STORE CREATE EXCEPTION ===")
          console.error("Branch Store: Exception during create:", error)
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to create branch'
            state.isCreating = false
          })
          return null
        }
      },

      updateBranch: async (branchId: string, updates: UpdateBranchData) => {
        console.log("=== BRANCH STORE UPDATE START ===")
        
        if (!ensureAuthToken()) {
          set((state) => {
            state.error = 'Authentication required'
            state.isUpdating = false
          })
          return null
        }

        set((state) => {
          state.isUpdating = true
          state.error = null
        })

        try {
          const response = await apiClient.put<Branch>(`/api/shops/${branchId}`, updates)
          
          if (response.success && response.data) {
            set((state) => {
              const index = state.branches.findIndex(branch => branch._id === branchId)
              if (index !== -1) {
                state.branches[index] = response.data!
              }
              if (state.selectedBranch?._id === branchId) {
                state.selectedBranch = response.data!
              }
              state.isUpdating = false
            })
            console.log("=== BRANCH STORE UPDATE SUCCESS ===")
            return response.data
          } else {
            set((state) => {
              state.error = response.error || 'Failed to update branch'
              state.isUpdating = false
            })
            console.log("=== BRANCH STORE UPDATE FAILED ===")
            return null
          }
        } catch (error) {
          console.error("=== BRANCH STORE UPDATE EXCEPTION ===")
          console.error("Branch Store: Exception during update:", error)
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update branch'
            state.isUpdating = false
          })
          return null
        }
      },

      deleteBranch: async (branchId: string) => {
        console.log("=== BRANCH STORE DELETE START ===")
        
        if (!ensureAuthToken()) {
          set((state) => {
            state.error = 'Authentication required'
            state.isDeleting = false
          })
          return false
        }

        set((state) => {
          state.isDeleting = true
          state.error = null
        })

        try {
          const response = await apiClient.delete<boolean>(`/api/shops/${branchId}`)
          
          if (response.success) {
            set((state) => {
              state.branches = state.branches.filter(branch => branch._id !== branchId)
              if (state.selectedBranch?._id === branchId) {
                state.selectedBranch = null
              }
              state.totalItems -= 1
              state.isDeleting = false
            })
            console.log("=== BRANCH STORE DELETE SUCCESS ===")
            return true
          } else {
            set((state) => {
              state.error = response.error || 'Failed to delete branch'
              state.isDeleting = false
            })
            console.log("=== BRANCH STORE DELETE FAILED ===")
            return false
          }
        } catch (error) {
          console.error("=== BRANCH STORE DELETE EXCEPTION ===")
          console.error("Branch Store: Exception during delete:", error)
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete branch'
            state.isDeleting = false
          })
          return false
        }
      },

      // Utility actions
      clearError: () => {
        set((state) => {
          state.error = null
        })
      },

      setSelectedBranch: (branch: Branch | null) => {
        set((state) => {
          state.selectedBranch = branch
        })
      },

      // Computed getters
      getActiveBranches: () => {
        return get().branches.filter(branch => branch.status === 'Active')
      },

      getBranchesByRegion: (region: string) => {
        return get().branches.filter(branch => branch.region === region)
      },

      getBranchByManager: (managerId: string) => {
        return get().branches.find(branch => branch.managerId === managerId)
      }
    })),
    {
      name: 'branch-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        branches: state.branches,
        selectedBranch: state.selectedBranch
      })
    }
  )
)
