// stores/__tests__/employeesStore.basic.test.ts - Basic tests for employees store

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useEmployeesStore } from '../employeesStore'
import type { Employee, CreateEmployeeData } from '@/types/frontend'

// Mock the employee service
vi.mock('@/services/frontend', () => ({
  employeeService: {
    getEmployees: vi.fn(),
    getEmployeeById: vi.fn(),
    createEmployee: vi.fn(),
    updateEmployee: vi.fn(),
    deleteEmployee: vi.fn(),
  }
}))

describe('EmployeesStore - Basic Tests', () => {
  beforeEach(() => {
    // Reset store state before each test
    useEmployeesStore.getState().reset()
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const state = useEmployeesStore.getState()
      
      expect(state.employees).toEqual([])
      expect(state.selectedEmployee).toBeNull()
      expect(state.isLoading).toBe(false)
      expect(state.isCreating).toBe(false)
      expect(state.isUpdating).toBe(false)
      expect(state.isDeleting).toBe(false)
      expect(state.error).toBeNull()
      expect(state.currentPage).toBe(1)
      expect(state.totalPages).toBe(0)
      expect(state.totalItems).toBe(0)
      expect(state.pageSize).toBe(10)
      expect(state.filters).toEqual({})
      expect(state.searchQuery).toBe('')
      expect(state.selectedTab).toBe('all')
      expect(state.sortBy).toBe('firstName')
      expect(state.sortOrder).toBe('asc')
    })
  })

  describe('UI State Management', () => {
    it('should update search query', () => {
      const { setSearchQuery } = useEmployeesStore.getState()
      
      setSearchQuery('john')
      
      const state = useEmployeesStore.getState()
      expect(state.searchQuery).toBe('john')
      expect(state.currentPage).toBe(1) // Should reset to first page
    })

    it('should update selected tab', () => {
      const { setSelectedTab } = useEmployeesStore.getState()
      
      setSelectedTab('managers')
      
      const state = useEmployeesStore.getState()
      expect(state.selectedTab).toBe('managers')
      expect(state.currentPage).toBe(1) // Should reset to first page
    })

    it('should update sorting', () => {
      const { setSorting } = useEmployeesStore.getState()
      
      setSorting('lastName', 'desc')
      
      const state = useEmployeesStore.getState()
      expect(state.sortBy).toBe('lastName')
      expect(state.sortOrder).toBe('desc')
    })

    it('should update page', () => {
      const { setPage } = useEmployeesStore.getState()
      
      setPage(3)
      
      const state = useEmployeesStore.getState()
      expect(state.currentPage).toBe(3)
    })

    it('should update page size and reset page', () => {
      const { setPageSize, setPage } = useEmployeesStore.getState()
      
      // Set page to 3 first
      setPage(3)
      expect(useEmployeesStore.getState().currentPage).toBe(3)
      
      // Change page size should reset to page 1
      setPageSize(20)
      
      const state = useEmployeesStore.getState()
      expect(state.pageSize).toBe(20)
      expect(state.currentPage).toBe(1)
    })

    it('should update filters and reset page', () => {
      const { setFilters, setPage } = useEmployeesStore.getState()
      
      // Set page to 3 first
      setPage(3)
      expect(useEmployeesStore.getState().currentPage).toBe(3)
      
      // Change filters should reset to page 1
      setFilters({ department: 'Sales' })
      
      const state = useEmployeesStore.getState()
      expect(state.filters).toEqual({ department: 'Sales' })
      expect(state.currentPage).toBe(1)
    })

    it('should set selected employee', () => {
      const { setSelectedEmployee } = useEmployeesStore.getState()
      
      const mockEmployee: Employee = {
        id: 'emp-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        role: 'employee',
        department: 'Sales',
        branchId: 'branch-1',
        branchName: 'Main Branch',
        hireDate: '2023-01-15',
        salary: 50000,
        status: 'Active',
        address: '123 Main St',
        emergencyContact: {
          name: 'Jane Doe',
          phone: '+1234567891',
          relationship: 'spouse'
        },
        permissions: ['view_products']
      }
      
      setSelectedEmployee(mockEmployee)
      
      const state = useEmployeesStore.getState()
      expect(state.selectedEmployee).toEqual(mockEmployee)
    })

    it('should clear error', () => {
      const store = useEmployeesStore.getState()
      
      // Manually set an error
      useEmployeesStore.setState({ error: 'Test error' })
      expect(useEmployeesStore.getState().error).toBe('Test error')
      
      // Clear error
      store.clearError()
      
      expect(useEmployeesStore.getState().error).toBeNull()
    })

    it('should reset store to initial state', () => {
      const { setSearchQuery, setSelectedTab, setPage, reset } = useEmployeesStore.getState()
      
      // Modify state
      setSearchQuery('test')
      setSelectedTab('managers')
      setPage(5)
      
      // Verify state is modified
      let state = useEmployeesStore.getState()
      expect(state.searchQuery).toBe('test')
      expect(state.selectedTab).toBe('managers')
      expect(state.currentPage).toBe(5)
      
      // Reset
      reset()
      
      // Verify state is back to initial
      state = useEmployeesStore.getState()
      expect(state.searchQuery).toBe('')
      expect(state.selectedTab).toBe('all')
      expect(state.currentPage).toBe(1)
    })
  })

  describe('Computed Properties', () => {
    beforeEach(() => {
      // Set up mock employees data
      const mockEmployees: Employee[] = [
        {
          id: 'emp-1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+1234567890',
          role: 'branch_manager',
          department: 'Management',
          branchId: 'branch-1',
          branchName: 'Main Branch',
          hireDate: '2023-01-15',
          salary: 80000,
          status: 'Active',
          address: '123 Main St',
          emergencyContact: { name: 'Jane Doe', phone: '+1234567891', relationship: 'spouse' },
          permissions: ['manage_employees']
        },
        {
          id: 'emp-2',
          firstName: 'Alice',
          lastName: 'Smith',
          email: '<EMAIL>',
          phone: '+1234567892',
          role: 'employee',
          department: 'Sales',
          branchId: 'branch-1',
          branchName: 'Main Branch',
          hireDate: '2023-02-20',
          salary: 50000,
          status: 'Active',
          address: '456 Oak Ave',
          emergencyContact: { name: 'Bob Smith', phone: '+1234567893', relationship: 'spouse' },
          permissions: ['view_products']
        },
        {
          id: 'emp-3',
          firstName: 'Bob',
          lastName: 'Johnson',
          email: '<EMAIL>',
          phone: '+1234567894',
          role: 'employee',
          department: 'IT',
          branchId: 'branch-2',
          branchName: 'Secondary Branch',
          hireDate: '2023-03-10',
          salary: 60000,
          status: 'Inactive',
          address: '789 Pine St',
          emergencyContact: { name: 'Carol Johnson', phone: '+1234567895', relationship: 'spouse' },
          permissions: ['manage_systems']
        }
      ]
      
      useEmployeesStore.setState({ employees: mockEmployees })
    })

    it('should filter employees by search query', () => {
      const { setSearchQuery, getFilteredEmployees } = useEmployeesStore.getState()
      
      setSearchQuery('john')
      const filtered = getFilteredEmployees()
      
      expect(filtered).toHaveLength(2) // John Doe and Bob Johnson
      expect(filtered.map(emp => emp.firstName)).toEqual(['John', 'Bob'])
    })

    it('should filter employees by tab - managers', () => {
      const { setSelectedTab, getFilteredEmployees } = useEmployeesStore.getState()
      
      setSelectedTab('managers')
      const filtered = getFilteredEmployees()
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0].role).toBe('branch_manager')
    })

    it('should filter employees by tab - employees', () => {
      const { setSelectedTab, getFilteredEmployees } = useEmployeesStore.getState()
      
      setSelectedTab('employees')
      const filtered = getFilteredEmployees()
      
      expect(filtered).toHaveLength(2)
      expect(filtered.every(emp => emp.role !== 'branch_manager')).toBe(true)
    })

    it('should filter employees by tab - inactive', () => {
      const { setSelectedTab, getFilteredEmployees } = useEmployeesStore.getState()
      
      setSelectedTab('inactive')
      const filtered = getFilteredEmployees()
      
      expect(filtered).toHaveLength(1)
      expect(filtered[0].status).toBe('Inactive')
    })

    it('should get employees by branch', () => {
      const { getEmployeesByBranch } = useEmployeesStore.getState()
      
      const branchEmployees = getEmployeesByBranch('branch-1')
      
      expect(branchEmployees).toHaveLength(2)
      expect(branchEmployees.every(emp => emp.branchId === 'branch-1')).toBe(true)
    })

    it('should get employees by role', () => {
      const { getEmployeesByRole } = useEmployeesStore.getState()
      
      const managers = getEmployeesByRole('branch_manager')
      
      expect(managers).toHaveLength(1)
      expect(managers[0].role).toBe('branch_manager')
    })

    it('should get active employees', () => {
      const { getActiveEmployees } = useEmployeesStore.getState()
      
      const activeEmployees = getActiveEmployees()
      
      expect(activeEmployees).toHaveLength(2)
      expect(activeEmployees.every(emp => emp.status === 'Active')).toBe(true)
    })

    it('should get inactive employees', () => {
      const { getInactiveEmployees } = useEmployeesStore.getState()
      
      const inactiveEmployees = getInactiveEmployees()
      
      expect(inactiveEmployees).toHaveLength(1)
      expect(inactiveEmployees[0].status).toBe('Inactive')
    })
  })
})
