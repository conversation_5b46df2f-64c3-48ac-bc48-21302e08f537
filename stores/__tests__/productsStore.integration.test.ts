// stores/__tests__/productsStore.integration.test.ts - Products store integration tests

import { renderHook, act } from '@testing-library/react'
import { useProductsStore } from '../productsStore'
import type { Product, ProductCategory, ApiResponse, CreateProductData, UpdateProductData } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  productService: {
    getProductCategories: jest.fn(() => [
      'Desktops', 'Laptops', 'TV Sets', 'Server Systems', 'Amplifiers',
      'Network Devices', 'Security Software', 'Application Software',
      'Mobile Devices', 'Audio Equipment', 'Gaming', 'Accessories'
    ]),
    getProductStatuses: jest.fn(() => ['Available', 'Out of Stock', 'Discontinued', 'Coming Soon']),
    formatPrice: jest.fn((price: number) => `MWK ${price.toLocaleString()}`),
    validateProductData: jest.fn(() => ({ isValid: true, errors: [] })),
    getProducts: jest.fn(),
    getProductById: jest.fn(),
    createProduct: jest.fn(),
    updateProduct: jest.fn(),
    deleteProduct: jest.fn(),
    getProductsByCategory: jest.fn(),
    getFeaturedProducts: jest.fn(),
    getLowStockProducts: jest.fn(),
    searchProducts: jest.fn(),
    updateProductStock: jest.fn(),
    updateProductStatus: jest.fn(),
    toggleFeatured: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Import the mocked module
const { productService } = require('@/services/frontend')
const mockProductService = productService as jest.Mocked<typeof productService>

// Mock product data
const mockProduct: Product = {
  _id: 'product-123',
  name: 'Test Laptop',
  category: 'Laptops',
  price: 500000,
  stock: 10,
  minStockLevel: 5,
  images: ['laptop1.jpg'],
  description: 'Test laptop description',
  specifications: ['Intel i5', '8GB RAM', '256GB SSD'],
  branchId: 'branch-123',
  branchName: 'Test Branch',
  brand: 'TestBrand',
  model: 'TestModel',
  sku: 'LAP-TEST-001',
  status: 'Available',
  isFeatured: false,
  warranty: '1 year',
  tags: ['laptop', 'portable'],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockProducts: Product[] = [
  mockProduct,
  { ...mockProduct, _id: 'product-124', name: 'Test Desktop', category: 'Desktops' },
  { ...mockProduct, _id: 'product-125', name: 'Test Phone', category: 'Mobile Devices' }
]

describe('ProductsStore - Integration Tests', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    
    // Reset store state
    useProductsStore.setState({
      products: [],
      selectedProduct: null,
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 0,
      totalProducts: 0,
      pageSize: 20,
      filters: {},
      searchQuery: '',
      selectedCategory: null,
      featuredProducts: [],
      lowStockProducts: []
    })
  })

  describe('Fetch Products Integration', () => {
    test('should fetch products successfully', async () => {
      const mockResponse: ApiResponse<Product[]> = {
        success: true,
        data: mockProducts,
        pagination: {
          page: 1,
          limit: 20,
          total: 3,
          totalPages: 1
        }
      }

      mockProductService.getProducts.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchProducts()
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.products).toEqual(mockProducts)
      expect(result.current.totalProducts).toBe(3)
      expect(result.current.totalPages).toBe(1)
      expect(result.current.currentPage).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockProductService.getProducts).toHaveBeenCalledWith(
        { page: 1, limit: 20 },
        {}
      )
    })

    test('should handle fetch products failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Failed to fetch products'
      }

      mockProductService.getProducts.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchProducts()
        expect(fetchResult.success).toBe(false)
        expect(fetchResult.error).toBe('Failed to fetch products')
      })

      expect(result.current.products).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Failed to fetch products')
    })

    test('should fetch product by ID successfully', async () => {
      const mockResponse: ApiResponse<Product> = {
        success: true,
        data: mockProduct
      }

      mockProductService.getProductById.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchProductById('product-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.selectedProduct).toEqual(mockProduct)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockProductService.getProductById).toHaveBeenCalledWith('product-123')
    })
  })

  describe('Create Product Integration', () => {
    test('should create product successfully', async () => {
      const createData: CreateProductData = {
        name: 'New Laptop',
        category: 'Laptops',
        price: 600000,
        stock: 15,
        minStockLevel: 5,
        images: ['laptop2.jpg'],
        description: 'New laptop description',
        specifications: ['Intel i7', '16GB RAM', '512GB SSD'],
        branchId: 'branch-123',
        brand: 'NewBrand',
        model: 'NewModel',
        warranty: '2 years',
        tags: ['laptop', 'new']
      }

      const newProduct: Product = {
        ...mockProduct,
        _id: 'product-new',
        ...createData
      }

      const mockResponse: ApiResponse<Product> = {
        success: true,
        data: newProduct
      }

      mockProductService.createProduct.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const createResult = await result.current.createProduct(createData)
        expect(createResult.success).toBe(true)
        expect(createResult.product).toEqual(newProduct)
      })

      expect(result.current.products).toContain(newProduct)
      expect(result.current.totalProducts).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockProductService.validateProductData).toHaveBeenCalledWith(createData)
      expect(mockProductService.createProduct).toHaveBeenCalledWith(createData)
    })

    test('should handle validation error during create', async () => {
      const createData: CreateProductData = {
        name: '', // Invalid name
        category: 'Laptops',
        price: 600000,
        stock: 15,
        minStockLevel: 5,
        images: ['laptop2.jpg'],
        description: 'New laptop description',
        specifications: ['Intel i7', '16GB RAM', '512GB SSD'],
        branchId: 'branch-123',
        brand: 'NewBrand',
        model: 'NewModel',
        warranty: '2 years',
        tags: ['laptop', 'new']
      }

      mockProductService.validateProductData.mockReturnValueOnce({
        isValid: false,
        errors: [{ field: 'name', message: 'Product name is required' }]
      })

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const createResult = await result.current.createProduct(createData)
        expect(createResult.success).toBe(false)
        expect(createResult.error).toBe('Product name is required')
      })

      expect(result.current.products).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Product name is required')
      expect(mockProductService.createProduct).not.toHaveBeenCalled()
    })
  })

  describe('Update Product Integration', () => {
    test('should update product successfully', async () => {
      // Set initial product in store
      useProductsStore.setState({
        products: [mockProduct],
        selectedProduct: mockProduct
      })

      const updateData: UpdateProductData = {
        name: 'Updated Laptop',
        price: 550000
      }

      const updatedProduct: Product = {
        ...mockProduct,
        ...updateData
      }

      const mockResponse: ApiResponse<Product> = {
        success: true,
        data: updatedProduct
      }

      mockProductService.updateProduct.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const updateResult = await result.current.updateProduct('product-123', updateData)
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.products[0]).toEqual(updatedProduct)
      expect(result.current.selectedProduct).toEqual(updatedProduct)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockProductService.validateProductData).toHaveBeenCalledWith(updateData)
      expect(mockProductService.updateProduct).toHaveBeenCalledWith('product-123', updateData)
    })
  })

  describe('Delete Product Integration', () => {
    test('should delete product successfully', async () => {
      // Set initial products in store
      useProductsStore.setState({
        products: mockProducts,
        selectedProduct: mockProduct,
        totalProducts: 3
      })

      const mockResponse: ApiResponse<boolean> = {
        success: true,
        data: true
      }

      mockProductService.deleteProduct.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const deleteResult = await result.current.deleteProduct('product-123')
        expect(deleteResult.success).toBe(true)
      })

      expect(result.current.products).not.toContain(mockProduct)
      expect(result.current.products).toHaveLength(2)
      expect(result.current.selectedProduct).toBeNull()
      expect(result.current.totalProducts).toBe(2)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockProductService.deleteProduct).toHaveBeenCalledWith('product-123')
    })
  })

  describe('Category Integration', () => {
    test('should fetch products by category successfully', async () => {
      const laptopProducts = mockProducts.filter(p => p.category === 'Laptops')
      
      const mockResponse: ApiResponse<Product[]> = {
        success: true,
        data: laptopProducts,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockProductService.getProductsByCategory.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchProductsByCategory('Laptops')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.products).toEqual(laptopProducts)
      expect(result.current.selectedCategory).toBe('Laptops')
      expect(result.current.filters.category).toBe('Laptops')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockProductService.getProductsByCategory).toHaveBeenCalledWith(
        'Laptops',
        { page: 1, limit: 20 }
      )
    })
  })

  describe('Featured Products Integration', () => {
    test('should fetch featured products successfully', async () => {
      const featuredProducts = [{ ...mockProduct, isFeatured: true }]
      
      const mockResponse: ApiResponse<Product[]> = {
        success: true,
        data: featuredProducts
      }

      mockProductService.getFeaturedProducts.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchFeaturedProducts()
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.featuredProducts).toEqual(featuredProducts)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockProductService.getFeaturedProducts).toHaveBeenCalledWith(
        { page: 1, limit: 12 }
      )
    })

    test('should toggle product featured status successfully', async () => {
      // Set initial product in store
      useProductsStore.setState({
        products: [mockProduct],
        selectedProduct: mockProduct
      })

      const featuredProduct: Product = {
        ...mockProduct,
        isFeatured: true
      }

      const mockResponse: ApiResponse<Product> = {
        success: true,
        data: featuredProduct
      }

      mockProductService.toggleFeatured.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const toggleResult = await result.current.toggleProductFeatured('product-123', true)
        expect(toggleResult.success).toBe(true)
      })

      expect(result.current.products[0]).toEqual(featuredProduct)
      expect(result.current.selectedProduct).toEqual(featuredProduct)
      expect(result.current.featuredProducts).toContain(featuredProduct)

      expect(mockProductService.toggleFeatured).toHaveBeenCalledWith('product-123', true)
    })
  })

  describe('Stock Management Integration', () => {
    test('should fetch low stock products successfully', async () => {
      const lowStockProducts = [{ ...mockProduct, stock: 2, minStockLevel: 5 }]
      
      const mockResponse: ApiResponse<Product[]> = {
        success: true,
        data: lowStockProducts
      }

      mockProductService.getLowStockProducts.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchLowStockProducts('branch-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.lowStockProducts).toEqual(lowStockProducts)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockProductService.getLowStockProducts).toHaveBeenCalledWith(
        'branch-123',
        { page: 1, limit: 20 }
      )
    })

    test('should update product stock successfully', async () => {
      // Set initial product in store
      useProductsStore.setState({
        products: [mockProduct],
        selectedProduct: mockProduct
      })

      const updatedProduct: Product = {
        ...mockProduct,
        stock: 25
      }

      const mockResponse: ApiResponse<Product> = {
        success: true,
        data: updatedProduct
      }

      mockProductService.updateProductStock.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const updateResult = await result.current.updateProductStock('product-123', 25)
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.products[0]).toEqual(updatedProduct)
      expect(result.current.selectedProduct).toEqual(updatedProduct)

      expect(mockProductService.updateProductStock).toHaveBeenCalledWith('product-123', 25)
    })
  })

  describe('Search Integration', () => {
    test('should search products successfully', async () => {
      const searchResults = [mockProduct]
      
      const mockResponse: ApiResponse<Product[]> = {
        success: true,
        data: searchResults,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockProductService.searchProducts.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useProductsStore())

      await act(async () => {
        const searchResult = await result.current.searchProducts('laptop')
        expect(searchResult.success).toBe(true)
      })

      expect(result.current.products).toEqual(searchResults)
      expect(result.current.searchQuery).toBe('laptop')
      expect(result.current.filters.search).toBe('laptop')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockProductService.searchProducts).toHaveBeenCalledWith(
        'laptop',
        { page: 1, limit: 20 },
        {}
      )
    })
  })
})
