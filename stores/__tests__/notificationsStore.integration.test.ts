// stores/__tests__/notificationsStore.integration.test.ts - Notifications store integration tests

import { renderHook, act } from '@testing-library/react'
import { useNotificationsStore } from '../notificationsStore'
import type { Notification, NotificationSettings, SystemAlert, UserMessage, NotificationTemplate, ApiResponse } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  notificationsService: {
    validateNotificationData: jest.fn(() => ({ isValid: true, errors: [] })),
    formatNotificationTime: jest.fn(() => '5m ago'),
    getNotificationIcon: jest.fn(() => 'ℹ️'),
    getNotificationColor: jest.fn(() => 'blue'),
    getPriorityLabel: jest.fn(() => 'Medium Priority'),
    isNotificationExpired: jest.fn(() => false),
    canUserAccessNotification: jest.fn(() => true),
    getNotificationSummary: jest.fn(() => 'Notification summary'),
    shouldShowNotification: jest.fn(() => true),
    playNotificationSound: jest.fn(),
    showBrowserNotification: jest.fn(),
    getNotifications: jest.fn(),
    getNotificationById: jest.fn(),
    createNotification: jest.fn(),
    updateNotification: jest.fn(),
    deleteNotification: jest.fn(),
    markAllAsRead: jest.fn(),
    deleteMultiple: jest.fn(),
    sendNotification: jest.fn(),
    broadcastNotification: jest.fn(),
    sendSystemAlert: jest.fn(),
    sendUserMessage: jest.fn(),
    subscribeToPush: jest.fn(),
    unsubscribeFromPush: jest.fn(),
    sendPushNotification: jest.fn(),
    sendEmailNotification: jest.fn(),
    sendSMSNotification: jest.fn(),
    getNotificationSettings: jest.fn(),
    updateNotificationSettings: jest.fn(),
    getNotificationTemplates: jest.fn(),
    createNotificationTemplate: jest.fn(),
    updateNotificationTemplate: jest.fn(),
    deleteNotificationTemplate: jest.fn(),
    enableNotificationChannel: jest.fn(),
    disableNotificationChannel: jest.fn(),
    updateChannelPreferences: jest.fn(),
    searchNotifications: jest.fn()
  },
  socketService: {
    connect: jest.fn(),
    disconnect: jest.fn(),
    reconnect: jest.fn(),
    emit: jest.fn(),
    on: jest.fn(),
    off: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Import the mocked module
const { notificationsService, socketService } = require('@/services/frontend')
const mockNotificationsService = notificationsService as jest.Mocked<typeof notificationsService>
const mockSocketService = socketService as jest.Mocked<typeof socketService>

// Mock notification data
const mockNotification: Notification = {
  _id: 'notification-123',
  title: 'New Order Received',
  message: 'You have received a new order #ORD-2024-001',
  type: 'info',
  priority: 'medium',
  status: 'unread',
  recipientId: 'user-123',
  recipientName: 'John Doe',
  createdBy: 'system',
  createdByName: 'System',
  data: {
    orderId: 'order-123',
    orderNumber: 'ORD-2024-001',
    amount: 150000
  },
  channels: ['in-app', 'email'],
  readAt: null,
  archivedAt: null,
  expiresAt: '2024-02-01T00:00:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockNotifications: Notification[] = [
  mockNotification,
  { ...mockNotification, _id: 'notification-124', title: 'Payment Received', type: 'success' },
  { ...mockNotification, _id: 'notification-125', title: 'Low Stock Alert', type: 'warning', priority: 'high' }
]

const mockNotificationSettings: NotificationSettings = {
  _id: 'settings-123',
  userId: 'user-123',
  enabled: true,
  channels: {
    'in-app': { enabled: true, preferences: {} },
    'email': { enabled: true, preferences: { frequency: 'immediate' } },
    'sms': { enabled: false, preferences: {} },
    'push': { enabled: true, preferences: {} }
  },
  types: {
    'info': true,
    'success': true,
    'warning': true,
    'error': true,
    'system': true,
    'message': true,
    'reminder': true,
    'update': true
  },
  quietHours: {
    enabled: true,
    startTime: '22:00',
    endTime: '08:00',
    timezone: 'Africa/Blantyre'
  },
  soundEnabled: true,
  vibrationEnabled: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockSystemAlert: SystemAlert = {
  _id: 'alert-123',
  title: 'System Maintenance',
  message: 'Scheduled maintenance will begin at 2:00 AM',
  type: 'system',
  priority: 'high',
  status: 'unread',
  recipientId: 'all',
  recipientName: 'All Users',
  createdBy: 'admin',
  createdByName: 'System Admin',
  alertType: 'maintenance',
  severity: 'medium',
  affectedSystems: ['database', 'api'],
  estimatedDuration: '2 hours',
  channels: ['in-app', 'email'],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockUserMessage: UserMessage = {
  _id: 'message-123',
  title: 'Welcome to FathahiTech',
  message: 'Thank you for joining our platform!',
  type: 'message',
  priority: 'low',
  status: 'unread',
  recipientId: 'user-123',
  recipientName: 'John Doe',
  createdBy: 'admin',
  createdByName: 'Admin',
  senderId: 'admin',
  senderName: 'Admin',
  messageType: 'welcome',
  isPrivate: false,
  channels: ['in-app'],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockNotificationTemplate: NotificationTemplate = {
  _id: 'template-123',
  name: 'Order Confirmation',
  description: 'Template for order confirmation notifications',
  type: 'info',
  subject: 'Order Confirmed - {{orderNumber}}',
  body: 'Your order {{orderNumber}} has been confirmed. Total: {{amount}}',
  variables: ['orderNumber', 'amount'],
  channels: ['email', 'in-app'],
  isActive: true,
  createdBy: 'admin',
  createdByName: 'Admin',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('NotificationsStore - Integration Tests', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    
    // Reset store state
    useNotificationsStore.setState({
      notifications: [],
      unreadNotifications: [],
      selectedNotification: null,
      isLoading: false,
      error: null,
      isConnected: false,
      connectionStatus: 'disconnected',
      lastHeartbeat: null,
      unreadCount: 0,
      totalCount: 0,
      priorityCount: {
        low: 0,
        medium: 0,
        high: 0,
        urgent: 0
      },
      systemAlerts: [],
      userMessages: [],
      emailNotifications: [],
      smsNotifications: [],
      inAppNotifications: [],
      notificationSettings: null,
      notificationTemplates: [],
      enabledChannels: [],
      pushSubscription: null,
      pushEnabled: false,
      currentPage: 1,
      totalPages: 0,
      pageSize: 20,
      filters: {},
      searchQuery: ''
    })
  })

  describe('Socket.IO Connection Integration', () => {
    test('should connect to socket successfully', async () => {
      const mockResponse = { success: true }
      mockSocketService.connect.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const connectResult = await result.current.connectSocket('user-123', 'auth-token')
        expect(connectResult.success).toBe(true)
      })

      expect(result.current.isConnected).toBe(true)
      expect(result.current.connectionStatus).toBe('connected')
      expect(result.current.lastHeartbeat).toBeTruthy()

      expect(mockSocketService.connect).toHaveBeenCalledWith('user-123', 'auth-token')
      expect(mockSocketService.on).toHaveBeenCalledTimes(6) // 6 event listeners
    })

    test('should handle socket connection failure', async () => {
      const mockResponse = { success: false, error: 'Connection failed' }
      mockSocketService.connect.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const connectResult = await result.current.connectSocket('user-123', 'auth-token')
        expect(connectResult.success).toBe(false)
        expect(connectResult.error).toBe('Connection failed')
      })

      expect(result.current.isConnected).toBe(false)
      expect(result.current.connectionStatus).toBe('error')
      expect(result.current.error).toBe('Connection failed')
    })

    test('should disconnect socket', async () => {
      // First connect
      useNotificationsStore.setState({
        isConnected: true,
        connectionStatus: 'connected',
        lastHeartbeat: '2024-01-01T00:00:00Z'
      })

      const { result } = renderHook(() => useNotificationsStore())

      act(() => {
        result.current.disconnectSocket()
      })

      expect(result.current.isConnected).toBe(false)
      expect(result.current.connectionStatus).toBe('disconnected')
      expect(result.current.lastHeartbeat).toBeNull()

      expect(mockSocketService.disconnect).toHaveBeenCalled()
    })

    test('should reconnect socket successfully', async () => {
      const mockResponse = { success: true }
      mockSocketService.reconnect.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const reconnectResult = await result.current.reconnectSocket()
        expect(reconnectResult.success).toBe(true)
      })

      expect(result.current.isConnected).toBe(true)
      expect(result.current.connectionStatus).toBe('connected')
      expect(result.current.lastHeartbeat).toBeTruthy()

      expect(mockSocketService.reconnect).toHaveBeenCalled()
    })
  })

  describe('Fetch Notifications Integration', () => {
    test('should fetch notifications successfully', async () => {
      const mockResponse: ApiResponse<Notification[]> = {
        success: true,
        data: mockNotifications,
        pagination: {
          page: 1,
          limit: 20,
          total: 3,
          totalPages: 1
        }
      }

      mockNotificationsService.getNotifications.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchNotifications()
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.notifications).toEqual(mockNotifications)
      expect(result.current.unreadNotifications).toHaveLength(3) // All are unread by default
      expect(result.current.totalCount).toBe(3)
      expect(result.current.unreadCount).toBe(3)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockNotificationsService.getNotifications).toHaveBeenCalledWith(
        { page: 1, limit: 20 },
        {}
      )
    })

    test('should handle fetch notifications failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Failed to fetch notifications'
      }

      mockNotificationsService.getNotifications.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchNotifications()
        expect(fetchResult.success).toBe(false)
        expect(fetchResult.error).toBe('Failed to fetch notifications')
      })

      expect(result.current.notifications).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Failed to fetch notifications')
    })

    test('should fetch notification by ID successfully', async () => {
      const mockResponse: ApiResponse<Notification> = {
        success: true,
        data: mockNotification
      }

      mockNotificationsService.getNotificationById.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchNotificationById('notification-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.selectedNotification).toEqual(mockNotification)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockNotificationsService.getNotificationById).toHaveBeenCalledWith('notification-123')
    })
  })

  describe('Create Notification Integration', () => {
    test('should create notification successfully', async () => {
      const notificationData = {
        title: 'New Test Notification',
        message: 'This is a test notification',
        type: 'info' as const,
        priority: 'medium' as const,
        recipientId: 'user-123'
      }

      const newNotification: Notification = {
        ...mockNotification,
        _id: 'notification-new',
        ...notificationData
      }

      const mockResponse: ApiResponse<Notification> = {
        success: true,
        data: newNotification
      }

      mockNotificationsService.createNotification.mockResolvedValueOnce(mockResponse)

      // Mock socket connection
      useNotificationsStore.setState({ isConnected: true })

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const createResult = await result.current.createNotification(notificationData)
        expect(createResult.success).toBe(true)
        expect(createResult.notification).toEqual(newNotification)
      })

      expect(result.current.notifications).toContain(newNotification)
      expect(result.current.unreadNotifications).toContain(newNotification)
      expect(result.current.totalCount).toBe(1)
      expect(result.current.unreadCount).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockNotificationsService.validateNotificationData).toHaveBeenCalledWith(notificationData)
      expect(mockNotificationsService.createNotification).toHaveBeenCalledWith(notificationData)
      expect(mockSocketService.emit).toHaveBeenCalledWith('notification:send', {
        recipientId: 'user-123',
        notification: newNotification
      })
    })

    test('should handle validation error during create', async () => {
      const notificationData = {
        title: '',
        message: '',
        type: 'info' as const,
        priority: 'medium' as const
      }

      mockNotificationsService.validateNotificationData.mockReturnValueOnce({
        isValid: false,
        errors: [
          { field: 'title', message: 'Title is required' },
          { field: 'message', message: 'Message is required' }
        ]
      })

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const createResult = await result.current.createNotification(notificationData)
        expect(createResult.success).toBe(false)
        expect(createResult.error).toBe('Title is required, Message is required')
      })

      expect(result.current.notifications).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Title is required, Message is required')
      expect(mockNotificationsService.createNotification).not.toHaveBeenCalled()
    })
  })

  describe('Notification Management Integration', () => {
    test('should mark notification as read successfully', async () => {
      // Set initial notification in store
      useNotificationsStore.setState({
        notifications: [mockNotification],
        unreadNotifications: [mockNotification],
        unreadCount: 1
      })

      const readNotification: Notification = {
        ...mockNotification,
        status: 'read',
        readAt: '2024-01-01T12:00:00Z'
      }

      const mockResponse: ApiResponse<Notification> = {
        success: true,
        data: readNotification
      }

      mockNotificationsService.updateNotification.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const markResult = await result.current.markAsRead('notification-123')
        expect(markResult.success).toBe(true)
      })

      expect(result.current.notifications[0]).toEqual(readNotification)
      expect(result.current.unreadNotifications).toHaveLength(0)
      expect(result.current.unreadCount).toBe(0)

      expect(mockNotificationsService.updateNotification).toHaveBeenCalledWith('notification-123', {
        status: 'read',
        readAt: expect.any(String)
      })
    })

    test('should mark all notifications as read successfully', async () => {
      // Set initial notifications in store
      useNotificationsStore.setState({
        notifications: mockNotifications,
        unreadNotifications: mockNotifications,
        unreadCount: 3
      })

      const mockResponse: ApiResponse<boolean> = {
        success: true,
        data: true
      }

      mockNotificationsService.markAllAsRead.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const markAllResult = await result.current.markAllAsRead()
        expect(markAllResult.success).toBe(true)
      })

      expect(result.current.notifications.every(n => n.status === 'read')).toBe(true)
      expect(result.current.unreadNotifications).toHaveLength(0)
      expect(result.current.unreadCount).toBe(0)

      expect(mockNotificationsService.markAllAsRead).toHaveBeenCalled()
    })

    test('should delete multiple notifications successfully', async () => {
      // Set initial notifications in store
      useNotificationsStore.setState({
        notifications: mockNotifications,
        unreadNotifications: mockNotifications,
        unreadCount: 3,
        totalCount: 3
      })

      const notificationIds = ['notification-123', 'notification-124']
      
      const mockResponse: ApiResponse<boolean> = {
        success: true,
        data: true
      }

      mockNotificationsService.deleteMultiple.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const deleteResult = await result.current.deleteMultiple(notificationIds)
        expect(deleteResult.success).toBe(true)
      })

      expect(result.current.notifications).toHaveLength(1)
      expect(result.current.notifications[0]._id).toBe('notification-125')
      expect(result.current.unreadNotifications).toHaveLength(1)
      expect(result.current.unreadCount).toBe(1)
      expect(result.current.totalCount).toBe(1)

      expect(mockNotificationsService.deleteMultiple).toHaveBeenCalledWith(notificationIds)
    })
  })

  describe('Real-time Events Integration', () => {
    test('should send notification successfully', async () => {
      const notificationData = {
        title: 'Real-time Test',
        message: 'This is a real-time notification',
        type: 'info' as const
      }

      const mockResponse: ApiResponse<Notification> = {
        success: true,
        data: { ...mockNotification, ...notificationData }
      }

      mockNotificationsService.sendNotification.mockResolvedValueOnce(mockResponse)

      // Mock socket connection
      useNotificationsStore.setState({ isConnected: true })

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const sendResult = await result.current.sendNotification('user-456', notificationData)
        expect(sendResult.success).toBe(true)
      })

      expect(mockNotificationsService.sendNotification).toHaveBeenCalledWith('user-456', notificationData)
      expect(mockSocketService.emit).toHaveBeenCalledWith('notification:send', {
        recipientId: 'user-456',
        notification: mockResponse.data
      })
    })

    test('should broadcast notification successfully', async () => {
      const notificationData = {
        title: 'System Announcement',
        message: 'Important system update',
        type: 'system' as const
      }

      const userIds = ['user-123', 'user-456', 'user-789']

      const mockResponse: ApiResponse<Notification> = {
        success: true,
        data: { ...mockNotification, ...notificationData }
      }

      mockNotificationsService.broadcastNotification.mockResolvedValueOnce(mockResponse)

      // Mock socket connection
      useNotificationsStore.setState({ isConnected: true })

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const broadcastResult = await result.current.broadcastNotification(notificationData, userIds)
        expect(broadcastResult.success).toBe(true)
      })

      expect(mockNotificationsService.broadcastNotification).toHaveBeenCalledWith(notificationData, userIds)
      expect(mockSocketService.emit).toHaveBeenCalledWith('notification:broadcast', {
        notification: mockResponse.data,
        userIds
      })
    })

    test('should send system alert successfully', async () => {
      const alertData = {
        title: 'Critical System Alert',
        message: 'Database connection lost',
        alertType: 'error' as const,
        severity: 'high' as const
      }

      const mockResponse: ApiResponse<SystemAlert> = {
        success: true,
        data: { ...mockSystemAlert, ...alertData }
      }

      mockNotificationsService.sendSystemAlert.mockResolvedValueOnce(mockResponse)

      // Mock socket connection
      useNotificationsStore.setState({ isConnected: true })

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const alertResult = await result.current.sendSystemAlert(alertData)
        expect(alertResult.success).toBe(true)
      })

      expect(result.current.systemAlerts).toContain(mockResponse.data)
      expect(mockNotificationsService.sendSystemAlert).toHaveBeenCalledWith(alertData)
      expect(mockSocketService.emit).toHaveBeenCalledWith('system:alert', mockResponse.data)
    })

    test('should send user message successfully', async () => {
      const messageData = {
        title: 'Personal Message',
        message: 'Hello from the admin team!',
        messageType: 'personal' as const
      }

      const mockResponse: ApiResponse<UserMessage> = {
        success: true,
        data: { ...mockUserMessage, ...messageData }
      }

      mockNotificationsService.sendUserMessage.mockResolvedValueOnce(mockResponse)

      // Mock socket connection
      useNotificationsStore.setState({ isConnected: true })

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const messageResult = await result.current.sendUserMessage(messageData)
        expect(messageResult.success).toBe(true)
      })

      expect(result.current.userMessages).toContain(mockResponse.data)
      expect(mockNotificationsService.sendUserMessage).toHaveBeenCalledWith(messageData)
      expect(mockSocketService.emit).toHaveBeenCalledWith('user:message', mockResponse.data)
    })
  })

  describe('Push Notifications Integration', () => {
    test('should subscribe to push notifications successfully', async () => {
      const pushSubscription = {
        endpoint: 'https://fcm.googleapis.com/fcm/send/...',
        keys: {
          p256dh: 'key1',
          auth: 'key2'
        }
      }

      const mockResponse: ApiResponse<any> = {
        success: true,
        data: pushSubscription
      }

      mockNotificationsService.subscribeToPush.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const subscribeResult = await result.current.subscribeToPush()
        expect(subscribeResult.success).toBe(true)
      })

      expect(result.current.pushSubscription).toEqual(pushSubscription)
      expect(result.current.pushEnabled).toBe(true)

      expect(mockNotificationsService.subscribeToPush).toHaveBeenCalled()
    })

    test('should unsubscribe from push notifications successfully', async () => {
      // Set initial push subscription
      useNotificationsStore.setState({
        pushSubscription: { endpoint: 'test', keys: { p256dh: 'key1', auth: 'key2' } },
        pushEnabled: true
      })

      const mockResponse: ApiResponse<boolean> = {
        success: true,
        data: true
      }

      mockNotificationsService.unsubscribeFromPush.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const unsubscribeResult = await result.current.unsubscribeFromPush()
        expect(unsubscribeResult.success).toBe(true)
      })

      expect(result.current.pushSubscription).toBeNull()
      expect(result.current.pushEnabled).toBe(false)

      expect(mockNotificationsService.unsubscribeFromPush).toHaveBeenCalled()
    })
  })

  describe('Settings Integration', () => {
    test('should fetch notification settings successfully', async () => {
      const mockResponse: ApiResponse<NotificationSettings> = {
        success: true,
        data: mockNotificationSettings
      }

      mockNotificationsService.getNotificationSettings.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchNotificationSettings()
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.notificationSettings).toEqual(mockNotificationSettings)

      expect(mockNotificationsService.getNotificationSettings).toHaveBeenCalled()
    })

    test('should update notification settings successfully', async () => {
      const settingsUpdate = {
        enabled: false,
        soundEnabled: false
      }

      const updatedSettings: NotificationSettings = {
        ...mockNotificationSettings,
        ...settingsUpdate
      }

      const mockResponse: ApiResponse<NotificationSettings> = {
        success: true,
        data: updatedSettings
      }

      mockNotificationsService.updateNotificationSettings.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const updateResult = await result.current.updateNotificationSettings(settingsUpdate)
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.notificationSettings).toEqual(updatedSettings)

      expect(mockNotificationsService.updateNotificationSettings).toHaveBeenCalledWith(settingsUpdate)
    })
  })

  describe('Templates Integration', () => {
    test('should create notification template successfully', async () => {
      const templateData = {
        name: 'New Template',
        description: 'Test template',
        type: 'info' as const,
        subject: 'Test Subject',
        body: 'Test Body'
      }

      const newTemplate: NotificationTemplate = {
        ...mockNotificationTemplate,
        _id: 'template-new',
        ...templateData
      }

      const mockResponse: ApiResponse<NotificationTemplate> = {
        success: true,
        data: newTemplate
      }

      mockNotificationsService.createNotificationTemplate.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const createResult = await result.current.createNotificationTemplate(templateData)
        expect(createResult.success).toBe(true)
      })

      expect(result.current.notificationTemplates).toContain(newTemplate)

      expect(mockNotificationsService.createNotificationTemplate).toHaveBeenCalledWith(templateData)
    })
  })

  describe('Search Integration', () => {
    test('should search notifications successfully', async () => {
      const searchResults = [mockNotification]
      
      const mockResponse: ApiResponse<Notification[]> = {
        success: true,
        data: searchResults,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockNotificationsService.searchNotifications.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useNotificationsStore())

      await act(async () => {
        const searchResult = await result.current.searchNotifications('order notification')
        expect(searchResult.success).toBe(true)
      })

      expect(result.current.notifications).toEqual(searchResults)
      expect(result.current.searchQuery).toBe('order notification')
      expect(result.current.filters.search).toBe('order notification')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockNotificationsService.searchNotifications).toHaveBeenCalledWith(
        'order notification',
        { page: 1, limit: 20 },
        {}
      )
    })
  })
})
