// stores/__tests__/authStore.integration.test.ts - Authentication store integration tests

import { renderHook, act } from '@testing-library/react'
import { useAuthStore } from '../authStore'
import type { User, UserRole, ApiResponse } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  apiClient: {
    post: jest.fn(),
    put: jest.fn(),
    setAuthToken: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Import the mocked module
const { apiClient, handleServiceError } = require('@/services/frontend')
const mockApiClient = apiClient as jest.Mocked<typeof apiClient>

// Mock user data (using AuthUser structure)
const mockUser = {
  id: 'user-123',
  username: 'testuser',
  email: '<EMAIL>',
  name: '<PERSON>',
  role: 'branch_manager' as const,
  branchId: 'branch-123',
  avatar: null
}

const mockToken = 'mock-jwt-token'

describe('AuthStore - Integration Tests', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    
    // Reset store state
    useAuthStore.getState().logout()
  })

  describe('Login Integration', () => {
    test('should login successfully with valid credentials', async () => {
      const mockResponse = {
        success: true,
        user: mockUser,
        accessToken: mockToken,
        refreshToken: 'refresh-token'
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const loginResult = await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
        
        expect(loginResult.success).toBe(true)
      })

      expect(result.current.user).toEqual(mockUser)
      expect(result.current.token).toBe(mockToken)
      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.userRole).toBe('branch_manager')
      expect(result.current.userBranch).toBe('branch-123')
      expect(result.current.isBranchManager).toBe(true)
      expect(result.current.isAdmin).toBe(false)

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/login', {
        username: 'testuser',
        password: 'password123'
      })
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith(mockToken)
    })

    test('should handle login failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Invalid credentials'
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const loginResult = await result.current.login({
          username: 'testuser',
          password: 'wrongpassword'
        })
        
        expect(loginResult.success).toBe(false)
        expect(loginResult.error).toBe('Invalid credentials')
      })

      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Invalid credentials')
    })

    test('should handle network error during login', async () => {
      const networkError = new Error('Network error')
      mockApiClient.post.mockRejectedValueOnce(networkError)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const loginResult = await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
        
        expect(loginResult.success).toBe(false)
        expect(loginResult.error).toBe('Network error')
      })

      expect(result.current.user).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.error).toBe('Network error')
    })
  })

  describe('Register Integration', () => {
    test('should register successfully with valid data', async () => {
      const mockResponse: ApiResponse<{ user: User; token: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken
        }
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      const registerData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'branch_manager' as UserRole,
        branchId: 'branch-123'
      }

      await act(async () => {
        const registerResult = await result.current.register(registerData)
        expect(registerResult.success).toBe(true)
      })

      expect(result.current.user).toEqual(mockUser)
      expect(result.current.token).toBe(mockToken)
      expect(result.current.isAuthenticated).toBe(true)

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/register', {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'branch_manager',
        branchId: 'branch-123'
      })
    })

    test('should fail registration when passwords do not match', async () => {
      const { result } = renderHook(() => useAuthStore())

      const registerData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'differentpassword',
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'branch_manager' as UserRole,
        branchId: 'branch-123'
      }

      await act(async () => {
        const registerResult = await result.current.register(registerData)
        expect(registerResult.success).toBe(false)
        expect(registerResult.error).toBe('Passwords do not match')
      })

      expect(result.current.user).toBeNull()
      expect(result.current.error).toBe('Passwords do not match')
      expect(mockApiClient.post).not.toHaveBeenCalled()
    })
  })

  describe('Token Refresh Integration', () => {
    test('should refresh token successfully', async () => {
      // First login
      const loginResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      const refreshResponse: ApiResponse<{ user: User; token: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: 'new-token'
        }
      }

      mockApiClient.post
        .mockResolvedValueOnce(loginResponse)
        .mockResolvedValueOnce(refreshResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
      })

      await act(async () => {
        const refreshResult = await result.current.refreshToken()
        expect(refreshResult.success).toBe(true)
      })

      expect(result.current.token).toBe('new-token')
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith('new-token')
    })

    test('should logout on failed token refresh', async () => {
      // First login
      const loginResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      const refreshResponse: ApiResponse<never> = {
        success: false,
        error: 'Token expired'
      }

      mockApiClient.post
        .mockResolvedValueOnce(loginResponse)
        .mockResolvedValueOnce(refreshResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
      })

      expect(result.current.isAuthenticated).toBe(true)

      await act(async () => {
        const refreshResult = await result.current.refreshToken()
        expect(refreshResult.success).toBe(false)
      })

      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
    })
  })

  describe('Profile Update Integration', () => {
    beforeEach(async () => {
      // Login first
      const mockResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
      })
    })

    test('should update profile successfully', async () => {
      const updatedUser: User = {
        ...mockUser,
        firstName: 'Jane',
        lastName: 'Smith',
        phone: '+265987654321'
      }

      const mockResponse: ApiResponse<User> = {
        success: true,
        data: updatedUser
      }

      mockApiClient.put.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const updateResult = await result.current.updateProfile({
          firstName: 'Jane',
          lastName: 'Smith',
          phone: '+265987654321'
        })
        
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.user).toEqual(updatedUser)
      expect(result.current.getDisplayName()).toBe('Jane Smith')
    })
  })

  describe('Password Change Integration', () => {
    beforeEach(async () => {
      // Login first
      const mockResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
      })
    })

    test('should change password successfully', async () => {
      const mockResponse: ApiResponse<{ success: boolean }> = {
        success: true,
        data: { success: true }
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const changeResult = await result.current.changePassword('oldpassword', 'newpassword')
        expect(changeResult.success).toBe(true)
      })

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/change-password', {
        currentPassword: 'oldpassword',
        newPassword: 'newpassword'
      })
    })
  })
})
