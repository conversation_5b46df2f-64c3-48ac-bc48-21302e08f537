// stores/__tests__/inventoryStore.basic.test.ts - Basic inventory store tests

import { useInventoryStore } from '../inventoryStore'
import type { InventoryItem, StockMovement, StockMovementType, LowStockAlert } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  inventoryService: {
    validateInventoryData: jest.fn(() => ({ isValid: true, errors: [] })),
    calculateTotalStockValue: jest.fn((items: InventoryItem[], branchId?: string) => {
      return items.reduce((total, item) => {
        if (branchId) {
          const branchStock = item.branchStock.find(bs => bs.branchId === branchId)
          return total + (branchStock ? branchStock.quantity * item.costPrice : 0)
        }
        return total + item.branchStock.reduce((sum, bs) => sum + bs.quantity * item.costPrice, 0)
      }, 0)
    }),
    calculateReorderQuantity: jest.fn((item: InventoryItem, branchId: string) => {
      const branchStock = item.branchStock.find(bs => bs.branchId === branchId)
      if (!branchStock) return 0
      return Math.max(0, branchStock.maxStock - branchStock.quantity)
    }),
    isLowStock: jest.fn((item: InventoryItem, branchId: string) => {
      const branchStock = item.branchStock.find(bs => bs.branchId === branchId)
      if (!branchStock) return false
      return branchStock.quantity <= branchStock.reorderPoint
    }),
    isCriticalStock: jest.fn((item: InventoryItem, branchId: string) => {
      const branchStock = item.branchStock.find(bs => bs.branchId === branchId)
      if (!branchStock) return false
      return branchStock.quantity <= branchStock.reorderPoint * 0.5
    }),
    isOutOfStock: jest.fn((item: InventoryItem, branchId: string) => {
      const branchStock = item.branchStock.find(bs => bs.branchId === branchId)
      if (!branchStock) return true
      return branchStock.quantity <= 0
    }),
    getStockLevel: jest.fn((item: InventoryItem, branchId: string) => {
      const branchStock = item.branchStock.find(bs => bs.branchId === branchId)
      return branchStock ? branchStock.quantity : 0
    }),
    getAvailableStock: jest.fn((item: InventoryItem, branchId: string) => {
      const branchStock = item.branchStock.find(bs => bs.branchId === branchId)
      return branchStock ? branchStock.quantity - branchStock.reservedQuantity : 0
    }),
    formatStockStatus: jest.fn((item: InventoryItem, branchId: string) => {
      const branchStock = item.branchStock.find(bs => bs.branchId === branchId)
      if (!branchStock || branchStock.quantity <= 0) return 'Out of Stock'
      if (branchStock.quantity <= branchStock.reorderPoint * 0.5) return 'Critical'
      if (branchStock.quantity <= branchStock.reorderPoint) return 'Low Stock'
      return 'In Stock'
    }),
    getMovementTypeColor: jest.fn((type: StockMovementType) => {
      const colors: Record<StockMovementType, string> = {
        'Stock In': 'green',
        'Stock Out': 'red',
        'Transfer In': 'blue',
        'Transfer Out': 'orange',
        'Adjustment': 'purple',
        'Reserved': 'yellow',
        'Released': 'gray'
      }
      return colors[type] || 'gray'
    }),
    getInventoryItems: jest.fn(),
    getInventoryItemById: jest.fn(),
    createInventoryItem: jest.fn(),
    updateInventoryItem: jest.fn(),
    deleteInventoryItem: jest.fn(),
    updateStock: jest.fn(),
    adjustStock: jest.fn(),
    transferStock: jest.fn(),
    reserveStock: jest.fn(),
    releaseStock: jest.fn(),
    getStockMovements: jest.fn(),
    getRecentMovements: jest.fn(),
    createStockMovement: jest.fn(),
    getStockAdjustments: jest.fn(),
    getPendingAdjustments: jest.fn(),
    approveAdjustment: jest.fn(),
    rejectAdjustment: jest.fn(),
    getLowStockAlerts: jest.fn(),
    getCriticalStockItems: jest.fn(),
    updateReorderPoint: jest.fn(),
    dismissAlert: jest.fn(),
    getSuppliers: jest.fn(),
    getPurchaseOrders: jest.fn(),
    createPurchaseOrder: jest.fn(),
    updatePurchaseOrder: jest.fn(),
    receivePurchaseOrder: jest.fn(),
    getInventoryByBranch: jest.fn(),
    getInventoryByProduct: jest.fn(),
    getInventoryBySupplier: jest.fn(),
    searchInventory: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Mock inventory data
const mockInventoryItem: InventoryItem = {
  _id: 'inventory-123',
  productId: 'product-123',
  productName: 'Test Laptop',
  sku: 'LAP-TEST-001',
  category: 'Electronics',
  supplierId: 'supplier-123',
  supplierName: 'Tech Supplier',
  costPrice: 400000,
  sellingPrice: 500000,
  branchStock: [
    {
      branchId: 'branch-123',
      branchName: 'Main Branch',
      quantity: 50,
      reservedQuantity: 5,
      reorderPoint: 10,
      maxStock: 100,
      location: 'A1-B2'
    },
    {
      branchId: 'branch-124',
      branchName: 'North Branch',
      quantity: 25,
      reservedQuantity: 2,
      reorderPoint: 5,
      maxStock: 50,
      location: 'B1-A3'
    }
  ],
  totalQuantity: 75,
  totalReserved: 7,
  lastRestockDate: '2024-01-01T00:00:00Z',
  expiryDate: '2025-12-31T00:00:00Z',
  batchNumber: 'BATCH-2024-001',
  isActive: true,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockStockMovement: StockMovement = {
  _id: 'movement-123',
  itemId: 'inventory-123',
  productName: 'Test Laptop',
  branchId: 'branch-123',
  branchName: 'Main Branch',
  type: 'Stock In',
  quantity: 20,
  previousQuantity: 30,
  newQuantity: 50,
  reason: 'Purchase order received',
  reference: 'PO-2024-001',
  userId: 'user-123',
  userName: 'John Manager',
  createdAt: '2024-01-01T00:00:00Z'
}

const mockLowStockAlert: LowStockAlert = {
  _id: 'alert-123',
  itemId: 'inventory-123',
  productName: 'Test Laptop',
  branchId: 'branch-123',
  branchName: 'Main Branch',
  currentQuantity: 8,
  reorderPoint: 10,
  severity: 'Low',
  isActive: true,
  createdAt: '2024-01-01T00:00:00Z'
}

describe('InventoryStore - Basic Tests', () => {
  beforeEach(() => {
    // Reset store state before each test
    useInventoryStore.setState({
      inventoryItems: [],
      selectedItem: null,
      isLoading: false,
      error: null,
      stockMovements: [],
      recentMovements: [],
      stockAdjustments: [],
      pendingAdjustments: [],
      lowStockAlerts: [],
      criticalStockItems: [],
      suppliers: [],
      purchaseOrders: [],
      pendingOrders: [],
      currentPage: 1,
      totalPages: 0,
      totalItems: 0,
      pageSize: 20,
      filters: {},
      searchQuery: ''
    })
  })

  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const store = useInventoryStore.getState()
      
      expect(store.inventoryItems).toEqual([])
      expect(store.selectedItem).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.stockMovements).toEqual([])
      expect(store.recentMovements).toEqual([])
      expect(store.stockAdjustments).toEqual([])
      expect(store.pendingAdjustments).toEqual([])
      expect(store.lowStockAlerts).toEqual([])
      expect(store.criticalStockItems).toEqual([])
      expect(store.suppliers).toEqual([])
      expect(store.purchaseOrders).toEqual([])
      expect(store.pendingOrders).toEqual([])
      expect(store.currentPage).toBe(1)
      expect(store.totalPages).toBe(0)
      expect(store.totalItems).toBe(0)
      expect(store.pageSize).toBe(20)
      expect(store.filters).toEqual({})
      expect(store.searchQuery).toBe('')
    })

    test('should have all required methods', () => {
      const store = useInventoryStore.getState()
      
      // CRUD methods
      expect(typeof store.fetchInventoryItems).toBe('function')
      expect(typeof store.fetchInventoryItemById).toBe('function')
      expect(typeof store.createInventoryItem).toBe('function')
      expect(typeof store.updateInventoryItem).toBe('function')
      expect(typeof store.deleteInventoryItem).toBe('function')
      
      // Stock management methods
      expect(typeof store.updateStock).toBe('function')
      expect(typeof store.adjustStock).toBe('function')
      expect(typeof store.transferStock).toBe('function')
      expect(typeof store.reserveStock).toBe('function')
      expect(typeof store.releaseStock).toBe('function')
      
      // Stock movements methods
      expect(typeof store.fetchStockMovements).toBe('function')
      expect(typeof store.fetchRecentMovements).toBe('function')
      expect(typeof store.createStockMovement).toBe('function')
      
      // Stock adjustments methods
      expect(typeof store.fetchStockAdjustments).toBe('function')
      expect(typeof store.fetchPendingAdjustments).toBe('function')
      expect(typeof store.approveAdjustment).toBe('function')
      expect(typeof store.rejectAdjustment).toBe('function')
      
      // Alerts & monitoring methods
      expect(typeof store.fetchLowStockAlerts).toBe('function')
      expect(typeof store.fetchCriticalStockItems).toBe('function')
      expect(typeof store.updateReorderPoint).toBe('function')
      expect(typeof store.dismissAlert).toBe('function')
      
      // Suppliers & purchase orders methods
      expect(typeof store.fetchSuppliers).toBe('function')
      expect(typeof store.fetchPurchaseOrders).toBe('function')
      expect(typeof store.createPurchaseOrder).toBe('function')
      expect(typeof store.updatePurchaseOrder).toBe('function')
      expect(typeof store.receivePurchaseOrder).toBe('function')
      
      // Query methods
      expect(typeof store.fetchInventoryByBranch).toBe('function')
      expect(typeof store.fetchInventoryByProduct).toBe('function')
      expect(typeof store.fetchInventoryBySupplier).toBe('function')
      
      // Search and filtering methods
      expect(typeof store.searchInventory).toBe('function')
      expect(typeof store.setFilters).toBe('function')
      expect(typeof store.clearFilters).toBe('function')
      expect(typeof store.setSearchQuery).toBe('function')
      
      // Pagination methods
      expect(typeof store.setCurrentPage).toBe('function')
      expect(typeof store.setPageSize).toBe('function')
      
      // UI state methods
      expect(typeof store.setSelectedItem).toBe('function')
      expect(typeof store.clearError).toBe('function')
      expect(typeof store.setLoading).toBe('function')
      
      // Utility methods
      expect(typeof store.getInventoryByBranch).toBe('function')
      expect(typeof store.getInventoryByProduct).toBe('function')
      expect(typeof store.getLowStockItems).toBe('function')
      expect(typeof store.getCriticalStockItems).toBe('function')
      expect(typeof store.getOutOfStockItems).toBe('function')
      expect(typeof store.getTotalStockValue).toBe('function')
      expect(typeof store.getStockMovementsByType).toBe('function')
      expect(typeof store.calculateReorderQuantity).toBe('function')
      expect(typeof store.isLowStock).toBe('function')
      expect(typeof store.isCriticalStock).toBe('function')
      expect(typeof store.isOutOfStock).toBe('function')
      expect(typeof store.getStockLevel).toBe('function')
      expect(typeof store.getAvailableStock).toBe('function')
      expect(typeof store.formatStockStatus).toBe('function')
      expect(typeof store.getMovementTypeColor).toBe('function')
    })
  })

  describe('State Management', () => {
    test('should set selected item', () => {
      const store = useInventoryStore.getState()
      
      store.setSelectedItem(mockInventoryItem)
      expect(useInventoryStore.getState().selectedItem).toEqual(mockInventoryItem)
      
      store.setSelectedItem(null)
      expect(useInventoryStore.getState().selectedItem).toBeNull()
    })

    test('should clear error', () => {
      useInventoryStore.setState({ error: 'Test error' })
      
      const store = useInventoryStore.getState()
      expect(store.error).toBe('Test error')
      
      store.clearError()
      expect(useInventoryStore.getState().error).toBeNull()
    })

    test('should set loading state', () => {
      const store = useInventoryStore.getState()
      
      store.setLoading(true)
      expect(useInventoryStore.getState().isLoading).toBe(true)
      
      store.setLoading(false)
      expect(useInventoryStore.getState().isLoading).toBe(false)
    })

    test('should set search query', () => {
      const store = useInventoryStore.getState()
      
      store.setSearchQuery('laptop')
      expect(useInventoryStore.getState().searchQuery).toBe('laptop')
      
      store.setSearchQuery('')
      expect(useInventoryStore.getState().searchQuery).toBe('')
    })

    test('should set filters', () => {
      const store = useInventoryStore.getState()
      
      store.setFilters({ branchId: 'branch-123', category: 'Electronics' })
      expect(useInventoryStore.getState().filters).toEqual({
        branchId: 'branch-123',
        category: 'Electronics'
      })
      
      store.setFilters({ supplierId: 'supplier-123' })
      expect(useInventoryStore.getState().filters).toEqual({
        branchId: 'branch-123',
        category: 'Electronics',
        supplierId: 'supplier-123'
      })
    })

    test('should clear filters', () => {
      useInventoryStore.setState({
        filters: { branchId: 'branch-123', category: 'Electronics' },
        searchQuery: 'test'
      })
      
      const store = useInventoryStore.getState()
      store.clearFilters()
      
      const updatedStore = useInventoryStore.getState()
      expect(updatedStore.filters).toEqual({})
      expect(updatedStore.searchQuery).toBe('')
    })

    test('should set pagination', () => {
      const store = useInventoryStore.getState()
      
      store.setCurrentPage(3)
      expect(useInventoryStore.getState().currentPage).toBe(3)
      
      store.setPageSize(50)
      expect(useInventoryStore.getState().pageSize).toBe(50)
    })
  })

  describe('Utility Methods', () => {
    beforeEach(() => {
      // Set up test data
      const inventoryItems: InventoryItem[] = [
        { ...mockInventoryItem, _id: 'item-1', productId: 'product-1', branchStock: [
          { branchId: 'branch-1', branchName: 'Branch 1', quantity: 50, reservedQuantity: 5, reorderPoint: 10, maxStock: 100, location: 'A1' },
          { branchId: 'branch-2', branchName: 'Branch 2', quantity: 30, reservedQuantity: 3, reorderPoint: 8, maxStock: 80, location: 'B1' }
        ]},
        { ...mockInventoryItem, _id: 'item-2', productId: 'product-2', branchStock: [
          { branchId: 'branch-1', branchName: 'Branch 1', quantity: 5, reservedQuantity: 1, reorderPoint: 10, maxStock: 50, location: 'A2' },
          { branchId: 'branch-3', branchName: 'Branch 3', quantity: 20, reservedQuantity: 2, reorderPoint: 5, maxStock: 40, location: 'C1' }
        ]},
        { ...mockInventoryItem, _id: 'item-3', productId: 'product-1', branchStock: [
          { branchId: 'branch-2', branchName: 'Branch 2', quantity: 0, reservedQuantity: 0, reorderPoint: 5, maxStock: 30, location: 'B2' }
        ]}
      ]
      
      const stockMovements: StockMovement[] = [
        { ...mockStockMovement, _id: 'movement-1', type: 'Stock In' },
        { ...mockStockMovement, _id: 'movement-2', type: 'Stock Out' },
        { ...mockStockMovement, _id: 'movement-3', type: 'Transfer In' }
      ]
      
      useInventoryStore.setState({ inventoryItems, stockMovements })
    })

    test('should get inventory by branch', () => {
      const store = useInventoryStore.getState()
      
      const branch1Items = store.getInventoryByBranch('branch-1')
      expect(branch1Items).toHaveLength(2)
      expect(branch1Items.every(item => 
        item.branchStock.some(bs => bs.branchId === 'branch-1')
      )).toBe(true)
      
      const branch3Items = store.getInventoryByBranch('branch-3')
      expect(branch3Items).toHaveLength(1)
    })

    test('should get inventory by product', () => {
      const store = useInventoryStore.getState()
      
      const product1Items = store.getInventoryByProduct('product-1')
      expect(product1Items).toHaveLength(2)
      expect(product1Items.every(item => item.productId === 'product-1')).toBe(true)
      
      const product2Items = store.getInventoryByProduct('product-2')
      expect(product2Items).toHaveLength(1)
      expect(product2Items[0].productId).toBe('product-2')
    })

    test('should get low stock items', () => {
      const store = useInventoryStore.getState()
      
      const lowStockItems = store.getLowStockItems('branch-1')
      expect(lowStockItems.length).toBeGreaterThan(0)
    })

    test('should get critical stock items', () => {
      const store = useInventoryStore.getState()
      
      const criticalItems = store.getCriticalStockItems('branch-1')
      expect(Array.isArray(criticalItems)).toBe(true)
    })

    test('should get out of stock items', () => {
      const store = useInventoryStore.getState()
      
      const outOfStockItems = store.getOutOfStockItems('branch-2')
      expect(outOfStockItems.length).toBeGreaterThan(0)
    })

    test('should calculate total stock value', () => {
      const store = useInventoryStore.getState()
      
      const totalValue = store.getTotalStockValue()
      expect(typeof totalValue).toBe('number')
      expect(totalValue).toBeGreaterThan(0)
      
      const branch1Value = store.getTotalStockValue('branch-1')
      expect(typeof branch1Value).toBe('number')
    })

    test('should get stock movements by type', () => {
      const store = useInventoryStore.getState()
      
      const stockInMovements = store.getStockMovementsByType('Stock In')
      expect(stockInMovements).toHaveLength(1)
      expect(stockInMovements[0].type).toBe('Stock In')
      
      const stockOutMovements = store.getStockMovementsByType('Stock Out')
      expect(stockOutMovements).toHaveLength(1)
      expect(stockOutMovements[0].type).toBe('Stock Out')
    })

    test('should calculate reorder quantity', () => {
      const store = useInventoryStore.getState()
      
      const reorderQty = store.calculateReorderQuantity(mockInventoryItem, 'branch-123')
      expect(typeof reorderQty).toBe('number')
      expect(reorderQty).toBeGreaterThanOrEqual(0)
    })

    test('should check stock status', () => {
      const store = useInventoryStore.getState()
      
      const isLow = store.isLowStock(mockInventoryItem, 'branch-123')
      const isCritical = store.isCriticalStock(mockInventoryItem, 'branch-123')
      const isOutOfStock = store.isOutOfStock(mockInventoryItem, 'branch-123')
      
      expect(typeof isLow).toBe('boolean')
      expect(typeof isCritical).toBe('boolean')
      expect(typeof isOutOfStock).toBe('boolean')
    })

    test('should get stock levels', () => {
      const store = useInventoryStore.getState()
      
      const stockLevel = store.getStockLevel(mockInventoryItem, 'branch-123')
      expect(stockLevel).toBe(50)
      
      const availableStock = store.getAvailableStock(mockInventoryItem, 'branch-123')
      expect(availableStock).toBe(45) // 50 - 5 reserved
    })

    test('should format stock status', () => {
      const store = useInventoryStore.getState()
      
      const status = store.formatStockStatus(mockInventoryItem, 'branch-123')
      expect(typeof status).toBe('string')
      expect(['In Stock', 'Low Stock', 'Critical', 'Out of Stock']).toContain(status)
    })

    test('should get movement type color', () => {
      const store = useInventoryStore.getState()
      
      const stockInColor = store.getMovementTypeColor('Stock In')
      expect(stockInColor).toBe('green')
      
      const stockOutColor = store.getMovementTypeColor('Stock Out')
      expect(stockOutColor).toBe('red')
      
      const transferInColor = store.getMovementTypeColor('Transfer In')
      expect(transferInColor).toBe('blue')
    })
  })
})
