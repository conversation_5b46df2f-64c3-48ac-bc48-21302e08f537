// stores/__tests__/reportsStore.basic.test.ts - Basic reports store tests

import { useReportsStore } from '../reportsStore'
import type { Report, ReportType, ReportStatus, DashboardData, ReportSchedule, DateRange } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  reportsService: {
    validateReportData: jest.fn(() => ({ isValid: true, errors: [] })),
    calculateReportMetrics: jest.fn((reports: Report[]) => {
      const totalReports = reports.length
      const completedReports = reports.filter(r => r.status === 'Completed').length
      const failedReports = reports.filter(r => r.status === 'Failed').length
      const averageGenerationTime = reports.reduce((sum, r) => sum + (r.generationTime || 0), 0) / totalReports || 0
      
      return {
        totalReports,
        completedReports,
        failedReports,
        averageGenerationTime
      }
    }),
    formatReportSize: jest.fn((sizeInBytes: number) => {
      if (sizeInBytes < 1024) return `${sizeInBytes} B`
      if (sizeInBytes < 1024 * 1024) return `${(sizeInBytes / 1024).toFixed(1)} KB`
      if (sizeInBytes < 1024 * 1024 * 1024) return `${(sizeInBytes / (1024 * 1024)).toFixed(1)} MB`
      return `${(sizeInBytes / (1024 * 1024 * 1024)).toFixed(1)} GB`
    }),
    getReportTypeIcon: jest.fn((reportType: ReportType) => {
      const icons: Record<ReportType, string> = {
        'Sales': '📊',
        'Inventory': '📦',
        'Customer': '👥',
        'Branch': '🏢',
        'Financial': '💰',
        'Custom': '⚙️'
      }
      return icons[reportType] || '📄'
    }),
    getReportStatusColor: jest.fn((status: ReportStatus) => {
      const colors: Record<ReportStatus, string> = {
        'Pending': 'yellow',
        'Processing': 'blue',
        'Completed': 'green',
        'Failed': 'red',
        'Cancelled': 'gray'
      }
      return colors[status] || 'gray'
    }),
    getExportFormatIcon: jest.fn((format: string) => {
      const icons: Record<string, string> = {
        'PDF': '📄',
        'Excel': '📊',
        'CSV': '📋',
        'JSON': '🔧'
      }
      return icons[format] || '📄'
    }),
    isReportExpired: jest.fn((report: Report, expiryDays = 30) => {
      const reportDate = new Date(report.createdAt)
      const expiryDate = new Date(reportDate.getTime() + expiryDays * 24 * 60 * 60 * 1000)
      return new Date() > expiryDate
    }),
    canUserAccessReport: jest.fn((report: Report, userId: string) => {
      return report.createdBy === userId || report.sharedWith?.includes(userId) || false
    }),
    getReportSummary: jest.fn((report: Report) => {
      return `${report.reportType} report generated on ${new Date(report.createdAt).toLocaleDateString()}`
    }),
    formatDateRange: jest.fn((dateRange: DateRange) => {
      return `${new Date(dateRange.startDate).toLocaleDateString()} - ${new Date(dateRange.endDate).toLocaleDateString()}`
    }),
    getReports: jest.fn(),
    getReportById: jest.fn(),
    createReport: jest.fn(),
    updateReport: jest.fn(),
    deleteReport: jest.fn(),
    getDashboardData: jest.fn(),
    generateReport: jest.fn(),
    generateSalesReport: jest.fn(),
    generateInventoryReport: jest.fn(),
    generateCustomerReport: jest.fn(),
    generateBranchReport: jest.fn(),
    generateFinancialReport: jest.fn(),
    createCustomReport: jest.fn(),
    updateCustomReport: jest.fn(),
    executeCustomReport: jest.fn(),
    getReportSchedules: jest.fn(),
    createReportSchedule: jest.fn(),
    updateReportSchedule: jest.fn(),
    deleteReportSchedule: jest.fn(),
    toggleSchedule: jest.fn(),
    exportReport: jest.fn(),
    downloadReport: jest.fn(),
    shareReport: jest.fn(),
    getReportsByType: jest.fn(),
    getRecentReports: jest.fn(),
    getPopularReports: jest.fn(),
    getReportUsageStats: jest.fn(),
    searchReports: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Mock report data
const mockReport: Report = {
  _id: 'report-123',
  title: 'Monthly Sales Report',
  description: 'Sales performance for January 2024',
  reportType: 'Sales',
  status: 'Completed',
  format: 'PDF',
  fileSize: 1024000,
  filePath: '/reports/sales-jan-2024.pdf',
  downloadUrl: 'https://example.com/reports/sales-jan-2024.pdf',
  parameters: {
    dateRange: {
      startDate: '2024-01-01',
      endDate: '2024-01-31'
    },
    branchId: 'branch-123'
  },
  generationTime: 5000,
  createdBy: 'user-123',
  createdByName: 'John Manager',
  sharedWith: ['user-456', 'user-789'],
  tags: ['sales', 'monthly', 'performance'],
  isScheduled: false,
  expiresAt: '2024-03-01T00:00:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockDashboardData: DashboardData = {
  totalSales: 5000000,
  totalOrders: 150,
  totalCustomers: 75,
  totalProducts: 200,
  salesGrowth: 15.5,
  orderGrowth: 12.3,
  customerGrowth: 8.7,
  topProducts: [
    { productId: 'product-1', productName: 'Laptop', sales: 1000000, quantity: 20 },
    { productId: 'product-2', productName: 'Phone', sales: 800000, quantity: 30 }
  ],
  topCustomers: [
    { customerId: 'customer-1', customerName: 'John Doe', totalSpent: 500000 },
    { customerId: 'customer-2', customerName: 'Jane Smith', totalSpent: 400000 }
  ],
  salesByBranch: [
    { branchId: 'branch-1', branchName: 'Main Branch', sales: 3000000 },
    { branchId: 'branch-2', branchName: 'North Branch', sales: 2000000 }
  ],
  salesTrend: [
    { date: '2024-01-01', sales: 100000 },
    { date: '2024-01-02', sales: 120000 },
    { date: '2024-01-03', sales: 110000 }
  ],
  lowStockItems: 5,
  criticalStockItems: 2,
  pendingOrders: 10,
  lastUpdated: '2024-01-01T12:00:00Z'
}

const mockReportSchedule: ReportSchedule = {
  _id: 'schedule-123',
  name: 'Weekly Sales Report',
  description: 'Automated weekly sales report',
  reportType: 'Sales',
  frequency: 'Weekly',
  parameters: {
    dateRange: 'last_week',
    format: 'PDF'
  },
  recipients: ['<EMAIL>', '<EMAIL>'],
  isActive: true,
  nextRunDate: '2024-01-08T09:00:00Z',
  lastRunDate: '2024-01-01T09:00:00Z',
  createdBy: 'user-123',
  createdByName: 'John Manager',
  createdAt: '2023-12-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('ReportsStore - Basic Tests', () => {
  beforeEach(() => {
    // Reset store state before each test
    useReportsStore.setState({
      reports: [],
      selectedReport: null,
      isLoading: false,
      error: null,
      dashboardData: null,
      dashboardLoading: false,
      lastUpdated: null,
      salesReports: [],
      inventoryReports: [],
      customerReports: [],
      branchReports: [],
      financialReports: [],
      customReports: [],
      reportSchedules: [],
      activeSchedules: [],
      currentPage: 1,
      totalPages: 0,
      totalReports: 0,
      pageSize: 20,
      filters: {},
      searchQuery: ''
    })
  })

  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const store = useReportsStore.getState()
      
      expect(store.reports).toEqual([])
      expect(store.selectedReport).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.dashboardData).toBeNull()
      expect(store.dashboardLoading).toBe(false)
      expect(store.lastUpdated).toBeNull()
      expect(store.salesReports).toEqual([])
      expect(store.inventoryReports).toEqual([])
      expect(store.customerReports).toEqual([])
      expect(store.branchReports).toEqual([])
      expect(store.financialReports).toEqual([])
      expect(store.customReports).toEqual([])
      expect(store.reportSchedules).toEqual([])
      expect(store.activeSchedules).toEqual([])
      expect(store.currentPage).toBe(1)
      expect(store.totalPages).toBe(0)
      expect(store.totalReports).toBe(0)
      expect(store.pageSize).toBe(20)
      expect(store.filters).toEqual({})
      expect(store.searchQuery).toBe('')
    })

    test('should have all required methods', () => {
      const store = useReportsStore.getState()
      
      // CRUD methods
      expect(typeof store.fetchReports).toBe('function')
      expect(typeof store.fetchReportById).toBe('function')
      expect(typeof store.createReport).toBe('function')
      expect(typeof store.updateReport).toBe('function')
      expect(typeof store.deleteReport).toBe('function')
      
      // Dashboard methods
      expect(typeof store.fetchDashboardData).toBe('function')
      expect(typeof store.refreshDashboard).toBe('function')
      
      // Report generation methods
      expect(typeof store.generateReport).toBe('function')
      expect(typeof store.generateSalesReport).toBe('function')
      expect(typeof store.generateInventoryReport).toBe('function')
      expect(typeof store.generateCustomerReport).toBe('function')
      expect(typeof store.generateBranchReport).toBe('function')
      expect(typeof store.generateFinancialReport).toBe('function')
      
      // Custom reports methods
      expect(typeof store.createCustomReport).toBe('function')
      expect(typeof store.updateCustomReport).toBe('function')
      expect(typeof store.executeCustomReport).toBe('function')
      
      // Scheduling methods
      expect(typeof store.fetchReportSchedules).toBe('function')
      expect(typeof store.createReportSchedule).toBe('function')
      expect(typeof store.updateReportSchedule).toBe('function')
      expect(typeof store.deleteReportSchedule).toBe('function')
      expect(typeof store.toggleSchedule).toBe('function')
      
      // Export methods
      expect(typeof store.exportReport).toBe('function')
      expect(typeof store.downloadReport).toBe('function')
      expect(typeof store.shareReport).toBe('function')
      
      // Analytics methods
      expect(typeof store.fetchReportsByType).toBe('function')
      expect(typeof store.fetchRecentReports).toBe('function')
      expect(typeof store.fetchPopularReports).toBe('function')
      expect(typeof store.getReportUsageStats).toBe('function')
      
      // Search and filtering methods
      expect(typeof store.searchReports).toBe('function')
      expect(typeof store.setFilters).toBe('function')
      expect(typeof store.clearFilters).toBe('function')
      expect(typeof store.setSearchQuery).toBe('function')
      
      // Pagination methods
      expect(typeof store.setCurrentPage).toBe('function')
      expect(typeof store.setPageSize).toBe('function')
      
      // UI state methods
      expect(typeof store.setSelectedReport).toBe('function')
      expect(typeof store.clearError).toBe('function')
      expect(typeof store.setLoading).toBe('function')
      
      // Utility methods
      expect(typeof store.getReportsByType).toBe('function')
      expect(typeof store.getReportsByStatus).toBe('function')
      expect(typeof store.getReportsByDateRange).toBe('function')
      expect(typeof store.getActiveSchedules).toBe('function')
      expect(typeof store.getSchedulesByFrequency).toBe('function')
      expect(typeof store.calculateReportMetrics).toBe('function')
      expect(typeof store.formatReportSize).toBe('function')
      expect(typeof store.getReportTypeIcon).toBe('function')
      expect(typeof store.getReportStatusColor).toBe('function')
      expect(typeof store.getExportFormatIcon).toBe('function')
      expect(typeof store.isReportExpired).toBe('function')
      expect(typeof store.canUserAccessReport).toBe('function')
      expect(typeof store.getReportSummary).toBe('function')
      expect(typeof store.formatDateRange).toBe('function')
    })
  })

  describe('State Management', () => {
    test('should set selected report', () => {
      const store = useReportsStore.getState()
      
      store.setSelectedReport(mockReport)
      expect(useReportsStore.getState().selectedReport).toEqual(mockReport)
      
      store.setSelectedReport(null)
      expect(useReportsStore.getState().selectedReport).toBeNull()
    })

    test('should clear error', () => {
      useReportsStore.setState({ error: 'Test error' })
      
      const store = useReportsStore.getState()
      expect(store.error).toBe('Test error')
      
      store.clearError()
      expect(useReportsStore.getState().error).toBeNull()
    })

    test('should set loading state', () => {
      const store = useReportsStore.getState()
      
      store.setLoading(true)
      expect(useReportsStore.getState().isLoading).toBe(true)
      
      store.setLoading(false)
      expect(useReportsStore.getState().isLoading).toBe(false)
    })

    test('should set search query', () => {
      const store = useReportsStore.getState()
      
      store.setSearchQuery('sales report')
      expect(useReportsStore.getState().searchQuery).toBe('sales report')
      
      store.setSearchQuery('')
      expect(useReportsStore.getState().searchQuery).toBe('')
    })

    test('should set filters', () => {
      const store = useReportsStore.getState()
      
      store.setFilters({ reportType: 'Sales', status: 'Completed' })
      expect(useReportsStore.getState().filters).toEqual({
        reportType: 'Sales',
        status: 'Completed'
      })
      
      store.setFilters({ createdBy: 'user-123' })
      expect(useReportsStore.getState().filters).toEqual({
        reportType: 'Sales',
        status: 'Completed',
        createdBy: 'user-123'
      })
    })

    test('should clear filters', () => {
      useReportsStore.setState({
        filters: { reportType: 'Sales', status: 'Completed' },
        searchQuery: 'test'
      })
      
      const store = useReportsStore.getState()
      store.clearFilters()
      
      const updatedStore = useReportsStore.getState()
      expect(updatedStore.filters).toEqual({})
      expect(updatedStore.searchQuery).toBe('')
    })

    test('should set pagination', () => {
      const store = useReportsStore.getState()
      
      store.setCurrentPage(3)
      expect(useReportsStore.getState().currentPage).toBe(3)
      
      store.setPageSize(50)
      expect(useReportsStore.getState().pageSize).toBe(50)
    })
  })

  describe('Utility Methods', () => {
    beforeEach(() => {
      // Set up test data
      const reports: Report[] = [
        { ...mockReport, _id: 'report-1', reportType: 'Sales', status: 'Completed', createdAt: '2024-01-01T00:00:00Z' },
        { ...mockReport, _id: 'report-2', reportType: 'Inventory', status: 'Pending', createdAt: '2024-01-02T00:00:00Z' },
        { ...mockReport, _id: 'report-3', reportType: 'Sales', status: 'Failed', createdAt: '2024-01-03T00:00:00Z' },
        { ...mockReport, _id: 'report-4', reportType: 'Customer', status: 'Completed', createdAt: '2024-01-04T00:00:00Z' }
      ]
      
      const schedules: ReportSchedule[] = [
        { ...mockReportSchedule, _id: 'schedule-1', frequency: 'Daily', isActive: true },
        { ...mockReportSchedule, _id: 'schedule-2', frequency: 'Weekly', isActive: false },
        { ...mockReportSchedule, _id: 'schedule-3', frequency: 'Monthly', isActive: true }
      ]
      
      useReportsStore.setState({ reports, reportSchedules: schedules })
    })

    test('should get reports by type', () => {
      const store = useReportsStore.getState()
      
      const salesReports = store.getReportsByType('Sales')
      expect(salesReports).toHaveLength(2)
      expect(salesReports.every(r => r.reportType === 'Sales')).toBe(true)
      
      const inventoryReports = store.getReportsByType('Inventory')
      expect(inventoryReports).toHaveLength(1)
      expect(inventoryReports[0].reportType).toBe('Inventory')
    })

    test('should get reports by status', () => {
      const store = useReportsStore.getState()
      
      const completedReports = store.getReportsByStatus('Completed')
      expect(completedReports).toHaveLength(2)
      expect(completedReports.every(r => r.status === 'Completed')).toBe(true)
      
      const pendingReports = store.getReportsByStatus('Pending')
      expect(pendingReports).toHaveLength(1)
      expect(pendingReports[0].status).toBe('Pending')
    })

    test('should get reports by date range', () => {
      const store = useReportsStore.getState()
      
      const reportsInRange = store.getReportsByDateRange('2024-01-01', '2024-01-02')
      expect(reportsInRange).toHaveLength(2)
      
      const reportsOutOfRange = store.getReportsByDateRange('2024-01-05', '2024-01-06')
      expect(reportsOutOfRange).toHaveLength(0)
    })

    test('should get active schedules', () => {
      const store = useReportsStore.getState()
      
      const activeSchedules = store.getActiveSchedules()
      expect(activeSchedules).toHaveLength(2)
      expect(activeSchedules.every(s => s.isActive)).toBe(true)
    })

    test('should get schedules by frequency', () => {
      const store = useReportsStore.getState()
      
      const dailySchedules = store.getSchedulesByFrequency('Daily')
      expect(dailySchedules).toHaveLength(1)
      expect(dailySchedules[0].frequency).toBe('Daily')
      
      const weeklySchedules = store.getSchedulesByFrequency('Weekly')
      expect(weeklySchedules).toHaveLength(1)
      expect(weeklySchedules[0].frequency).toBe('Weekly')
    })

    test('should calculate report metrics', () => {
      const store = useReportsStore.getState()
      
      const metrics = store.calculateReportMetrics(store.reports)
      expect(metrics.totalReports).toBe(4)
      expect(metrics.completedReports).toBe(2)
      expect(metrics.failedReports).toBe(1)
      expect(typeof metrics.averageGenerationTime).toBe('number')
    })

    test('should format report size', () => {
      const store = useReportsStore.getState()
      
      expect(store.formatReportSize(500)).toBe('500 B')
      expect(store.formatReportSize(1536)).toBe('1.5 KB')
      expect(store.formatReportSize(1048576)).toBe('1.0 MB')
      expect(store.formatReportSize(1073741824)).toBe('1.0 GB')
    })

    test('should get report type icon', () => {
      const store = useReportsStore.getState()
      
      expect(store.getReportTypeIcon('Sales')).toBe('📊')
      expect(store.getReportTypeIcon('Inventory')).toBe('📦')
      expect(store.getReportTypeIcon('Customer')).toBe('👥')
      expect(store.getReportTypeIcon('Branch')).toBe('🏢')
      expect(store.getReportTypeIcon('Financial')).toBe('💰')
      expect(store.getReportTypeIcon('Custom')).toBe('⚙️')
    })

    test('should get report status color', () => {
      const store = useReportsStore.getState()
      
      expect(store.getReportStatusColor('Pending')).toBe('yellow')
      expect(store.getReportStatusColor('Processing')).toBe('blue')
      expect(store.getReportStatusColor('Completed')).toBe('green')
      expect(store.getReportStatusColor('Failed')).toBe('red')
      expect(store.getReportStatusColor('Cancelled')).toBe('gray')
    })

    test('should get export format icon', () => {
      const store = useReportsStore.getState()
      
      expect(store.getExportFormatIcon('PDF')).toBe('📄')
      expect(store.getExportFormatIcon('Excel')).toBe('📊')
      expect(store.getExportFormatIcon('CSV')).toBe('📋')
      expect(store.getExportFormatIcon('JSON')).toBe('🔧')
    })

    test('should check if report is expired', () => {
      const store = useReportsStore.getState()
      
      const oldReport = { ...mockReport, createdAt: '2023-01-01T00:00:00Z' }
      const newReport = { ...mockReport, createdAt: new Date().toISOString() }
      
      expect(store.isReportExpired(oldReport, 30)).toBe(true)
      expect(store.isReportExpired(newReport, 30)).toBe(false)
    })

    test('should check user access to report', () => {
      const store = useReportsStore.getState()
      
      expect(store.canUserAccessReport(mockReport, 'user-123')).toBe(true) // Creator
      expect(store.canUserAccessReport(mockReport, 'user-456')).toBe(true) // Shared with
      expect(store.canUserAccessReport(mockReport, 'user-999')).toBe(false) // No access
    })

    test('should get report summary', () => {
      const store = useReportsStore.getState()
      
      const summary = store.getReportSummary(mockReport)
      expect(summary).toContain('Sales report')
      expect(summary).toContain('generated on')
    })

    test('should format date range', () => {
      const store = useReportsStore.getState()
      
      const dateRange: DateRange = {
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      }
      
      const formatted = store.formatDateRange(dateRange)
      expect(formatted).toContain('2024')
      expect(formatted).toContain('-')
    })
  })
})
