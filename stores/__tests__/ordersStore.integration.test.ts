// stores/__tests__/ordersStore.integration.test.ts - Orders store integration tests

import { renderHook, act } from '@testing-library/react'
import { useOrdersStore } from '../ordersStore'
import type { Order, OrderStatus, PaymentStatus, ApiResponse, CreateOrderData, UpdateOrderData, OrderItem } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  orderService: {
    getOrderStatuses: jest.fn(() => ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled', 'Returned']),
    getPaymentStatuses: jest.fn(() => ['Pending', 'Paid', 'Failed', 'Refunded', 'Partially Refunded']),
    getPaymentMethods: jest.fn(() => ['Cash', 'Card', 'Mobile Money', 'Bank Transfer', 'Credit']),
    validateOrderData: jest.fn(() => ({ isValid: true, errors: [] })),
    calculateOrderTotals: jest.fn((items: OrderItem[], taxRate = 0.18, shippingCost = 0, discountAmount = 0) => {
      const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0)
      const tax = subtotal * taxRate
      const total = subtotal + tax + shippingCost - discountAmount
      return { subtotal, tax, shipping: shippingCost, discount: discountAmount, total: Math.max(0, total) }
    }),
    canCancelOrder: jest.fn((order: Order) => ['Pending', 'Processing'].includes(order.status)),
    canReturnOrder: jest.fn((order: Order) => order.status === 'Delivered' && order.paymentStatus === 'Paid'),
    getOrderStatusColor: jest.fn((status: OrderStatus) => 'blue'),
    getPaymentStatusColor: jest.fn((status: PaymentStatus) => 'green'),
    formatOrderTotal: jest.fn((total: number) => `MWK ${total.toLocaleString()}`),
    formatOrderNumber: jest.fn((orderNumber: string) => orderNumber.toUpperCase()),
    getOrders: jest.fn(),
    getOrderById: jest.fn(),
    createOrder: jest.fn(),
    updateOrder: jest.fn(),
    cancelOrder: jest.fn(),
    updateOrderStatus: jest.fn(),
    updatePaymentStatus: jest.fn(),
    addTrackingNumber: jest.fn(),
    getOrdersByCustomer: jest.fn(),
    getOrdersByBranch: jest.fn(),
    getOrdersByStatus: jest.fn(),
    getRecentOrders: jest.fn(),
    searchOrders: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Import the mocked module
const { orderService } = require('@/services/frontend')
const mockOrderService = orderService as jest.Mocked<typeof orderService>

// Mock order data
const mockOrderItem: OrderItem = {
  id: 'item-1',
  productId: 'product-123',
  productName: 'Test Laptop',
  sku: 'LAP-TEST-001',
  price: 500000,
  quantity: 2,
  subtotal: 1000000
}

const mockOrder: Order = {
  _id: 'order-123',
  orderNumber: 'ORD-2024-001',
  customerId: 'customer-123',
  customerName: 'John Doe',
  branchId: 'branch-123',
  branchName: 'Test Branch',
  items: [mockOrderItem],
  subtotal: 1000000,
  tax: 180000,
  shipping: 5000,
  discount: 0,
  total: 1185000,
  status: 'Pending',
  paymentStatus: 'Pending',
  paymentMethod: 'Card',
  shippingAddress: {
    street: '123 Main St',
    city: 'Lilongwe',
    region: 'Central',
    country: 'Malawi',
    postalCode: '12345'
  },
  billingAddress: {
    street: '123 Main St',
    city: 'Lilongwe',
    region: 'Central',
    country: 'Malawi',
    postalCode: '12345'
  },
  notes: 'Test order',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockOrders: Order[] = [
  mockOrder,
  { ...mockOrder, _id: 'order-124', orderNumber: 'ORD-2024-002', status: 'Processing' },
  { ...mockOrder, _id: 'order-125', orderNumber: 'ORD-2024-003', status: 'Delivered' }
]

describe('OrdersStore - Integration Tests', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    
    // Reset store state
    useOrdersStore.setState({
      orders: [],
      selectedOrder: null,
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 0,
      totalOrders: 0,
      pageSize: 20,
      filters: {},
      searchQuery: '',
      pendingOrders: [],
      processingOrders: [],
      shippedOrders: [],
      deliveredOrders: [],
      cancelledOrders: [],
      recentOrders: []
    })
  })

  describe('Fetch Orders Integration', () => {
    test('should fetch orders successfully', async () => {
      const mockResponse: ApiResponse<Order[]> = {
        success: true,
        data: mockOrders,
        pagination: {
          page: 1,
          limit: 20,
          total: 3,
          totalPages: 1
        }
      }

      mockOrderService.getOrders.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchOrders()
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.orders).toEqual(mockOrders)
      expect(result.current.totalOrders).toBe(3)
      expect(result.current.totalPages).toBe(1)
      expect(result.current.currentPage).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      // Check status groups are updated
      expect(result.current.pendingOrders).toHaveLength(1)
      expect(result.current.processingOrders).toHaveLength(1)
      expect(result.current.deliveredOrders).toHaveLength(1)

      expect(mockOrderService.getOrders).toHaveBeenCalledWith(
        { page: 1, limit: 20 },
        {}
      )
    })

    test('should handle fetch orders failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Failed to fetch orders'
      }

      mockOrderService.getOrders.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchOrders()
        expect(fetchResult.success).toBe(false)
        expect(fetchResult.error).toBe('Failed to fetch orders')
      })

      expect(result.current.orders).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Failed to fetch orders')
    })

    test('should fetch order by ID successfully', async () => {
      const mockResponse: ApiResponse<Order> = {
        success: true,
        data: mockOrder
      }

      mockOrderService.getOrderById.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchOrderById('order-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.selectedOrder).toEqual(mockOrder)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockOrderService.getOrderById).toHaveBeenCalledWith('order-123')
    })
  })

  describe('Create Order Integration', () => {
    test('should create order successfully', async () => {
      const createData: CreateOrderData = {
        customerId: 'customer-123',
        branchId: 'branch-123',
        items: [mockOrderItem],
        shippingAddress: {
          street: '123 Main St',
          city: 'Lilongwe',
          region: 'Central',
          country: 'Malawi',
          postalCode: '12345'
        },
        billingAddress: {
          street: '123 Main St',
          city: 'Lilongwe',
          region: 'Central',
          country: 'Malawi',
          postalCode: '12345'
        },
        paymentMethod: 'Card',
        notes: 'Test order'
      }

      const newOrder: Order = {
        ...mockOrder,
        _id: 'order-new'
      }

      const mockResponse: ApiResponse<Order> = {
        success: true,
        data: newOrder
      }

      mockOrderService.createOrder.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const createResult = await result.current.createOrder(createData)
        expect(createResult.success).toBe(true)
        expect(createResult.order).toEqual(newOrder)
      })

      expect(result.current.orders).toContain(newOrder)
      expect(result.current.totalOrders).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockOrderService.validateOrderData).toHaveBeenCalledWith(createData)
      expect(mockOrderService.createOrder).toHaveBeenCalledWith(createData)
    })

    test('should handle validation error during create', async () => {
      const createData: CreateOrderData = {
        customerId: '', // Invalid customer ID
        branchId: 'branch-123',
        items: [],
        shippingAddress: {
          street: '123 Main St',
          city: 'Lilongwe',
          region: 'Central',
          country: 'Malawi',
          postalCode: '12345'
        },
        billingAddress: {
          street: '123 Main St',
          city: 'Lilongwe',
          region: 'Central',
          country: 'Malawi',
          postalCode: '12345'
        },
        paymentMethod: 'Card'
      }

      mockOrderService.validateOrderData.mockReturnValueOnce({
        isValid: false,
        errors: [
          { field: 'customerId', message: 'Customer ID is required' },
          { field: 'items', message: 'Order must have at least one item' }
        ]
      })

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const createResult = await result.current.createOrder(createData)
        expect(createResult.success).toBe(false)
        expect(createResult.error).toBe('Customer ID is required, Order must have at least one item')
      })

      expect(result.current.orders).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Customer ID is required, Order must have at least one item')
      expect(mockOrderService.createOrder).not.toHaveBeenCalled()
    })
  })

  describe('Update Order Integration', () => {
    test('should update order successfully', async () => {
      // Set initial order in store
      useOrdersStore.setState({
        orders: [mockOrder],
        selectedOrder: mockOrder
      })

      const updateData: UpdateOrderData = {
        notes: 'Updated order notes',
        shippingAddress: {
          street: '456 New St',
          city: 'Blantyre',
          region: 'Southern',
          country: 'Malawi',
          postalCode: '54321'
        }
      }

      const updatedOrder: Order = {
        ...mockOrder,
        ...updateData
      }

      const mockResponse: ApiResponse<Order> = {
        success: true,
        data: updatedOrder
      }

      mockOrderService.updateOrder.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const updateResult = await result.current.updateOrder('order-123', updateData)
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.orders[0]).toEqual(updatedOrder)
      expect(result.current.selectedOrder).toEqual(updatedOrder)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockOrderService.updateOrder).toHaveBeenCalledWith('order-123', updateData)
    })
  })

  describe('Order Status Management Integration', () => {
    test('should update order status successfully', async () => {
      // Set initial order in store
      useOrdersStore.setState({
        orders: [mockOrder],
        selectedOrder: mockOrder
      })

      const updatedOrder: Order = {
        ...mockOrder,
        status: 'Processing'
      }

      const mockResponse: ApiResponse<Order> = {
        success: true,
        data: updatedOrder
      }

      mockOrderService.updateOrderStatus.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const updateResult = await result.current.updateOrderStatus('order-123', 'Processing')
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.orders[0]).toEqual(updatedOrder)
      expect(result.current.selectedOrder).toEqual(updatedOrder)

      expect(mockOrderService.updateOrderStatus).toHaveBeenCalledWith('order-123', 'Processing')
    })

    test('should update payment status successfully', async () => {
      // Set initial order in store
      useOrdersStore.setState({
        orders: [mockOrder],
        selectedOrder: mockOrder
      })

      const updatedOrder: Order = {
        ...mockOrder,
        paymentStatus: 'Paid'
      }

      const mockResponse: ApiResponse<Order> = {
        success: true,
        data: updatedOrder
      }

      mockOrderService.updatePaymentStatus.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const updateResult = await result.current.updatePaymentStatus('order-123', 'Paid')
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.orders[0]).toEqual(updatedOrder)
      expect(result.current.selectedOrder).toEqual(updatedOrder)

      expect(mockOrderService.updatePaymentStatus).toHaveBeenCalledWith('order-123', 'Paid')
    })

    test('should add tracking number successfully', async () => {
      // Set initial order in store
      useOrdersStore.setState({
        orders: [mockOrder],
        selectedOrder: mockOrder
      })

      const updatedOrder: Order = {
        ...mockOrder,
        trackingNumber: 'TRK123456789'
      }

      const mockResponse: ApiResponse<Order> = {
        success: true,
        data: updatedOrder
      }

      mockOrderService.addTrackingNumber.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const updateResult = await result.current.addTrackingNumber('order-123', 'TRK123456789')
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.orders[0]).toEqual(updatedOrder)
      expect(result.current.selectedOrder).toEqual(updatedOrder)

      expect(mockOrderService.addTrackingNumber).toHaveBeenCalledWith('order-123', 'TRK123456789')
    })

    test('should cancel order successfully', async () => {
      // Set initial order in store
      useOrdersStore.setState({
        orders: [mockOrder],
        selectedOrder: mockOrder
      })

      const cancelledOrder: Order = {
        ...mockOrder,
        status: 'Cancelled'
      }

      const mockResponse: ApiResponse<Order> = {
        success: true,
        data: cancelledOrder
      }

      mockOrderService.cancelOrder.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const cancelResult = await result.current.cancelOrder('order-123', 'Customer requested cancellation')
        expect(cancelResult.success).toBe(true)
      })

      expect(result.current.orders[0]).toEqual(cancelledOrder)
      expect(result.current.selectedOrder).toEqual(cancelledOrder)

      expect(mockOrderService.cancelOrder).toHaveBeenCalledWith('order-123', 'Customer requested cancellation')
    })
  })

  describe('Order Queries Integration', () => {
    test('should fetch orders by customer successfully', async () => {
      const customerOrders = [mockOrder]
      
      const mockResponse: ApiResponse<Order[]> = {
        success: true,
        data: customerOrders,
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1
        }
      }

      mockOrderService.getOrdersByCustomer.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchOrdersByCustomer('customer-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.orders).toEqual(customerOrders)
      expect(result.current.filters.customerId).toBe('customer-123')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockOrderService.getOrdersByCustomer).toHaveBeenCalledWith(
        'customer-123',
        { page: 1, limit: 10 }
      )
    })

    test('should fetch orders by branch successfully', async () => {
      const branchOrders = mockOrders
      
      const mockResponse: ApiResponse<Order[]> = {
        success: true,
        data: branchOrders,
        pagination: {
          page: 1,
          limit: 20,
          total: 3,
          totalPages: 1
        }
      }

      mockOrderService.getOrdersByBranch.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchOrdersByBranch('branch-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.orders).toEqual(branchOrders)
      expect(result.current.filters.branchId).toBe('branch-123')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockOrderService.getOrdersByBranch).toHaveBeenCalledWith(
        'branch-123',
        { page: 1, limit: 20 }
      )
    })

    test('should fetch recent orders successfully', async () => {
      const recentOrders = [mockOrder]
      
      const mockResponse: ApiResponse<Order[]> = {
        success: true,
        data: recentOrders
      }

      mockOrderService.getRecentOrders.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchRecentOrders(5, 'branch-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.recentOrders).toEqual(recentOrders)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockOrderService.getRecentOrders).toHaveBeenCalledWith(5, 'branch-123')
    })
  })

  describe('Search Integration', () => {
    test('should search orders successfully', async () => {
      const searchResults = [mockOrder]
      
      const mockResponse: ApiResponse<Order[]> = {
        success: true,
        data: searchResults,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockOrderService.searchOrders.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useOrdersStore())

      await act(async () => {
        const searchResult = await result.current.searchOrders('ORD-2024')
        expect(searchResult.success).toBe(true)
      })

      expect(result.current.orders).toEqual(searchResults)
      expect(result.current.searchQuery).toBe('ORD-2024')
      expect(result.current.filters.search).toBe('ORD-2024')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockOrderService.searchOrders).toHaveBeenCalledWith(
        'ORD-2024',
        { page: 1, limit: 20 },
        {}
      )
    })
  })
})
