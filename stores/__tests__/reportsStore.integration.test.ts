// stores/__tests__/reportsStore.integration.test.ts - Reports store integration tests

import { renderHook, act } from '@testing-library/react'
import { useReportsStore } from '../reportsStore'
import type { Report, DashboardData, ReportSchedule, SalesReport, ApiResponse, DateRange } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  reportsService: {
    validateReportData: jest.fn(() => ({ isValid: true, errors: [] })),
    calculateReportMetrics: jest.fn(() => ({ totalReports: 10, completedReports: 8, failedReports: 1, averageGenerationTime: 5000 })),
    formatReportSize: jest.fn(() => '1.5 MB'),
    getReportTypeIcon: jest.fn(() => '📊'),
    getReportStatusColor: jest.fn(() => 'green'),
    getExportFormatIcon: jest.fn(() => '📄'),
    isReportExpired: jest.fn(() => false),
    canUserAccessReport: jest.fn(() => true),
    getReportSummary: jest.fn(() => 'Sales report summary'),
    formatDateRange: jest.fn(() => '2024-01-01 - 2024-01-31'),
    getReports: jest.fn(),
    getReportById: jest.fn(),
    createReport: jest.fn(),
    updateReport: jest.fn(),
    deleteReport: jest.fn(),
    getDashboardData: jest.fn(),
    generateReport: jest.fn(),
    generateSalesReport: jest.fn(),
    generateInventoryReport: jest.fn(),
    generateCustomerReport: jest.fn(),
    generateBranchReport: jest.fn(),
    generateFinancialReport: jest.fn(),
    createCustomReport: jest.fn(),
    updateCustomReport: jest.fn(),
    executeCustomReport: jest.fn(),
    getReportSchedules: jest.fn(),
    createReportSchedule: jest.fn(),
    updateReportSchedule: jest.fn(),
    deleteReportSchedule: jest.fn(),
    toggleSchedule: jest.fn(),
    exportReport: jest.fn(),
    downloadReport: jest.fn(),
    shareReport: jest.fn(),
    getReportsByType: jest.fn(),
    getRecentReports: jest.fn(),
    getPopularReports: jest.fn(),
    getReportUsageStats: jest.fn(),
    searchReports: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Import the mocked module
const { reportsService } = require('@/services/frontend')
const mockReportsService = reportsService as jest.Mocked<typeof reportsService>

// Mock report data
const mockReport: Report = {
  _id: 'report-123',
  title: 'Monthly Sales Report',
  description: 'Sales performance for January 2024',
  reportType: 'Sales',
  status: 'Completed',
  format: 'PDF',
  fileSize: 1024000,
  filePath: '/reports/sales-jan-2024.pdf',
  downloadUrl: 'https://example.com/reports/sales-jan-2024.pdf',
  parameters: {
    dateRange: {
      startDate: '2024-01-01',
      endDate: '2024-01-31'
    },
    branchId: 'branch-123'
  },
  generationTime: 5000,
  createdBy: 'user-123',
  createdByName: 'John Manager',
  sharedWith: ['user-456', 'user-789'],
  tags: ['sales', 'monthly', 'performance'],
  isScheduled: false,
  expiresAt: '2024-03-01T00:00:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockReports: Report[] = [
  mockReport,
  { ...mockReport, _id: 'report-124', title: 'Inventory Report', reportType: 'Inventory' },
  { ...mockReport, _id: 'report-125', title: 'Customer Report', reportType: 'Customer' }
]

const mockDashboardData: DashboardData = {
  totalSales: 5000000,
  totalOrders: 150,
  totalCustomers: 75,
  totalProducts: 200,
  salesGrowth: 15.5,
  orderGrowth: 12.3,
  customerGrowth: 8.7,
  topProducts: [
    { productId: 'product-1', productName: 'Laptop', sales: 1000000, quantity: 20 },
    { productId: 'product-2', productName: 'Phone', sales: 800000, quantity: 30 }
  ],
  topCustomers: [
    { customerId: 'customer-1', customerName: 'John Doe', totalSpent: 500000 },
    { customerId: 'customer-2', customerName: 'Jane Smith', totalSpent: 400000 }
  ],
  salesByBranch: [
    { branchId: 'branch-1', branchName: 'Main Branch', sales: 3000000 },
    { branchId: 'branch-2', branchName: 'North Branch', sales: 2000000 }
  ],
  salesTrend: [
    { date: '2024-01-01', sales: 100000 },
    { date: '2024-01-02', sales: 120000 },
    { date: '2024-01-03', sales: 110000 }
  ],
  lowStockItems: 5,
  criticalStockItems: 2,
  pendingOrders: 10,
  lastUpdated: '2024-01-01T12:00:00Z'
}

const mockReportSchedule: ReportSchedule = {
  _id: 'schedule-123',
  name: 'Weekly Sales Report',
  description: 'Automated weekly sales report',
  reportType: 'Sales',
  frequency: 'Weekly',
  parameters: {
    dateRange: 'last_week',
    format: 'PDF'
  },
  recipients: ['<EMAIL>', '<EMAIL>'],
  isActive: true,
  nextRunDate: '2024-01-08T09:00:00Z',
  lastRunDate: '2024-01-01T09:00:00Z',
  createdBy: 'user-123',
  createdByName: 'John Manager',
  createdAt: '2023-12-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockSalesReport: SalesReport = {
  ...mockReport,
  reportType: 'Sales',
  salesData: {
    totalSales: 1000000,
    totalOrders: 50,
    averageOrderValue: 20000,
    salesByProduct: [
      { productId: 'product-1', productName: 'Laptop', sales: 600000, quantity: 15 },
      { productId: 'product-2', productName: 'Phone', sales: 400000, quantity: 20 }
    ],
    salesByBranch: [
      { branchId: 'branch-1', branchName: 'Main Branch', sales: 700000 },
      { branchId: 'branch-2', branchName: 'North Branch', sales: 300000 }
    ]
  }
}

describe('ReportsStore - Integration Tests', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    
    // Reset store state
    useReportsStore.setState({
      reports: [],
      selectedReport: null,
      isLoading: false,
      error: null,
      dashboardData: null,
      dashboardLoading: false,
      lastUpdated: null,
      salesReports: [],
      inventoryReports: [],
      customerReports: [],
      branchReports: [],
      financialReports: [],
      customReports: [],
      reportSchedules: [],
      activeSchedules: [],
      currentPage: 1,
      totalPages: 0,
      totalReports: 0,
      pageSize: 20,
      filters: {},
      searchQuery: ''
    })
  })

  describe('Fetch Reports Integration', () => {
    test('should fetch reports successfully', async () => {
      const mockResponse: ApiResponse<Report[]> = {
        success: true,
        data: mockReports,
        pagination: {
          page: 1,
          limit: 20,
          total: 3,
          totalPages: 1
        }
      }

      mockReportsService.getReports.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchReports()
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.reports).toEqual(mockReports)
      expect(result.current.totalReports).toBe(3)
      expect(result.current.totalPages).toBe(1)
      expect(result.current.currentPage).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockReportsService.getReports).toHaveBeenCalledWith(
        { page: 1, limit: 20 },
        {}
      )
    })

    test('should handle fetch reports failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Failed to fetch reports'
      }

      mockReportsService.getReports.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchReports()
        expect(fetchResult.success).toBe(false)
        expect(fetchResult.error).toBe('Failed to fetch reports')
      })

      expect(result.current.reports).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Failed to fetch reports')
    })

    test('should fetch report by ID successfully', async () => {
      const mockResponse: ApiResponse<Report> = {
        success: true,
        data: mockReport
      }

      mockReportsService.getReportById.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchReportById('report-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.selectedReport).toEqual(mockReport)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockReportsService.getReportById).toHaveBeenCalledWith('report-123')
    })
  })

  describe('Dashboard Integration', () => {
    test('should fetch dashboard data successfully', async () => {
      const mockResponse: ApiResponse<DashboardData> = {
        success: true,
        data: mockDashboardData
      }

      mockReportsService.getDashboardData.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      const dateRange: DateRange = {
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      }

      await act(async () => {
        const fetchResult = await result.current.fetchDashboardData(dateRange, 'branch-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.dashboardData).toEqual(mockDashboardData)
      expect(result.current.dashboardLoading).toBe(false)
      expect(result.current.lastUpdated).toBeTruthy()
      expect(result.current.error).toBeNull()

      expect(mockReportsService.getDashboardData).toHaveBeenCalledWith(dateRange, 'branch-123')
    })

    test('should refresh dashboard successfully', async () => {
      const mockResponse: ApiResponse<DashboardData> = {
        success: true,
        data: mockDashboardData
      }

      mockReportsService.getDashboardData.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const refreshResult = await result.current.refreshDashboard()
        expect(refreshResult.success).toBe(true)
      })

      expect(result.current.dashboardData).toEqual(mockDashboardData)
      expect(result.current.dashboardLoading).toBe(false)
    })
  })

  describe('Report Generation Integration', () => {
    test('should generate sales report successfully', async () => {
      const mockResponse: ApiResponse<SalesReport> = {
        success: true,
        data: mockSalesReport
      }

      mockReportsService.generateSalesReport.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      const dateRange: DateRange = {
        startDate: '2024-01-01',
        endDate: '2024-01-31'
      }

      await act(async () => {
        const generateResult = await result.current.generateSalesReport(dateRange, 'branch-123', 'PDF')
        expect(generateResult.success).toBe(true)
      })

      expect(result.current.salesReports).toContain(mockSalesReport)
      expect(result.current.reports).toContain(mockSalesReport)
      expect(result.current.totalReports).toBe(1)

      expect(mockReportsService.generateSalesReport).toHaveBeenCalledWith(dateRange, 'branch-123', 'PDF')
    })

    test('should generate inventory report successfully', async () => {
      const inventoryReport = { ...mockReport, reportType: 'Inventory' as const }
      
      const mockResponse: ApiResponse<Report> = {
        success: true,
        data: inventoryReport
      }

      mockReportsService.generateInventoryReport.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const generateResult = await result.current.generateInventoryReport('branch-123', true, 'Excel')
        expect(generateResult.success).toBe(true)
      })

      expect(result.current.inventoryReports).toContain(inventoryReport)
      expect(result.current.reports).toContain(inventoryReport)

      expect(mockReportsService.generateInventoryReport).toHaveBeenCalledWith('branch-123', true, 'Excel')
    })

    test('should generate custom report successfully', async () => {
      const customReport = { ...mockReport, reportType: 'Custom' as const }
      
      const mockResponse: ApiResponse<Report> = {
        success: true,
        data: customReport
      }

      mockReportsService.generateReport.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const generateResult = await result.current.generateReport('Custom', { customQuery: 'SELECT * FROM sales' }, 'CSV')
        expect(generateResult.success).toBe(true)
        expect(generateResult.reportId).toBe('report-123')
      })

      expect(result.current.reports).toContain(customReport)

      expect(mockReportsService.generateReport).toHaveBeenCalledWith('Custom', { customQuery: 'SELECT * FROM sales' }, 'CSV')
    })
  })

  describe('Report Scheduling Integration', () => {
    test('should create report schedule successfully', async () => {
      const scheduleData = {
        name: 'Daily Sales Report',
        reportType: 'Sales' as const,
        frequency: 'Daily',
        parameters: { format: 'PDF' },
        recipients: ['<EMAIL>']
      }

      const mockResponse: ApiResponse<ReportSchedule> = {
        success: true,
        data: mockReportSchedule
      }

      mockReportsService.createReportSchedule.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const createResult = await result.current.createReportSchedule(scheduleData)
        expect(createResult.success).toBe(true)
        expect(createResult.schedule).toEqual(mockReportSchedule)
      })

      expect(result.current.reportSchedules).toContain(mockReportSchedule)
      expect(result.current.activeSchedules).toContain(mockReportSchedule)

      expect(mockReportsService.createReportSchedule).toHaveBeenCalledWith(scheduleData)
    })

    test('should toggle schedule successfully', async () => {
      // Set initial schedule in store
      useReportsStore.setState({
        reportSchedules: [mockReportSchedule],
        activeSchedules: [mockReportSchedule]
      })

      const deactivatedSchedule: ReportSchedule = {
        ...mockReportSchedule,
        isActive: false
      }

      const mockResponse: ApiResponse<ReportSchedule> = {
        success: true,
        data: deactivatedSchedule
      }

      mockReportsService.toggleSchedule.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const toggleResult = await result.current.toggleSchedule('schedule-123', false)
        expect(toggleResult.success).toBe(true)
      })

      expect(result.current.reportSchedules[0]).toEqual(deactivatedSchedule)
      expect(result.current.activeSchedules).toHaveLength(0)

      expect(mockReportsService.toggleSchedule).toHaveBeenCalledWith('schedule-123', false)
    })
  })

  describe('Report Export Integration', () => {
    test('should export report successfully', async () => {
      const mockResponse: ApiResponse<{ downloadUrl: string }> = {
        success: true,
        data: { downloadUrl: 'https://example.com/download/report-123.pdf' }
      }

      mockReportsService.exportReport.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const exportResult = await result.current.exportReport('report-123', 'PDF')
        expect(exportResult.success).toBe(true)
        expect(exportResult.downloadUrl).toBe('https://example.com/download/report-123.pdf')
      })

      expect(mockReportsService.exportReport).toHaveBeenCalledWith('report-123', 'PDF')
    })

    test('should share report successfully', async () => {
      const mockResponse: ApiResponse<boolean> = {
        success: true,
        data: true
      }

      mockReportsService.shareReport.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      const recipients = ['<EMAIL>', '<EMAIL>']
      const message = 'Please review this report'

      await act(async () => {
        const shareResult = await result.current.shareReport('report-123', recipients, message)
        expect(shareResult.success).toBe(true)
      })

      expect(mockReportsService.shareReport).toHaveBeenCalledWith('report-123', recipients, message)
    })
  })

  describe('Report Analytics Integration', () => {
    test('should fetch reports by type successfully', async () => {
      const salesReports = mockReports.filter(r => r.reportType === 'Sales')
      
      const mockResponse: ApiResponse<Report[]> = {
        success: true,
        data: salesReports,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockReportsService.getReportsByType.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const fetchResult = await result.current.fetchReportsByType('Sales')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.reports).toEqual(salesReports)
      expect(result.current.filters.reportType).toBe('Sales')

      expect(mockReportsService.getReportsByType).toHaveBeenCalledWith('Sales', { page: 1, limit: 20 })
    })

    test('should get report usage stats successfully', async () => {
      const usageStats = {
        totalViews: 150,
        totalDownloads: 45,
        uniqueUsers: 25,
        averageViewTime: 120
      }

      const mockResponse: ApiResponse<any> = {
        success: true,
        data: usageStats
      }

      mockReportsService.getReportUsageStats.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const statsResult = await result.current.getReportUsageStats('report-123', 'month')
        expect(statsResult.success).toBe(true)
        expect(statsResult.stats).toEqual(usageStats)
      })

      expect(mockReportsService.getReportUsageStats).toHaveBeenCalledWith('report-123', 'month')
    })
  })

  describe('Search Integration', () => {
    test('should search reports successfully', async () => {
      const searchResults = [mockReport]
      
      const mockResponse: ApiResponse<Report[]> = {
        success: true,
        data: searchResults,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockReportsService.searchReports.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const searchResult = await result.current.searchReports('sales report')
        expect(searchResult.success).toBe(true)
      })

      expect(result.current.reports).toEqual(searchResults)
      expect(result.current.searchQuery).toBe('sales report')
      expect(result.current.filters.search).toBe('sales report')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockReportsService.searchReports).toHaveBeenCalledWith(
        'sales report',
        { page: 1, limit: 20 },
        {}
      )
    })
  })

  describe('Create Report Integration', () => {
    test('should create report successfully', async () => {
      const reportData = {
        title: 'New Sales Report',
        description: 'Q1 2024 sales analysis',
        reportType: 'Sales' as const,
        parameters: {
          dateRange: {
            startDate: '2024-01-01',
            endDate: '2024-03-31'
          }
        }
      }

      const newReport: Report = {
        ...mockReport,
        _id: 'report-new',
        ...reportData
      }

      const mockResponse: ApiResponse<Report> = {
        success: true,
        data: newReport
      }

      mockReportsService.createReport.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const createResult = await result.current.createReport(reportData)
        expect(createResult.success).toBe(true)
        expect(createResult.report).toEqual(newReport)
      })

      expect(result.current.reports).toContain(newReport)
      expect(result.current.totalReports).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockReportsService.validateReportData).toHaveBeenCalledWith(reportData)
      expect(mockReportsService.createReport).toHaveBeenCalledWith(reportData)
    })

    test('should handle validation error during create', async () => {
      const reportData = {
        title: '',
        reportType: 'Sales' as const,
        parameters: {}
      }

      mockReportsService.validateReportData.mockReturnValueOnce({
        isValid: false,
        errors: [
          { field: 'title', message: 'Report title is required' },
          { field: 'parameters', message: 'Report parameters are required' }
        ]
      })

      const { result } = renderHook(() => useReportsStore())

      await act(async () => {
        const createResult = await result.current.createReport(reportData)
        expect(createResult.success).toBe(false)
        expect(createResult.error).toBe('Report title is required, Report parameters are required')
      })

      expect(result.current.reports).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Report title is required, Report parameters are required')
      expect(mockReportsService.createReport).not.toHaveBeenCalled()
    })
  })
})
