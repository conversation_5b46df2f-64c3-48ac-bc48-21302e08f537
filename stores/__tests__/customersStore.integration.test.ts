// stores/__tests__/customersStore.integration.test.ts - Customers store integration tests

import { renderHook, act } from '@testing-library/react'
import { useCustomersStore } from '../customersStore'
import type { Customer, CustomerTier, ApiResponse, CreateCustomerData, UpdateCustomerData } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  customerService: {
    getCustomerTiers: jest.fn(() => ['VIP', 'Gold', 'Silver', 'Bronze', 'Regular']),
    getCustomerTier: jest.fn((customer: Customer) => {
      if (customer.loyaltyPoints >= 10000) return 'VIP'
      if (customer.loyaltyPoints >= 5000) return 'Gold'
      if (customer.loyaltyPoints >= 2000) return 'Silver'
      if (customer.loyaltyPoints >= 500) return 'Bronze'
      return 'Regular'
    }),
    validateCustomerData: jest.fn(() => ({ isValid: true, errors: [] })),
    calculateCustomerValue: jest.fn((customer: Customer) => customer.totalSpent || 0),
    calculateCustomerLifetimeValue: jest.fn((customer: Customer) => (customer.totalSpent || 0) * 1.5),
    getCustomerTierBenefits: jest.fn((tier: CustomerTier) => [`${tier} benefits`]),
    getNextTierRequirement: jest.fn(() => ({ nextTier: 'Gold' as CustomerTier, pointsNeeded: 1000 })),
    isCustomerActive: jest.fn(() => true),
    formatCustomerName: jest.fn((customer: Customer) => `${customer.firstName} ${customer.lastName}`.trim()),
    formatCustomerAddress: jest.fn(() => '123 Main St, Lilongwe'),
    getCustomers: jest.fn(),
    getCustomerById: jest.fn(),
    createCustomer: jest.fn(),
    updateCustomer: jest.fn(),
    deleteCustomer: jest.fn(),
    getCustomersByTier: jest.fn(),
    updateCustomerTier: jest.fn(),
    addLoyaltyPoints: jest.fn(),
    redeemLoyaltyPoints: jest.fn(),
    getTopCustomers: jest.fn(),
    getRecentCustomers: jest.fn(),
    getInactiveCustomers: jest.fn(),
    getCustomersByBranch: jest.fn(),
    getCustomerOrderHistory: jest.fn(),
    searchCustomers: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Import the mocked module
const { customerService } = require('@/services/frontend')
const mockCustomerService = customerService as jest.Mocked<typeof customerService>

// Mock customer data
const mockCustomer: Customer = {
  _id: 'customer-123',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+265123456789',
  address: {
    street: '123 Main St',
    city: 'Lilongwe',
    region: 'Central',
    country: 'Malawi',
    postalCode: '12345'
  },
  dateOfBirth: '1990-01-01',
  preferredBranch: 'branch-123',
  loyaltyPoints: 1500,
  totalSpent: 500000,
  totalOrders: 5,
  lastOrderDate: '2024-01-01T00:00:00Z',
  isActive: true,
  notes: 'VIP customer',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockCustomers: Customer[] = [
  mockCustomer,
  { ...mockCustomer, _id: 'customer-124', firstName: 'Jane', lastName: 'Smith', loyaltyPoints: 7000 },
  { ...mockCustomer, _id: 'customer-125', firstName: 'Bob', lastName: 'Johnson', loyaltyPoints: 300 }
]

describe('CustomersStore - Integration Tests', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    
    // Reset store state
    useCustomersStore.setState({
      customers: [],
      selectedCustomer: null,
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 0,
      totalCustomers: 0,
      pageSize: 20,
      filters: {},
      searchQuery: '',
      vipCustomers: [],
      goldCustomers: [],
      silverCustomers: [],
      bronzeCustomers: [],
      regularCustomers: [],
      topCustomers: [],
      recentCustomers: [],
      inactiveCustomers: []
    })
  })

  describe('Fetch Customers Integration', () => {
    test('should fetch customers successfully', async () => {
      const mockResponse: ApiResponse<Customer[]> = {
        success: true,
        data: mockCustomers,
        pagination: {
          page: 1,
          limit: 20,
          total: 3,
          totalPages: 1
        }
      }

      mockCustomerService.getCustomers.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchCustomers()
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.customers).toEqual(mockCustomers)
      expect(result.current.totalCustomers).toBe(3)
      expect(result.current.totalPages).toBe(1)
      expect(result.current.currentPage).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.getCustomers).toHaveBeenCalledWith(
        { page: 1, limit: 20 },
        {}
      )
    })

    test('should handle fetch customers failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Failed to fetch customers'
      }

      mockCustomerService.getCustomers.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchCustomers()
        expect(fetchResult.success).toBe(false)
        expect(fetchResult.error).toBe('Failed to fetch customers')
      })

      expect(result.current.customers).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Failed to fetch customers')
    })

    test('should fetch customer by ID successfully', async () => {
      const mockResponse: ApiResponse<Customer> = {
        success: true,
        data: mockCustomer
      }

      mockCustomerService.getCustomerById.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchCustomerById('customer-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.selectedCustomer).toEqual(mockCustomer)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.getCustomerById).toHaveBeenCalledWith('customer-123')
    })
  })

  describe('Create Customer Integration', () => {
    test('should create customer successfully', async () => {
      const createData: CreateCustomerData = {
        firstName: 'Alice',
        lastName: 'Wilson',
        email: '<EMAIL>',
        phone: '+265987654321',
        address: {
          street: '456 Oak St',
          city: 'Blantyre',
          region: 'Southern',
          country: 'Malawi',
          postalCode: '54321'
        },
        dateOfBirth: '1985-05-15',
        preferredBranch: 'branch-456'
      }

      const newCustomer: Customer = {
        ...mockCustomer,
        _id: 'customer-new',
        ...createData,
        loyaltyPoints: 0,
        totalSpent: 0,
        totalOrders: 0,
        lastOrderDate: undefined
      }

      const mockResponse: ApiResponse<Customer> = {
        success: true,
        data: newCustomer
      }

      mockCustomerService.createCustomer.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const createResult = await result.current.createCustomer(createData)
        expect(createResult.success).toBe(true)
        expect(createResult.customer).toEqual(newCustomer)
      })

      expect(result.current.customers).toContain(newCustomer)
      expect(result.current.totalCustomers).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.validateCustomerData).toHaveBeenCalledWith(createData)
      expect(mockCustomerService.createCustomer).toHaveBeenCalledWith(createData)
    })

    test('should handle validation error during create', async () => {
      const createData: CreateCustomerData = {
        firstName: '',
        lastName: 'Wilson',
        email: 'invalid-email',
        phone: '+265987654321',
        address: {
          street: '456 Oak St',
          city: 'Blantyre',
          region: 'Southern',
          country: 'Malawi',
          postalCode: '54321'
        },
        dateOfBirth: '1985-05-15',
        preferredBranch: 'branch-456'
      }

      mockCustomerService.validateCustomerData.mockReturnValueOnce({
        isValid: false,
        errors: [
          { field: 'firstName', message: 'First name is required' },
          { field: 'email', message: 'Invalid email format' }
        ]
      })

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const createResult = await result.current.createCustomer(createData)
        expect(createResult.success).toBe(false)
        expect(createResult.error).toBe('First name is required, Invalid email format')
      })

      expect(result.current.customers).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('First name is required, Invalid email format')
      expect(mockCustomerService.createCustomer).not.toHaveBeenCalled()
    })
  })

  describe('Update Customer Integration', () => {
    test('should update customer successfully', async () => {
      // Set initial customer in store
      useCustomersStore.setState({
        customers: [mockCustomer],
        selectedCustomer: mockCustomer
      })

      const updateData: UpdateCustomerData = {
        firstName: 'Johnny',
        phone: '+265999888777',
        notes: 'Updated customer notes'
      }

      const updatedCustomer: Customer = {
        ...mockCustomer,
        ...updateData
      }

      const mockResponse: ApiResponse<Customer> = {
        success: true,
        data: updatedCustomer
      }

      mockCustomerService.updateCustomer.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const updateResult = await result.current.updateCustomer('customer-123', updateData)
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.customers[0]).toEqual(updatedCustomer)
      expect(result.current.selectedCustomer).toEqual(updatedCustomer)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.validateCustomerData).toHaveBeenCalledWith(updateData)
      expect(mockCustomerService.updateCustomer).toHaveBeenCalledWith('customer-123', updateData)
    })
  })

  describe('Delete Customer Integration', () => {
    test('should delete customer successfully', async () => {
      // Set initial customers in store
      useCustomersStore.setState({
        customers: mockCustomers,
        selectedCustomer: mockCustomer,
        totalCustomers: 3
      })

      const mockResponse: ApiResponse<boolean> = {
        success: true,
        data: true
      }

      mockCustomerService.deleteCustomer.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const deleteResult = await result.current.deleteCustomer('customer-123')
        expect(deleteResult.success).toBe(true)
      })

      expect(result.current.customers).not.toContain(mockCustomer)
      expect(result.current.customers).toHaveLength(2)
      expect(result.current.selectedCustomer).toBeNull()
      expect(result.current.totalCustomers).toBe(2)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.deleteCustomer).toHaveBeenCalledWith('customer-123')
    })
  })

  describe('Customer Tiers Integration', () => {
    test('should fetch customers by tier successfully', async () => {
      const goldCustomers = mockCustomers.filter(c => c.loyaltyPoints >= 5000)
      
      const mockResponse: ApiResponse<Customer[]> = {
        success: true,
        data: goldCustomers,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockCustomerService.getCustomersByTier.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchCustomersByTier('Gold')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.customers).toEqual(goldCustomers)
      expect(result.current.filters.tier).toBe('Gold')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.getCustomersByTier).toHaveBeenCalledWith(
        'Gold',
        { page: 1, limit: 20 }
      )
    })

    test('should update customer tier successfully', async () => {
      // Set initial customer in store
      useCustomersStore.setState({
        customers: [mockCustomer],
        selectedCustomer: mockCustomer
      })

      const updatedCustomer: Customer = {
        ...mockCustomer,
        loyaltyPoints: 8000 // Gold tier
      }

      const mockResponse: ApiResponse<Customer> = {
        success: true,
        data: updatedCustomer
      }

      mockCustomerService.updateCustomerTier.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const updateResult = await result.current.updateCustomerTier('customer-123', 'Gold')
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.customers[0]).toEqual(updatedCustomer)
      expect(result.current.selectedCustomer).toEqual(updatedCustomer)

      expect(mockCustomerService.updateCustomerTier).toHaveBeenCalledWith('customer-123', 'Gold')
    })
  })

  describe('Loyalty Points Integration', () => {
    test('should add loyalty points successfully', async () => {
      // Set initial customer in store
      useCustomersStore.setState({
        customers: [mockCustomer],
        selectedCustomer: mockCustomer
      })

      const updatedCustomer: Customer = {
        ...mockCustomer,
        loyaltyPoints: 2000 // 1500 + 500
      }

      const mockResponse: ApiResponse<Customer> = {
        success: true,
        data: updatedCustomer
      }

      mockCustomerService.addLoyaltyPoints.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const addResult = await result.current.addLoyaltyPoints('customer-123', 500, 'Purchase bonus')
        expect(addResult.success).toBe(true)
      })

      expect(result.current.customers[0]).toEqual(updatedCustomer)
      expect(result.current.selectedCustomer).toEqual(updatedCustomer)

      expect(mockCustomerService.addLoyaltyPoints).toHaveBeenCalledWith('customer-123', 500, 'Purchase bonus')
    })

    test('should redeem loyalty points successfully', async () => {
      // Set initial customer in store
      useCustomersStore.setState({
        customers: [mockCustomer],
        selectedCustomer: mockCustomer
      })

      const updatedCustomer: Customer = {
        ...mockCustomer,
        loyaltyPoints: 1000 // 1500 - 500
      }

      const mockResponse: ApiResponse<Customer> = {
        success: true,
        data: updatedCustomer
      }

      mockCustomerService.redeemLoyaltyPoints.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const redeemResult = await result.current.redeemLoyaltyPoints('customer-123', 500, 'Discount applied')
        expect(redeemResult.success).toBe(true)
      })

      expect(result.current.customers[0]).toEqual(updatedCustomer)
      expect(result.current.selectedCustomer).toEqual(updatedCustomer)

      expect(mockCustomerService.redeemLoyaltyPoints).toHaveBeenCalledWith('customer-123', 500, 'Discount applied')
    })
  })

  describe('Customer Analytics Integration', () => {
    test('should fetch top customers successfully', async () => {
      const topCustomers = [mockCustomers[1]] // Jane Smith with 7000 points
      
      const mockResponse: ApiResponse<Customer[]> = {
        success: true,
        data: topCustomers
      }

      mockCustomerService.getTopCustomers.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchTopCustomers(5, 'branch-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.topCustomers).toEqual(topCustomers)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.getTopCustomers).toHaveBeenCalledWith(5, 'branch-123')
    })

    test('should fetch recent customers successfully', async () => {
      const recentCustomers = [mockCustomer]
      
      const mockResponse: ApiResponse<Customer[]> = {
        success: true,
        data: recentCustomers
      }

      mockCustomerService.getRecentCustomers.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchRecentCustomers(10)
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.recentCustomers).toEqual(recentCustomers)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.getRecentCustomers).toHaveBeenCalledWith(10, undefined)
    })

    test('should fetch inactive customers successfully', async () => {
      const inactiveCustomers = [mockCustomers[2]] // Bob Johnson
      
      const mockResponse: ApiResponse<Customer[]> = {
        success: true,
        data: inactiveCustomers
      }

      mockCustomerService.getInactiveCustomers.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchInactiveCustomers(60)
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.inactiveCustomers).toEqual(inactiveCustomers)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.getInactiveCustomers).toHaveBeenCalledWith(60, { page: 1, limit: 20 })
    })
  })

  describe('Customer Queries Integration', () => {
    test('should fetch customers by branch successfully', async () => {
      const branchCustomers = mockCustomers
      
      const mockResponse: ApiResponse<Customer[]> = {
        success: true,
        data: branchCustomers,
        pagination: {
          page: 1,
          limit: 20,
          total: 3,
          totalPages: 1
        }
      }

      mockCustomerService.getCustomersByBranch.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchCustomersByBranch('branch-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.customers).toEqual(branchCustomers)
      expect(result.current.filters.branchId).toBe('branch-123')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.getCustomersByBranch).toHaveBeenCalledWith(
        'branch-123',
        { page: 1, limit: 20 }
      )
    })

    test('should fetch customer order history successfully', async () => {
      const mockResponse: ApiResponse<any[]> = {
        success: true,
        data: []
      }

      mockCustomerService.getCustomerOrderHistory.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const fetchResult = await result.current.fetchCustomerOrderHistory('customer-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(mockCustomerService.getCustomerOrderHistory).toHaveBeenCalledWith(
        'customer-123',
        { page: 1, limit: 10 }
      )
    })
  })

  describe('Search Integration', () => {
    test('should search customers successfully', async () => {
      const searchResults = [mockCustomer]
      
      const mockResponse: ApiResponse<Customer[]> = {
        success: true,
        data: searchResults,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockCustomerService.searchCustomers.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useCustomersStore())

      await act(async () => {
        const searchResult = await result.current.searchCustomers('john doe')
        expect(searchResult.success).toBe(true)
      })

      expect(result.current.customers).toEqual(searchResults)
      expect(result.current.searchQuery).toBe('john doe')
      expect(result.current.filters.search).toBe('john doe')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockCustomerService.searchCustomers).toHaveBeenCalledWith(
        'john doe',
        { page: 1, limit: 20 },
        {}
      )
    })
  })
})
