// stores/__tests__/customersStore.basic.test.ts - Basic customers store tests

import { useCustomersStore } from '../customersStore'
import type { Customer, CustomerTier } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  customerService: {
    getCustomerTiers: jest.fn(() => ['VIP', 'Gold', 'Silver', 'Bronze', 'Regular']),
    getCustomerTier: jest.fn((customer: Customer) => {
      if (customer.loyaltyPoints >= 10000) return 'VIP'
      if (customer.loyaltyPoints >= 5000) return 'Gold'
      if (customer.loyaltyPoints >= 2000) return 'Silver'
      if (customer.loyaltyPoints >= 500) return 'Bronze'
      return 'Regular'
    }),
    validateCustomerData: jest.fn(() => ({ isValid: true, errors: [] })),
    calculateCustomerValue: jest.fn((customer: Customer) => customer.totalSpent || 0),
    calculateCustomerLifetimeValue: jest.fn((customer: Customer) => (customer.totalSpent || 0) * 1.5),
    getCustomerTierBenefits: jest.fn((tier: CustomerTier) => {
      const benefits: Record<CustomerTier, string[]> = {
        'VIP': ['Free shipping', '20% discount', 'Priority support', 'Exclusive products'],
        'Gold': ['Free shipping', '15% discount', 'Priority support'],
        'Silver': ['Free shipping', '10% discount'],
        'Bronze': ['5% discount'],
        'Regular': ['Standard benefits']
      }
      return benefits[tier] || []
    }),
    getNextTierRequirement: jest.fn((customer: Customer) => {
      const currentPoints = customer.loyaltyPoints
      if (currentPoints >= 10000) return { nextTier: null, pointsNeeded: 0 }
      if (currentPoints >= 5000) return { nextTier: 'VIP' as CustomerTier, pointsNeeded: 10000 - currentPoints }
      if (currentPoints >= 2000) return { nextTier: 'Gold' as CustomerTier, pointsNeeded: 5000 - currentPoints }
      if (currentPoints >= 500) return { nextTier: 'Silver' as CustomerTier, pointsNeeded: 2000 - currentPoints }
      return { nextTier: 'Bronze' as CustomerTier, pointsNeeded: 500 - currentPoints }
    }),
    isCustomerActive: jest.fn((customer: Customer, daysSinceLastOrder = 90) => {
      if (!customer.lastOrderDate) return false
      const lastOrderDate = new Date(customer.lastOrderDate)
      const now = new Date('2024-01-15') // Fixed date for testing
      const daysSince = (now.getTime() - lastOrderDate.getTime()) / (1000 * 60 * 60 * 24)
      return daysSince <= daysSinceLastOrder
    }),
    formatCustomerName: jest.fn((customer: Customer) => `${customer.firstName} ${customer.lastName}`.trim()),
    formatCustomerAddress: jest.fn((customer: Customer) => {
      const parts = [customer.address?.street, customer.address?.city, customer.address?.region].filter(Boolean)
      return parts.join(', ')
    }),
    getCustomers: jest.fn(),
    getCustomerById: jest.fn(),
    createCustomer: jest.fn(),
    updateCustomer: jest.fn(),
    deleteCustomer: jest.fn(),
    getCustomersByTier: jest.fn(),
    updateCustomerTier: jest.fn(),
    addLoyaltyPoints: jest.fn(),
    redeemLoyaltyPoints: jest.fn(),
    getTopCustomers: jest.fn(),
    getRecentCustomers: jest.fn(),
    getInactiveCustomers: jest.fn(),
    getCustomersByBranch: jest.fn(),
    getCustomerOrderHistory: jest.fn(),
    searchCustomers: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Mock customer data
const mockCustomer: Customer = {
  _id: 'customer-123',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+265123456789',
  address: {
    street: '123 Main St',
    city: 'Lilongwe',
    region: 'Central',
    country: 'Malawi',
    postalCode: '12345'
  },
  dateOfBirth: '1990-01-01',
  preferredBranch: 'branch-123',
  loyaltyPoints: 1500,
  totalSpent: 500000,
  totalOrders: 5,
  lastOrderDate: '2024-01-01T00:00:00Z',
  isActive: true,
  notes: 'VIP customer',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('CustomersStore - Basic Tests', () => {
  beforeEach(() => {
    // Reset store state before each test
    useCustomersStore.setState({
      customers: [],
      selectedCustomer: null,
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 0,
      totalCustomers: 0,
      pageSize: 20,
      filters: {},
      searchQuery: '',
      vipCustomers: [],
      goldCustomers: [],
      silverCustomers: [],
      bronzeCustomers: [],
      regularCustomers: [],
      topCustomers: [],
      recentCustomers: [],
      inactiveCustomers: []
    })
  })

  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const store = useCustomersStore.getState()
      
      expect(store.customers).toEqual([])
      expect(store.selectedCustomer).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.currentPage).toBe(1)
      expect(store.totalPages).toBe(0)
      expect(store.totalCustomers).toBe(0)
      expect(store.pageSize).toBe(20)
      expect(store.filters).toEqual({})
      expect(store.searchQuery).toBe('')
      expect(store.vipCustomers).toEqual([])
      expect(store.goldCustomers).toEqual([])
      expect(store.silverCustomers).toEqual([])
      expect(store.bronzeCustomers).toEqual([])
      expect(store.regularCustomers).toEqual([])
      expect(store.topCustomers).toEqual([])
      expect(store.recentCustomers).toEqual([])
      expect(store.inactiveCustomers).toEqual([])
    })

    test('should have all required methods', () => {
      const store = useCustomersStore.getState()
      
      // CRUD methods
      expect(typeof store.fetchCustomers).toBe('function')
      expect(typeof store.fetchCustomerById).toBe('function')
      expect(typeof store.createCustomer).toBe('function')
      expect(typeof store.updateCustomer).toBe('function')
      expect(typeof store.deleteCustomer).toBe('function')
      
      // Tier and loyalty methods
      expect(typeof store.fetchCustomersByTier).toBe('function')
      expect(typeof store.updateCustomerTier).toBe('function')
      expect(typeof store.addLoyaltyPoints).toBe('function')
      expect(typeof store.redeemLoyaltyPoints).toBe('function')
      
      // Analytics methods
      expect(typeof store.fetchTopCustomers).toBe('function')
      expect(typeof store.fetchRecentCustomers).toBe('function')
      expect(typeof store.fetchInactiveCustomers).toBe('function')
      
      // Query methods
      expect(typeof store.fetchCustomersByBranch).toBe('function')
      expect(typeof store.fetchCustomerOrderHistory).toBe('function')
      
      // Search and filtering methods
      expect(typeof store.searchCustomers).toBe('function')
      expect(typeof store.setFilters).toBe('function')
      expect(typeof store.clearFilters).toBe('function')
      expect(typeof store.setSearchQuery).toBe('function')
      
      // Pagination methods
      expect(typeof store.setCurrentPage).toBe('function')
      expect(typeof store.setPageSize).toBe('function')
      
      // UI state methods
      expect(typeof store.setSelectedCustomer).toBe('function')
      expect(typeof store.clearError).toBe('function')
      expect(typeof store.setLoading).toBe('function')
      
      // Utility methods
      expect(typeof store.getCustomersByBranch).toBe('function')
      expect(typeof store.getCustomersByTier).toBe('function')
      expect(typeof store.getActiveCustomers).toBe('function')
      expect(typeof store.getInactiveCustomers).toBe('function')
      expect(typeof store.getAvailableTiers).toBe('function')
      expect(typeof store.calculateCustomerValue).toBe('function')
      expect(typeof store.calculateCustomerLifetimeValue).toBe('function')
      expect(typeof store.getCustomerTierBenefits).toBe('function')
      expect(typeof store.getNextTierRequirement).toBe('function')
      expect(typeof store.isCustomerActive).toBe('function')
      expect(typeof store.formatCustomerName).toBe('function')
      expect(typeof store.formatCustomerAddress).toBe('function')
      expect(typeof store.updateTierGroups).toBe('function')
    })
  })

  describe('State Management', () => {
    test('should set selected customer', () => {
      const store = useCustomersStore.getState()
      
      store.setSelectedCustomer(mockCustomer)
      expect(useCustomersStore.getState().selectedCustomer).toEqual(mockCustomer)
      
      store.setSelectedCustomer(null)
      expect(useCustomersStore.getState().selectedCustomer).toBeNull()
    })

    test('should clear error', () => {
      useCustomersStore.setState({ error: 'Test error' })
      
      const store = useCustomersStore.getState()
      expect(store.error).toBe('Test error')
      
      store.clearError()
      expect(useCustomersStore.getState().error).toBeNull()
    })

    test('should set loading state', () => {
      const store = useCustomersStore.getState()
      
      store.setLoading(true)
      expect(useCustomersStore.getState().isLoading).toBe(true)
      
      store.setLoading(false)
      expect(useCustomersStore.getState().isLoading).toBe(false)
    })

    test('should set search query', () => {
      const store = useCustomersStore.getState()
      
      store.setSearchQuery('john doe')
      expect(useCustomersStore.getState().searchQuery).toBe('john doe')
      
      store.setSearchQuery('')
      expect(useCustomersStore.getState().searchQuery).toBe('')
    })

    test('should set filters', () => {
      const store = useCustomersStore.getState()
      
      store.setFilters({ tier: 'Gold', isActive: true })
      expect(useCustomersStore.getState().filters).toEqual({
        tier: 'Gold',
        isActive: true
      })
      
      store.setFilters({ preferredBranch: 'branch-123' })
      expect(useCustomersStore.getState().filters).toEqual({
        tier: 'Gold',
        isActive: true,
        preferredBranch: 'branch-123'
      })
    })

    test('should clear filters', () => {
      useCustomersStore.setState({
        filters: { tier: 'Gold', isActive: true },
        searchQuery: 'test'
      })
      
      const store = useCustomersStore.getState()
      store.clearFilters()
      
      const updatedStore = useCustomersStore.getState()
      expect(updatedStore.filters).toEqual({})
      expect(updatedStore.searchQuery).toBe('')
    })

    test('should set pagination', () => {
      const store = useCustomersStore.getState()
      
      store.setCurrentPage(3)
      expect(useCustomersStore.getState().currentPage).toBe(3)
      
      store.setPageSize(50)
      expect(useCustomersStore.getState().pageSize).toBe(50)
    })
  })

  describe('Tier Groups', () => {
    test('should update tier groups correctly', () => {
      const customers: Customer[] = [
        { ...mockCustomer, _id: 'customer-1', loyaltyPoints: 15000 }, // VIP
        { ...mockCustomer, _id: 'customer-2', loyaltyPoints: 7000 },  // Gold
        { ...mockCustomer, _id: 'customer-3', loyaltyPoints: 3000 },  // Silver
        { ...mockCustomer, _id: 'customer-4', loyaltyPoints: 800 },   // Bronze
        { ...mockCustomer, _id: 'customer-5', loyaltyPoints: 100 },   // Regular
        { ...mockCustomer, _id: 'customer-6', loyaltyPoints: 6000 }   // Gold
      ]
      
      useCustomersStore.setState({ customers })
      
      const store = useCustomersStore.getState()
      store.updateTierGroups()
      
      const updatedStore = useCustomersStore.getState()
      expect(updatedStore.vipCustomers).toHaveLength(1)
      expect(updatedStore.goldCustomers).toHaveLength(2)
      expect(updatedStore.silverCustomers).toHaveLength(1)
      expect(updatedStore.bronzeCustomers).toHaveLength(1)
      expect(updatedStore.regularCustomers).toHaveLength(1)
    })
  })

  describe('Utility Methods', () => {
    beforeEach(() => {
      // Set up test data
      const customers: Customer[] = [
        { ...mockCustomer, _id: 'customer-1', preferredBranch: 'branch-1', isActive: true, loyaltyPoints: 7000, lastOrderDate: '2024-01-01' },
        { ...mockCustomer, _id: 'customer-2', preferredBranch: 'branch-1', isActive: false, loyaltyPoints: 3000, lastOrderDate: '2023-01-01' },
        { ...mockCustomer, _id: 'customer-3', preferredBranch: 'branch-2', isActive: true, loyaltyPoints: 800, lastOrderDate: '2024-01-15' },
        { ...mockCustomer, _id: 'customer-4', preferredBranch: 'branch-2', isActive: true, loyaltyPoints: 100, lastOrderDate: '2023-06-01' }
      ]
      
      useCustomersStore.setState({ customers })
    })

    test('should get customers by branch', () => {
      const store = useCustomersStore.getState()
      
      const branch1Customers = store.getCustomersByBranch('branch-1')
      expect(branch1Customers).toHaveLength(2)
      expect(branch1Customers.every(c => c.preferredBranch === 'branch-1')).toBe(true)
      
      const branch2Customers = store.getCustomersByBranch('branch-2')
      expect(branch2Customers).toHaveLength(2)
      expect(branch2Customers.every(c => c.preferredBranch === 'branch-2')).toBe(true)
    })

    test('should get customers by tier', () => {
      const store = useCustomersStore.getState()
      
      const goldCustomers = store.getCustomersByTier('Gold')
      expect(goldCustomers).toHaveLength(1)
      expect(goldCustomers[0].loyaltyPoints).toBe(7000)
      
      const silverCustomers = store.getCustomersByTier('Silver')
      expect(silverCustomers).toHaveLength(1)
      expect(silverCustomers[0].loyaltyPoints).toBe(3000)
    })

    test('should get active customers', () => {
      const store = useCustomersStore.getState()
      
      const activeCustomers = store.getActiveCustomers()
      expect(activeCustomers).toHaveLength(3)
      expect(activeCustomers.every(c => c.isActive)).toBe(true)
    })

    test('should get inactive customers', () => {
      const store = useCustomersStore.getState()
      
      const inactiveCustomers = store.getInactiveCustomers(30) // 30 days
      expect(inactiveCustomers.length).toBeGreaterThan(0)
    })

    test('should get available tiers', () => {
      const store = useCustomersStore.getState()
      
      const tiers = store.getAvailableTiers()
      expect(tiers).toHaveLength(5)
      expect(tiers).toContain('VIP')
      expect(tiers).toContain('Gold')
      expect(tiers).toContain('Silver')
      expect(tiers).toContain('Bronze')
      expect(tiers).toContain('Regular')
    })

    test('should calculate customer value', () => {
      const store = useCustomersStore.getState()
      
      const customerValue = store.calculateCustomerValue(mockCustomer)
      expect(customerValue).toBe(500000) // totalSpent
    })

    test('should calculate customer lifetime value', () => {
      const store = useCustomersStore.getState()
      
      const lifetimeValue = store.calculateCustomerLifetimeValue(mockCustomer)
      expect(lifetimeValue).toBe(750000) // totalSpent * 1.5
    })

    test('should get customer tier benefits', () => {
      const store = useCustomersStore.getState()
      
      const vipBenefits = store.getCustomerTierBenefits('VIP')
      expect(vipBenefits).toContain('Free shipping')
      expect(vipBenefits).toContain('20% discount')
      expect(vipBenefits).toContain('Priority support')
      expect(vipBenefits).toContain('Exclusive products')
      
      const regularBenefits = store.getCustomerTierBenefits('Regular')
      expect(regularBenefits).toContain('Standard benefits')
    })

    test('should get next tier requirement', () => {
      const store = useCustomersStore.getState()
      
      const silverCustomer = { ...mockCustomer, loyaltyPoints: 3000 }
      const nextTier = store.getNextTierRequirement(silverCustomer)
      expect(nextTier.nextTier).toBe('Gold')
      expect(nextTier.pointsNeeded).toBe(2000) // 5000 - 3000
      
      const vipCustomer = { ...mockCustomer, loyaltyPoints: 15000 }
      const vipNextTier = store.getNextTierRequirement(vipCustomer)
      expect(vipNextTier.nextTier).toBeNull()
      expect(vipNextTier.pointsNeeded).toBe(0)
    })

    test('should check if customer is active', () => {
      const store = useCustomersStore.getState()
      
      const activeCustomer = { ...mockCustomer, lastOrderDate: '2024-01-01' }
      const inactiveCustomer = { ...mockCustomer, lastOrderDate: '2023-01-01' }
      
      expect(store.isCustomerActive(activeCustomer, 90)).toBe(true)
      expect(store.isCustomerActive(inactiveCustomer, 90)).toBe(false)
    })

    test('should format customer name', () => {
      const store = useCustomersStore.getState()
      
      const formattedName = store.formatCustomerName(mockCustomer)
      expect(formattedName).toBe('John Doe')
      
      const customerWithoutLastName = { ...mockCustomer, lastName: '' }
      const formattedNameSingle = store.formatCustomerName(customerWithoutLastName)
      expect(formattedNameSingle).toBe('John')
    })

    test('should format customer address', () => {
      const store = useCustomersStore.getState()
      
      const formattedAddress = store.formatCustomerAddress(mockCustomer)
      expect(formattedAddress).toBe('123 Main St, Lilongwe, Central')
      
      const customerWithoutAddress = { ...mockCustomer, address: undefined }
      const formattedEmptyAddress = store.formatCustomerAddress(customerWithoutAddress)
      expect(formattedEmptyAddress).toBe('')
    })
  })
})
