// stores/__tests__/ordersStore.basic.test.ts - Basic orders store tests

import { useOrdersStore } from '../ordersStore'
import type { Order, OrderStatus, PaymentStatus, PaymentMethod, OrderItem } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  orderService: {
    getOrderStatuses: jest.fn(() => ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled', 'Returned']),
    getPaymentStatuses: jest.fn(() => ['Pending', 'Paid', 'Failed', 'Refunded', 'Partially Refunded']),
    getPaymentMethods: jest.fn(() => ['Cash', 'Card', 'Mobile Money', 'Bank Transfer', 'Credit']),
    validateOrderData: jest.fn(() => ({ isValid: true, errors: [] })),
    calculateOrderTotals: jest.fn((items: OrderItem[], taxRate = 0.18, shippingCost = 0, discountAmount = 0) => {
      const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0)
      const tax = subtotal * taxRate
      const total = subtotal + tax + shippingCost - discountAmount
      return { subtotal, tax, shipping: shippingCost, discount: discountAmount, total: Math.max(0, total) }
    }),
    canCancelOrder: jest.fn((order: Order) => ['Pending', 'Processing'].includes(order.status)),
    canReturnOrder: jest.fn((order: Order) => order.status === 'Delivered' && order.paymentStatus === 'Paid'),
    getOrderStatusColor: jest.fn((status: OrderStatus) => {
      const colors: Record<OrderStatus, string> = {
        'Pending': 'yellow', 'Processing': 'blue', 'Shipped': 'purple',
        'Delivered': 'green', 'Cancelled': 'red', 'Returned': 'orange'
      }
      return colors[status] || 'gray'
    }),
    getPaymentStatusColor: jest.fn((status: PaymentStatus) => {
      const colors: Record<PaymentStatus, string> = {
        'Pending': 'yellow', 'Paid': 'green', 'Failed': 'red',
        'Refunded': 'orange', 'Partially Refunded': 'orange'
      }
      return colors[status] || 'gray'
    }),
    formatOrderTotal: jest.fn((total: number) => `MWK ${total.toLocaleString()}`),
    formatOrderNumber: jest.fn((orderNumber: string) => orderNumber.toUpperCase()),
    getOrders: jest.fn(),
    getOrderById: jest.fn(),
    createOrder: jest.fn(),
    updateOrder: jest.fn(),
    cancelOrder: jest.fn(),
    updateOrderStatus: jest.fn(),
    updatePaymentStatus: jest.fn(),
    addTrackingNumber: jest.fn(),
    getOrdersByCustomer: jest.fn(),
    getOrdersByBranch: jest.fn(),
    getOrdersByStatus: jest.fn(),
    getRecentOrders: jest.fn(),
    searchOrders: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Mock order data
const mockOrderItem: OrderItem = {
  id: 'item-1',
  productId: 'product-123',
  productName: 'Test Laptop',
  sku: 'LAP-TEST-001',
  price: 500000,
  quantity: 2,
  subtotal: 1000000
}

const mockOrder: Order = {
  _id: 'order-123',
  orderNumber: 'ORD-2024-001',
  customerId: 'customer-123',
  customerName: 'John Doe',
  branchId: 'branch-123',
  branchName: 'Test Branch',
  items: [mockOrderItem],
  subtotal: 1000000,
  tax: 180000,
  shipping: 5000,
  discount: 0,
  total: 1185000,
  status: 'Pending',
  paymentStatus: 'Pending',
  paymentMethod: 'Card',
  shippingAddress: {
    street: '123 Main St',
    city: 'Lilongwe',
    region: 'Central',
    country: 'Malawi',
    postalCode: '12345'
  },
  billingAddress: {
    street: '123 Main St',
    city: 'Lilongwe',
    region: 'Central',
    country: 'Malawi',
    postalCode: '12345'
  },
  notes: 'Test order',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('OrdersStore - Basic Tests', () => {
  beforeEach(() => {
    // Reset store state before each test
    useOrdersStore.setState({
      orders: [],
      selectedOrder: null,
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 0,
      totalOrders: 0,
      pageSize: 20,
      filters: {},
      searchQuery: '',
      pendingOrders: [],
      processingOrders: [],
      shippedOrders: [],
      deliveredOrders: [],
      cancelledOrders: [],
      recentOrders: []
    })
  })

  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const store = useOrdersStore.getState()
      
      expect(store.orders).toEqual([])
      expect(store.selectedOrder).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.currentPage).toBe(1)
      expect(store.totalPages).toBe(0)
      expect(store.totalOrders).toBe(0)
      expect(store.pageSize).toBe(20)
      expect(store.filters).toEqual({})
      expect(store.searchQuery).toBe('')
      expect(store.pendingOrders).toEqual([])
      expect(store.processingOrders).toEqual([])
      expect(store.shippedOrders).toEqual([])
      expect(store.deliveredOrders).toEqual([])
      expect(store.cancelledOrders).toEqual([])
      expect(store.recentOrders).toEqual([])
    })

    test('should have all required methods', () => {
      const store = useOrdersStore.getState()
      
      // CRUD methods
      expect(typeof store.fetchOrders).toBe('function')
      expect(typeof store.fetchOrderById).toBe('function')
      expect(typeof store.createOrder).toBe('function')
      expect(typeof store.updateOrder).toBe('function')
      expect(typeof store.cancelOrder).toBe('function')
      
      // Status management methods
      expect(typeof store.updateOrderStatus).toBe('function')
      expect(typeof store.updatePaymentStatus).toBe('function')
      expect(typeof store.addTrackingNumber).toBe('function')
      
      // Query methods
      expect(typeof store.fetchOrdersByCustomer).toBe('function')
      expect(typeof store.fetchOrdersByBranch).toBe('function')
      expect(typeof store.fetchOrdersByStatus).toBe('function')
      expect(typeof store.fetchRecentOrders).toBe('function')
      
      // Search and filtering methods
      expect(typeof store.searchOrders).toBe('function')
      expect(typeof store.setFilters).toBe('function')
      expect(typeof store.clearFilters).toBe('function')
      expect(typeof store.setSearchQuery).toBe('function')
      
      // Pagination methods
      expect(typeof store.setCurrentPage).toBe('function')
      expect(typeof store.setPageSize).toBe('function')
      
      // UI state methods
      expect(typeof store.setSelectedOrder).toBe('function')
      expect(typeof store.clearError).toBe('function')
      expect(typeof store.setLoading).toBe('function')
      
      // Utility methods
      expect(typeof store.getOrdersByCustomer).toBe('function')
      expect(typeof store.getOrdersByBranch).toBe('function')
      expect(typeof store.getOrdersByStatus).toBe('function')
      expect(typeof store.getOrdersByPaymentStatus).toBe('function')
      expect(typeof store.getAvailableStatuses).toBe('function')
      expect(typeof store.getAvailablePaymentStatuses).toBe('function')
      expect(typeof store.getAvailablePaymentMethods).toBe('function')
      expect(typeof store.calculateOrderTotals).toBe('function')
      expect(typeof store.canCancelOrder).toBe('function')
      expect(typeof store.canReturnOrder).toBe('function')
      expect(typeof store.getOrderStatusColor).toBe('function')
      expect(typeof store.getPaymentStatusColor).toBe('function')
      expect(typeof store.formatOrderTotal).toBe('function')
      expect(typeof store.formatOrderNumber).toBe('function')
      expect(typeof store.updateStatusGroups).toBe('function')
    })
  })

  describe('State Management', () => {
    test('should set selected order', () => {
      const store = useOrdersStore.getState()
      
      store.setSelectedOrder(mockOrder)
      expect(useOrdersStore.getState().selectedOrder).toEqual(mockOrder)
      
      store.setSelectedOrder(null)
      expect(useOrdersStore.getState().selectedOrder).toBeNull()
    })

    test('should clear error', () => {
      useOrdersStore.setState({ error: 'Test error' })
      
      const store = useOrdersStore.getState()
      expect(store.error).toBe('Test error')
      
      store.clearError()
      expect(useOrdersStore.getState().error).toBeNull()
    })

    test('should set loading state', () => {
      const store = useOrdersStore.getState()
      
      store.setLoading(true)
      expect(useOrdersStore.getState().isLoading).toBe(true)
      
      store.setLoading(false)
      expect(useOrdersStore.getState().isLoading).toBe(false)
    })

    test('should set search query', () => {
      const store = useOrdersStore.getState()
      
      store.setSearchQuery('ORD-2024')
      expect(useOrdersStore.getState().searchQuery).toBe('ORD-2024')
      
      store.setSearchQuery('')
      expect(useOrdersStore.getState().searchQuery).toBe('')
    })

    test('should set filters', () => {
      const store = useOrdersStore.getState()
      
      store.setFilters({ status: 'Pending', paymentStatus: 'Paid' })
      expect(useOrdersStore.getState().filters).toEqual({
        status: 'Pending',
        paymentStatus: 'Paid'
      })
      
      store.setFilters({ customerId: 'customer-123' })
      expect(useOrdersStore.getState().filters).toEqual({
        status: 'Pending',
        paymentStatus: 'Paid',
        customerId: 'customer-123'
      })
    })

    test('should clear filters', () => {
      useOrdersStore.setState({
        filters: { status: 'Pending', paymentStatus: 'Paid' },
        searchQuery: 'test'
      })
      
      const store = useOrdersStore.getState()
      store.clearFilters()
      
      const updatedStore = useOrdersStore.getState()
      expect(updatedStore.filters).toEqual({})
      expect(updatedStore.searchQuery).toBe('')
    })

    test('should set pagination', () => {
      const store = useOrdersStore.getState()
      
      store.setCurrentPage(3)
      expect(useOrdersStore.getState().currentPage).toBe(3)
      
      store.setPageSize(50)
      expect(useOrdersStore.getState().pageSize).toBe(50)
    })
  })

  describe('Status Groups', () => {
    test('should update status groups correctly', () => {
      const orders: Order[] = [
        { ...mockOrder, _id: 'order-1', status: 'Pending' },
        { ...mockOrder, _id: 'order-2', status: 'Processing' },
        { ...mockOrder, _id: 'order-3', status: 'Shipped' },
        { ...mockOrder, _id: 'order-4', status: 'Delivered' },
        { ...mockOrder, _id: 'order-5', status: 'Cancelled' },
        { ...mockOrder, _id: 'order-6', status: 'Pending' }
      ]
      
      useOrdersStore.setState({ orders })
      
      const store = useOrdersStore.getState()
      store.updateStatusGroups()
      
      const updatedStore = useOrdersStore.getState()
      expect(updatedStore.pendingOrders).toHaveLength(2)
      expect(updatedStore.processingOrders).toHaveLength(1)
      expect(updatedStore.shippedOrders).toHaveLength(1)
      expect(updatedStore.deliveredOrders).toHaveLength(1)
      expect(updatedStore.cancelledOrders).toHaveLength(1)
    })
  })

  describe('Utility Methods', () => {
    beforeEach(() => {
      // Set up test data
      const orders: Order[] = [
        { ...mockOrder, _id: 'order-1', customerId: 'customer-1', branchId: 'branch-1', status: 'Pending', paymentStatus: 'Pending' },
        { ...mockOrder, _id: 'order-2', customerId: 'customer-1', branchId: 'branch-2', status: 'Processing', paymentStatus: 'Paid' },
        { ...mockOrder, _id: 'order-3', customerId: 'customer-2', branchId: 'branch-1', status: 'Delivered', paymentStatus: 'Paid' },
        { ...mockOrder, _id: 'order-4', customerId: 'customer-2', branchId: 'branch-2', status: 'Cancelled', paymentStatus: 'Failed' }
      ]
      
      useOrdersStore.setState({ orders })
    })

    test('should get orders by customer', () => {
      const store = useOrdersStore.getState()
      
      const customer1Orders = store.getOrdersByCustomer('customer-1')
      expect(customer1Orders).toHaveLength(2)
      expect(customer1Orders.every(o => o.customerId === 'customer-1')).toBe(true)
      
      const customer2Orders = store.getOrdersByCustomer('customer-2')
      expect(customer2Orders).toHaveLength(2)
      expect(customer2Orders.every(o => o.customerId === 'customer-2')).toBe(true)
    })

    test('should get orders by branch', () => {
      const store = useOrdersStore.getState()
      
      const branch1Orders = store.getOrdersByBranch('branch-1')
      expect(branch1Orders).toHaveLength(2)
      expect(branch1Orders.every(o => o.branchId === 'branch-1')).toBe(true)
      
      const branch2Orders = store.getOrdersByBranch('branch-2')
      expect(branch2Orders).toHaveLength(2)
      expect(branch2Orders.every(o => o.branchId === 'branch-2')).toBe(true)
    })

    test('should get orders by status', () => {
      const store = useOrdersStore.getState()
      
      const pendingOrders = store.getOrdersByStatus('Pending')
      expect(pendingOrders).toHaveLength(1)
      expect(pendingOrders[0].status).toBe('Pending')
      
      const deliveredOrders = store.getOrdersByStatus('Delivered')
      expect(deliveredOrders).toHaveLength(1)
      expect(deliveredOrders[0].status).toBe('Delivered')
    })

    test('should get orders by payment status', () => {
      const store = useOrdersStore.getState()
      
      const paidOrders = store.getOrdersByPaymentStatus('Paid')
      expect(paidOrders).toHaveLength(2)
      expect(paidOrders.every(o => o.paymentStatus === 'Paid')).toBe(true)
      
      const pendingOrders = store.getOrdersByPaymentStatus('Pending')
      expect(pendingOrders).toHaveLength(1)
      expect(pendingOrders[0].paymentStatus).toBe('Pending')
    })

    test('should get available statuses', () => {
      const store = useOrdersStore.getState()
      
      const statuses = store.getAvailableStatuses()
      expect(statuses).toHaveLength(6)
      expect(statuses).toContain('Pending')
      expect(statuses).toContain('Processing')
      expect(statuses).toContain('Delivered')
    })

    test('should get available payment statuses', () => {
      const store = useOrdersStore.getState()
      
      const paymentStatuses = store.getAvailablePaymentStatuses()
      expect(paymentStatuses).toHaveLength(5)
      expect(paymentStatuses).toContain('Pending')
      expect(paymentStatuses).toContain('Paid')
      expect(paymentStatuses).toContain('Failed')
    })

    test('should get available payment methods', () => {
      const store = useOrdersStore.getState()
      
      const paymentMethods = store.getAvailablePaymentMethods()
      expect(paymentMethods).toHaveLength(5)
      expect(paymentMethods).toContain('Cash')
      expect(paymentMethods).toContain('Card')
      expect(paymentMethods).toContain('Mobile Money')
    })

    test('should calculate order totals', () => {
      const store = useOrdersStore.getState()
      
      const items: OrderItem[] = [
        { ...mockOrderItem, subtotal: 500000 },
        { ...mockOrderItem, id: 'item-2', subtotal: 300000 }
      ]
      
      const totals = store.calculateOrderTotals(items, 0.18, 5000, 10000)
      expect(totals.subtotal).toBe(800000)
      expect(totals.tax).toBe(144000) // 18% of 800000
      expect(totals.shipping).toBe(5000)
      expect(totals.discount).toBe(10000)
      expect(totals.total).toBe(939000) // 800000 + 144000 + 5000 - 10000
    })

    test('should check if order can be cancelled', () => {
      const store = useOrdersStore.getState()
      
      const pendingOrder = { ...mockOrder, status: 'Pending' as OrderStatus }
      const processingOrder = { ...mockOrder, status: 'Processing' as OrderStatus }
      const shippedOrder = { ...mockOrder, status: 'Shipped' as OrderStatus }
      
      expect(store.canCancelOrder(pendingOrder)).toBe(true)
      expect(store.canCancelOrder(processingOrder)).toBe(true)
      expect(store.canCancelOrder(shippedOrder)).toBe(false)
    })

    test('should check if order can be returned', () => {
      const store = useOrdersStore.getState()
      
      const deliveredPaidOrder = { ...mockOrder, status: 'Delivered' as OrderStatus, paymentStatus: 'Paid' as PaymentStatus }
      const deliveredPendingOrder = { ...mockOrder, status: 'Delivered' as OrderStatus, paymentStatus: 'Pending' as PaymentStatus }
      const shippedPaidOrder = { ...mockOrder, status: 'Shipped' as OrderStatus, paymentStatus: 'Paid' as PaymentStatus }
      
      expect(store.canReturnOrder(deliveredPaidOrder)).toBe(true)
      expect(store.canReturnOrder(deliveredPendingOrder)).toBe(false)
      expect(store.canReturnOrder(shippedPaidOrder)).toBe(false)
    })

    test('should get status colors', () => {
      const store = useOrdersStore.getState()
      
      expect(store.getOrderStatusColor('Pending')).toBe('yellow')
      expect(store.getOrderStatusColor('Processing')).toBe('blue')
      expect(store.getOrderStatusColor('Delivered')).toBe('green')
      
      expect(store.getPaymentStatusColor('Pending')).toBe('yellow')
      expect(store.getPaymentStatusColor('Paid')).toBe('green')
      expect(store.getPaymentStatusColor('Failed')).toBe('red')
    })

    test('should format order data', () => {
      const store = useOrdersStore.getState()
      
      const formattedTotal = store.formatOrderTotal(1185000)
      expect(formattedTotal).toBe('MWK 1,185,000')
      
      const formattedOrderNumber = store.formatOrderNumber('ord-2024-001')
      expect(formattedOrderNumber).toBe('ORD-2024-001')
    })
  })
})
