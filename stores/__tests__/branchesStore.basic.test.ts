// stores/__tests__/branchesStore.basic.test.ts - Basic branches store tests

import { useBranchesStore } from '../branchesStore'
import type { Branch, BranchStatus } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  branchService: {
    getBranchStatuses: jest.fn(() => ['Active', 'Inactive', 'Maintenance']),
    getAvailableRegions: jest.fn(() => ['Northern', 'Central', 'Southern']),
    validateBranchData: jest.fn(() => ({ isValid: true, errors: [] })),
    calculateDistance: jest.fn((coord1: any, coord2: any) => {
      const lat1 = coord1.latitude
      const lon1 = coord1.longitude
      const lat2 = coord2.latitude
      const lon2 = coord2.longitude
      
      // Simple distance calculation for testing
      const deltaLat = lat2 - lat1
      const deltaLon = lon2 - lon1
      return Math.sqrt(deltaLat * deltaLat + deltaLon * deltaLon) * 111 // Rough km conversion
    }),
    findNearestBranches: jest.fn((branches: Branch[], coordinates: any, limit = 5) => {
      return branches
        .map(branch => ({
          ...branch,
          distance: Math.sqrt(
            Math.pow(branch.coordinates.latitude - coordinates.latitude, 2) +
            Math.pow(branch.coordinates.longitude - coordinates.longitude, 2)
          ) * 111
        }))
        .sort((a, b) => a.distance - b.distance)
        .slice(0, limit)
    }),
    getBranchOperatingHours: jest.fn((branch: Branch, date?: Date) => {
      const dayOfWeek = date ? date.getDay() : new Date().getDay()
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
      
      if (isWeekend) {
        return { open: '09:00', close: '17:00', isOpen: true }
      } else {
        return { open: '08:00', close: '18:00', isOpen: true }
      }
    }),
    isBranchOpen: jest.fn((branch: Branch, date?: Date) => {
      return branch.status === 'Active'
    }),
    formatBranchAddress: jest.fn((branch: Branch) => {
      const { street, city, region, country } = branch.address
      return [street, city, region, country].filter(Boolean).join(', ')
    }),
    formatBranchContact: jest.fn((branch: Branch) => {
      return `${branch.phone} | ${branch.email}`
    }),
    getBranches: jest.fn(),
    getBranchById: jest.fn(),
    createBranch: jest.fn(),
    updateBranch: jest.fn(),
    deleteBranch: jest.fn(),
    updateBranchStatus: jest.fn(),
    deactivateBranch: jest.fn(),
    setMaintenanceMode: jest.fn(),
    getTopPerformingBranches: jest.fn(),
    getRecentBranches: jest.fn(),
    getBranchPerformance: jest.fn(),
    getBranchAnalytics: jest.fn(),
    getBranchesByRegion: jest.fn(),
    getBranchesByStatus: jest.fn(),
    getBranchesByManager: jest.fn(),
    searchBranches: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Mock branch data
const mockBranch: Branch = {
  _id: 'branch-123',
  name: 'Main Branch',
  code: 'MB001',
  address: {
    street: '123 Main Street',
    city: 'Lilongwe',
    region: 'Central',
    country: 'Malawi',
    postalCode: '12345'
  },
  coordinates: {
    latitude: -13.9626,
    longitude: 33.7741
  },
  phone: '+265123456789',
  email: '<EMAIL>',
  managerId: 'manager-123',
  managerName: 'John Manager',
  status: 'Active',
  operatingHours: {
    monday: { open: '08:00', close: '18:00' },
    tuesday: { open: '08:00', close: '18:00' },
    wednesday: { open: '08:00', close: '18:00' },
    thursday: { open: '08:00', close: '18:00' },
    friday: { open: '08:00', close: '18:00' },
    saturday: { open: '09:00', close: '17:00' },
    sunday: { open: '09:00', close: '17:00' }
  },
  services: ['Sales', 'Repairs', 'Support'],
  capacity: 50,
  currentStaff: 12,
  isMainBranch: true,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('BranchesStore - Basic Tests', () => {
  beforeEach(() => {
    // Reset store state before each test
    useBranchesStore.setState({
      branches: [],
      selectedBranch: null,
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 0,
      totalBranches: 0,
      pageSize: 20,
      filters: {},
      searchQuery: '',
      activeBranches: [],
      inactiveBranches: [],
      maintenanceBranches: [],
      topPerformingBranches: [],
      recentBranches: [],
      branchPerformanceData: {}
    })
  })

  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const store = useBranchesStore.getState()
      
      expect(store.branches).toEqual([])
      expect(store.selectedBranch).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.currentPage).toBe(1)
      expect(store.totalPages).toBe(0)
      expect(store.totalBranches).toBe(0)
      expect(store.pageSize).toBe(20)
      expect(store.filters).toEqual({})
      expect(store.searchQuery).toBe('')
      expect(store.activeBranches).toEqual([])
      expect(store.inactiveBranches).toEqual([])
      expect(store.maintenanceBranches).toEqual([])
      expect(store.topPerformingBranches).toEqual([])
      expect(store.recentBranches).toEqual([])
      expect(store.branchPerformanceData).toEqual({})
    })

    test('should have all required methods', () => {
      const store = useBranchesStore.getState()
      
      // CRUD methods
      expect(typeof store.fetchBranches).toBe('function')
      expect(typeof store.fetchBranchById).toBe('function')
      expect(typeof store.createBranch).toBe('function')
      expect(typeof store.updateBranch).toBe('function')
      expect(typeof store.deleteBranch).toBe('function')
      
      // Status management methods
      expect(typeof store.updateBranchStatus).toBe('function')
      expect(typeof store.activateBranch).toBe('function')
      expect(typeof store.deactivateBranch).toBe('function')
      expect(typeof store.setMaintenanceMode).toBe('function')
      
      // Analytics methods
      expect(typeof store.fetchTopPerformingBranches).toBe('function')
      expect(typeof store.fetchRecentBranches).toBe('function')
      expect(typeof store.fetchBranchPerformance).toBe('function')
      expect(typeof store.fetchBranchAnalytics).toBe('function')
      
      // Query methods
      expect(typeof store.fetchBranchesByRegion).toBe('function')
      expect(typeof store.fetchBranchesByStatus).toBe('function')
      expect(typeof store.fetchBranchesByManager).toBe('function')
      
      // Search and filtering methods
      expect(typeof store.searchBranches).toBe('function')
      expect(typeof store.setFilters).toBe('function')
      expect(typeof store.clearFilters).toBe('function')
      expect(typeof store.setSearchQuery).toBe('function')
      
      // Pagination methods
      expect(typeof store.setCurrentPage).toBe('function')
      expect(typeof store.setPageSize).toBe('function')
      
      // UI state methods
      expect(typeof store.setSelectedBranch).toBe('function')
      expect(typeof store.clearError).toBe('function')
      expect(typeof store.setLoading).toBe('function')
      
      // Utility methods
      expect(typeof store.getBranchesByRegion).toBe('function')
      expect(typeof store.getBranchesByStatus).toBe('function')
      expect(typeof store.getActiveBranches).toBe('function')
      expect(typeof store.getInactiveBranches).toBe('function')
      expect(typeof store.getAvailableStatuses).toBe('function')
      expect(typeof store.getAvailableRegions).toBe('function')
      expect(typeof store.calculateBranchDistance).toBe('function')
      expect(typeof store.findNearestBranches).toBe('function')
      expect(typeof store.getBranchOperatingHours).toBe('function')
      expect(typeof store.isBranchOpen).toBe('function')
      expect(typeof store.formatBranchAddress).toBe('function')
      expect(typeof store.formatBranchContact).toBe('function')
      expect(typeof store.updateStatusGroups).toBe('function')
    })
  })

  describe('State Management', () => {
    test('should set selected branch', () => {
      const store = useBranchesStore.getState()
      
      store.setSelectedBranch(mockBranch)
      expect(useBranchesStore.getState().selectedBranch).toEqual(mockBranch)
      
      store.setSelectedBranch(null)
      expect(useBranchesStore.getState().selectedBranch).toBeNull()
    })

    test('should clear error', () => {
      useBranchesStore.setState({ error: 'Test error' })
      
      const store = useBranchesStore.getState()
      expect(store.error).toBe('Test error')
      
      store.clearError()
      expect(useBranchesStore.getState().error).toBeNull()
    })

    test('should set loading state', () => {
      const store = useBranchesStore.getState()
      
      store.setLoading(true)
      expect(useBranchesStore.getState().isLoading).toBe(true)
      
      store.setLoading(false)
      expect(useBranchesStore.getState().isLoading).toBe(false)
    })

    test('should set search query', () => {
      const store = useBranchesStore.getState()
      
      store.setSearchQuery('main branch')
      expect(useBranchesStore.getState().searchQuery).toBe('main branch')
      
      store.setSearchQuery('')
      expect(useBranchesStore.getState().searchQuery).toBe('')
    })

    test('should set filters', () => {
      const store = useBranchesStore.getState()
      
      store.setFilters({ status: 'Active', region: 'Central' })
      expect(useBranchesStore.getState().filters).toEqual({
        status: 'Active',
        region: 'Central'
      })
      
      store.setFilters({ managerId: 'manager-123' })
      expect(useBranchesStore.getState().filters).toEqual({
        status: 'Active',
        region: 'Central',
        managerId: 'manager-123'
      })
    })

    test('should clear filters', () => {
      useBranchesStore.setState({
        filters: { status: 'Active', region: 'Central' },
        searchQuery: 'test'
      })
      
      const store = useBranchesStore.getState()
      store.clearFilters()
      
      const updatedStore = useBranchesStore.getState()
      expect(updatedStore.filters).toEqual({})
      expect(updatedStore.searchQuery).toBe('')
    })

    test('should set pagination', () => {
      const store = useBranchesStore.getState()
      
      store.setCurrentPage(3)
      expect(useBranchesStore.getState().currentPage).toBe(3)
      
      store.setPageSize(50)
      expect(useBranchesStore.getState().pageSize).toBe(50)
    })
  })

  describe('Status Groups', () => {
    test('should update status groups correctly', () => {
      const branches: Branch[] = [
        { ...mockBranch, _id: 'branch-1', status: 'Active' },
        { ...mockBranch, _id: 'branch-2', status: 'Inactive' },
        { ...mockBranch, _id: 'branch-3', status: 'Maintenance' },
        { ...mockBranch, _id: 'branch-4', status: 'Active' },
        { ...mockBranch, _id: 'branch-5', status: 'Inactive' }
      ]
      
      useBranchesStore.setState({ branches })
      
      const store = useBranchesStore.getState()
      store.updateStatusGroups()
      
      const updatedStore = useBranchesStore.getState()
      expect(updatedStore.activeBranches).toHaveLength(2)
      expect(updatedStore.inactiveBranches).toHaveLength(2)
      expect(updatedStore.maintenanceBranches).toHaveLength(1)
    })
  })

  describe('Utility Methods', () => {
    beforeEach(() => {
      // Set up test data
      const branches: Branch[] = [
        { ...mockBranch, _id: 'branch-1', address: { ...mockBranch.address, region: 'Northern' }, status: 'Active' },
        { ...mockBranch, _id: 'branch-2', address: { ...mockBranch.address, region: 'Central' }, status: 'Inactive' },
        { ...mockBranch, _id: 'branch-3', address: { ...mockBranch.address, region: 'Southern' }, status: 'Active' },
        { ...mockBranch, _id: 'branch-4', address: { ...mockBranch.address, region: 'Central' }, status: 'Maintenance' }
      ]
      
      useBranchesStore.setState({ branches })
    })

    test('should get branches by region', () => {
      const store = useBranchesStore.getState()
      
      const centralBranches = store.getBranchesByRegion('Central')
      expect(centralBranches).toHaveLength(2)
      expect(centralBranches.every(b => b.address.region === 'Central')).toBe(true)
      
      const northernBranches = store.getBranchesByRegion('Northern')
      expect(northernBranches).toHaveLength(1)
      expect(northernBranches[0].address.region).toBe('Northern')
    })

    test('should get branches by status', () => {
      const store = useBranchesStore.getState()
      
      const activeBranches = store.getBranchesByStatus('Active')
      expect(activeBranches).toHaveLength(2)
      expect(activeBranches.every(b => b.status === 'Active')).toBe(true)
      
      const inactiveBranches = store.getBranchesByStatus('Inactive')
      expect(inactiveBranches).toHaveLength(1)
      expect(inactiveBranches[0].status).toBe('Inactive')
    })

    test('should get active branches', () => {
      const store = useBranchesStore.getState()
      
      const activeBranches = store.getActiveBranches()
      expect(activeBranches).toHaveLength(2)
      expect(activeBranches.every(b => b.status === 'Active')).toBe(true)
    })

    test('should get inactive branches', () => {
      const store = useBranchesStore.getState()
      
      const inactiveBranches = store.getInactiveBranches()
      expect(inactiveBranches).toHaveLength(1)
      expect(inactiveBranches.every(b => b.status === 'Inactive')).toBe(true)
    })

    test('should get available statuses', () => {
      const store = useBranchesStore.getState()
      
      const statuses = store.getAvailableStatuses()
      expect(statuses).toHaveLength(3)
      expect(statuses).toContain('Active')
      expect(statuses).toContain('Inactive')
      expect(statuses).toContain('Maintenance')
    })

    test('should get available regions', () => {
      const store = useBranchesStore.getState()
      
      const regions = store.getAvailableRegions()
      expect(regions).toHaveLength(3)
      expect(regions).toContain('Northern')
      expect(regions).toContain('Central')
      expect(regions).toContain('Southern')
    })

    test('should calculate branch distance', () => {
      const store = useBranchesStore.getState()
      
      const branch1 = { ...mockBranch, coordinates: { latitude: -13.9626, longitude: 33.7741 } }
      const branch2 = { ...mockBranch, coordinates: { latitude: -15.7861, longitude: 35.0058 } }
      
      const distance = store.calculateBranchDistance(branch1, branch2)
      expect(distance).toBeGreaterThan(0)
      expect(typeof distance).toBe('number')
    })

    test('should find nearest branches', () => {
      const store = useBranchesStore.getState()
      
      const userLocation = { latitude: -13.9626, longitude: 33.7741 }
      const nearestBranches = store.findNearestBranches(userLocation.latitude, userLocation.longitude, 3)
      
      expect(nearestBranches).toHaveLength(3)
      expect(nearestBranches[0]).toHaveProperty('distance')
    })

    test('should get branch operating hours', () => {
      const store = useBranchesStore.getState()
      
      const operatingHours = store.getBranchOperatingHours(mockBranch)
      expect(operatingHours).toHaveProperty('open')
      expect(operatingHours).toHaveProperty('close')
      expect(operatingHours).toHaveProperty('isOpen')
      expect(typeof operatingHours.open).toBe('string')
      expect(typeof operatingHours.close).toBe('string')
      expect(typeof operatingHours.isOpen).toBe('boolean')
    })

    test('should check if branch is open', () => {
      const store = useBranchesStore.getState()
      
      const activeBranch = { ...mockBranch, status: 'Active' as BranchStatus }
      const inactiveBranch = { ...mockBranch, status: 'Inactive' as BranchStatus }
      
      expect(store.isBranchOpen(activeBranch)).toBe(true)
      expect(store.isBranchOpen(inactiveBranch)).toBe(false)
    })

    test('should format branch address', () => {
      const store = useBranchesStore.getState()
      
      const formattedAddress = store.formatBranchAddress(mockBranch)
      expect(formattedAddress).toBe('123 Main Street, Lilongwe, Central, Malawi')
    })

    test('should format branch contact', () => {
      const store = useBranchesStore.getState()
      
      const formattedContact = store.formatBranchContact(mockBranch)
      expect(formattedContact).toBe('+265123456789 | <EMAIL>')
    })
  })
})
