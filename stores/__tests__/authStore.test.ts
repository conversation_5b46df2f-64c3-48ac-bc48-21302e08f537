// stores/__tests__/authStore.test.ts - Comprehensive authentication store tests

import { renderHook, act } from '@testing-library/react'
import { useAuthStore } from '../authStore'
import type { User, UserRole, ApiResponse } from '@/types/frontend'

// Mock the entire services module
jest.mock('@/services/frontend', () => ({
  apiClient: {
    post: jest.fn(),
    put: jest.fn(),
    setAuthToken: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Import the mocked module
const { apiClient, handleServiceError } = require('@/services/frontend')
const mockApiClient = apiClient as jest.Mocked<typeof apiClient>
const mockHandleServiceError = handleServiceError as jest.MockedFunction<typeof handleServiceError>

// Mock user data (using AuthUser structure)
const mockUser = {
  id: 'user-123',
  username: 'testuser',
  email: '<EMAIL>',
  name: '<PERSON>',
  role: 'branch_manager' as const,
  branchId: 'branch-123',
  avatar: null
}

const mockToken = 'mock-jwt-token'

describe('AuthStore', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    
    // Reset store state
    useAuthStore.getState().logout()
  })

  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const { result } = renderHook(() => useAuthStore())
      
      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.userRole).toBeNull()
      expect(result.current.userBranch).toBeNull()
      expect(result.current.isAdmin).toBe(false)
      expect(result.current.isBranchManager).toBe(false)
    })
  })

  describe('Login', () => {
    test('should login successfully with valid credentials', async () => {
      const mockResponse = {
        success: true,
        user: mockUser,
        accessToken: mockToken,
        refreshToken: 'refresh-token'
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const loginResult = await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
        
        expect(loginResult.success).toBe(true)
      })

      expect(result.current.user).toEqual(mockUser)
      expect(result.current.token).toBe(mockToken)
      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
      expect(result.current.userRole).toBe('branch_manager')
      expect(result.current.userBranch).toBe('branch-123')
      expect(result.current.isBranchManager).toBe(true)
      expect(result.current.isAdmin).toBe(false)

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/login', {
        username: 'testuser',
        password: 'password123'
      })
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith(mockToken)
    })

    test('should handle login failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Invalid credentials'
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const loginResult = await result.current.login({
          username: 'testuser',
          password: 'wrongpassword'
        })
        
        expect(loginResult.success).toBe(false)
        expect(loginResult.error).toBe('Invalid credentials')
      })

      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Invalid credentials')
    })

    test('should handle network error during login', async () => {
      const networkError = new Error('Network error')
      mockApiClient.post.mockRejectedValueOnce(networkError)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const loginResult = await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
        
        expect(loginResult.success).toBe(false)
        expect(loginResult.error).toBe('Network error')
      })

      expect(result.current.user).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.error).toBe('Network error')
    })
  })

  describe('Register', () => {
    test('should register successfully with valid data', async () => {
      const mockResponse = {
        success: true,
        user: mockUser,
        accessToken: mockToken
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      const registerData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'branch_manager' as UserRole,
        branchId: 'branch-123'
      }

      await act(async () => {
        const registerResult = await result.current.register(registerData)
        expect(registerResult.success).toBe(true)
      })

      expect(result.current.user).toEqual(mockUser)
      expect(result.current.token).toBe(mockToken)
      expect(result.current.isAuthenticated).toBe(true)

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/register', {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'branch_manager',
        branchId: 'branch-123'
      })
    })

    test('should fail registration when passwords do not match', async () => {
      const { result } = renderHook(() => useAuthStore())

      const registerData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'differentpassword',
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'branch_manager' as UserRole,
        branchId: 'branch-123'
      }

      await act(async () => {
        const registerResult = await result.current.register(registerData)
        expect(registerResult.success).toBe(false)
        expect(registerResult.error).toBe('Passwords do not match')
      })

      expect(result.current.user).toBeNull()
      expect(result.current.error).toBe('Passwords do not match')
      expect(mockApiClient.post).not.toHaveBeenCalled()
    })
  })

  describe('Logout', () => {
    test('should logout and clear state', async () => {
      // First login
      const mockResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
      })

      // Verify logged in
      expect(result.current.isAuthenticated).toBe(true)

      // Now logout
      act(() => {
        result.current.logout()
      })

      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.error).toBeNull()
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith(null)
    })
  })

  describe('Utility Methods', () => {
    beforeEach(async () => {
      // Login first for utility method tests
      const mockResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
      })
    })

    test('hasPermission should work correctly', () => {
      const { result } = renderHook(() => useAuthStore())

      expect(result.current.hasPermission('branch_manager')).toBe(true)
      expect(result.current.hasPermission('overall_admin')).toBe(false)
    })

    test('canAccessBranch should work correctly', () => {
      const { result } = renderHook(() => useAuthStore())

      expect(result.current.canAccessBranch('branch-123')).toBe(true)
      expect(result.current.canAccessBranch('other-branch')).toBe(false)
    })

    test('getDisplayName should return correct name', () => {
      const { result } = renderHook(() => useAuthStore())

      expect(result.current.getDisplayName()).toBe('John Doe')
    })
  })

  describe('Admin User', () => {
    test('should handle admin user correctly', async () => {
      const adminUser: User = {
        ...mockUser,
        role: 'overall_admin',
        branchId: undefined,
        branchName: undefined
      }

      const mockResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: adminUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'admin',
          password: 'password123'
        })
      })

      expect(result.current.isAdmin).toBe(true)
      expect(result.current.isBranchManager).toBe(false)
      expect(result.current.hasPermission('overall_admin')).toBe(true)
      expect(result.current.hasPermission('branch_manager')).toBe(true)
      expect(result.current.canAccessBranch('any-branch')).toBe(true)
    })
  })

  describe('Token Refresh', () => {
    test('should refresh token successfully', async () => {
      // First login
      const loginResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      const refreshResponse: ApiResponse<{ user: User; token: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: 'new-token'
        }
      }

      mockApiClient.post
        .mockResolvedValueOnce(loginResponse)
        .mockResolvedValueOnce(refreshResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
      })

      await act(async () => {
        const refreshResult = await result.current.refreshToken()
        expect(refreshResult.success).toBe(true)
      })

      expect(result.current.token).toBe('new-token')
      expect(mockApiClient.setAuthToken).toHaveBeenCalledWith('new-token')
    })

    test('should logout on failed token refresh', async () => {
      // First login
      const loginResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      const refreshResponse: ApiResponse<never> = {
        success: false,
        error: 'Token expired'
      }

      mockApiClient.post
        .mockResolvedValueOnce(loginResponse)
        .mockResolvedValueOnce(refreshResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
      })

      expect(result.current.isAuthenticated).toBe(true)

      await act(async () => {
        const refreshResult = await result.current.refreshToken()
        expect(refreshResult.success).toBe(false)
      })

      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.user).toBeNull()
      expect(result.current.token).toBeNull()
    })
  })

  describe('Profile Update', () => {
    beforeEach(async () => {
      // Login first
      const mockResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
      })
    })

    test('should update profile successfully', async () => {
      const updatedUser: User = {
        ...mockUser,
        firstName: 'Jane',
        lastName: 'Smith',
        phone: '+265987654321'
      }

      const mockResponse: ApiResponse<User> = {
        success: true,
        data: updatedUser
      }

      mockApiClient.put.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const updateResult = await result.current.updateProfile({
          firstName: 'Jane',
          lastName: 'Smith',
          phone: '+265987654321'
        })

        expect(updateResult.success).toBe(true)
      })

      expect(result.current.user).toEqual(updatedUser)
      expect(result.current.getDisplayName()).toBe('Jane Smith')
    })

    test('should handle profile update failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Update failed'
      }

      mockApiClient.put.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const updateResult = await result.current.updateProfile({
          firstName: 'Jane'
        })

        expect(updateResult.success).toBe(false)
        expect(updateResult.error).toBe('Update failed')
      })

      expect(result.current.error).toBe('Update failed')
    })
  })

  describe('Password Change', () => {
    beforeEach(async () => {
      // Login first
      const mockResponse: ApiResponse<{ user: User; token: string; refreshToken: string }> = {
        success: true,
        data: {
          user: mockUser,
          token: mockToken,
          refreshToken: 'refresh-token'
        }
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        await result.current.login({
          username: 'testuser',
          password: 'password123'
        })
      })
    })

    test('should change password successfully', async () => {
      const mockResponse: ApiResponse<{ success: boolean }> = {
        success: true,
        data: { success: true }
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const changeResult = await result.current.changePassword('oldpassword', 'newpassword')
        expect(changeResult.success).toBe(true)
      })

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/change-password', {
        currentPassword: 'oldpassword',
        newPassword: 'newpassword'
      })
    })

    test('should handle password change failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Current password is incorrect'
      }

      mockApiClient.post.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useAuthStore())

      await act(async () => {
        const changeResult = await result.current.changePassword('wrongpassword', 'newpassword')
        expect(changeResult.success).toBe(false)
        expect(changeResult.error).toBe('Current password is incorrect')
      })

      expect(result.current.error).toBe('Current password is incorrect')
    })
  })

  describe('Error Handling', () => {
    test('should clear error', () => {
      const { result } = renderHook(() => useAuthStore())

      act(() => {
        result.current.setLoading(false)
        // Manually set error for testing
        useAuthStore.setState({ error: 'Test error' })
      })

      expect(result.current.error).toBe('Test error')

      act(() => {
        result.current.clearError()
      })

      expect(result.current.error).toBeNull()
    })

    test('should set loading state', () => {
      const { result } = renderHook(() => useAuthStore())

      act(() => {
        result.current.setLoading(true)
      })

      expect(result.current.isLoading).toBe(true)

      act(() => {
        result.current.setLoading(false)
      })

      expect(result.current.isLoading).toBe(false)
    })
  })

  describe('Unauthenticated State', () => {
    test('should handle operations when not authenticated', async () => {
      const { result } = renderHook(() => useAuthStore())

      const updateResult = await result.current.updateProfile({ name: 'Test User' })
      expect(updateResult.success).toBe(false)
      expect(updateResult.error).toBe('User not authenticated')

      const refreshResult = await result.current.refreshToken()
      expect(refreshResult.success).toBe(false)
      expect(refreshResult.error).toBe('No token available')

      expect(result.current.hasPermission('branch_manager')).toBe(false)
      expect(result.current.canAccessBranch('any-branch')).toBe(false)
      expect(result.current.getDisplayName()).toBe('Guest')
    })
  })
})
