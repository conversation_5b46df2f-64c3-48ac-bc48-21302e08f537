// stores/__tests__/notificationsStore.basic.test.ts - Basic notifications store tests

import { useNotificationsStore } from '../notificationsStore'
import type { Notification, NotificationType, NotificationPriority, NotificationStatus, NotificationSettings, SystemAlert, UserMessage } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  notificationsService: {
    validateNotificationData: jest.fn(() => ({ isValid: true, errors: [] })),
    formatNotificationTime: jest.fn((timestamp: string) => {
      const date = new Date(timestamp)
      const now = new Date()
      const diffMs = now.getTime() - date.getTime()
      const diffMins = Math.floor(diffMs / 60000)
      
      if (diffMins < 1) return 'Just now'
      if (diffMins < 60) return `${diffMins}m ago`
      if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`
      return `${Math.floor(diffMins / 1440)}d ago`
    }),
    getNotificationIcon: jest.fn((type: NotificationType) => {
      const icons: Record<NotificationType, string> = {
        'info': 'ℹ️',
        'success': '✅',
        'warning': '⚠️',
        'error': '❌',
        'system': '⚙️',
        'message': '💬',
        'reminder': '⏰',
        'update': '🔄'
      }
      return icons[type] || 'ℹ️'
    }),
    getNotificationColor: jest.fn((priority: NotificationPriority) => {
      const colors: Record<NotificationPriority, string> = {
        'low': 'gray',
        'medium': 'blue',
        'high': 'orange',
        'urgent': 'red'
      }
      return colors[priority] || 'gray'
    }),
    getPriorityLabel: jest.fn((priority: NotificationPriority) => {
      const labels: Record<NotificationPriority, string> = {
        'low': 'Low Priority',
        'medium': 'Medium Priority',
        'high': 'High Priority',
        'urgent': 'Urgent'
      }
      return labels[priority] || 'Unknown'
    }),
    isNotificationExpired: jest.fn((notification: Notification) => {
      if (!notification.expiresAt) return false
      return new Date() > new Date(notification.expiresAt)
    }),
    canUserAccessNotification: jest.fn((notification: Notification, userId: string) => {
      return notification.recipientId === userId || notification.createdBy === userId
    }),
    getNotificationSummary: jest.fn((notification: Notification) => {
      return `${notification.type} notification: ${notification.title}`
    }),
    shouldShowNotification: jest.fn((notification: Notification, settings: NotificationSettings) => {
      if (!settings.enabled) return false
      if (settings.quietHours?.enabled) {
        const now = new Date()
        const currentHour = now.getHours()
        const startHour = parseInt(settings.quietHours.startTime.split(':')[0])
        const endHour = parseInt(settings.quietHours.endTime.split(':')[0])
        
        if (startHour <= endHour) {
          if (currentHour >= startHour && currentHour < endHour) return false
        } else {
          if (currentHour >= startHour || currentHour < endHour) return false
        }
      }
      return settings.types?.[notification.type] !== false
    }),
    playNotificationSound: jest.fn((priority: NotificationPriority) => {
      // Mock sound playing
      console.log(`Playing ${priority} priority sound`)
    }),
    showBrowserNotification: jest.fn((notification: Notification) => {
      // Mock browser notification
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico'
        })
      }
    }),
    getNotifications: jest.fn(),
    getNotificationById: jest.fn(),
    createNotification: jest.fn(),
    updateNotification: jest.fn(),
    deleteNotification: jest.fn(),
    markAllAsRead: jest.fn(),
    deleteMultiple: jest.fn(),
    sendNotification: jest.fn(),
    broadcastNotification: jest.fn(),
    sendSystemAlert: jest.fn(),
    sendUserMessage: jest.fn(),
    subscribeToPush: jest.fn(),
    unsubscribeFromPush: jest.fn(),
    sendPushNotification: jest.fn(),
    sendEmailNotification: jest.fn(),
    sendSMSNotification: jest.fn(),
    getNotificationSettings: jest.fn(),
    updateNotificationSettings: jest.fn(),
    getNotificationTemplates: jest.fn(),
    createNotificationTemplate: jest.fn(),
    updateNotificationTemplate: jest.fn(),
    deleteNotificationTemplate: jest.fn(),
    enableNotificationChannel: jest.fn(),
    disableNotificationChannel: jest.fn(),
    updateChannelPreferences: jest.fn(),
    searchNotifications: jest.fn()
  },
  socketService: {
    connect: jest.fn(),
    disconnect: jest.fn(),
    reconnect: jest.fn(),
    emit: jest.fn(),
    on: jest.fn(),
    off: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Mock notification data
const mockNotification: Notification = {
  _id: 'notification-123',
  title: 'New Order Received',
  message: 'You have received a new order #ORD-2024-001',
  type: 'info',
  priority: 'medium',
  status: 'unread',
  recipientId: 'user-123',
  recipientName: 'John Doe',
  createdBy: 'system',
  createdByName: 'System',
  data: {
    orderId: 'order-123',
    orderNumber: 'ORD-2024-001',
    amount: 150000
  },
  channels: ['in-app', 'email'],
  readAt: null,
  archivedAt: null,
  expiresAt: '2024-02-01T00:00:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockNotificationSettings: NotificationSettings = {
  _id: 'settings-123',
  userId: 'user-123',
  enabled: true,
  channels: {
    'in-app': { enabled: true, preferences: {} },
    'email': { enabled: true, preferences: { frequency: 'immediate' } },
    'sms': { enabled: false, preferences: {} },
    'push': { enabled: true, preferences: {} }
  },
  types: {
    'info': true,
    'success': true,
    'warning': true,
    'error': true,
    'system': true,
    'message': true,
    'reminder': true,
    'update': true
  },
  quietHours: {
    enabled: true,
    startTime: '22:00',
    endTime: '08:00',
    timezone: 'Africa/Blantyre'
  },
  soundEnabled: true,
  vibrationEnabled: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockSystemAlert: SystemAlert = {
  _id: 'alert-123',
  title: 'System Maintenance',
  message: 'Scheduled maintenance will begin at 2:00 AM',
  type: 'system',
  priority: 'high',
  status: 'unread',
  recipientId: 'all',
  recipientName: 'All Users',
  createdBy: 'admin',
  createdByName: 'System Admin',
  alertType: 'maintenance',
  severity: 'medium',
  affectedSystems: ['database', 'api'],
  estimatedDuration: '2 hours',
  channels: ['in-app', 'email'],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockUserMessage: UserMessage = {
  _id: 'message-123',
  title: 'Welcome to FathahiTech',
  message: 'Thank you for joining our platform!',
  type: 'message',
  priority: 'low',
  status: 'unread',
  recipientId: 'user-123',
  recipientName: 'John Doe',
  createdBy: 'admin',
  createdByName: 'Admin',
  senderId: 'admin',
  senderName: 'Admin',
  messageType: 'welcome',
  isPrivate: false,
  channels: ['in-app'],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('NotificationsStore - Basic Tests', () => {
  beforeEach(() => {
    // Reset store state before each test
    useNotificationsStore.setState({
      notifications: [],
      unreadNotifications: [],
      selectedNotification: null,
      isLoading: false,
      error: null,
      isConnected: false,
      connectionStatus: 'disconnected',
      lastHeartbeat: null,
      unreadCount: 0,
      totalCount: 0,
      priorityCount: {
        low: 0,
        medium: 0,
        high: 0,
        urgent: 0
      },
      systemAlerts: [],
      userMessages: [],
      emailNotifications: [],
      smsNotifications: [],
      inAppNotifications: [],
      notificationSettings: null,
      notificationTemplates: [],
      enabledChannels: [],
      pushSubscription: null,
      pushEnabled: false,
      currentPage: 1,
      totalPages: 0,
      pageSize: 20,
      filters: {},
      searchQuery: ''
    })
  })

  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const store = useNotificationsStore.getState()
      
      expect(store.notifications).toEqual([])
      expect(store.unreadNotifications).toEqual([])
      expect(store.selectedNotification).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.isConnected).toBe(false)
      expect(store.connectionStatus).toBe('disconnected')
      expect(store.lastHeartbeat).toBeNull()
      expect(store.unreadCount).toBe(0)
      expect(store.totalCount).toBe(0)
      expect(store.priorityCount).toEqual({
        low: 0,
        medium: 0,
        high: 0,
        urgent: 0
      })
      expect(store.systemAlerts).toEqual([])
      expect(store.userMessages).toEqual([])
      expect(store.emailNotifications).toEqual([])
      expect(store.smsNotifications).toEqual([])
      expect(store.inAppNotifications).toEqual([])
      expect(store.notificationSettings).toBeNull()
      expect(store.notificationTemplates).toEqual([])
      expect(store.enabledChannels).toEqual([])
      expect(store.pushSubscription).toBeNull()
      expect(store.pushEnabled).toBe(false)
      expect(store.currentPage).toBe(1)
      expect(store.totalPages).toBe(0)
      expect(store.pageSize).toBe(20)
      expect(store.filters).toEqual({})
      expect(store.searchQuery).toBe('')
    })

    test('should have all required methods', () => {
      const store = useNotificationsStore.getState()
      
      // Socket.IO connection methods
      expect(typeof store.connectSocket).toBe('function')
      expect(typeof store.disconnectSocket).toBe('function')
      expect(typeof store.reconnectSocket).toBe('function')
      
      // CRUD methods
      expect(typeof store.fetchNotifications).toBe('function')
      expect(typeof store.fetchNotificationById).toBe('function')
      expect(typeof store.createNotification).toBe('function')
      expect(typeof store.updateNotification).toBe('function')
      expect(typeof store.deleteNotification).toBe('function')
      
      // Notification management methods
      expect(typeof store.markAsRead).toBe('function')
      expect(typeof store.markAsUnread).toBe('function')
      expect(typeof store.markAllAsRead).toBe('function')
      expect(typeof store.archiveNotification).toBe('function')
      expect(typeof store.deleteMultiple).toBe('function')
      
      // Real-time event methods
      expect(typeof store.sendNotification).toBe('function')
      expect(typeof store.broadcastNotification).toBe('function')
      expect(typeof store.sendSystemAlert).toBe('function')
      expect(typeof store.sendUserMessage).toBe('function')
      
      // Push notification methods
      expect(typeof store.subscribeToPush).toBe('function')
      expect(typeof store.unsubscribeFromPush).toBe('function')
      expect(typeof store.sendPushNotification).toBe('function')
      
      // Email & SMS methods
      expect(typeof store.sendEmailNotification).toBe('function')
      expect(typeof store.sendSMSNotification).toBe('function')
      
      // Settings & templates methods
      expect(typeof store.fetchNotificationSettings).toBe('function')
      expect(typeof store.updateNotificationSettings).toBe('function')
      expect(typeof store.fetchNotificationTemplates).toBe('function')
      expect(typeof store.createNotificationTemplate).toBe('function')
      expect(typeof store.updateNotificationTemplate).toBe('function')
      expect(typeof store.deleteNotificationTemplate).toBe('function')
      
      // Channel methods
      expect(typeof store.enableNotificationChannel).toBe('function')
      expect(typeof store.disableNotificationChannel).toBe('function')
      expect(typeof store.updateChannelPreferences).toBe('function')
      
      // Search and filtering methods
      expect(typeof store.searchNotifications).toBe('function')
      expect(typeof store.setFilters).toBe('function')
      expect(typeof store.clearFilters).toBe('function')
      expect(typeof store.setSearchQuery).toBe('function')
      
      // Pagination methods
      expect(typeof store.setCurrentPage).toBe('function')
      expect(typeof store.setPageSize).toBe('function')
      
      // UI state methods
      expect(typeof store.setSelectedNotification).toBe('function')
      expect(typeof store.clearError).toBe('function')
      expect(typeof store.setLoading).toBe('function')
      
      // Socket event handlers
      expect(typeof store.onNotificationReceived).toBe('function')
      expect(typeof store.onNotificationUpdated).toBe('function')
      expect(typeof store.onNotificationDeleted).toBe('function')
      expect(typeof store.onSystemAlert).toBe('function')
      expect(typeof store.onUserMessage).toBe('function')
      expect(typeof store.onConnectionStatusChanged).toBe('function')
      
      // Utility methods
      expect(typeof store.getNotificationsByType).toBe('function')
      expect(typeof store.getNotificationsByPriority).toBe('function')
      expect(typeof store.getNotificationsByStatus).toBe('function')
      expect(typeof store.getNotificationsByDateRange).toBe('function')
      expect(typeof store.getUnreadNotifications).toBe('function')
      expect(typeof store.getRecentNotifications).toBe('function')
      expect(typeof store.calculateNotificationCounts).toBe('function')
      expect(typeof store.formatNotificationTime).toBe('function')
      expect(typeof store.getNotificationIcon).toBe('function')
      expect(typeof store.getNotificationColor).toBe('function')
      expect(typeof store.getPriorityLabel).toBe('function')
      expect(typeof store.isNotificationExpired).toBe('function')
      expect(typeof store.canUserAccessNotification).toBe('function')
      expect(typeof store.getNotificationSummary).toBe('function')
      expect(typeof store.shouldShowNotification).toBe('function')
      expect(typeof store.playNotificationSound).toBe('function')
      expect(typeof store.showBrowserNotification).toBe('function')
    })
  })

  describe('State Management', () => {
    test('should set selected notification', () => {
      const store = useNotificationsStore.getState()
      
      store.setSelectedNotification(mockNotification)
      expect(useNotificationsStore.getState().selectedNotification).toEqual(mockNotification)
      
      store.setSelectedNotification(null)
      expect(useNotificationsStore.getState().selectedNotification).toBeNull()
    })

    test('should clear error', () => {
      useNotificationsStore.setState({ error: 'Test error' })
      
      const store = useNotificationsStore.getState()
      expect(store.error).toBe('Test error')
      
      store.clearError()
      expect(useNotificationsStore.getState().error).toBeNull()
    })

    test('should set loading state', () => {
      const store = useNotificationsStore.getState()
      
      store.setLoading(true)
      expect(useNotificationsStore.getState().isLoading).toBe(true)
      
      store.setLoading(false)
      expect(useNotificationsStore.getState().isLoading).toBe(false)
    })

    test('should set search query', () => {
      const store = useNotificationsStore.getState()
      
      store.setSearchQuery('order notification')
      expect(useNotificationsStore.getState().searchQuery).toBe('order notification')
      
      store.setSearchQuery('')
      expect(useNotificationsStore.getState().searchQuery).toBe('')
    })

    test('should set filters', () => {
      const store = useNotificationsStore.getState()
      
      store.setFilters({ type: 'info', priority: 'high' })
      expect(useNotificationsStore.getState().filters).toEqual({
        type: 'info',
        priority: 'high'
      })
      
      store.setFilters({ status: 'unread' })
      expect(useNotificationsStore.getState().filters).toEqual({
        type: 'info',
        priority: 'high',
        status: 'unread'
      })
    })

    test('should clear filters', () => {
      useNotificationsStore.setState({
        filters: { type: 'info', priority: 'high' },
        searchQuery: 'test'
      })
      
      const store = useNotificationsStore.getState()
      store.clearFilters()
      
      const updatedStore = useNotificationsStore.getState()
      expect(updatedStore.filters).toEqual({})
      expect(updatedStore.searchQuery).toBe('')
    })

    test('should set pagination', () => {
      const store = useNotificationsStore.getState()
      
      store.setCurrentPage(3)
      expect(useNotificationsStore.getState().currentPage).toBe(3)
      
      store.setPageSize(50)
      expect(useNotificationsStore.getState().pageSize).toBe(50)
    })
  })

  describe('Socket Event Handlers', () => {
    beforeEach(() => {
      // Set up test data
      const notifications: Notification[] = [
        { ...mockNotification, _id: 'notification-1', priority: 'low', status: 'read' },
        { ...mockNotification, _id: 'notification-2', priority: 'medium', status: 'unread' },
        { ...mockNotification, _id: 'notification-3', priority: 'high', status: 'unread' }
      ]
      
      useNotificationsStore.setState({ 
        notifications,
        unreadNotifications: notifications.filter(n => n.status === 'unread'),
        unreadCount: 2,
        totalCount: 3
      })
    })

    test('should handle notification received', () => {
      const store = useNotificationsStore.getState()
      const newNotification: Notification = {
        ...mockNotification,
        _id: 'notification-new',
        title: 'New Message',
        status: 'unread'
      }
      
      store.onNotificationReceived(newNotification)
      
      const updatedStore = useNotificationsStore.getState()
      expect(updatedStore.notifications).toHaveLength(4)
      expect(updatedStore.notifications[0]).toEqual(newNotification)
      expect(updatedStore.unreadNotifications).toHaveLength(3)
      expect(updatedStore.unreadCount).toBe(3)
      expect(updatedStore.totalCount).toBe(4)
    })

    test('should handle notification updated', () => {
      const store = useNotificationsStore.getState()
      const updatedNotification: Notification = {
        ...mockNotification,
        _id: 'notification-2',
        status: 'read',
        readAt: new Date().toISOString()
      }
      
      store.onNotificationUpdated(updatedNotification)
      
      const updatedStore = useNotificationsStore.getState()
      expect(updatedStore.notifications[1]).toEqual(updatedNotification)
      expect(updatedStore.unreadNotifications).toHaveLength(1)
      expect(updatedStore.unreadCount).toBe(1)
    })

    test('should handle notification deleted', () => {
      const store = useNotificationsStore.getState()
      
      store.onNotificationDeleted('notification-2')
      
      const updatedStore = useNotificationsStore.getState()
      expect(updatedStore.notifications).toHaveLength(2)
      expect(updatedStore.notifications.find(n => n._id === 'notification-2')).toBeUndefined()
      expect(updatedStore.unreadNotifications).toHaveLength(1)
      expect(updatedStore.unreadCount).toBe(1)
      expect(updatedStore.totalCount).toBe(2)
    })

    test('should handle system alert', () => {
      const store = useNotificationsStore.getState()
      
      store.onSystemAlert(mockSystemAlert)
      
      const updatedStore = useNotificationsStore.getState()
      expect(updatedStore.systemAlerts).toHaveLength(1)
      expect(updatedStore.systemAlerts[0]).toEqual(mockSystemAlert)
    })

    test('should handle user message', () => {
      const store = useNotificationsStore.getState()
      
      store.onUserMessage(mockUserMessage)
      
      const updatedStore = useNotificationsStore.getState()
      expect(updatedStore.userMessages).toHaveLength(1)
      expect(updatedStore.userMessages[0]).toEqual(mockUserMessage)
    })

    test('should handle connection status changed', () => {
      const store = useNotificationsStore.getState()
      
      store.onConnectionStatusChanged('connected')
      
      const updatedStore = useNotificationsStore.getState()
      expect(updatedStore.connectionStatus).toBe('connected')
      expect(updatedStore.isConnected).toBe(true)
      expect(updatedStore.lastHeartbeat).toBeTruthy()
    })
  })

  describe('Utility Methods', () => {
    beforeEach(() => {
      // Set up test data
      const notifications: Notification[] = [
        { ...mockNotification, _id: 'notification-1', type: 'info', priority: 'low', status: 'read', createdAt: '2024-01-01T00:00:00Z' },
        { ...mockNotification, _id: 'notification-2', type: 'success', priority: 'medium', status: 'unread', createdAt: '2024-01-02T00:00:00Z' },
        { ...mockNotification, _id: 'notification-3', type: 'warning', priority: 'high', status: 'unread', createdAt: '2024-01-03T00:00:00Z' },
        { ...mockNotification, _id: 'notification-4', type: 'error', priority: 'urgent', status: 'archived', createdAt: '2024-01-04T00:00:00Z' }
      ]
      
      useNotificationsStore.setState({
        notifications,
        unreadNotifications: notifications.filter(n => n.status === 'unread')
      })
    })

    test('should get notifications by type', () => {
      const store = useNotificationsStore.getState()
      
      const infoNotifications = store.getNotificationsByType('info')
      expect(infoNotifications).toHaveLength(1)
      expect(infoNotifications[0].type).toBe('info')
      
      const successNotifications = store.getNotificationsByType('success')
      expect(successNotifications).toHaveLength(1)
      expect(successNotifications[0].type).toBe('success')
    })

    test('should get notifications by priority', () => {
      const store = useNotificationsStore.getState()
      
      const lowPriorityNotifications = store.getNotificationsByPriority('low')
      expect(lowPriorityNotifications).toHaveLength(1)
      expect(lowPriorityNotifications[0].priority).toBe('low')
      
      const urgentNotifications = store.getNotificationsByPriority('urgent')
      expect(urgentNotifications).toHaveLength(1)
      expect(urgentNotifications[0].priority).toBe('urgent')
    })

    test('should get notifications by status', () => {
      const store = useNotificationsStore.getState()
      
      const unreadNotifications = store.getNotificationsByStatus('unread')
      expect(unreadNotifications).toHaveLength(2)
      expect(unreadNotifications.every(n => n.status === 'unread')).toBe(true)
      
      const readNotifications = store.getNotificationsByStatus('read')
      expect(readNotifications).toHaveLength(1)
      expect(readNotifications[0].status).toBe('read')
    })

    test('should get notifications by date range', () => {
      const store = useNotificationsStore.getState()
      
      const notificationsInRange = store.getNotificationsByDateRange('2024-01-01', '2024-01-02')
      expect(notificationsInRange).toHaveLength(2)
      
      const notificationsOutOfRange = store.getNotificationsByDateRange('2024-01-05', '2024-01-06')
      expect(notificationsOutOfRange).toHaveLength(0)
    })

    test('should get unread notifications', () => {
      const store = useNotificationsStore.getState()

      // The store should have unreadNotifications populated from the beforeEach setup
      const unreadNotifications = store.getUnreadNotifications()
      expect(unreadNotifications).toHaveLength(2)
      expect(unreadNotifications.every(n => n.status === 'unread')).toBe(true)
    })

    test('should get recent notifications', () => {
      const store = useNotificationsStore.getState()
      
      const recentNotifications = store.getRecentNotifications(2)
      expect(recentNotifications).toHaveLength(2)
      expect(recentNotifications[0]._id).toBe('notification-1')
      expect(recentNotifications[1]._id).toBe('notification-2')
    })

    test('should calculate notification counts', () => {
      const store = useNotificationsStore.getState()
      
      const counts = store.calculateNotificationCounts()
      expect(counts.total).toBe(4)
      expect(counts.unread).toBe(2)
      expect(counts.byPriority.low).toBe(1)
      expect(counts.byPriority.medium).toBe(1)
      expect(counts.byPriority.high).toBe(1)
      expect(counts.byPriority.urgent).toBe(1)
    })

    test('should format notification time', () => {
      const store = useNotificationsStore.getState()
      
      const recentTime = new Date(Date.now() - 30000).toISOString() // 30 seconds ago
      const formattedTime = store.formatNotificationTime(recentTime)
      expect(formattedTime).toBe('Just now')
      
      const oldTime = new Date(Date.now() - 3600000).toISOString() // 1 hour ago
      const formattedOldTime = store.formatNotificationTime(oldTime)
      expect(formattedOldTime).toBe('1h ago')
    })

    test('should get notification icon', () => {
      const store = useNotificationsStore.getState()
      
      expect(store.getNotificationIcon('info')).toBe('ℹ️')
      expect(store.getNotificationIcon('success')).toBe('✅')
      expect(store.getNotificationIcon('warning')).toBe('⚠️')
      expect(store.getNotificationIcon('error')).toBe('❌')
    })

    test('should get notification color', () => {
      const store = useNotificationsStore.getState()
      
      expect(store.getNotificationColor('low')).toBe('gray')
      expect(store.getNotificationColor('medium')).toBe('blue')
      expect(store.getNotificationColor('high')).toBe('orange')
      expect(store.getNotificationColor('urgent')).toBe('red')
    })

    test('should get priority label', () => {
      const store = useNotificationsStore.getState()
      
      expect(store.getPriorityLabel('low')).toBe('Low Priority')
      expect(store.getPriorityLabel('medium')).toBe('Medium Priority')
      expect(store.getPriorityLabel('high')).toBe('High Priority')
      expect(store.getPriorityLabel('urgent')).toBe('Urgent')
    })

    test('should check if notification is expired', () => {
      const store = useNotificationsStore.getState()

      const expiredNotification = { ...mockNotification, expiresAt: '2023-01-01T00:00:00Z' }
      const validNotification = { ...mockNotification, expiresAt: '2025-01-01T00:00:00Z' }

      // Mock the service to return expected values
      const { notificationsService } = require('@/services/frontend')
      notificationsService.isNotificationExpired
        .mockReturnValueOnce(true)  // For expired notification
        .mockReturnValueOnce(false) // For valid notification

      expect(store.isNotificationExpired(expiredNotification)).toBe(true)
      expect(store.isNotificationExpired(validNotification)).toBe(false)
    })

    test('should check user access to notification', () => {
      const store = useNotificationsStore.getState()
      
      expect(store.canUserAccessNotification(mockNotification, 'user-123')).toBe(true) // Recipient
      expect(store.canUserAccessNotification(mockNotification, 'system')).toBe(true) // Creator
      expect(store.canUserAccessNotification(mockNotification, 'user-456')).toBe(false) // No access
    })

    test('should get notification summary', () => {
      const store = useNotificationsStore.getState()
      
      const summary = store.getNotificationSummary(mockNotification)
      expect(summary).toContain('info notification')
      expect(summary).toContain('New Order Received')
    })

    test('should determine if notification should be shown', () => {
      const store = useNotificationsStore.getState()
      
      const enabledSettings = { ...mockNotificationSettings, enabled: true }
      const disabledSettings = { ...mockNotificationSettings, enabled: false }
      
      expect(store.shouldShowNotification(mockNotification, enabledSettings)).toBe(true)
      expect(store.shouldShowNotification(mockNotification, disabledSettings)).toBe(false)
    })

    test('should play notification sound', () => {
      const store = useNotificationsStore.getState()
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
      
      store.playNotificationSound('urgent')
      expect(consoleSpy).toHaveBeenCalledWith('Playing urgent priority sound')
      
      consoleSpy.mockRestore()
    })
  })
})
