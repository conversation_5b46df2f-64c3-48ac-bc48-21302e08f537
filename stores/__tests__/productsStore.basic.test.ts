// stores/__tests__/productsStore.basic.test.ts - Basic products store tests

import { useProductsStore } from '../productsStore'
import type { Product, ProductCategory, ProductStatus } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  productService: {
    getProductCategories: jest.fn(() => [
      'Desktops', 'Laptops', 'TV Sets', 'Server Systems', 'Amplifiers',
      'Network Devices', 'Security Software', 'Application Software',
      'Mobile Devices', 'Audio Equipment', 'Gaming', 'Accessories'
    ]),
    getProductStatuses: jest.fn(() => ['Available', 'Out of Stock', 'Discontinued', 'Coming Soon']),
    formatPrice: jest.fn((price: number) => `MWK ${price.toLocaleString()}`),
    validateProductData: jest.fn(() => ({ isValid: true, errors: [] })),
    getProducts: jest.fn(),
    getProductById: jest.fn(),
    createProduct: jest.fn(),
    updateProduct: jest.fn(),
    deleteProduct: jest.fn(),
    getProductsByCategory: jest.fn(),
    getFeaturedProducts: jest.fn(),
    getLowStockProducts: jest.fn(),
    searchProducts: jest.fn(),
    updateProductStock: jest.fn(),
    updateProductStatus: jest.fn(),
    toggleFeatured: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Mock product data
const mockProduct: Product = {
  _id: 'product-123',
  name: 'Test Laptop',
  category: 'Laptops',
  price: 500000,
  stock: 10,
  minStockLevel: 5,
  images: ['laptop1.jpg'],
  description: 'Test laptop description',
  specifications: ['Intel i5', '8GB RAM', '256GB SSD'],
  branchId: 'branch-123',
  branchName: 'Test Branch',
  brand: 'TestBrand',
  model: 'TestModel',
  sku: 'LAP-TEST-001',
  status: 'Available',
  isFeatured: false,
  warranty: '1 year',
  tags: ['laptop', 'portable'],
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('ProductsStore - Basic Tests', () => {
  beforeEach(() => {
    // Reset store state before each test
    useProductsStore.setState({
      products: [],
      selectedProduct: null,
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 0,
      totalProducts: 0,
      pageSize: 20,
      filters: {},
      searchQuery: '',
      selectedCategory: null,
      featuredProducts: [],
      lowStockProducts: []
    })
  })

  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const store = useProductsStore.getState()
      
      expect(store.products).toEqual([])
      expect(store.selectedProduct).toBeNull()
      expect(store.isLoading).toBe(false)
      expect(store.error).toBeNull()
      expect(store.currentPage).toBe(1)
      expect(store.totalPages).toBe(0)
      expect(store.totalProducts).toBe(0)
      expect(store.pageSize).toBe(20)
      expect(store.filters).toEqual({})
      expect(store.searchQuery).toBe('')
      expect(store.selectedCategory).toBeNull()
      expect(store.featuredProducts).toEqual([])
      expect(store.lowStockProducts).toEqual([])
      expect(store.categories).toHaveLength(12)
    })

    test('should have all required methods', () => {
      const store = useProductsStore.getState()
      
      // CRUD methods
      expect(typeof store.fetchProducts).toBe('function')
      expect(typeof store.fetchProductById).toBe('function')
      expect(typeof store.createProduct).toBe('function')
      expect(typeof store.updateProduct).toBe('function')
      expect(typeof store.deleteProduct).toBe('function')
      
      // Category methods
      expect(typeof store.fetchProductsByCategory).toBe('function')
      expect(typeof store.setSelectedCategory).toBe('function')
      
      // Featured products methods
      expect(typeof store.fetchFeaturedProducts).toBe('function')
      expect(typeof store.toggleProductFeatured).toBe('function')
      
      // Stock management methods
      expect(typeof store.fetchLowStockProducts).toBe('function')
      expect(typeof store.updateProductStock).toBe('function')
      expect(typeof store.updateProductStatus).toBe('function')
      
      // Search and filtering methods
      expect(typeof store.searchProducts).toBe('function')
      expect(typeof store.setFilters).toBe('function')
      expect(typeof store.clearFilters).toBe('function')
      expect(typeof store.setSearchQuery).toBe('function')
      
      // Pagination methods
      expect(typeof store.setCurrentPage).toBe('function')
      expect(typeof store.setPageSize).toBe('function')
      
      // UI state methods
      expect(typeof store.setSelectedProduct).toBe('function')
      expect(typeof store.clearError).toBe('function')
      expect(typeof store.setLoading).toBe('function')
      
      // Utility methods
      expect(typeof store.getProductsByBranch).toBe('function')
      expect(typeof store.getProductsByStatus).toBe('function')
      expect(typeof store.getAvailableCategories).toBe('function')
      expect(typeof store.getAvailableStatuses).toBe('function')
      expect(typeof store.calculateTotalValue).toBe('function')
      expect(typeof store.isProductLowStock).toBe('function')
      expect(typeof store.formatProductPrice).toBe('function')
    })
  })

  describe('State Management', () => {
    test('should set selected product', () => {
      const store = useProductsStore.getState()
      
      store.setSelectedProduct(mockProduct)
      expect(useProductsStore.getState().selectedProduct).toEqual(mockProduct)
      
      store.setSelectedProduct(null)
      expect(useProductsStore.getState().selectedProduct).toBeNull()
    })

    test('should clear error', () => {
      useProductsStore.setState({ error: 'Test error' })
      
      const store = useProductsStore.getState()
      expect(store.error).toBe('Test error')
      
      store.clearError()
      expect(useProductsStore.getState().error).toBeNull()
    })

    test('should set loading state', () => {
      const store = useProductsStore.getState()
      
      store.setLoading(true)
      expect(useProductsStore.getState().isLoading).toBe(true)
      
      store.setLoading(false)
      expect(useProductsStore.getState().isLoading).toBe(false)
    })

    test('should set search query', () => {
      const store = useProductsStore.getState()
      
      store.setSearchQuery('laptop')
      expect(useProductsStore.getState().searchQuery).toBe('laptop')
      
      store.setSearchQuery('')
      expect(useProductsStore.getState().searchQuery).toBe('')
    })

    test('should set filters', () => {
      const store = useProductsStore.getState()
      
      store.setFilters({ category: 'Laptops', status: 'Available' })
      expect(useProductsStore.getState().filters).toEqual({
        category: 'Laptops',
        status: 'Available'
      })
      
      store.setFilters({ branchId: 'branch-123' })
      expect(useProductsStore.getState().filters).toEqual({
        category: 'Laptops',
        status: 'Available',
        branchId: 'branch-123'
      })
    })

    test('should clear filters', () => {
      useProductsStore.setState({
        filters: { category: 'Laptops', status: 'Available' },
        selectedCategory: 'Laptops',
        searchQuery: 'test'
      })
      
      const store = useProductsStore.getState()
      store.clearFilters()
      
      const updatedStore = useProductsStore.getState()
      expect(updatedStore.filters).toEqual({})
      expect(updatedStore.selectedCategory).toBeNull()
      expect(updatedStore.searchQuery).toBe('')
    })

    test('should set selected category', () => {
      const store = useProductsStore.getState()
      
      store.setSelectedCategory('Laptops')
      const updatedStore = useProductsStore.getState()
      expect(updatedStore.selectedCategory).toBe('Laptops')
      expect(updatedStore.filters.category).toBe('Laptops')
      
      store.setSelectedCategory(null)
      const clearedStore = useProductsStore.getState()
      expect(clearedStore.selectedCategory).toBeNull()
      expect(clearedStore.filters.category).toBeUndefined()
    })

    test('should set pagination', () => {
      const store = useProductsStore.getState()
      
      store.setCurrentPage(3)
      expect(useProductsStore.getState().currentPage).toBe(3)
      
      store.setPageSize(50)
      expect(useProductsStore.getState().pageSize).toBe(50)
    })
  })

  describe('Utility Methods', () => {
    beforeEach(() => {
      // Set up test data
      const products: Product[] = [
        { ...mockProduct, _id: 'prod-1', branchId: 'branch-1', status: 'Available', stock: 3, minStockLevel: 5 },
        { ...mockProduct, _id: 'prod-2', branchId: 'branch-1', status: 'Out of Stock', stock: 15, minStockLevel: 5 },
        { ...mockProduct, _id: 'prod-3', branchId: 'branch-2', status: 'Available', stock: 20, minStockLevel: 5 },
        { ...mockProduct, _id: 'prod-4', branchId: 'branch-2', status: 'Discontinued', stock: 2, minStockLevel: 5 }
      ]
      
      useProductsStore.setState({ products })
    })

    test('should get products by branch', () => {
      const store = useProductsStore.getState()
      
      const branch1Products = store.getProductsByBranch('branch-1')
      expect(branch1Products).toHaveLength(2)
      expect(branch1Products.every(p => p.branchId === 'branch-1')).toBe(true)
      
      const branch2Products = store.getProductsByBranch('branch-2')
      expect(branch2Products).toHaveLength(2)
      expect(branch2Products.every(p => p.branchId === 'branch-2')).toBe(true)
    })

    test('should get products by status', () => {
      const store = useProductsStore.getState()
      
      const availableProducts = store.getProductsByStatus('Available')
      expect(availableProducts).toHaveLength(2)
      expect(availableProducts.every(p => p.status === 'Available')).toBe(true)
      
      const outOfStockProducts = store.getProductsByStatus('Out of Stock')
      expect(outOfStockProducts).toHaveLength(1)
      expect(outOfStockProducts[0].status).toBe('Out of Stock')
    })

    test('should get available categories', () => {
      const store = useProductsStore.getState()
      
      const categories = store.getAvailableCategories()
      expect(categories).toHaveLength(12)
      expect(categories).toContain('Laptops')
      expect(categories).toContain('Desktops')
    })

    test('should get available statuses', () => {
      const store = useProductsStore.getState()
      
      const statuses = store.getAvailableStatuses()
      expect(statuses).toHaveLength(4)
      expect(statuses).toContain('Available')
      expect(statuses).toContain('Out of Stock')
    })

    test('should calculate total value', () => {
      const store = useProductsStore.getState()
      
      // Total value = (500000 * 3) + (500000 * 15) + (500000 * 20) + (500000 * 2)
      // = 1500000 + 7500000 + 10000000 + 1000000 = 20000000
      const totalValue = store.calculateTotalValue()
      expect(totalValue).toBe(20000000)
      
      // Test with specific products
      const specificProducts = store.products.slice(0, 2)
      const specificValue = store.calculateTotalValue(specificProducts)
      expect(specificValue).toBe(9000000) // (500000 * 3) + (500000 * 15)
    })

    test('should check if product is low stock', () => {
      const store = useProductsStore.getState()
      
      const lowStockProduct = { ...mockProduct, stock: 3, minStockLevel: 5 }
      const normalStockProduct = { ...mockProduct, stock: 10, minStockLevel: 5 }
      
      expect(store.isProductLowStock(lowStockProduct)).toBe(true)
      expect(store.isProductLowStock(normalStockProduct)).toBe(false)
    })

    test('should format product price', () => {
      const store = useProductsStore.getState()
      
      const formattedPrice = store.formatProductPrice(500000)
      expect(formattedPrice).toBe('MWK 500,000')
    })
  })

  describe('Categories', () => {
    test('should have predefined categories', () => {
      const store = useProductsStore.getState()
      
      expect(store.categories).toContain('Laptops')
      expect(store.categories).toContain('Desktops')
      expect(store.categories).toContain('TV Sets')
      expect(store.categories).toContain('Mobile Devices')
      expect(store.categories).toHaveLength(12)
    })
  })
})
