// stores/__tests__/inventoryStore.integration.test.ts - Inventory store integration tests

import { renderHook, act } from '@testing-library/react'
import { useInventoryStore } from '../inventoryStore'
import type { InventoryItem, StockMovement, StockAdjustment, PurchaseOrder, ApiResponse } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  inventoryService: {
    validateInventoryData: jest.fn(() => ({ isValid: true, errors: [] })),
    calculateTotalStockValue: jest.fn(() => 1000000),
    calculateReorderQuantity: jest.fn(() => 50),
    isLowStock: jest.fn(() => false),
    isCriticalStock: jest.fn(() => false),
    isOutOfStock: jest.fn(() => false),
    getStockLevel: jest.fn(() => 50),
    getAvailableStock: jest.fn(() => 45),
    formatStockStatus: jest.fn(() => 'In Stock'),
    getMovementTypeColor: jest.fn(() => 'green'),
    getInventoryItems: jest.fn(),
    getInventoryItemById: jest.fn(),
    createInventoryItem: jest.fn(),
    updateInventoryItem: jest.fn(),
    deleteInventoryItem: jest.fn(),
    updateStock: jest.fn(),
    adjustStock: jest.fn(),
    transferStock: jest.fn(),
    reserveStock: jest.fn(),
    releaseStock: jest.fn(),
    getStockMovements: jest.fn(),
    getRecentMovements: jest.fn(),
    createStockMovement: jest.fn(),
    getStockAdjustments: jest.fn(),
    getPendingAdjustments: jest.fn(),
    approveAdjustment: jest.fn(),
    rejectAdjustment: jest.fn(),
    getLowStockAlerts: jest.fn(),
    getCriticalStockItems: jest.fn(),
    updateReorderPoint: jest.fn(),
    dismissAlert: jest.fn(),
    getSuppliers: jest.fn(),
    getPurchaseOrders: jest.fn(),
    createPurchaseOrder: jest.fn(),
    updatePurchaseOrder: jest.fn(),
    receivePurchaseOrder: jest.fn(),
    getInventoryByBranch: jest.fn(),
    getInventoryByProduct: jest.fn(),
    getInventoryBySupplier: jest.fn(),
    searchInventory: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Import the mocked module
const { inventoryService } = require('@/services/frontend')
const mockInventoryService = inventoryService as jest.Mocked<typeof inventoryService>

// Mock inventory data
const mockInventoryItem: InventoryItem = {
  _id: 'inventory-123',
  productId: 'product-123',
  productName: 'Test Laptop',
  sku: 'LAP-TEST-001',
  category: 'Electronics',
  supplierId: 'supplier-123',
  supplierName: 'Tech Supplier',
  costPrice: 400000,
  sellingPrice: 500000,
  branchStock: [
    {
      branchId: 'branch-123',
      branchName: 'Main Branch',
      quantity: 50,
      reservedQuantity: 5,
      reorderPoint: 10,
      maxStock: 100,
      location: 'A1-B2'
    }
  ],
  totalQuantity: 50,
  totalReserved: 5,
  lastRestockDate: '2024-01-01T00:00:00Z',
  expiryDate: '2025-12-31T00:00:00Z',
  batchNumber: 'BATCH-2024-001',
  isActive: true,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockInventoryItems: InventoryItem[] = [
  mockInventoryItem,
  { ...mockInventoryItem, _id: 'inventory-124', productName: 'Test Phone', sku: 'PHN-TEST-002' },
  { ...mockInventoryItem, _id: 'inventory-125', productName: 'Test Tablet', sku: 'TAB-TEST-003' }
]

const mockStockMovement: StockMovement = {
  _id: 'movement-123',
  itemId: 'inventory-123',
  productName: 'Test Laptop',
  branchId: 'branch-123',
  branchName: 'Main Branch',
  type: 'Stock In',
  quantity: 20,
  previousQuantity: 30,
  newQuantity: 50,
  reason: 'Purchase order received',
  reference: 'PO-2024-001',
  userId: 'user-123',
  userName: 'John Manager',
  createdAt: '2024-01-01T00:00:00Z'
}

const mockStockAdjustment: StockAdjustment = {
  _id: 'adjustment-123',
  itemId: 'inventory-123',
  productName: 'Test Laptop',
  branchId: 'branch-123',
  branchName: 'Main Branch',
  adjustmentQuantity: 5,
  reason: 'Damaged items',
  status: 'Pending',
  requestedBy: 'user-123',
  requestedByName: 'John Manager',
  createdAt: '2024-01-01T00:00:00Z'
}

const mockPurchaseOrder: PurchaseOrder = {
  _id: 'po-123',
  orderNumber: 'PO-2024-001',
  supplierId: 'supplier-123',
  supplierName: 'Tech Supplier',
  branchId: 'branch-123',
  branchName: 'Main Branch',
  items: [
    {
      itemId: 'inventory-123',
      productName: 'Test Laptop',
      orderedQuantity: 50,
      receivedQuantity: 0,
      unitCost: 400000
    }
  ],
  totalAmount: 20000000,
  status: 'Pending',
  orderDate: '2024-01-01T00:00:00Z',
  expectedDeliveryDate: '2024-01-15T00:00:00Z',
  createdBy: 'user-123',
  createdByName: 'John Manager',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

describe('InventoryStore - Integration Tests', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    
    // Reset store state
    useInventoryStore.setState({
      inventoryItems: [],
      selectedItem: null,
      isLoading: false,
      error: null,
      stockMovements: [],
      recentMovements: [],
      stockAdjustments: [],
      pendingAdjustments: [],
      lowStockAlerts: [],
      criticalStockItems: [],
      suppliers: [],
      purchaseOrders: [],
      pendingOrders: [],
      currentPage: 1,
      totalPages: 0,
      totalItems: 0,
      pageSize: 20,
      filters: {},
      searchQuery: ''
    })
  })

  describe('Fetch Inventory Integration', () => {
    test('should fetch inventory items successfully', async () => {
      const mockResponse: ApiResponse<InventoryItem[]> = {
        success: true,
        data: mockInventoryItems,
        pagination: {
          page: 1,
          limit: 20,
          total: 3,
          totalPages: 1
        }
      }

      mockInventoryService.getInventoryItems.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const fetchResult = await result.current.fetchInventoryItems()
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.inventoryItems).toEqual(mockInventoryItems)
      expect(result.current.totalItems).toBe(3)
      expect(result.current.totalPages).toBe(1)
      expect(result.current.currentPage).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockInventoryService.getInventoryItems).toHaveBeenCalledWith(
        { page: 1, limit: 20 },
        {}
      )
    })

    test('should handle fetch inventory failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Failed to fetch inventory items'
      }

      mockInventoryService.getInventoryItems.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const fetchResult = await result.current.fetchInventoryItems()
        expect(fetchResult.success).toBe(false)
        expect(fetchResult.error).toBe('Failed to fetch inventory items')
      })

      expect(result.current.inventoryItems).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Failed to fetch inventory items')
    })

    test('should fetch inventory item by ID successfully', async () => {
      const mockResponse: ApiResponse<InventoryItem> = {
        success: true,
        data: mockInventoryItem
      }

      mockInventoryService.getInventoryItemById.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const fetchResult = await result.current.fetchInventoryItemById('inventory-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.selectedItem).toEqual(mockInventoryItem)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockInventoryService.getInventoryItemById).toHaveBeenCalledWith('inventory-123')
    })
  })

  describe('Create Inventory Item Integration', () => {
    test('should create inventory item successfully', async () => {
      const createData = {
        productId: 'product-new',
        productName: 'New Product',
        sku: 'NEW-PROD-001',
        category: 'Electronics',
        supplierId: 'supplier-123',
        costPrice: 300000,
        sellingPrice: 400000,
        branchStock: [
          {
            branchId: 'branch-123',
            branchName: 'Main Branch',
            quantity: 30,
            reservedQuantity: 0,
            reorderPoint: 5,
            maxStock: 50,
            location: 'A1-C1'
          }
        ]
      }

      const newItem: InventoryItem = {
        ...mockInventoryItem,
        _id: 'inventory-new',
        ...createData,
        totalQuantity: 30,
        totalReserved: 0
      }

      const mockResponse: ApiResponse<InventoryItem> = {
        success: true,
        data: newItem
      }

      mockInventoryService.createInventoryItem.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const createResult = await result.current.createInventoryItem(createData)
        expect(createResult.success).toBe(true)
        expect(createResult.item).toEqual(newItem)
      })

      expect(result.current.inventoryItems).toContain(newItem)
      expect(result.current.totalItems).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockInventoryService.validateInventoryData).toHaveBeenCalledWith(createData)
      expect(mockInventoryService.createInventoryItem).toHaveBeenCalledWith(createData)
    })

    test('should handle validation error during create', async () => {
      const createData = {
        productId: '',
        productName: '',
        sku: 'INVALID',
        category: 'Electronics',
        supplierId: 'supplier-123',
        costPrice: -100,
        sellingPrice: 0
      }

      mockInventoryService.validateInventoryData.mockReturnValueOnce({
        isValid: false,
        errors: [
          { field: 'productId', message: 'Product ID is required' },
          { field: 'productName', message: 'Product name is required' },
          { field: 'costPrice', message: 'Cost price must be positive' }
        ]
      })

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const createResult = await result.current.createInventoryItem(createData)
        expect(createResult.success).toBe(false)
        expect(createResult.error).toBe('Product ID is required, Product name is required, Cost price must be positive')
      })

      expect(result.current.inventoryItems).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Product ID is required, Product name is required, Cost price must be positive')
      expect(mockInventoryService.createInventoryItem).not.toHaveBeenCalled()
    })
  })

  describe('Stock Management Integration', () => {
    test('should update stock successfully', async () => {
      // Set initial item in store
      useInventoryStore.setState({
        inventoryItems: [mockInventoryItem],
        selectedItem: mockInventoryItem
      })

      const updatedItem: InventoryItem = {
        ...mockInventoryItem,
        branchStock: [
          {
            ...mockInventoryItem.branchStock[0],
            quantity: 70
          }
        ],
        totalQuantity: 70
      }

      const mockResponse: ApiResponse<InventoryItem> = {
        success: true,
        data: updatedItem
      }

      mockInventoryService.updateStock.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const updateResult = await result.current.updateStock('inventory-123', 20, 'branch-123', 'Restock')
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.inventoryItems[0]).toEqual(updatedItem)
      expect(result.current.selectedItem).toEqual(updatedItem)

      expect(mockInventoryService.updateStock).toHaveBeenCalledWith('inventory-123', 20, 'branch-123', 'Restock')
    })

    test('should transfer stock successfully', async () => {
      // Set initial item in store
      useInventoryStore.setState({
        inventoryItems: [mockInventoryItem],
        selectedItem: mockInventoryItem
      })

      const updatedItem: InventoryItem = {
        ...mockInventoryItem,
        branchStock: [
          {
            ...mockInventoryItem.branchStock[0],
            quantity: 40
          },
          {
            branchId: 'branch-124',
            branchName: 'North Branch',
            quantity: 10,
            reservedQuantity: 0,
            reorderPoint: 5,
            maxStock: 30,
            location: 'B1-A1'
          }
        ]
      }

      const mockResponse: ApiResponse<InventoryItem> = {
        success: true,
        data: updatedItem
      }

      mockInventoryService.transferStock.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const transferResult = await result.current.transferStock('inventory-123', 'branch-123', 'branch-124', 10, 'Branch transfer')
        expect(transferResult.success).toBe(true)
      })

      expect(result.current.inventoryItems[0]).toEqual(updatedItem)
      expect(result.current.selectedItem).toEqual(updatedItem)

      expect(mockInventoryService.transferStock).toHaveBeenCalledWith('inventory-123', 'branch-123', 'branch-124', 10, 'Branch transfer')
    })

    test('should reserve stock successfully', async () => {
      // Set initial item in store
      useInventoryStore.setState({
        inventoryItems: [mockInventoryItem],
        selectedItem: mockInventoryItem
      })

      const updatedItem: InventoryItem = {
        ...mockInventoryItem,
        branchStock: [
          {
            ...mockInventoryItem.branchStock[0],
            reservedQuantity: 10
          }
        ],
        totalReserved: 10
      }

      const mockResponse: ApiResponse<InventoryItem> = {
        success: true,
        data: updatedItem
      }

      mockInventoryService.reserveStock.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const reserveResult = await result.current.reserveStock('inventory-123', 5, 'branch-123', 'order-123')
        expect(reserveResult.success).toBe(true)
      })

      expect(result.current.inventoryItems[0]).toEqual(updatedItem)
      expect(result.current.selectedItem).toEqual(updatedItem)

      expect(mockInventoryService.reserveStock).toHaveBeenCalledWith('inventory-123', 5, 'branch-123', 'order-123')
    })
  })

  describe('Stock Adjustments Integration', () => {
    test('should create stock adjustment successfully', async () => {
      const adjustmentData = {
        itemId: 'inventory-123',
        branchId: 'branch-123',
        adjustmentQuantity: -5,
        reason: 'Damaged items'
      }

      const mockResponse: ApiResponse<StockAdjustment> = {
        success: true,
        data: mockStockAdjustment
      }

      mockInventoryService.adjustStock.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const adjustResult = await result.current.adjustStock(adjustmentData)
        expect(adjustResult.success).toBe(true)
      })

      expect(result.current.stockAdjustments).toContain(mockStockAdjustment)

      expect(mockInventoryService.adjustStock).toHaveBeenCalledWith(adjustmentData)
    })

    test('should approve adjustment successfully', async () => {
      // Set initial adjustment in store
      useInventoryStore.setState({
        stockAdjustments: [mockStockAdjustment],
        pendingAdjustments: [mockStockAdjustment],
        inventoryItems: [mockInventoryItem]
      })

      const approvedAdjustment: StockAdjustment = {
        ...mockStockAdjustment,
        status: 'Approved',
        approvedBy: 'manager-123',
        approvedByName: 'Manager Name',
        approvedAt: '2024-01-02T00:00:00Z'
      }

      const mockResponse: ApiResponse<StockAdjustment> = {
        success: true,
        data: approvedAdjustment
      }

      mockInventoryService.approveAdjustment.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const approveResult = await result.current.approveAdjustment('adjustment-123')
        expect(approveResult.success).toBe(true)
      })

      expect(result.current.stockAdjustments[0]).toEqual(approvedAdjustment)
      expect(result.current.pendingAdjustments).toHaveLength(0)

      expect(mockInventoryService.approveAdjustment).toHaveBeenCalledWith('adjustment-123')
    })
  })

  describe('Purchase Orders Integration', () => {
    test('should create purchase order successfully', async () => {
      const orderData = {
        supplierId: 'supplier-123',
        branchId: 'branch-123',
        items: [
          {
            itemId: 'inventory-123',
            orderedQuantity: 50,
            unitCost: 400000
          }
        ],
        expectedDeliveryDate: '2024-01-15T00:00:00Z'
      }

      const mockResponse: ApiResponse<PurchaseOrder> = {
        success: true,
        data: mockPurchaseOrder
      }

      mockInventoryService.createPurchaseOrder.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const createResult = await result.current.createPurchaseOrder(orderData)
        expect(createResult.success).toBe(true)
        expect(createResult.order).toEqual(mockPurchaseOrder)
      })

      expect(result.current.purchaseOrders).toContain(mockPurchaseOrder)
      expect(result.current.pendingOrders).toContain(mockPurchaseOrder)

      expect(mockInventoryService.createPurchaseOrder).toHaveBeenCalledWith(orderData)
    })

    test('should receive purchase order successfully', async () => {
      // Set initial purchase order and inventory item in store
      useInventoryStore.setState({
        purchaseOrders: [mockPurchaseOrder],
        pendingOrders: [mockPurchaseOrder],
        inventoryItems: [mockInventoryItem]
      })

      const receivedItems = [
        {
          itemId: 'inventory-123',
          branchId: 'branch-123',
          receivedQuantity: 50
        }
      ]

      const receivedOrder: PurchaseOrder = {
        ...mockPurchaseOrder,
        status: 'Received',
        receivedDate: '2024-01-10T00:00:00Z',
        items: [
          {
            ...mockPurchaseOrder.items[0],
            receivedQuantity: 50
          }
        ]
      }

      const mockResponse: ApiResponse<PurchaseOrder> = {
        success: true,
        data: receivedOrder
      }

      mockInventoryService.receivePurchaseOrder.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const receiveResult = await result.current.receivePurchaseOrder('po-123', receivedItems)
        expect(receiveResult.success).toBe(true)
      })

      expect(result.current.purchaseOrders[0]).toEqual(receivedOrder)
      expect(result.current.pendingOrders).toHaveLength(0)

      // Check that inventory was updated
      expect(result.current.inventoryItems[0].branchStock[0].quantity).toBe(100) // 50 + 50

      expect(mockInventoryService.receivePurchaseOrder).toHaveBeenCalledWith('po-123', receivedItems)
    })
  })

  describe('Inventory Queries Integration', () => {
    test('should fetch inventory by branch successfully', async () => {
      const branchInventory = [mockInventoryItem]
      
      const mockResponse: ApiResponse<InventoryItem[]> = {
        success: true,
        data: branchInventory,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockInventoryService.getInventoryByBranch.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const fetchResult = await result.current.fetchInventoryByBranch('branch-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.inventoryItems).toEqual(branchInventory)
      expect(result.current.filters.branchId).toBe('branch-123')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockInventoryService.getInventoryByBranch).toHaveBeenCalledWith(
        'branch-123',
        { page: 1, limit: 20 }
      )
    })

    test('should fetch inventory by product successfully', async () => {
      const mockResponse: ApiResponse<InventoryItem> = {
        success: true,
        data: mockInventoryItem
      }

      mockInventoryService.getInventoryByProduct.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const fetchResult = await result.current.fetchInventoryByProduct('product-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.selectedItem).toEqual(mockInventoryItem)

      expect(mockInventoryService.getInventoryByProduct).toHaveBeenCalledWith('product-123')
    })
  })

  describe('Search Integration', () => {
    test('should search inventory successfully', async () => {
      const searchResults = [mockInventoryItem]
      
      const mockResponse: ApiResponse<InventoryItem[]> = {
        success: true,
        data: searchResults,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockInventoryService.searchInventory.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useInventoryStore())

      await act(async () => {
        const searchResult = await result.current.searchInventory('laptop')
        expect(searchResult.success).toBe(true)
      })

      expect(result.current.inventoryItems).toEqual(searchResults)
      expect(result.current.searchQuery).toBe('laptop')
      expect(result.current.filters.search).toBe('laptop')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockInventoryService.searchInventory).toHaveBeenCalledWith(
        'laptop',
        { page: 1, limit: 20 },
        {}
      )
    })
  })
})
