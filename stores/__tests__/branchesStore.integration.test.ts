// stores/__tests__/branchesStore.integration.test.ts - Branches store integration tests

import { renderHook, act } from '@testing-library/react'
import { useBranchesStore } from '../branchesStore'
import type { Branch, BranchStatus, ApiResponse, CreateBranchData, UpdateBranchData } from '@/types/frontend'

// Mock the services module
jest.mock('@/services/frontend', () => ({
  branchService: {
    getBranchStatuses: jest.fn(() => ['Active', 'Inactive', 'Maintenance']),
    getAvailableRegions: jest.fn(() => ['Northern', 'Central', 'Southern']),
    validateBranchData: jest.fn(() => ({ isValid: true, errors: [] })),
    calculateDistance: jest.fn(() => 10.5),
    findNearestBranches: jest.fn(() => []),
    getBranchOperatingHours: jest.fn(() => ({ open: '08:00', close: '18:00', isOpen: true })),
    isBranchOpen: jest.fn(() => true),
    formatBranchAddress: jest.fn(() => '123 Main St, Lilongwe, Central'),
    formatBranchContact: jest.fn(() => '+265123456789 | <EMAIL>'),
    getBranches: jest.fn(),
    getBranchById: jest.fn(),
    createBranch: jest.fn(),
    updateBranch: jest.fn(),
    deleteBranch: jest.fn(),
    updateBranchStatus: jest.fn(),
    deactivateBranch: jest.fn(),
    setMaintenanceMode: jest.fn(),
    getTopPerformingBranches: jest.fn(),
    getRecentBranches: jest.fn(),
    getBranchPerformance: jest.fn(),
    getBranchAnalytics: jest.fn(),
    getBranchesByRegion: jest.fn(),
    getBranchesByStatus: jest.fn(),
    getBranchesByManager: jest.fn(),
    searchBranches: jest.fn()
  },
  handleServiceError: jest.fn((error: any) => ({
    message: error?.message || 'Unknown error',
    type: 'unknown'
  }))
}))

// Import the mocked module
const { branchService } = require('@/services/frontend')
const mockBranchService = branchService as jest.Mocked<typeof branchService>

// Mock branch data
const mockBranch: Branch = {
  _id: 'branch-123',
  name: 'Main Branch',
  code: 'MB001',
  address: {
    street: '123 Main Street',
    city: 'Lilongwe',
    region: 'Central',
    country: 'Malawi',
    postalCode: '12345'
  },
  coordinates: {
    latitude: -13.9626,
    longitude: 33.7741
  },
  phone: '+265123456789',
  email: '<EMAIL>',
  managerId: 'manager-123',
  managerName: 'John Manager',
  status: 'Active',
  operatingHours: {
    monday: { open: '08:00', close: '18:00' },
    tuesday: { open: '08:00', close: '18:00' },
    wednesday: { open: '08:00', close: '18:00' },
    thursday: { open: '08:00', close: '18:00' },
    friday: { open: '08:00', close: '18:00' },
    saturday: { open: '09:00', close: '17:00' },
    sunday: { open: '09:00', close: '17:00' }
  },
  services: ['Sales', 'Repairs', 'Support'],
  capacity: 50,
  currentStaff: 12,
  isMainBranch: true,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
}

const mockBranches: Branch[] = [
  mockBranch,
  { ...mockBranch, _id: 'branch-124', name: 'North Branch', code: 'NB002', status: 'Inactive' },
  { ...mockBranch, _id: 'branch-125', name: 'South Branch', code: 'SB003', status: 'Maintenance' }
]

describe('BranchesStore - Integration Tests', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks()
    
    // Reset store state
    useBranchesStore.setState({
      branches: [],
      selectedBranch: null,
      isLoading: false,
      error: null,
      currentPage: 1,
      totalPages: 0,
      totalBranches: 0,
      pageSize: 20,
      filters: {},
      searchQuery: '',
      activeBranches: [],
      inactiveBranches: [],
      maintenanceBranches: [],
      topPerformingBranches: [],
      recentBranches: [],
      branchPerformanceData: {}
    })
  })

  describe('Fetch Branches Integration', () => {
    test('should fetch branches successfully', async () => {
      const mockResponse: ApiResponse<Branch[]> = {
        success: true,
        data: mockBranches,
        pagination: {
          page: 1,
          limit: 20,
          total: 3,
          totalPages: 1
        }
      }

      mockBranchService.getBranches.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const fetchResult = await result.current.fetchBranches()
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.branches).toEqual(mockBranches)
      expect(result.current.totalBranches).toBe(3)
      expect(result.current.totalPages).toBe(1)
      expect(result.current.currentPage).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      // Check status groups are updated
      expect(result.current.activeBranches).toHaveLength(1)
      expect(result.current.inactiveBranches).toHaveLength(1)
      expect(result.current.maintenanceBranches).toHaveLength(1)

      expect(mockBranchService.getBranches).toHaveBeenCalledWith(
        { page: 1, limit: 20 },
        {}
      )
    })

    test('should handle fetch branches failure', async () => {
      const mockResponse: ApiResponse<never> = {
        success: false,
        error: 'Failed to fetch branches'
      }

      mockBranchService.getBranches.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const fetchResult = await result.current.fetchBranches()
        expect(fetchResult.success).toBe(false)
        expect(fetchResult.error).toBe('Failed to fetch branches')
      })

      expect(result.current.branches).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Failed to fetch branches')
    })

    test('should fetch branch by ID successfully', async () => {
      const mockResponse: ApiResponse<Branch> = {
        success: true,
        data: mockBranch
      }

      mockBranchService.getBranchById.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const fetchResult = await result.current.fetchBranchById('branch-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.selectedBranch).toEqual(mockBranch)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockBranchService.getBranchById).toHaveBeenCalledWith('branch-123')
    })
  })

  describe('Create Branch Integration', () => {
    test('should create branch successfully', async () => {
      const createData: CreateBranchData = {
        name: 'New Branch',
        code: 'NB004',
        address: {
          street: '456 New Street',
          city: 'Blantyre',
          region: 'Southern',
          country: 'Malawi',
          postalCode: '54321'
        },
        coordinates: {
          latitude: -15.7861,
          longitude: 35.0058
        },
        phone: '+265987654321',
        email: '<EMAIL>',
        managerId: 'manager-456',
        operatingHours: {
          monday: { open: '08:00', close: '18:00' },
          tuesday: { open: '08:00', close: '18:00' },
          wednesday: { open: '08:00', close: '18:00' },
          thursday: { open: '08:00', close: '18:00' },
          friday: { open: '08:00', close: '18:00' },
          saturday: { open: '09:00', close: '17:00' },
          sunday: { open: '09:00', close: '17:00' }
        },
        services: ['Sales', 'Support'],
        capacity: 30
      }

      const newBranch: Branch = {
        ...mockBranch,
        _id: 'branch-new',
        ...createData,
        managerName: 'New Manager',
        status: 'Active',
        currentStaff: 0,
        isMainBranch: false
      }

      const mockResponse: ApiResponse<Branch> = {
        success: true,
        data: newBranch
      }

      mockBranchService.createBranch.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const createResult = await result.current.createBranch(createData)
        expect(createResult.success).toBe(true)
        expect(createResult.branch).toEqual(newBranch)
      })

      expect(result.current.branches).toContain(newBranch)
      expect(result.current.totalBranches).toBe(1)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockBranchService.validateBranchData).toHaveBeenCalledWith(createData)
      expect(mockBranchService.createBranch).toHaveBeenCalledWith(createData)
    })

    test('should handle validation error during create', async () => {
      const createData: CreateBranchData = {
        name: '',
        code: 'INVALID',
        address: {
          street: '456 New Street',
          city: 'Blantyre',
          region: 'Southern',
          country: 'Malawi',
          postalCode: '54321'
        },
        coordinates: {
          latitude: -15.7861,
          longitude: 35.0058
        },
        phone: 'invalid-phone',
        email: 'invalid-email',
        managerId: 'manager-456',
        operatingHours: {
          monday: { open: '08:00', close: '18:00' },
          tuesday: { open: '08:00', close: '18:00' },
          wednesday: { open: '08:00', close: '18:00' },
          thursday: { open: '08:00', close: '18:00' },
          friday: { open: '08:00', close: '18:00' },
          saturday: { open: '09:00', close: '17:00' },
          sunday: { open: '09:00', close: '17:00' }
        },
        services: [],
        capacity: 0
      }

      mockBranchService.validateBranchData.mockReturnValueOnce({
        isValid: false,
        errors: [
          { field: 'name', message: 'Branch name is required' },
          { field: 'phone', message: 'Invalid phone format' },
          { field: 'email', message: 'Invalid email format' }
        ]
      })

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const createResult = await result.current.createBranch(createData)
        expect(createResult.success).toBe(false)
        expect(createResult.error).toBe('Branch name is required, Invalid phone format, Invalid email format')
      })

      expect(result.current.branches).toEqual([])
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBe('Branch name is required, Invalid phone format, Invalid email format')
      expect(mockBranchService.createBranch).not.toHaveBeenCalled()
    })
  })

  describe('Update Branch Integration', () => {
    test('should update branch successfully', async () => {
      // Set initial branch in store
      useBranchesStore.setState({
        branches: [mockBranch],
        selectedBranch: mockBranch
      })

      const updateData: UpdateBranchData = {
        name: 'Updated Main Branch',
        phone: '+265999888777',
        capacity: 60
      }

      const updatedBranch: Branch = {
        ...mockBranch,
        ...updateData
      }

      const mockResponse: ApiResponse<Branch> = {
        success: true,
        data: updatedBranch
      }

      mockBranchService.updateBranch.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const updateResult = await result.current.updateBranch('branch-123', updateData)
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.branches[0]).toEqual(updatedBranch)
      expect(result.current.selectedBranch).toEqual(updatedBranch)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockBranchService.validateBranchData).toHaveBeenCalledWith(updateData)
      expect(mockBranchService.updateBranch).toHaveBeenCalledWith('branch-123', updateData)
    })
  })

  describe('Delete Branch Integration', () => {
    test('should delete branch successfully', async () => {
      // Set initial branches in store
      useBranchesStore.setState({
        branches: mockBranches,
        selectedBranch: mockBranch,
        totalBranches: 3
      })

      const mockResponse: ApiResponse<boolean> = {
        success: true,
        data: true
      }

      mockBranchService.deleteBranch.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const deleteResult = await result.current.deleteBranch('branch-123')
        expect(deleteResult.success).toBe(true)
      })

      expect(result.current.branches).not.toContain(mockBranch)
      expect(result.current.branches).toHaveLength(2)
      expect(result.current.selectedBranch).toBeNull()
      expect(result.current.totalBranches).toBe(2)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockBranchService.deleteBranch).toHaveBeenCalledWith('branch-123')
    })
  })

  describe('Branch Status Management Integration', () => {
    test('should update branch status successfully', async () => {
      // Set initial branch in store
      useBranchesStore.setState({
        branches: [mockBranch],
        selectedBranch: mockBranch
      })

      const updatedBranch: Branch = {
        ...mockBranch,
        status: 'Maintenance'
      }

      const mockResponse: ApiResponse<Branch> = {
        success: true,
        data: updatedBranch
      }

      mockBranchService.updateBranchStatus.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const updateResult = await result.current.updateBranchStatus('branch-123', 'Maintenance')
        expect(updateResult.success).toBe(true)
      })

      expect(result.current.branches[0]).toEqual(updatedBranch)
      expect(result.current.selectedBranch).toEqual(updatedBranch)

      expect(mockBranchService.updateBranchStatus).toHaveBeenCalledWith('branch-123', 'Maintenance')
    })

    test('should activate branch successfully', async () => {
      // Set initial branch in store
      useBranchesStore.setState({
        branches: [{ ...mockBranch, status: 'Inactive' }],
        selectedBranch: { ...mockBranch, status: 'Inactive' }
      })

      const activatedBranch: Branch = {
        ...mockBranch,
        status: 'Active'
      }

      const mockResponse: ApiResponse<Branch> = {
        success: true,
        data: activatedBranch
      }

      mockBranchService.updateBranchStatus.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const activateResult = await result.current.activateBranch('branch-123')
        expect(activateResult.success).toBe(true)
      })

      expect(result.current.branches[0]).toEqual(activatedBranch)
      expect(result.current.selectedBranch).toEqual(activatedBranch)

      expect(mockBranchService.updateBranchStatus).toHaveBeenCalledWith('branch-123', 'Active')
    })

    test('should deactivate branch successfully', async () => {
      // Set initial branch in store
      useBranchesStore.setState({
        branches: [mockBranch],
        selectedBranch: mockBranch
      })

      const deactivatedBranch: Branch = {
        ...mockBranch,
        status: 'Inactive'
      }

      const mockResponse: ApiResponse<Branch> = {
        success: true,
        data: deactivatedBranch
      }

      mockBranchService.deactivateBranch.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const deactivateResult = await result.current.deactivateBranch('branch-123', 'Maintenance required')
        expect(deactivateResult.success).toBe(true)
      })

      expect(result.current.branches[0]).toEqual(deactivatedBranch)
      expect(result.current.selectedBranch).toEqual(deactivatedBranch)

      expect(mockBranchService.deactivateBranch).toHaveBeenCalledWith('branch-123', 'Maintenance required')
    })

    test('should set maintenance mode successfully', async () => {
      // Set initial branch in store
      useBranchesStore.setState({
        branches: [mockBranch],
        selectedBranch: mockBranch
      })

      const maintenanceBranch: Branch = {
        ...mockBranch,
        status: 'Maintenance'
      }

      const mockResponse: ApiResponse<Branch> = {
        success: true,
        data: maintenanceBranch
      }

      mockBranchService.setMaintenanceMode.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const maintenanceResult = await result.current.setMaintenanceMode('branch-123', 'System upgrade')
        expect(maintenanceResult.success).toBe(true)
      })

      expect(result.current.branches[0]).toEqual(maintenanceBranch)
      expect(result.current.selectedBranch).toEqual(maintenanceBranch)

      expect(mockBranchService.setMaintenanceMode).toHaveBeenCalledWith('branch-123', 'System upgrade')
    })
  })

  describe('Branch Analytics Integration', () => {
    test('should fetch top performing branches successfully', async () => {
      const topBranches = [mockBranches[0]]
      
      const mockResponse: ApiResponse<Branch[]> = {
        success: true,
        data: topBranches
      }

      mockBranchService.getTopPerformingBranches.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const fetchResult = await result.current.fetchTopPerformingBranches(5, 'revenue')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.topPerformingBranches).toEqual(topBranches)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockBranchService.getTopPerformingBranches).toHaveBeenCalledWith(5, 'revenue')
    })

    test('should fetch recent branches successfully', async () => {
      const recentBranches = [mockBranch]
      
      const mockResponse: ApiResponse<Branch[]> = {
        success: true,
        data: recentBranches
      }

      mockBranchService.getRecentBranches.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const fetchResult = await result.current.fetchRecentBranches(10)
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.recentBranches).toEqual(recentBranches)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockBranchService.getRecentBranches).toHaveBeenCalledWith(10)
    })

    test('should fetch branch performance successfully', async () => {
      const performanceData = {
        revenue: 1000000,
        orders: 150,
        customers: 75
      }
      
      const mockResponse: ApiResponse<any> = {
        success: true,
        data: performanceData
      }

      mockBranchService.getBranchPerformance.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const fetchResult = await result.current.fetchBranchPerformance('branch-123', 'month')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.branchPerformanceData['branch-123']).toEqual(performanceData)

      expect(mockBranchService.getBranchPerformance).toHaveBeenCalledWith('branch-123', 'month')
    })
  })

  describe('Branch Queries Integration', () => {
    test('should fetch branches by region successfully', async () => {
      const centralBranches = mockBranches.filter(b => b.address.region === 'Central')
      
      const mockResponse: ApiResponse<Branch[]> = {
        success: true,
        data: centralBranches,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockBranchService.getBranchesByRegion.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const fetchResult = await result.current.fetchBranchesByRegion('Central')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.branches).toEqual(centralBranches)
      expect(result.current.filters.region).toBe('Central')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockBranchService.getBranchesByRegion).toHaveBeenCalledWith(
        'Central',
        { page: 1, limit: 20 }
      )
    })

    test('should fetch branches by status successfully', async () => {
      const activeBranches = mockBranches.filter(b => b.status === 'Active')
      
      const mockResponse: ApiResponse<Branch[]> = {
        success: true,
        data: activeBranches,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockBranchService.getBranchesByStatus.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const fetchResult = await result.current.fetchBranchesByStatus('Active')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.branches).toEqual(activeBranches)
      expect(result.current.filters.status).toBe('Active')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockBranchService.getBranchesByStatus).toHaveBeenCalledWith(
        'Active',
        { page: 1, limit: 20 }
      )
    })

    test('should fetch branches by manager successfully', async () => {
      const managerBranches = [mockBranch]
      
      const mockResponse: ApiResponse<Branch[]> = {
        success: true,
        data: managerBranches,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockBranchService.getBranchesByManager.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const fetchResult = await result.current.fetchBranchesByManager('manager-123')
        expect(fetchResult.success).toBe(true)
      })

      expect(result.current.branches).toEqual(managerBranches)
      expect(result.current.filters.managerId).toBe('manager-123')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockBranchService.getBranchesByManager).toHaveBeenCalledWith(
        'manager-123',
        { page: 1, limit: 20 }
      )
    })
  })

  describe('Search Integration', () => {
    test('should search branches successfully', async () => {
      const searchResults = [mockBranch]
      
      const mockResponse: ApiResponse<Branch[]> = {
        success: true,
        data: searchResults,
        pagination: {
          page: 1,
          limit: 20,
          total: 1,
          totalPages: 1
        }
      }

      mockBranchService.searchBranches.mockResolvedValueOnce(mockResponse)

      const { result } = renderHook(() => useBranchesStore())

      await act(async () => {
        const searchResult = await result.current.searchBranches('main branch')
        expect(searchResult.success).toBe(true)
      })

      expect(result.current.branches).toEqual(searchResults)
      expect(result.current.searchQuery).toBe('main branch')
      expect(result.current.filters.search).toBe('main branch')
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()

      expect(mockBranchService.searchBranches).toHaveBeenCalledWith(
        'main branch',
        { page: 1, limit: 20 },
        {}
      )
    })
  })
})
