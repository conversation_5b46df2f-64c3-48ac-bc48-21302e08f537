// stores/reportsStore.ts - Reports and analytics management state with Zustand

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { reportsService, handleServiceError } from '@/services/frontend'
import type { 
  Report, 
  ReportType,
  ReportStatus,
  ReportFilters,
  DashboardData,
  SalesReport,
  InventoryReport,
  CustomerReport,
  BranchReport,
  FinancialReport,
  CustomReport,
  ReportSchedule,
  ExportFormat,
  DateRange,
  PaginationParams,
  ApiResponse 
} from '@/types/frontend'

// ============================================================================
// Reports Store Types
// ============================================================================

export interface ReportsState {
  // Reports State
  reports: Report[]
  selectedReport: Report | null
  isLoading: boolean
  error: string | null
  
  // Dashboard Data
  dashboardData: DashboardData | null
  dashboardLoading: boolean
  lastUpdated: string | null
  
  // Report Types
  salesReports: SalesReport[]
  inventoryReports: InventoryReport[]
  customerReports: CustomerReport[]
  branchReports: BranchReport[]
  financialReports: FinancialReport[]
  customReports: CustomReport[]
  
  // Report Schedules
  reportSchedules: ReportSchedule[]
  activeSchedules: ReportSchedule[]
  
  // Pagination & Filtering
  currentPage: number
  totalPages: number
  totalReports: number
  pageSize: number
  filters: ReportFilters
  searchQuery: string
  
  // Actions - Reports CRUD
  fetchReports: (pagination?: PaginationParams, filters?: ReportFilters) => Promise<{ success: boolean; error?: string }>
  fetchReportById: (reportId: string) => Promise<{ success: boolean; error?: string }>
  createReport: (reportData: Partial<Report>) => Promise<{ success: boolean; error?: string; report?: Report }>
  updateReport: (reportId: string, updates: Partial<Report>) => Promise<{ success: boolean; error?: string }>
  deleteReport: (reportId: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Dashboard
  fetchDashboardData: (dateRange?: DateRange, branchId?: string) => Promise<{ success: boolean; error?: string }>
  refreshDashboard: () => Promise<{ success: boolean; error?: string }>
  
  // Actions - Report Generation
  generateReport: (reportType: ReportType, filters: any, format?: ExportFormat) => Promise<{ success: boolean; error?: string; reportId?: string }>
  generateSalesReport: (dateRange: DateRange, branchId?: string, format?: ExportFormat) => Promise<{ success: boolean; error?: string }>
  generateInventoryReport: (branchId?: string, includeMovements?: boolean, format?: ExportFormat) => Promise<{ success: boolean; error?: string }>
  generateCustomerReport: (reportType: string, dateRange?: DateRange, format?: ExportFormat) => Promise<{ success: boolean; error?: string }>
  generateBranchReport: (branchId?: string, dateRange?: DateRange, format?: ExportFormat) => Promise<{ success: boolean; error?: string }>
  generateFinancialReport: (reportType: string, dateRange: DateRange, format?: ExportFormat) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Custom Reports
  createCustomReport: (reportData: Partial<CustomReport>) => Promise<{ success: boolean; error?: string; report?: CustomReport }>
  updateCustomReport: (reportId: string, updates: Partial<CustomReport>) => Promise<{ success: boolean; error?: string }>
  executeCustomReport: (reportId: string, parameters?: any) => Promise<{ success: boolean; error?: string; data?: any }>
  
  // Actions - Report Scheduling
  fetchReportSchedules: () => Promise<{ success: boolean; error?: string }>
  createReportSchedule: (scheduleData: Partial<ReportSchedule>) => Promise<{ success: boolean; error?: string; schedule?: ReportSchedule }>
  updateReportSchedule: (scheduleId: string, updates: Partial<ReportSchedule>) => Promise<{ success: boolean; error?: string }>
  deleteReportSchedule: (scheduleId: string) => Promise<{ success: boolean; error?: string }>
  toggleSchedule: (scheduleId: string, active: boolean) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Report Export
  exportReport: (reportId: string, format: ExportFormat) => Promise<{ success: boolean; error?: string; downloadUrl?: string }>
  downloadReport: (reportId: string, format: ExportFormat) => Promise<{ success: boolean; error?: string }>
  shareReport: (reportId: string, recipients: string[], message?: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Report Analytics
  fetchReportsByType: (reportType: ReportType, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  fetchRecentReports: (limit?: number, userId?: string) => Promise<{ success: boolean; error?: string }>
  fetchPopularReports: (limit?: number, period?: string) => Promise<{ success: boolean; error?: string }>
  getReportUsageStats: (reportId: string, period?: string) => Promise<{ success: boolean; error?: string; stats?: any }>
  
  // Actions - Search & Filtering
  searchReports: (query: string, pagination?: PaginationParams, filters?: Omit<ReportFilters, 'search'>) => Promise<{ success: boolean; error?: string }>
  setFilters: (filters: Partial<ReportFilters>) => void
  clearFilters: () => void
  setSearchQuery: (query: string) => void
  
  // Actions - Pagination
  setCurrentPage: (page: number) => void
  setPageSize: (size: number) => void
  
  // Actions - UI State
  setSelectedReport: (report: Report | null) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  
  // Utility Methods
  getReportsByType: (reportType: ReportType) => Report[]
  getReportsByStatus: (status: ReportStatus) => Report[]
  getReportsByDateRange: (startDate: string, endDate: string) => Report[]
  getActiveSchedules: () => ReportSchedule[]
  getSchedulesByFrequency: (frequency: string) => ReportSchedule[]
  calculateReportMetrics: (reports: Report[]) => { totalReports: number; completedReports: number; failedReports: number; averageGenerationTime: number }
  formatReportSize: (sizeInBytes: number) => string
  getReportTypeIcon: (reportType: ReportType) => string
  getReportStatusColor: (status: ReportStatus) => string
  getExportFormatIcon: (format: ExportFormat) => string
  isReportExpired: (report: Report, expiryDays?: number) => boolean
  canUserAccessReport: (report: Report, userId: string) => boolean
  getReportSummary: (report: Report) => string
  formatDateRange: (dateRange: DateRange) => string
}

// ============================================================================
// Reports Store Implementation
// ============================================================================

export const useReportsStore = create<ReportsState>()(
  immer((set, get) => ({
    // Initial state
    reports: [],
    selectedReport: null,
    isLoading: false,
    error: null,
    
    // Dashboard Data
    dashboardData: null,
    dashboardLoading: false,
    lastUpdated: null,
    
    // Report Types
    salesReports: [],
    inventoryReports: [],
    customerReports: [],
    branchReports: [],
    financialReports: [],
    customReports: [],
    
    // Report Schedules
    reportSchedules: [],
    activeSchedules: [],
    
    // Pagination & Filtering
    currentPage: 1,
    totalPages: 0,
    totalReports: 0,
    pageSize: 20,
    filters: {},
    searchQuery: '',

    // Actions - Reports CRUD
    fetchReports: async (pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await reportsService.getReports(pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.reports = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalReports = response.pagination?.total || response.data!.length
            state.filters = filters
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch reports'
          })

          return { success: false, error: response.error || 'Failed to fetch reports' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchReportById: async (reportId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await reportsService.getReportById(reportId)

        if (response.success && response.data) {
          set((state) => {
            state.selectedReport = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch report'
          })

          return { success: false, error: response.error || 'Failed to fetch report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    createReport: async (reportData: Partial<Report>) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate report data
        const validation = reportsService.validateReportData(reportData)
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        const response = await reportsService.createReport(reportData)

        if (response.success && response.data) {
          set((state) => {
            state.reports.unshift(response.data!)
            state.totalReports += 1
            state.isLoading = false
            state.error = null
          })

          return { success: true, report: response.data }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to create report'
          })

          return { success: false, error: response.error || 'Failed to create report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    updateReport: async (reportId: string, updates: Partial<Report>) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate report data
        const validation = reportsService.validateReportData(updates)
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        const response = await reportsService.updateReport(reportId, updates)

        if (response.success && response.data) {
          set((state) => {
            const index = state.reports.findIndex(r => r._id === reportId)
            if (index !== -1) {
              state.reports[index] = response.data!
            }
            
            if (state.selectedReport?._id === reportId) {
              state.selectedReport = response.data!
            }
            
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to update report'
          })

          return { success: false, error: response.error || 'Failed to update report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    deleteReport: async (reportId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await reportsService.deleteReport(reportId)

        if (response.success) {
          set((state) => {
            state.reports = state.reports.filter(r => r._id !== reportId)
            
            if (state.selectedReport?._id === reportId) {
              state.selectedReport = null
            }
            
            state.totalReports = Math.max(0, state.totalReports - 1)
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to delete report'
          })

          return { success: false, error: response.error || 'Failed to delete report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Dashboard
    fetchDashboardData: async (dateRange?: DateRange, branchId?: string) => {
      set((state) => {
        state.dashboardLoading = true
        state.error = null
      })

      try {
        const response = await reportsService.getDashboardData(dateRange, branchId)

        if (response.success && response.data) {
          set((state) => {
            state.dashboardData = response.data!
            state.lastUpdated = new Date().toISOString()
            state.dashboardLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.dashboardLoading = false
            state.error = response.error || 'Failed to fetch dashboard data'
          })

          return { success: false, error: response.error || 'Failed to fetch dashboard data' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.dashboardLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    refreshDashboard: async () => {
      return get().fetchDashboardData()
    },

    // Actions - Report Generation
    generateReport: async (reportType: ReportType, filters: any, format: ExportFormat = 'PDF') => {
      try {
        const response = await reportsService.generateReport(reportType, filters, format)

        if (response.success && response.data) {
          set((state) => {
            state.reports.unshift(response.data!)
            state.totalReports += 1
          })

          return { success: true, reportId: response.data!._id }
        } else {
          return { success: false, error: response.error || 'Failed to generate report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    generateSalesReport: async (dateRange: DateRange, branchId?: string, format: ExportFormat = 'PDF') => {
      try {
        const response = await reportsService.generateSalesReport(dateRange, branchId, format)

        if (response.success && response.data) {
          set((state) => {
            state.salesReports.unshift(response.data!)
            state.reports.unshift(response.data!)
            state.totalReports += 1
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to generate sales report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    generateInventoryReport: async (branchId?: string, includeMovements = false, format: ExportFormat = 'PDF') => {
      try {
        const response = await reportsService.generateInventoryReport(branchId, includeMovements, format)

        if (response.success && response.data) {
          set((state) => {
            state.inventoryReports.unshift(response.data!)
            state.reports.unshift(response.data!)
            state.totalReports += 1
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to generate inventory report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    generateCustomerReport: async (reportType: string, dateRange?: DateRange, format: ExportFormat = 'PDF') => {
      try {
        const response = await reportsService.generateCustomerReport(reportType, dateRange, format)

        if (response.success && response.data) {
          set((state) => {
            state.customerReports.unshift(response.data!)
            state.reports.unshift(response.data!)
            state.totalReports += 1
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to generate customer report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    generateBranchReport: async (branchId?: string, dateRange?: DateRange, format: ExportFormat = 'PDF') => {
      try {
        const response = await reportsService.generateBranchReport(branchId, dateRange, format)

        if (response.success && response.data) {
          set((state) => {
            state.branchReports.unshift(response.data!)
            state.reports.unshift(response.data!)
            state.totalReports += 1
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to generate branch report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    generateFinancialReport: async (reportType: string, dateRange: DateRange, format: ExportFormat = 'PDF') => {
      try {
        const response = await reportsService.generateFinancialReport(reportType, dateRange, format)

        if (response.success && response.data) {
          set((state) => {
            state.financialReports.unshift(response.data!)
            state.reports.unshift(response.data!)
            state.totalReports += 1
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to generate financial report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Custom Reports
    createCustomReport: async (reportData: Partial<CustomReport>) => {
      try {
        const response = await reportsService.createCustomReport(reportData)

        if (response.success && response.data) {
          set((state) => {
            state.customReports.unshift(response.data!)
          })

          return { success: true, report: response.data }
        } else {
          return { success: false, error: response.error || 'Failed to create custom report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    updateCustomReport: async (reportId: string, updates: Partial<CustomReport>) => {
      try {
        const response = await reportsService.updateCustomReport(reportId, updates)

        if (response.success && response.data) {
          set((state) => {
            const index = state.customReports.findIndex(r => r._id === reportId)
            if (index !== -1) {
              state.customReports[index] = response.data!
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update custom report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    executeCustomReport: async (reportId: string, parameters?: any) => {
      try {
        const response = await reportsService.executeCustomReport(reportId, parameters)

        if (response.success) {
          return { success: true, data: response.data }
        } else {
          return { success: false, error: response.error || 'Failed to execute custom report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Report Scheduling
    fetchReportSchedules: async () => {
      try {
        const response = await reportsService.getReportSchedules()

        if (response.success && response.data) {
          set((state) => {
            state.reportSchedules = response.data!
            state.activeSchedules = response.data!.filter(schedule => schedule.isActive)
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to fetch report schedules' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    createReportSchedule: async (scheduleData: Partial<ReportSchedule>) => {
      try {
        const response = await reportsService.createReportSchedule(scheduleData)

        if (response.success && response.data) {
          set((state) => {
            state.reportSchedules.unshift(response.data!)
            if (response.data!.isActive) {
              state.activeSchedules.unshift(response.data!)
            }
          })

          return { success: true, schedule: response.data }
        } else {
          return { success: false, error: response.error || 'Failed to create report schedule' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    updateReportSchedule: async (scheduleId: string, updates: Partial<ReportSchedule>) => {
      try {
        const response = await reportsService.updateReportSchedule(scheduleId, updates)

        if (response.success && response.data) {
          set((state) => {
            const index = state.reportSchedules.findIndex(s => s._id === scheduleId)
            if (index !== -1) {
              state.reportSchedules[index] = response.data!
            }
            
            // Update active schedules
            state.activeSchedules = state.reportSchedules.filter(schedule => schedule.isActive)
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update report schedule' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    deleteReportSchedule: async (scheduleId: string) => {
      try {
        const response = await reportsService.deleteReportSchedule(scheduleId)

        if (response.success) {
          set((state) => {
            state.reportSchedules = state.reportSchedules.filter(s => s._id !== scheduleId)
            state.activeSchedules = state.activeSchedules.filter(s => s._id !== scheduleId)
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to delete report schedule' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    toggleSchedule: async (scheduleId: string, active: boolean) => {
      try {
        const response = await reportsService.toggleSchedule(scheduleId, active)

        if (response.success && response.data) {
          set((state) => {
            const index = state.reportSchedules.findIndex(s => s._id === scheduleId)
            if (index !== -1) {
              state.reportSchedules[index] = response.data!
            }
            
            // Update active schedules
            state.activeSchedules = state.reportSchedules.filter(schedule => schedule.isActive)
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to toggle schedule' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Report Export
    exportReport: async (reportId: string, format: ExportFormat) => {
      try {
        const response = await reportsService.exportReport(reportId, format)

        if (response.success) {
          return { success: true, downloadUrl: response.data?.downloadUrl }
        } else {
          return { success: false, error: response.error || 'Failed to export report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    downloadReport: async (reportId: string, format: ExportFormat) => {
      try {
        const response = await reportsService.downloadReport(reportId, format)

        if (response.success) {
          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to download report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    shareReport: async (reportId: string, recipients: string[], message?: string) => {
      try {
        const response = await reportsService.shareReport(reportId, recipients, message)

        if (response.success) {
          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to share report' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Report Analytics
    fetchReportsByType: async (reportType: ReportType, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await reportsService.getReportsByType(reportType, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.reports = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalReports = response.pagination?.total || response.data!.length
            state.filters = { ...state.filters, reportType }
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch reports by type'
          })

          return { success: false, error: response.error || 'Failed to fetch reports by type' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchRecentReports: async (limit = 10, userId?: string) => {
      try {
        const response = await reportsService.getRecentReports(limit, userId)

        if (response.success && response.data) {
          // Don't replace all reports, just update a subset for recent reports display
          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to fetch recent reports' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    fetchPopularReports: async (limit = 10, period = 'month') => {
      try {
        const response = await reportsService.getPopularReports(limit, period)

        if (response.success && response.data) {
          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to fetch popular reports' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    getReportUsageStats: async (reportId: string, period = 'month') => {
      try {
        const response = await reportsService.getReportUsageStats(reportId, period)

        if (response.success) {
          return { success: true, stats: response.data }
        } else {
          return { success: false, error: response.error || 'Failed to get report usage stats' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Search & Filtering
    searchReports: async (query: string, pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
        state.searchQuery = query
      })

      try {
        const response = await reportsService.searchReports(query, pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.reports = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalReports = response.pagination?.total || response.data!.length
            state.filters = { ...filters, search: query }
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to search reports'
          })

          return { success: false, error: response.error || 'Failed to search reports' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    setFilters: (filters: Partial<ReportFilters>) => {
      set((state) => {
        state.filters = { ...state.filters, ...filters }
      })
    },

    clearFilters: () => {
      set((state) => {
        state.filters = {}
        state.searchQuery = ''
      })
    },

    setSearchQuery: (query: string) => {
      set((state) => {
        state.searchQuery = query
      })
    },

    // Actions - Pagination
    setCurrentPage: (page: number) => {
      set((state) => {
        state.currentPage = page
      })
    },

    setPageSize: (size: number) => {
      set((state) => {
        state.pageSize = size
      })
    },

    // Actions - UI State
    setSelectedReport: (report: Report | null) => {
      set((state) => {
        state.selectedReport = report
      })
    },

    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    setLoading: (loading: boolean) => {
      set((state) => {
        state.isLoading = loading
      })
    },

    // Utility Methods
    getReportsByType: (reportType: ReportType) => {
      return get().reports.filter(report => report.reportType === reportType)
    },

    getReportsByStatus: (status: ReportStatus) => {
      return get().reports.filter(report => report.status === status)
    },

    getReportsByDateRange: (startDate: string, endDate: string) => {
      return get().reports.filter(report => {
        const reportDate = new Date(report.createdAt)
        return reportDate >= new Date(startDate) && reportDate <= new Date(endDate)
      })
    },

    getActiveSchedules: () => {
      return get().reportSchedules.filter(schedule => schedule.isActive)
    },

    getSchedulesByFrequency: (frequency: string) => {
      return get().reportSchedules.filter(schedule => schedule.frequency === frequency)
    },

    calculateReportMetrics: (reports: Report[]) => {
      return reportsService.calculateReportMetrics(reports)
    },

    formatReportSize: (sizeInBytes: number) => {
      return reportsService.formatReportSize(sizeInBytes)
    },

    getReportTypeIcon: (reportType: ReportType) => {
      return reportsService.getReportTypeIcon(reportType)
    },

    getReportStatusColor: (status: ReportStatus) => {
      return reportsService.getReportStatusColor(status)
    },

    getExportFormatIcon: (format: ExportFormat) => {
      return reportsService.getExportFormatIcon(format)
    },

    isReportExpired: (report: Report, expiryDays = 30) => {
      return reportsService.isReportExpired(report, expiryDays)
    },

    canUserAccessReport: (report: Report, userId: string) => {
      return reportsService.canUserAccessReport(report, userId)
    },

    getReportSummary: (report: Report) => {
      return reportsService.getReportSummary(report)
    },

    formatDateRange: (dateRange: DateRange) => {
      return reportsService.formatDateRange(dateRange)
    }
  }))
)
