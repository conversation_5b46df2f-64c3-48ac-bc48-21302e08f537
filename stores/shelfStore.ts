// stores/shelfStore.ts - Shelf management state with Zustand

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { apiClient } from '@/services/frontend'
import type {
  Shelf,
  CreateShelfData,
  UpdateShelfData,
  ShelfFilters,
  PaginationParams,
  ApiResponse
} from '@/types/frontend'

interface ShelfState {
  // Data
  shelves: Shelf[]
  selectedShelf: Shelf | null
  
  // Loading states
  isLoading: boolean
  error: string | null
  
  // Pagination & Filtering
  currentPage: number
  totalPages: number
  totalItems: number
  pageSize: number
  filters: ShelfFilters
  searchQuery: string

  // Actions
  fetchShelves: (pagination?: PaginationParams, filters?: ShelfFilters) => Promise<{ success: boolean; error?: string }>
  fetchShelfById: (id: string) => Promise<{ success: boolean; error?: string }>
  createShelf: (data: CreateShelfData) => Promise<{ success: boolean; error?: string }>
  updateShelf: (id: string, data: UpdateShelfData) => Promise<{ success: boolean; error?: string }>
  deleteShelf: (id: string) => Promise<{ success: boolean; error?: string }>
  searchShelves: (query: string, pagination?: PaginationParams, filters?: ShelfFilters) => Promise<{ success: boolean; error?: string }>
  
  // Utility actions
  setSelectedShelf: (shelf: Shelf | null) => void
  clearError: () => void
  setFilters: (filters: ShelfFilters) => void
  resetPagination: () => void
}

export const useShelfStore = create<ShelfState>()(
  immer((set, get) => ({
    // Initial state
    shelves: [],
    selectedShelf: null,
    isLoading: false,
    error: null,
    
    // Pagination & Filtering
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 20,
    filters: {},
    searchQuery: '',

    // Actions
    fetchShelves: async (pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const params = new URLSearchParams()
        
        // Add pagination
        params.append('page', pagination.page.toString())
        params.append('limit', pagination.limit.toString())
        
        // Add filters
        if (filters.search) params.append('search', filters.search)
        if (filters.warehouseId) params.append('warehouseId', filters.warehouseId)
        if (filters.section) params.append('section', filters.section)
        if (filters.shelfType) params.append('shelfType', filters.shelfType)
        if (filters.accessLevel) params.append('accessLevel', filters.accessLevel)
        if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString())
        if (filters.isReserved !== undefined) params.append('isReserved', filters.isReserved.toString())
        if (filters.availabilityStatus) params.append('availabilityStatus', filters.availabilityStatus)

        const response: ApiResponse<Shelf[]> = await apiClient.get(`/api/shelves?${params}`)

        if (response.success) {
          set((state) => {
            state.shelves = response.data || []
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalItems = response.pagination?.total || 0
            state.filters = filters
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch shelves'
          })

          return { success: false, error: response.error || 'Failed to fetch shelves' }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
        
        set((state) => {
          state.isLoading = false
          state.error = errorMessage
        })

        return { success: false, error: errorMessage }
      }
    },

    fetchShelfById: async (id: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response: ApiResponse<Shelf> = await apiClient.get(`/api/shelves/${id}`)

        if (response.success && response.data) {
          set((state) => {
            state.selectedShelf = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch shelf'
          })

          return { success: false, error: response.error || 'Failed to fetch shelf' }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
        
        set((state) => {
          state.isLoading = false
          state.error = errorMessage
        })

        return { success: false, error: errorMessage }
      }
    },

    createShelf: async (data: CreateShelfData) => {
      try {
        const response: ApiResponse<Shelf> = await apiClient.post('/api/shelves', data)

        if (response.success && response.data) {
          set((state) => {
            state.shelves.unshift(response.data!)
            state.totalItems += 1
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to create shelf' }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
        return { success: false, error: errorMessage }
      }
    },

    updateShelf: async (id: string, data: UpdateShelfData) => {
      try {
        const response: ApiResponse<Shelf> = await apiClient.put(`/api/shelves/${id}`, data)

        if (response.success && response.data) {
          set((state) => {
            const index = state.shelves.findIndex(s => s._id === id)
            if (index !== -1) {
              state.shelves[index] = response.data!
            }

            if (state.selectedShelf?._id === id) {
              state.selectedShelf = response.data!
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update shelf' }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
        return { success: false, error: errorMessage }
      }
    },

    deleteShelf: async (id: string) => {
      try {
        const response: ApiResponse<any> = await apiClient.delete(`/api/shelves/${id}`)

        if (response.success) {
          set((state) => {
            state.shelves = state.shelves.filter(s => s._id !== id)
            state.totalItems = Math.max(0, state.totalItems - 1)
            
            if (state.selectedShelf?._id === id) {
              state.selectedShelf = null
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to delete shelf' }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
        return { success: false, error: errorMessage }
      }
    },

    searchShelves: async (query: string, pagination = { page: 1, limit: 20 }, filters = {}) => {
      const searchFilters = { ...filters, search: query }
      const result = await get().fetchShelves(pagination, searchFilters)
      
      set((state) => {
        state.searchQuery = query
      })

      return result
    },

    // Utility actions
    setSelectedShelf: (shelf: Shelf | null) => {
      set((state) => {
        state.selectedShelf = shelf
      })
    },

    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    setFilters: (filters: ShelfFilters) => {
      set((state) => {
        state.filters = filters
      })
    },

    resetPagination: () => {
      set((state) => {
        state.currentPage = 1
        state.totalPages = 0
        state.totalItems = 0
      })
    }
  }))
)
