// stores/employeeStore.ts - Employee management state with Zustand

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { employeeService } from '@/services/frontend/employeeService'

import type {
  Employee,
  CreateEmployeeData,
  UpdateEmployeeData,
  EmployeeFilters,
  PaginationParams
} from '@/types/frontend'

// Employee Store State Interface
interface EmployeeState {
  // State
  employees: Employee[]
  selectedEmployee: Employee | null
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean
  error: string | null

  // Pagination
  currentPage: number
  totalPages: number
  totalItems: number
  pageSize: number

  // Filters
  filters: EmployeeFilters
  searchQuery: string
  selectedTab: 'all' | 'managers' | 'employees' | 'inactive'
  sortBy: string
  sortOrder: 'asc' | 'desc'

  // Actions - CRUD
  fetchEmployees: (pagination?: PaginationParams, filters?: EmployeeFilters) => Promise<{ success: boolean; error?: string }>
  fetchEmployeeById: (employeeId: string) => Promise<Employee | null>
  createEmployee: (employeeData: CreateEmployeeData) => Promise<Employee | null>
  updateEmployee: (employeeId: string, updates: UpdateEmployeeData) => Promise<Employee | null>
  deleteEmployee: (employeeId: string) => Promise<boolean>

  // Actions - Filters & Search
  setFilters: (filters: Partial<EmployeeFilters>) => void
  setSearchQuery: (query: string) => void
  setSelectedTab: (tab: 'all' | 'managers' | 'employees' | 'inactive') => void
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void
  setPage: (page: number) => void
  setPageSize: (size: number) => void

  // Actions - Utility
  clearError: () => void
  reset: () => void
  refreshEmployees: () => Promise<void>

  // Computed Properties
  getFilteredEmployees: () => Employee[]
  getEmployeesByBranch: (branchId: string) => Employee[]
  getEmployeesByRole: (role: string) => Employee[]
  getActiveEmployees: () => Employee[]
  getInactiveEmployees: () => Employee[]
}

// Employee Store Implementation
export const useEmployeesStore = create<EmployeeState>()(
  immer((set, get) => ({
    // Initial state
    employees: [],
    selectedEmployee: null,
    isLoading: false,
    isCreating: false,
    isUpdating: false,
    isDeleting: false,
    error: null,
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 10,
    filters: {},
    searchQuery: '',
    selectedTab: 'all' as const,
    sortBy: 'firstName',
    sortOrder: 'asc' as const,

    // CRUD Operations
    fetchEmployees: async (pagination = { page: 1, limit: 10 }, filters = {}) => {
      console.log('🏪 STORE: fetchEmployees called with:', { pagination, filters })

      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        console.log('🏪 STORE: Calling employeeService.getEmployees...')
        const response = await employeeService.getEmployees(pagination, filters)
        console.log('🏪 STORE: Service response received:', {
          success: response.success,
          hasData: !!response.data,
          dataLength: response.data?.length || 0,
          error: response.error,
          pagination: response.pagination
        })

        if (response.success && response.data) {
          console.log('🏪 STORE: Setting employees in state:', response.data)
          set((state) => {
            state.employees = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalItems = response.pagination?.total || response.data!.length
            state.filters = filters
            state.isLoading = false
            state.error = null
          })
          console.log('🏪 STORE: State updated successfully')

          return { success: true }
        } else {
          console.log('🏪 STORE: Response failed:', response.error)
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch employees'
          })

          return { success: false, error: response.error || 'Failed to fetch employees' }
        }
      } catch (error) {
        console.error('🏪 STORE: Error in fetchEmployees:', error)
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to fetch employees'
          state.isLoading = false
        })

        return { success: false, error: error instanceof Error ? error.message : 'Failed to fetch employees' }
      }
    },

    fetchEmployeeById: async (employeeId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await employeeService.getEmployeeById(employeeId)

        if (response.success && response.data) {
          set((state) => {
            state.selectedEmployee = response.data!
            state.isLoading = false
          })
          return response.data
        } else {
          set((state) => {
            state.error = response.error || 'Failed to fetch employee'
            state.isLoading = false
          })
          return null
        }
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to fetch employee'
          state.isLoading = false
        })
        return null
      }
    },

    createEmployee: async (employeeData: CreateEmployeeData) => {
      set((state) => {
        state.isCreating = true
        state.error = null
      })

      try {
        const response = await employeeService.createEmployee(employeeData)

        if (response.success && response.data) {
          set((state) => {
            state.employees.push(response.data!)
            state.totalItems += 1
            state.isCreating = false
          })
          return response.data
        } else {
          set((state) => {
            state.error = response.error || 'Failed to create employee'
            state.isCreating = false
          })
          return null
        }
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to create employee'
          state.isCreating = false
        })
        return null
      }
    },

    updateEmployee: async (employeeId: string, updates: UpdateEmployeeData) => {
      set((state) => {
        state.isUpdating = true
        state.error = null
      })

      try {
        const response = await employeeService.updateEmployee(employeeId, updates)

        if (response.success && response.data) {
          set((state) => {
            const index = state.employees.findIndex(emp => emp._id === employeeId)
            if (index !== -1) {
              state.employees[index] = response.data!
            }
            if (state.selectedEmployee?._id === employeeId) {
              state.selectedEmployee = response.data!
            }
            state.isUpdating = false
          })
          return response.data
        } else {
          set((state) => {
            state.error = response.error || 'Failed to update employee'
            state.isUpdating = false
          })
          return null
        }
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to update employee'
          state.isUpdating = false
        })
        return null
      }
    },

    deleteEmployee: async (employeeId: string) => {
      set((state) => {
        state.isDeleting = true
        state.error = null
      })

      try {
        const response = await employeeService.deleteEmployee(employeeId)

        if (response.success) {
          set((state) => {
            state.employees = state.employees.filter(emp => emp._id !== employeeId)
            if (state.selectedEmployee?._id === employeeId) {
              state.selectedEmployee = null
            }
            state.totalItems -= 1
            state.isDeleting = false
          })
          return true
        } else {
          set((state) => {
            state.error = response.error || 'Failed to delete employee'
            state.isDeleting = false
          })
          return false
        }
      } catch (error) {
        set((state) => {
          state.error = error instanceof Error ? error.message : 'Failed to delete employee'
          state.isDeleting = false
        })
        return false
      }
    },

    // Filter & Search Actions
    setFilters: (filters: Partial<EmployeeFilters>) => {
      set((state) => {
        state.filters = { ...state.filters, ...filters }
      })
    },

    setSearchQuery: (query: string) => {
      set((state) => {
        state.searchQuery = query
      })
    },

    setSelectedTab: (tab: 'all' | 'managers' | 'employees' | 'inactive') => {
      set((state) => {
        state.selectedTab = tab
      })
    },

    setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => {
      set((state) => {
        state.sortBy = sortBy
        state.sortOrder = sortOrder
      })
    },

    setPage: (page: number) => {
      set((state) => {
        state.currentPage = page
      })
    },

    setPageSize: (size: number) => {
      set((state) => {
        state.pageSize = size
      })
    },

    // Utility Actions
    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    reset: () => {
      set((state) => {
        state.employees = []
        state.selectedEmployee = null
        state.isLoading = false
        state.isCreating = false
        state.isUpdating = false
        state.isDeleting = false
        state.error = null
        state.currentPage = 1
        state.totalPages = 0
        state.totalItems = 0
        state.pageSize = 10
        state.filters = {}
        state.searchQuery = ''
        state.selectedTab = 'all'
        state.sortBy = 'firstName'
        state.sortOrder = 'asc'
      })
    },

    refreshEmployees: async () => {
      const { currentPage, pageSize, filters } = get()
      await get().fetchEmployees({ page: currentPage, limit: pageSize }, filters)
    },

    // Computed Properties
    getFilteredEmployees: () => {
      const state = get()
      return state.employees.filter(emp => emp.isActive)
    },

    getEmployeesByBranch: (branchId: string) => {
      const state = get()
      return state.employees.filter(emp => emp.branchId === branchId)
    },

    getEmployeesByRole: (role: string) => {
      const state = get()
      return state.employees.filter(emp => emp.position.toLowerCase().includes(role.toLowerCase()))
    },

    getActiveEmployees: () => {
      const state = get()
      return state.employees.filter(emp => emp.isActive)
    },

    getInactiveEmployees: () => {
      const state = get()
      return state.employees.filter(emp => !emp.isActive)
    }
  }))
)
