// stores/branchesStore.ts - Branches management state with Zustand

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { shopService, handleServiceError } from '@/services/frontend'
import type { 
  Branch, 
  BranchStatus,
  CreateBranchData,
  UpdateBranchData,
  BranchFilters,
  PaginationParams,
  ApiResponse 
} from '@/types/frontend'

// ============================================================================
// Branches Store Types
// ============================================================================

export interface BranchesState {
  // Branches State
  branches: Branch[]
  selectedBranch: Branch | null
  isLoading: boolean
  error: string | null
  
  // Pagination & Filtering
  currentPage: number
  totalPages: number
  totalBranches: number
  pageSize: number
  filters: BranchFilters
  searchQuery: string
  
  // Branch Status Groups
  activeBranches: Branch[]
  inactiveBranches: Branch[]
  maintenanceBranches: Branch[]
  
  // Branch Analytics
  topPerformingBranches: Branch[]
  recentBranches: Branch[]
  branchPerformanceData: Record<string, any>
  
  // Actions - Branches CRUD
  fetchBranches: (pagination?: PaginationParams, filters?: BranchFilters) => Promise<{ success: boolean; error?: string }>
  fetchBranchById: (branchId: string) => Promise<{ success: boolean; error?: string }>
  createBranch: (branchData: CreateBranchData) => Promise<{ success: boolean; error?: string; branch?: Branch }>
  updateBranch: (branchId: string, updates: UpdateBranchData) => Promise<{ success: boolean; error?: string }>
  deleteBranch: (branchId: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Branch Status Management
  updateBranchStatus: (branchId: string, status: BranchStatus) => Promise<{ success: boolean; error?: string }>
  activateBranch: (branchId: string) => Promise<{ success: boolean; error?: string }>
  deactivateBranch: (branchId: string, reason?: string) => Promise<{ success: boolean; error?: string }>
  setMaintenanceMode: (branchId: string, maintenanceReason?: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Branch Analytics
  fetchTopPerformingBranches: (limit?: number, metric?: string) => Promise<{ success: boolean; error?: string }>
  fetchRecentBranches: (limit?: number) => Promise<{ success: boolean; error?: string }>
  fetchBranchPerformance: (branchId: string, period?: string) => Promise<{ success: boolean; error?: string }>
  fetchBranchAnalytics: (branchId?: string, period?: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Branch Queries
  fetchBranchesByRegion: (region: string, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  fetchBranchesByStatus: (status: BranchStatus, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  fetchBranchesByManager: (managerId: string, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Search & Filtering
  searchBranches: (query: string, pagination?: PaginationParams, filters?: Omit<BranchFilters, 'search'>) => Promise<{ success: boolean; error?: string }>
  setFilters: (filters: Partial<BranchFilters>) => void
  clearFilters: () => void
  setSearchQuery: (query: string) => void
  
  // Actions - Pagination
  setCurrentPage: (page: number) => void
  setPageSize: (size: number) => void
  
  // Actions - UI State
  setSelectedBranch: (branch: Branch | null) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  
  // Utility Methods
  getBranchesByRegion: (region: string) => Branch[]
  getBranchesByStatus: (status: BranchStatus) => Branch[]
  getActiveBranches: () => Branch[]
  getInactiveBranches: () => Branch[]
  getAvailableStatuses: () => BranchStatus[]
  getAvailableRegions: () => string[]
  calculateBranchDistance: (branch1: Branch, branch2: Branch) => number
  findNearestBranches: (latitude: number, longitude: number, limit?: number) => Branch[]
  getBranchOperatingHours: (branch: Branch, date?: Date) => { open: string; close: string; isOpen: boolean }
  isBranchOpen: (branch: Branch, date?: Date) => boolean
  formatBranchAddress: (branch: Branch) => string
  formatBranchContact: (branch: Branch) => string
  updateStatusGroups: () => void
}

// ============================================================================
// Branches Store Implementation
// ============================================================================

export const useBranchesStore = create<BranchesState>()(
  immer((set, get) => ({
    // Initial state
    branches: [],
    selectedBranch: null,
    isLoading: false,
    error: null,
    
    // Pagination & Filtering
    currentPage: 1,
    totalPages: 0,
    totalBranches: 0,
    pageSize: 20,
    filters: {},
    searchQuery: '',
    
    // Branch Status Groups
    activeBranches: [],
    inactiveBranches: [],
    maintenanceBranches: [],
    
    // Branch Analytics
    topPerformingBranches: [],
    recentBranches: [],
    branchPerformanceData: {},

    // Actions - Branches CRUD
    fetchBranches: async (pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await branchService.getBranches(pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.branches = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalBranches = response.pagination?.total || response.data!.length
            state.filters = filters
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch branches'
          })

          return { success: false, error: response.error || 'Failed to fetch branches' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchBranchById: async (branchId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await branchService.getBranchById(branchId)

        if (response.success && response.data) {
          set((state) => {
            state.selectedBranch = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch branch'
          })

          return { success: false, error: response.error || 'Failed to fetch branch' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    createBranch: async (branchData: CreateBranchData) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate branch data
        const validation = branchService.validateBranchData(branchData)
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        const response = await branchService.createBranch(branchData)

        if (response.success && response.data) {
          set((state) => {
            state.branches.unshift(response.data!)
            state.totalBranches += 1
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true, branch: response.data }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to create branch'
          })

          return { success: false, error: response.error || 'Failed to create branch' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    updateBranch: async (branchId: string, updates: UpdateBranchData) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate branch data
        const validation = branchService.validateBranchData(updates)
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        const response = await branchService.updateBranch(branchId, updates)

        if (response.success && response.data) {
          set((state) => {
            const index = state.branches.findIndex(b => b._id === branchId)
            if (index !== -1) {
              state.branches[index] = response.data!
            }
            
            if (state.selectedBranch?._id === branchId) {
              state.selectedBranch = response.data!
            }
            
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to update branch'
          })

          return { success: false, error: response.error || 'Failed to update branch' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    deleteBranch: async (branchId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await branchService.deleteBranch(branchId)

        if (response.success) {
          set((state) => {
            state.branches = state.branches.filter(b => b._id !== branchId)
            state.topPerformingBranches = state.topPerformingBranches.filter(b => b._id !== branchId)
            state.recentBranches = state.recentBranches.filter(b => b._id !== branchId)
            
            if (state.selectedBranch?._id === branchId) {
              state.selectedBranch = null
            }
            
            state.totalBranches = Math.max(0, state.totalBranches - 1)
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to delete branch'
          })

          return { success: false, error: response.error || 'Failed to delete branch' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Branch Status Management
    updateBranchStatus: async (branchId: string, status: BranchStatus) => {
      try {
        const response = await branchService.updateBranchStatus(branchId, status)

        if (response.success && response.data) {
          set((state) => {
            const index = state.branches.findIndex(b => b._id === branchId)
            if (index !== -1) {
              state.branches[index] = response.data!
            }
            
            if (state.selectedBranch?._id === branchId) {
              state.selectedBranch = response.data!
            }
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update branch status' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    activateBranch: async (branchId: string) => {
      return get().updateBranchStatus(branchId, 'Active')
    },

    deactivateBranch: async (branchId: string, reason?: string) => {
      try {
        const response = await branchService.deactivateBranch(branchId, reason)

        if (response.success && response.data) {
          set((state) => {
            const index = state.branches.findIndex(b => b._id === branchId)
            if (index !== -1) {
              state.branches[index] = response.data!
            }
            
            if (state.selectedBranch?._id === branchId) {
              state.selectedBranch = response.data!
            }
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to deactivate branch' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    setMaintenanceMode: async (branchId: string, maintenanceReason?: string) => {
      try {
        const response = await branchService.setMaintenanceMode(branchId, maintenanceReason)

        if (response.success && response.data) {
          set((state) => {
            const index = state.branches.findIndex(b => b._id === branchId)
            if (index !== -1) {
              state.branches[index] = response.data!
            }
            
            if (state.selectedBranch?._id === branchId) {
              state.selectedBranch = response.data!
            }
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to set maintenance mode' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Helper method to update status groups
    updateStatusGroups: () => {
      const state = get()
      const branches = state.branches
      
      set((draft) => {
        draft.activeBranches = branches.filter(b => b.status === 'Active')
        draft.inactiveBranches = branches.filter(b => b.status === 'Inactive')
        draft.maintenanceBranches = branches.filter(b => b.status === 'Maintenance')
      })
    },

    // Actions - Branch Analytics
    fetchTopPerformingBranches: async (limit = 10, metric = 'revenue') => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await branchService.getTopPerformingBranches(limit, metric)

        if (response.success && response.data) {
          set((state) => {
            state.topPerformingBranches = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch top performing branches'
          })

          return { success: false, error: response.error || 'Failed to fetch top performing branches' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchRecentBranches: async (limit = 10) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await branchService.getRecentBranches(limit)

        if (response.success && response.data) {
          set((state) => {
            state.recentBranches = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch recent branches'
          })

          return { success: false, error: response.error || 'Failed to fetch recent branches' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchBranchPerformance: async (branchId: string, period = 'month') => {
      try {
        const response = await branchService.getBranchPerformance(branchId, period)

        if (response.success && response.data) {
          set((state) => {
            state.branchPerformanceData[branchId] = response.data
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to fetch branch performance' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    fetchBranchAnalytics: async (branchId?: string, period = 'month') => {
      try {
        const response = await branchService.getBranchAnalytics(branchId, period)

        if (response.success && response.data) {
          set((state) => {
            if (branchId) {
              state.branchPerformanceData[branchId] = response.data
            } else {
              state.branchPerformanceData = { ...state.branchPerformanceData, ...response.data }
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to fetch branch analytics' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Branch Queries
    fetchBranchesByRegion: async (region: string, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await branchService.getBranchesByRegion(region, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.branches = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalBranches = response.pagination?.total || response.data!.length
            state.filters = { ...state.filters, region }
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch branches by region'
          })

          return { success: false, error: response.error || 'Failed to fetch branches by region' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchBranchesByStatus: async (status: BranchStatus, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await branchService.getBranchesByStatus(status, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.branches = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalBranches = response.pagination?.total || response.data!.length
            state.filters = { ...state.filters, status }
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch branches by status'
          })

          return { success: false, error: response.error || 'Failed to fetch branches by status' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchBranchesByManager: async (managerId: string, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await branchService.getBranchesByManager(managerId, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.branches = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalBranches = response.pagination?.total || response.data!.length
            state.filters = { ...state.filters, managerId }
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch branches by manager'
          })

          return { success: false, error: response.error || 'Failed to fetch branches by manager' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Search & Filtering
    searchBranches: async (query: string, pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
        state.searchQuery = query
      })

      try {
        const response = await branchService.searchBranches(query, pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.branches = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalBranches = response.pagination?.total || response.data!.length
            state.filters = { ...filters, search: query }
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to search branches'
          })

          return { success: false, error: response.error || 'Failed to search branches' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    setFilters: (filters: Partial<BranchFilters>) => {
      set((state) => {
        state.filters = { ...state.filters, ...filters }
      })
    },

    clearFilters: () => {
      set((state) => {
        state.filters = {}
        state.searchQuery = ''
      })
    },

    setSearchQuery: (query: string) => {
      set((state) => {
        state.searchQuery = query
      })
    },

    // Actions - Pagination
    setCurrentPage: (page: number) => {
      set((state) => {
        state.currentPage = page
      })
    },

    setPageSize: (size: number) => {
      set((state) => {
        state.pageSize = size
      })
    },

    // Actions - UI State
    setSelectedBranch: (branch: Branch | null) => {
      set((state) => {
        state.selectedBranch = branch
      })
    },

    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    setLoading: (loading: boolean) => {
      set((state) => {
        state.isLoading = loading
      })
    },

    // Utility Methods
    getBranchesByRegion: (region: string) => {
      return get().branches.filter(branch => branch.address.region === region)
    },

    getBranchesByStatus: (status: BranchStatus) => {
      return get().branches.filter(branch => branch.status === status)
    },

    getActiveBranches: () => {
      return get().branches.filter(branch => branch.status === 'Active')
    },

    getInactiveBranches: () => {
      return get().branches.filter(branch => branch.status === 'Inactive')
    },

    getAvailableStatuses: () => {
      return branchService.getBranchStatuses()
    },

    getAvailableRegions: () => {
      return branchService.getAvailableRegions()
    },

    calculateBranchDistance: (branch1: Branch, branch2: Branch) => {
      return branchService.calculateDistance(branch1.coordinates, branch2.coordinates)
    },

    findNearestBranches: (latitude: number, longitude: number, limit = 5) => {
      return branchService.findNearestBranches(get().branches, { latitude, longitude }, limit)
    },

    getBranchOperatingHours: (branch: Branch, date?: Date) => {
      return branchService.getBranchOperatingHours(branch, date)
    },

    isBranchOpen: (branch: Branch, date?: Date) => {
      return branchService.isBranchOpen(branch, date)
    },

    formatBranchAddress: (branch: Branch) => {
      return branchService.formatBranchAddress(branch)
    },

    formatBranchContact: (branch: Branch) => {
      return branchService.formatBranchContact(branch)
    }
  }))
)
