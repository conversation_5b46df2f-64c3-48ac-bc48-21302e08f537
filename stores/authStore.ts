// stores/authStore.ts - Authentication state management with Zustand

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { apiClient, handleServiceError } from '@/services/frontend'
import type { UserRole, ApiResponse } from '@/types/frontend'

// Auth-specific user type that matches API response
export interface AuthUser {
  id: string
  username: string
  email: string
  name: string
  role: UserRole
  branchId?: string
  avatar?: string
}

// Session information interface
export interface SessionInfo {
  sessionId: string
  deviceDescription: string
  loginTime: Date
  existingSessions: Array<{
    deviceDescription: string
    locationDescription: string
    loginTime: Date
    lastActivity: Date
  }>
}

// Active session interface
export interface ActiveSession {
  id: string
  deviceDescription: string
  locationDescription: string
  loginTime: Date
  lastActivity: Date
  isCurrentSession: boolean
}

// ============================================================================
// Authentication Types
// ============================================================================

export interface LoginCredentials {
  username: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  username: string
  email: string
  password: string
  confirmPassword: string
  firstName: string
  lastName: string
  phone?: string
  role: UserRole
  branchId?: string
}

export interface AuthState {
  // State
  user: AuthUser | null
  token: string | null
  tokenExpiry: Date | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null

  // Session management
  sessionInfo: SessionInfo | null
  activeSessions: ActiveSession[]
  sessionNotifications: string[]

  // Computed properties
  userRole: UserRole | null
  userBranch: string | null
  isAdmin: boolean
  isBranchManager: boolean
  isTokenExpired: boolean
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>
  register: (data: RegisterData) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  refreshToken: () => Promise<{ success: boolean; error?: string }>
  updateProfile: (updates: Partial<AuthUser>) => Promise<{ success: boolean; error?: string }>
  changePassword: (currentPassword: string, newPassword: string) => Promise<{ success: boolean; error?: string }>
  clearError: () => void
  setLoading: (boolean: boolean) => void
  checkTokenExpiry: () => void
  scheduleTokenRefresh: () => void

  // Session management actions
  getActiveSessions: () => Promise<{ success: boolean; sessions?: ActiveSession[]; error?: string }>
  terminateSession: (sessionId: string) => Promise<{ success: boolean; error?: string }>
  terminateAllOtherSessions: () => Promise<{ success: boolean; error?: string }>
  clearSessionNotifications: () => void
  addSessionNotification: (message: string) => void

  // Utility methods
  hasPermission: (requiredRole: UserRole) => boolean
  canAccessBranch: (branchId: string) => boolean
  getDisplayName: () => string
}

// ============================================================================
// Authentication Store Implementation
// ============================================================================

export const useAuthStore = create<AuthState>()(
  persist(
    immer((set, get) => ({
      // Initial state
      user: null,
      token: null,
      tokenExpiry: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Session management state
      sessionInfo: null,
      activeSessions: [],
      sessionNotifications: [],

      // Computed properties (as functions to ensure reactivity)
      userRole: null,
      userBranch: null,
      isAdmin: false,
      isBranchManager: false,

      // Helper function to update computed properties
      updateComputedProperties: () => {
        set((state) => {
          const user = state.user
          state.userRole = user?.role || null
          state.userBranch = user?.branchId || null
          state.isAdmin = user?.role === 'overall_admin'
          state.isBranchManager = user?.role === 'branch_manager'

          // Check token expiration
          if (state.tokenExpiry) {
            state.isTokenExpired = new Date() >= state.tokenExpiry
          } else {
            state.isTokenExpired = false
          }
        })
      },

      // Actions
      login: async (credentials: LoginCredentials) => {
        set((state) => {
          state.isLoading = true
          state.error = null
        })

        try {
          // Transform credentials to match API expectations
          const loginData = {
            email: credentials.username, // API expects 'email' field
            password: credentials.password
          }

          const response = await apiClient.post<{
            success: boolean
            user: AuthUser
            accessToken: string
            sessionInfo?: SessionInfo
          }>('/api/auth/login', loginData)

          // Debug: Log the actual response structure
          console.log('Login API Response:', response)
          console.log('Response keys:', Object.keys(response))

          // Handle the actual API response structure
          // The response is the direct JSON from the API, not wrapped in ApiResponse
          if (response.success && response.user && response.accessToken) {
            const { user, accessToken, sessionInfo } = response

            // Set auth token in API client
            apiClient.setAuthToken(accessToken)

            // Extract token expiry
            const { getTokenExpiration } = await import('@/lib/auth/jwt')
            const tokenExpiry = getTokenExpiration(accessToken)

            // Note: auth-token cookie is now set by the server (HTTP-only)

            set((state) => {
              state.user = user
              state.token = accessToken
              state.tokenExpiry = tokenExpiry
              state.isAuthenticated = true
              state.isLoading = false
              state.error = null
              state.sessionInfo = sessionInfo || null

              // Handle session notifications for existing sessions
              if (sessionInfo?.existingSessions && sessionInfo.existingSessions.length > 0) {
                const notifications = sessionInfo.existingSessions.map(session =>
                  `You are already logged in on ${session.deviceDescription} from ${session.locationDescription} since ${new Date(session.loginTime).toLocaleString()}`
                )
                state.sessionNotifications = notifications
              }
            })

            // Update computed properties
            get().updateComputedProperties()

            // Schedule token refresh
            get().scheduleTokenRefresh()

            // Token storage removed - tokens are now managed via HTTP-only cookies
            // localStorage.setItem('accessToken', accessToken) - REMOVED for security

            return { success: true }
          } else {
            const errorMessage = response.error || 'Login failed'
            set((state) => {
              state.isLoading = false
              state.error = errorMessage
            })

            return { success: false, error: errorMessage }
          }
        } catch (error) {
          const errorInfo = handleServiceError(error)
          
          set((state) => {
            state.isLoading = false
            state.error = errorInfo.message
          })

          return { success: false, error: errorInfo.message }
        }
      },

      register: async (data: RegisterData) => {
        set((state) => {
          state.isLoading = true
          state.error = null
        })

        try {
          // Validate passwords match
          if (data.password !== data.confirmPassword) {
            set((state) => {
              state.isLoading = false
              state.error = 'Passwords do not match'
            })
            return { success: false, error: 'Passwords do not match' }
          }

          // Transform data to match API expectations
          const { confirmPassword, firstName, lastName, ...restData } = data
          const registerPayload = {
            ...restData,
            name: `${firstName} ${lastName}`.trim()
          }

          const response = await apiClient.post<{
            user: AuthUser
            accessToken: string
          }>('/api/auth/register', registerPayload)

          if (response.success && response.data) {
            const { user, accessToken } = response.data

            // Set auth token in API client
            apiClient.setAuthToken(accessToken)

            // Note: auth-token cookie is now set by the server (HTTP-only)

            set((state) => {
              state.user = user
              state.token = accessToken
              state.isAuthenticated = true
              state.isLoading = false
              state.error = null
            })

            // Update computed properties
            get().updateComputedProperties()

            return { success: true }
          } else {
            set((state) => {
              state.isLoading = false
              state.error = response.error || 'Registration failed'
            })

            return { success: false, error: response.error || 'Registration failed' }
          }
        } catch (error) {
          const errorInfo = handleServiceError(error)
          
          set((state) => {
            state.isLoading = false
            state.error = errorInfo.message
          })

          return { success: false, error: errorInfo.message }
        }
      },

      refreshToken: async () => {
        try {
          const response = await apiClient.post<{
            user: User
            accessToken: string
          }>('/api/auth/refresh', {})

          if (response.success && response.data) {
            const { user, accessToken } = response.data

            // Set auth token in API client
            apiClient.setAuthToken(accessToken)

            // Note: auth-token cookie is now set by the server (HTTP-only)

            set((state) => {
              state.user = user
              state.token = accessToken
              state.isAuthenticated = true
              state.error = null
            })

            // Update computed properties
            get().updateComputedProperties()

            return { success: true }
          } else {
            // Refresh failed, logout user
            await get().logout()
            return { success: false, error: response.error || 'Token refresh failed' }
          }
        } catch (error) {
          // Refresh failed, logout user
          await get().logout()
          return { success: false, error: 'Token refresh failed' }
        }
      },

      logout: async () => {
        try {
          // Call server logout API to clear HTTP-only cookies
          await apiClient.post('/api/auth/logout', {})
        } catch (error) {
          // Continue with logout even if API call fails
          console.warn('Logout API call failed:', error)
        }

        // Clear auth token from API client
        apiClient.setAuthToken(null)

        // localStorage token removal not needed - tokens are managed via HTTP-only cookies
        // localStorage.removeItem('accessToken') - REMOVED

        set((state) => {
          state.user = null
          state.token = null
          state.tokenExpiry = null
          state.isAuthenticated = false
          state.isLoading = false
          state.error = null
          state.sessionInfo = null
          state.activeSessions = []
          state.sessionNotifications = []
        })

        // Update computed properties
        get().updateComputedProperties()
      },

      // Session management methods
      getActiveSessions: async () => {
        try {
          const response = await apiClient.get<{
            success: boolean
            sessions: ActiveSession[]
            currentSessionId: string
          }>('/api/auth/sessions')

          if (response.success && response.sessions) {
            set((state) => {
              state.activeSessions = response.sessions
            })
            return { success: true, sessions: response.sessions }
          } else {
            return { success: false, error: response.error || 'Failed to get sessions' }
          }
        } catch (error) {
          const errorInfo = handleServiceError(error)
          return { success: false, error: errorInfo.message }
        }
      },

      terminateSession: async (sessionId: string) => {
        try {
          const response = await apiClient.delete<{
            success: boolean
            message: string
          }>('/api/auth/sessions', { sessionId })

          if (response.success) {
            // Refresh the sessions list
            await get().getActiveSessions()
            return { success: true }
          } else {
            return { success: false, error: response.error || 'Failed to terminate session' }
          }
        } catch (error) {
          const errorInfo = handleServiceError(error)
          return { success: false, error: errorInfo.message }
        }
      },

      terminateAllOtherSessions: async () => {
        try {
          const response = await apiClient.delete<{
            success: boolean
            message: string
            terminatedCount: number
          }>('/api/auth/sessions', { terminateAll: true })

          if (response.success) {
            // Refresh the sessions list
            await get().getActiveSessions()
            return { success: true }
          } else {
            return { success: false, error: response.error || 'Failed to terminate sessions' }
          }
        } catch (error) {
          const errorInfo = handleServiceError(error)
          return { success: false, error: errorInfo.message }
        }
      },

      clearSessionNotifications: () => {
        set((state) => {
          state.sessionNotifications = []
        })
      },

      addSessionNotification: (message: string) => {
        set((state) => {
          state.sessionNotifications.push(message)
        })
      },

      updateProfile: async (updates: Partial<AuthUser>) => {
        const user = get().user
        if (!user) {
          return { success: false, error: 'User not authenticated' }
        }

        set((state) => {
          state.isLoading = true
          state.error = null
        })

        try {
          const response = await apiClient.put<AuthUser>('/auth/profile', updates)

          if (response.success && response.data) {
            set((state) => {
              state.user = response.data!
              state.isLoading = false
              state.error = null
            })

            return { success: true }
          } else {
            set((state) => {
              state.isLoading = false
              state.error = response.error || 'Profile update failed'
            })

            return { success: false, error: response.error || 'Profile update failed' }
          }
        } catch (error) {
          const errorInfo = handleServiceError(error)
          
          set((state) => {
            state.isLoading = false
            state.error = errorInfo.message
          })

          return { success: false, error: errorInfo.message }
        }
      },

      changePassword: async (currentPassword: string, newPassword: string) => {
        set((state) => {
          state.isLoading = true
          state.error = null
        })

        try {
          const response = await apiClient.post<{ success: boolean }>('/auth/change-password', {
            currentPassword,
            newPassword
          })

          if (response.success) {
            set((state) => {
              state.isLoading = false
              state.error = null
            })

            return { success: true }
          } else {
            set((state) => {
              state.isLoading = false
              state.error = response.error || 'Password change failed'
            })

            return { success: false, error: response.error || 'Password change failed' }
          }
        } catch (error) {
          const errorInfo = handleServiceError(error)
          
          set((state) => {
            state.isLoading = false
            state.error = errorInfo.message
          })

          return { success: false, error: errorInfo.message }
        }
      },

      clearError: () => {
        set((state) => {
          state.error = null
        })
      },

      setLoading: (loading: boolean) => {
        set((state) => {
          state.isLoading = loading
        })
      },

      checkTokenExpiry: () => {
        const { token, tokenExpiry, isAuthenticated } = get()

        if (!isAuthenticated || !token || !tokenExpiry) {
          return
        }

        const now = new Date()
        const timeUntilExpiry = tokenExpiry.getTime() - now.getTime()

        // If token expires in less than 5 minutes, refresh it
        if (timeUntilExpiry < 5 * 60 * 1000) {
          get().refreshToken()
        }
      },

      scheduleTokenRefresh: () => {
        const { tokenExpiry } = get()

        if (!tokenExpiry) {
          return
        }

        const now = new Date()
        const timeUntilExpiry = tokenExpiry.getTime() - now.getTime()

        // Schedule refresh 5 minutes before expiry
        const refreshTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 0)

        if (refreshTime > 0) {
          setTimeout(() => {
            get().refreshToken()
          }, refreshTime)
        }
      },

      // Utility methods
      hasPermission: (requiredRole: UserRole) => {
        const user = get().user
        if (!user) return false

        if (requiredRole === 'overall_admin') {
          return user.role === 'overall_admin'
        }

        if (requiredRole === 'branch_manager') {
          return user.role === 'overall_admin' || user.role === 'branch_manager'
        }

        return false
      },

      canAccessBranch: (branchId: string) => {
        const user = get().user
        if (!user) return false

        // Overall admins can access any branch
        if (user.role === 'overall_admin') return true

        // Branch managers can only access their assigned branch
        if (user.role === 'branch_manager') {
          return user.branchId === branchId
        }

        return false
      },

      getDisplayName: () => {
        const user = get().user
        if (!user) return 'Guest'

        return user.name || user.username
      }
    })),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        // Remove token from persistence - tokens are now managed via HTTP-only cookies
        // token: state.token,
        isAuthenticated: state.isAuthenticated,
        sessionInfo: state.sessionInfo,
        sessionNotifications: state.sessionNotifications
      }),
      onRehydrateStorage: () => (state) => {
        // Token rehydration removed - tokens are now managed via HTTP-only cookies
        // The API client will get tokens from cookies automatically
        // if (state?.token) {
        //   apiClient.setAuthToken(state.token)
        // }
      }
    }
  )
)
