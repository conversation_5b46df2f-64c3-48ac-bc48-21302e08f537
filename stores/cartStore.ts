// stores/cartStore.ts - Enhanced Shopping cart state management with <PERSON><PERSON><PERSON> (Mall97 Pattern)

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

// Types
export interface CartItem {
  id: string
  productId: string
  name: string
  price: number
  quantity: number
  image: string
  category: string
  branchId: string
  branchName: string
  maxStock: number
  sku: string
  variant?: string
}

export interface CartSummary {
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  itemCount: number
}

export interface ShippingAddress {
  fullName: string
  address: string
  city: string
  postalCode: string
  country: string
  phone: string
}

export interface SelectedBranch {
  id: string
  name: string
  location: string
  country: string
  region: string
}

export interface CartState {
  // State
  items: CartItem[]
  isOpen: boolean
  selectedBranch: SelectedBranch | null
  shippingAddress: ShippingAddress | null
  billingAddress: ShippingAddress | null
  paymentMethod: string | null
  isLoading: boolean
  isCheckingOut: boolean
  error: string | null

  // Computed properties
  summary: CartSummary
  isEmpty: boolean
  itemCount: number
  
  // Actions
  addItem: (item: Omit<CartItem, 'id' | 'quantity'> & { quantity?: number }) => void
  removeItem: (itemId: string) => void
  updateQuantity: (itemId: string, quantity: number) => void
  clearCart: () => void

  // Branch Actions
  setSelectedBranch: (branch: SelectedBranch) => void
  clearSelectedBranch: () => void
  canAddItemFromBranch: (branchId: string) => boolean
  getCartBranchId: () => string | null

  // UI Actions
  openCart: () => void
  closeCart: () => void
  toggleCart: () => void
  
  // Utility functions
  hasItem: (productId: string, variant?: string) => boolean
  getItem: (productId: string, variant?: string) => CartItem | undefined
  getBranches: () => Array<{ id: string; name: string }>
  validateStock: (itemId: string, requestedQuantity: number) => boolean
  removeOutOfStockItems: () => void
  getSummaryByBranch: (branchId: string) => CartSummary
}

// Helper function to calculate cart summary
function calculateCartSummary(items: CartItem[]): CartSummary {
  const subtotal = items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  const tax = 0 // Tax set to 0 - to be discussed separately if applicable
  const shipping = 0 // Shipping set to 0 - delivery prices vary by destination and need separate discussion
  const discount = 0
  const total = subtotal + tax + shipping - discount
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0)

  return {
    subtotal,
    tax,
    shipping,
    discount,
    total,
    itemCount
  }
}

console.log('🛒 Initializing cart store...')

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => {
      console.log('🛒 Cart store factory function called')
      return {
        // Initial state
        items: [],
        isOpen: false,
        selectedBranch: null,
        shippingAddress: null,
        billingAddress: null,
        paymentMethod: null,
        isLoading: false,
        isCheckingOut: false,
        error: null,

        // Computed properties - these will be updated manually
        summary: calculateCartSummary([]),
        isEmpty: true,
        itemCount: 0,
        
        // Actions
        addItem: (newItem) => {
          console.log('🛒 Adding item to cart:', newItem)
          
          const currentState = get()
          let updatedItems = [...currentState.items]
          
          // Check if item already exists
          const existingItemIndex = updatedItems.findIndex(item => 
            item.productId === newItem.productId && 
            item.variant === newItem.variant
          )
          
          if (existingItemIndex >= 0) {
            // Update existing item quantity
            const existingItem = updatedItems[existingItemIndex]
            const newQuantity = existingItem.quantity + (newItem.quantity || 1)
            
            if (newQuantity <= newItem.maxStock) {
              updatedItems[existingItemIndex] = {
                ...existingItem,
                quantity: newQuantity
              }
              console.log('🛒 Updated existing item quantity:', newQuantity)
            } else {
              console.log('🛒 Cannot add more items - would exceed stock limit')
              throw new Error(`Cannot add more items. Maximum stock is ${newItem.maxStock}`)
            }
          } else {
            // Add new item
            const cartItem: CartItem = {
              ...newItem,
              id: `cart-${newItem.productId}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
              quantity: newItem.quantity || 1
            }
            updatedItems.push(cartItem)
            console.log('🛒 Added new item to cart:', cartItem.name)
          }
          
          // Update state with computed properties
          const newItemCount = updatedItems.reduce((sum, item) => sum + item.quantity, 0)
          const newIsEmpty = updatedItems.length === 0
          const newSummary = calculateCartSummary(updatedItems)
          
          set({
            items: updatedItems,
            itemCount: newItemCount,
            isEmpty: newIsEmpty,
            summary: newSummary,
            error: null
          })
          
          console.log('🛒 Updated state with computed properties - itemCount:', newItemCount, 'isEmpty:', newIsEmpty)
          console.log('✅ Cart updated successfully.')
          console.log('🛒 Items in array:', updatedItems.length)
          console.log('🛒 Current cart items:', updatedItems.map(item => ({
            name: item.name,
            quantity: item.quantity,
            id: item.id
          })))
        },
        
        removeItem: (itemId) => {
          console.log('🛒 Removing item from cart:', itemId)
          const currentState = get()
          const updatedItems = currentState.items.filter(item => item.id !== itemId)
          
          // Update computed properties
          const newItemCount = updatedItems.reduce((sum, item) => sum + item.quantity, 0)
          const newIsEmpty = updatedItems.length === 0
          const newSummary = calculateCartSummary(updatedItems)
          
          set({
            items: updatedItems,
            itemCount: newItemCount,
            isEmpty: newIsEmpty,
            summary: newSummary,
            error: null
          })
          
          console.log('✅ Item removed. Remaining items:', updatedItems.length)
        },
        
        updateQuantity: (itemId, quantity) => {
          console.log('🛒 Updating quantity for item:', itemId, 'to:', quantity)
          
          const currentState = get()
          let updatedItems = [...currentState.items]
          
          if (quantity <= 0) {
            updatedItems = updatedItems.filter(item => item.id !== itemId)
            console.log('🛒 Quantity is 0, removing item')
          } else {
            const itemIndex = updatedItems.findIndex(item => item.id === itemId)
            if (itemIndex >= 0) {
              const item = updatedItems[itemIndex]
              
              // Check stock limit
              if (item.maxStock && quantity > item.maxStock) {
                console.warn('🛒 Stock limit exceeded for quantity update')
                set({ error: `Cannot add more items. Maximum stock available: ${item.maxStock}` })
                return
              }
              
              updatedItems[itemIndex] = {
                ...item,
                quantity: quantity
              }
              console.log('🛒 Quantity updated successfully')
            }
          }
          
          // Update computed properties
          const newItemCount = updatedItems.reduce((sum, item) => sum + item.quantity, 0)
          const newIsEmpty = updatedItems.length === 0
          const newSummary = calculateCartSummary(updatedItems)
          
          set({
            items: updatedItems,
            itemCount: newItemCount,
            isEmpty: newIsEmpty,
            summary: newSummary,
            error: null
          })
        },
        
        clearCart: () => {
          console.log('🛒 Clearing cart')
          set({
            items: [],
            selectedBranch: null,
            itemCount: 0,
            isEmpty: true,
            summary: calculateCartSummary([]),
            error: null
          })
          console.log('✅ Cart cleared successfully')
        },
        
        // UI Actions
        openCart: () => {
          console.log('🛒 Opening cart')
          set({ isOpen: true })
        },
        
        closeCart: () => {
          console.log('🛒 Closing cart')
          set({ isOpen: false })
        },
        
        toggleCart: () => {
          const currentState = get()
          console.log('🛒 Toggling cart from', currentState.isOpen ? 'open' : 'closed')
          set({ isOpen: !currentState.isOpen })
        },
        
        // Utility functions
        hasItem: (productId, variant) => {
          return get().items.some(item => 
            item.productId === productId && item.variant === variant
          )
        },
        
        getItem: (productId, variant) => {
          return get().items.find(item => 
            item.productId === productId && item.variant === variant
          )
        },
        
        getBranches: () => {
          const branches = new Map()
          get().items.forEach(item => {
            if (item.branchId && item.branchName) {
              branches.set(item.branchId, item.branchName)
            }
          })
          return Array.from(branches.entries()).map(([id, name]) => ({ id, name }))
        },
        
        validateStock: (itemId, requestedQuantity) => {
          const item = get().items.find(item => item.id === itemId)
          if (!item) return false
          return !item.maxStock || requestedQuantity <= item.maxStock
        },
        
        removeOutOfStockItems: () => {
          const currentState = get()
          const filteredItems = currentState.items.filter(item =>
            !item.maxStock || item.maxStock > 0
          )
          
          // Update computed properties
          const newItemCount = filteredItems.reduce((sum, item) => sum + item.quantity, 0)
          const newIsEmpty = filteredItems.length === 0
          const newSummary = calculateCartSummary(filteredItems)
          
          set({
            items: filteredItems,
            itemCount: newItemCount,
            isEmpty: newIsEmpty,
            summary: newSummary
          })
        },
        
        getSummaryByBranch: (branchId) => {
          const branchItems = get().items.filter(item => item.branchId === branchId)
          return calculateCartSummary(branchItems)
        },

        // Branch Management Actions
        setSelectedBranch: (branch) => {
          console.log('🛒 Setting selected branch:', branch.name)
          set({ selectedBranch: branch })
        },

        clearSelectedBranch: () => {
          console.log('🛒 Clearing selected branch')
          set({ selectedBranch: null })
        },

        canAddItemFromBranch: (branchId) => {
          const currentState = get()
          if (currentState.isEmpty) return true

          const cartBranchId = currentState.items[0]?.branchId
          return cartBranchId === branchId
        },

        getCartBranchId: () => {
          const currentState = get()
          return currentState.items[0]?.branchId || null
        }
      }
    },
    {
      name: 'fathahitech-cart',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        items: state.items,
        selectedBranch: state.selectedBranch,
        shippingAddress: state.shippingAddress,
        billingAddress: state.billingAddress,
        paymentMethod: state.paymentMethod
      }),
      onRehydrateStorage: () => (state) => {
        console.log('🛒 Cart store rehydrated with', state?.items?.length || 0, 'items')
        if (state?.items?.length) {
          console.log('🛒 Rehydrated items:', state.items.map(item => ({
            name: item.name,
            quantity: item.quantity
          })))
        }
      },
      skipHydration: false,
      version: 2,
      migrate: (persistedState: any, version: number) => {
        console.log('🛒 Migrating cart store from version:', version)
        
        if (version === 0 || !version || version === 1) {
          return {
            items: persistedState?.items || [],
            shippingAddress: persistedState?.shippingAddress || null,
            billingAddress: persistedState?.billingAddress || null,
            paymentMethod: persistedState?.paymentMethod || null
          }
        }
        
        return persistedState
      }
    }
  )
)
