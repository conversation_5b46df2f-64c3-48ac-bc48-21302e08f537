// stores/warehouseStore.ts - Warehouse management state with Zustand

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { apiClient } from '@/services/frontend'
import type {
  Warehouse,
  CreateWarehouseData,
  UpdateWarehouseData,
  WarehouseFilters,
  PaginationParams,
  ApiResponse
} from '@/types/frontend'

interface WarehouseState {
  // Data
  warehouses: Warehouse[]
  selectedWarehouse: Warehouse | null
  
  // Loading states
  isLoading: boolean
  error: string | null
  
  // Pagination & Filtering
  currentPage: number
  totalPages: number
  totalItems: number
  pageSize: number
  filters: WarehouseFilters
  searchQuery: string

  // Actions
  fetchWarehouses: (pagination?: PaginationParams, filters?: WarehouseFilters) => Promise<{ success: boolean; error?: string }>
  fetchWarehouseById: (id: string) => Promise<{ success: boolean; error?: string }>
  createWarehouse: (data: CreateWarehouseData) => Promise<{ success: boolean; error?: string }>
  updateWarehouse: (id: string, data: UpdateWarehouseData) => Promise<{ success: boolean; error?: string }>
  deleteWarehouse: (id: string) => Promise<{ success: boolean; error?: string }>
  searchWarehouses: (query: string, pagination?: PaginationParams, filters?: WarehouseFilters) => Promise<{ success: boolean; error?: string }>
  
  // Utility actions
  setSelectedWarehouse: (warehouse: Warehouse | null) => void
  clearError: () => void
  setFilters: (filters: WarehouseFilters) => void
  resetPagination: () => void
}

export const useWarehouseStore = create<WarehouseState>()(
  immer((set, get) => ({
    // Initial state
    warehouses: [],
    selectedWarehouse: null,
    isLoading: false,
    error: null,
    
    // Pagination & Filtering
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 20,
    filters: {},
    searchQuery: '',

    // Actions
    fetchWarehouses: async (pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const params = new URLSearchParams()
        
        // Add pagination
        params.append('page', pagination.page.toString())
        params.append('limit', pagination.limit.toString())
        
        // Add filters
        if (filters.search) params.append('search', filters.search)
        if (filters.type) params.append('type', filters.type)
        if (filters.branchId) params.append('branchId', filters.branchId)
        if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString())
        if (filters.managerId) params.append('managerId', filters.managerId)

        const response: ApiResponse<Warehouse[]> = await apiClient.get(`/api/warehouses?${params}`)

        if (response.success) {
          set((state) => {
            state.warehouses = response.data || []
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalItems = response.pagination?.total || 0
            state.filters = filters
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch warehouses'
          })

          return { success: false, error: response.error || 'Failed to fetch warehouses' }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
        
        set((state) => {
          state.isLoading = false
          state.error = errorMessage
        })

        return { success: false, error: errorMessage }
      }
    },

    fetchWarehouseById: async (id: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response: ApiResponse<Warehouse> = await apiClient.get(`/api/warehouses/${id}`)

        if (response.success && response.data) {
          set((state) => {
            state.selectedWarehouse = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch warehouse'
          })

          return { success: false, error: response.error || 'Failed to fetch warehouse' }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
        
        set((state) => {
          state.isLoading = false
          state.error = errorMessage
        })

        return { success: false, error: errorMessage }
      }
    },

    createWarehouse: async (data: CreateWarehouseData) => {
      try {
        const response: ApiResponse<Warehouse> = await apiClient.post('/api/warehouses', data)

        if (response.success && response.data) {
          set((state) => {
            state.warehouses.unshift(response.data!)
            state.totalItems += 1
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to create warehouse' }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
        return { success: false, error: errorMessage }
      }
    },

    updateWarehouse: async (id: string, data: UpdateWarehouseData) => {
      try {
        const response: ApiResponse<Warehouse> = await apiClient.put(`/api/warehouses/${id}`, data)

        if (response.success && response.data) {
          set((state) => {
            const index = state.warehouses.findIndex(w => w._id === id)
            if (index !== -1) {
              state.warehouses[index] = response.data!
            }

            if (state.selectedWarehouse?._id === id) {
              state.selectedWarehouse = response.data!
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update warehouse' }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
        return { success: false, error: errorMessage }
      }
    },

    deleteWarehouse: async (id: string) => {
      try {
        const response: ApiResponse<any> = await apiClient.delete(`/api/warehouses/${id}`)

        if (response.success) {
          set((state) => {
            state.warehouses = state.warehouses.filter(w => w._id !== id)
            state.totalItems = Math.max(0, state.totalItems - 1)
            
            if (state.selectedWarehouse?._id === id) {
              state.selectedWarehouse = null
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to delete warehouse' }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
        return { success: false, error: errorMessage }
      }
    },

    searchWarehouses: async (query: string, pagination = { page: 1, limit: 20 }, filters = {}) => {
      const searchFilters = { ...filters, search: query }
      const result = await get().fetchWarehouses(pagination, searchFilters)
      
      set((state) => {
        state.searchQuery = query
      })

      return result
    },

    // Utility actions
    setSelectedWarehouse: (warehouse: Warehouse | null) => {
      set((state) => {
        state.selectedWarehouse = warehouse
      })
    },

    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    setFilters: (filters: WarehouseFilters) => {
      set((state) => {
        state.filters = filters
      })
    },

    resetPagination: () => {
      set((state) => {
        state.currentPage = 1
        state.totalPages = 0
        state.totalItems = 0
      })
    }
  }))
)
