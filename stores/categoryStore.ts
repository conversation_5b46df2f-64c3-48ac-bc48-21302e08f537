import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { categoryService, type ProductCategory, type CreateCategoryData, type UpdateCategoryData, type CategoryImageUploadData } from '@/services/frontend/categoryService'
import type { PaginationParams } from '@/types/frontend'

interface CategoryState {
  // Data
  categories: ProductCategory[]
  activeCategories: ProductCategory[]
  selectedCategory: ProductCategory | null

  // UI State
  isLoading: boolean
  isCreating: boolean
  isUpdating: boolean
  isDeleting: boolean
  isUploadingImages: boolean
  error: string | null

  // Pagination
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }

  // Filters
  filters: {
    search: string
    activeOnly: boolean
  }
}

interface CategoryActions {
  // Data Actions
  fetchCategories: (pagination?: PaginationParams, search?: string, activeOnly?: boolean) => Promise<void>
  fetchActiveCategories: () => Promise<void>
  fetchCategoryById: (categoryId: string) => Promise<void>
  createCategory: (data: CreateCategoryData) => Promise<ProductCategory | null>
  updateCategory: (categoryId: string, data: UpdateCategoryData) => Promise<ProductCategory | null>
  deleteCategory: (categoryId: string) => Promise<boolean>

  // Image Actions
  createCategoryWithImages: (data: CreateCategoryData, imageData?: CategoryImageUploadData) => Promise<ProductCategory | null>
  updateCategoryWithImages: (categoryId: string, data: UpdateCategoryData, imageData?: CategoryImageUploadData) => Promise<ProductCategory | null>
  uploadCategoryImages: (categoryId: string, imageData: CategoryImageUploadData) => Promise<ProductCategory | null>
  removeCategoryImages: (categoryId: string, imageType?: 'featured' | 'icon') => Promise<ProductCategory | null>

  // UI Actions
  setSelectedCategory: (category: ProductCategory | null) => void
  setFilters: (filters: Partial<CategoryState['filters']>) => void
  setPagination: (pagination: Partial<CategoryState['pagination']>) => void
  clearError: () => void
  reset: () => void
}

type CategoryStore = CategoryState & CategoryActions

const initialState: CategoryState = {
  categories: [],
  activeCategories: [],
  selectedCategory: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  isUploadingImages: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  },
  filters: {
    search: '',
    activeOnly: false
  }
}

export const useCategoryStore = create<CategoryStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Fetch categories with pagination and filtering
      fetchCategories: async (pagination, search, activeOnly) => {
        set({ isLoading: true, error: null })
        
        try {
          const currentState = get()
          const paginationParams = pagination || {
            page: currentState.pagination.page,
            limit: currentState.pagination.limit
          }
          
          const filters = {
            search: search !== undefined ? search : currentState.filters.search,
            activeOnly: activeOnly !== undefined ? activeOnly : currentState.filters.activeOnly
          }

          const response = await categoryService.getCategories(paginationParams, filters)
          
          if (response.success && response.data) {
            set({
              categories: response.data,
              pagination: response.pagination || currentState.pagination,
              filters,
              isLoading: false
            })
          } else {
            set({
              error: response.error || 'Failed to fetch categories',
              isLoading: false
            })
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch categories',
            isLoading: false
          })
        }
      },

      // Fetch active categories for dropdowns
      fetchActiveCategories: async () => {
        try {
          const response = await categoryService.getActiveCategories()
          
          if (response.success && response.data) {
            set({ activeCategories: response.data })
          }
        } catch (error) {
          console.error('Failed to fetch active categories:', error)
        }
      },

      // Fetch category by ID
      fetchCategoryById: async (categoryId: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await categoryService.getCategoryById(categoryId)
          
          if (response.success && response.data) {
            set({
              selectedCategory: response.data,
              isLoading: false
            })
          } else {
            set({
              error: response.error || 'Category not found',
              isLoading: false
            })
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to fetch category',
            isLoading: false
          })
        }
      },

      // Create new category
      createCategory: async (data: CreateCategoryData) => {
        set({ isCreating: true, error: null })
        
        try {
          const response = await categoryService.createCategory(data)
          
          if (response.success && response.data) {
            // Refresh categories list
            await get().fetchCategories()
            await get().fetchActiveCategories()
            
            set({ isCreating: false })
            return response.data
          } else {
            set({
              error: response.error || 'Failed to create category',
              isCreating: false
            })
            return null
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to create category',
            isCreating: false
          })
          return null
        }
      },

      // Update category
      updateCategory: async (categoryId: string, data: UpdateCategoryData) => {
        set({ isUpdating: true, error: null })
        
        try {
          const response = await categoryService.updateCategory(categoryId, data)
          
          if (response.success && response.data) {
            // Update categories in state
            const currentState = get()
            const updatedCategories = currentState.categories.map(cat =>
              cat.id === categoryId ? response.data! : cat
            )
            
            set({
              categories: updatedCategories,
              selectedCategory: currentState.selectedCategory?.id === categoryId 
                ? response.data 
                : currentState.selectedCategory,
              isUpdating: false
            })
            
            // Refresh active categories if needed
            if (data.isActive !== undefined) {
              await get().fetchActiveCategories()
            }
            
            return response.data
          } else {
            set({
              error: response.error || 'Failed to update category',
              isUpdating: false
            })
            return null
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update category',
            isUpdating: false
          })
          return null
        }
      },

      // Delete category
      deleteCategory: async (categoryId: string) => {
        set({ isDeleting: true, error: null })
        
        try {
          const response = await categoryService.deleteCategory(categoryId)
          
          if (response.success) {
            // Remove category from state
            const currentState = get()
            const filteredCategories = currentState.categories.filter(cat => cat.id !== categoryId)
            
            set({
              categories: filteredCategories,
              selectedCategory: currentState.selectedCategory?.id === categoryId 
                ? null 
                : currentState.selectedCategory,
              isDeleting: false
            })
            
            // Refresh active categories
            await get().fetchActiveCategories()
            
            return true
          } else {
            set({
              error: response.error || 'Failed to delete category',
              isDeleting: false
            })
            return false
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to delete category',
            isDeleting: false
          })
          return false
        }
      },

      // Image Actions
      createCategoryWithImages: async (data: CreateCategoryData, imageData?: CategoryImageUploadData) => {
        set({ isCreating: true, error: null })
        try {
          const response = await categoryService.createCategoryWithImages(data, imageData)

          if (response.success && response.data) {
            const currentState = get()
            set({
              categories: [...currentState.categories, response.data],
              isCreating: false
            })

            // Refresh active categories
            await get().fetchActiveCategories()

            return response.data
          } else {
            set({
              error: response.error || 'Failed to create category with images',
              isCreating: false
            })
            return null
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to create category with images',
            isCreating: false
          })
          return null
        }
      },

      updateCategoryWithImages: async (categoryId: string, data: UpdateCategoryData, imageData?: CategoryImageUploadData) => {
        set({ isUpdating: true, error: null })
        try {
          const response = await categoryService.updateCategoryWithImages(categoryId, data, imageData)

          if (response.success && response.data) {
            const currentState = get()
            const updatedCategories = currentState.categories.map(cat =>
              cat.id === categoryId ? response.data! : cat
            )

            set({
              categories: updatedCategories,
              selectedCategory: currentState.selectedCategory?.id === categoryId
                ? response.data
                : currentState.selectedCategory,
              isUpdating: false
            })

            // Refresh active categories
            await get().fetchActiveCategories()

            return response.data
          } else {
            set({
              error: response.error || 'Failed to update category with images',
              isUpdating: false
            })
            return null
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update category with images',
            isUpdating: false
          })
          return null
        }
      },

      uploadCategoryImages: async (categoryId: string, imageData: CategoryImageUploadData) => {
        set({ isUploadingImages: true, error: null })
        try {
          const response = await categoryService.uploadCategoryImages(categoryId, imageData)

          if (response.success && response.data) {
            const currentState = get()
            const updatedCategories = currentState.categories.map(cat =>
              cat.id === categoryId ? response.data!.category : cat
            )

            set({
              categories: updatedCategories,
              selectedCategory: currentState.selectedCategory?.id === categoryId
                ? response.data.category
                : currentState.selectedCategory,
              isUploadingImages: false
            })

            return response.data.category
          } else {
            set({
              error: response.error || 'Failed to upload category images',
              isUploadingImages: false
            })
            return null
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to upload category images',
            isUploadingImages: false
          })
          return null
        }
      },

      removeCategoryImages: async (categoryId: string, imageType?: 'featured' | 'icon') => {
        set({ isUploadingImages: true, error: null })
        try {
          const response = await categoryService.removeCategoryImages(categoryId, imageType)

          if (response.success && response.data) {
            const currentState = get()
            const updatedCategories = currentState.categories.map(cat =>
              cat.id === categoryId ? response.data!.category : cat
            )

            set({
              categories: updatedCategories,
              selectedCategory: currentState.selectedCategory?.id === categoryId
                ? response.data.category
                : currentState.selectedCategory,
              isUploadingImages: false
            })

            return response.data.category
          } else {
            set({
              error: response.error || 'Failed to remove category images',
              isUploadingImages: false
            })
            return null
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to remove category images',
            isUploadingImages: false
          })
          return null
        }
      },

      // UI Actions
      setSelectedCategory: (category: ProductCategory | null) => {
        set({ selectedCategory: category })
      },

      setFilters: (filters: Partial<CategoryState['filters']>) => {
        set(state => ({
          filters: { ...state.filters, ...filters }
        }))
      },

      setPagination: (pagination: Partial<CategoryState['pagination']>) => {
        set(state => ({
          pagination: { ...state.pagination, ...pagination }
        }))
      },

      clearError: () => {
        set({ error: null })
      },

      reset: () => {
        set(initialState)
      }
    }),
    {
      name: 'category-store'
    }
  )
)
