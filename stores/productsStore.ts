// stores/productsStore.ts - Products and categories state management with Zustand

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { productService, handleServiceError } from '@/services/frontend'
import type { 
  Product, 
  ProductCategory, 
  ProductStatus,
  CreateProductData,
  UpdateProductData,
  ProductFilters,
  PaginationParams,
  ApiResponse 
} from '@/types/frontend'

// ============================================================================
// Products Store Types
// ============================================================================

export interface ProductsState {
  // Products State
  products: Product[]
  selectedProduct: Product | null
  isLoading: boolean
  error: string | null
  
  // Pagination & Filtering
  currentPage: number
  totalPages: number
  totalProducts: number
  pageSize: number
  filters: ProductFilters
  searchQuery: string
  
  // Categories
  categories: ProductCategory[]
  selectedCategory: ProductCategory | null
  
  // Featured Products
  featuredProducts: Product[]
  
  // Low Stock Products
  lowStockProducts: Product[]
  
  // Actions - Products CRUD
  fetchProducts: (pagination?: PaginationParams, filters?: ProductFilters) => Promise<{ success: boolean; error?: string }>
  fetchProductById: (productId: string) => Promise<{ success: boolean; error?: string }>
  createProduct: (productData: CreateProductData) => Promise<{ success: boolean; error?: string; product?: Product }>
  updateProduct: (productId: string, updates: UpdateProductData) => Promise<{ success: boolean; error?: string }>
  deleteProduct: (productId: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Categories
  fetchProductsByCategory: (category: ProductCategory, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  setSelectedCategory: (category: ProductCategory | null) => void
  
  // Actions - Featured Products
  fetchFeaturedProducts: (pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  toggleProductFeatured: (productId: string, isFeatured: boolean) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Stock Management
  fetchLowStockProducts: (branchId?: string, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  updateProductStock: (productId: string, stock: number) => Promise<{ success: boolean; error?: string }>
  updateProductStatus: (productId: string, status: ProductStatus) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Search & Filtering
  searchProducts: (query: string, pagination?: PaginationParams, filters?: Omit<ProductFilters, 'search'>) => Promise<{ success: boolean; error?: string }>
  setFilters: (filters: Partial<ProductFilters>) => void
  clearFilters: () => void
  setSearchQuery: (query: string) => void
  
  // Actions - Pagination
  setCurrentPage: (page: number) => void
  setPageSize: (size: number) => void
  
  // Actions - UI State
  setSelectedProduct: (product: Product | null) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  
  // Utility Methods
  getProductsByBranch: (branchId: string) => Product[]
  getProductsByStatus: (status: ProductStatus) => Product[]
  getAvailableCategories: () => ProductCategory[]
  getAvailableStatuses: () => ProductStatus[]
  calculateTotalValue: (products?: Product[]) => number
  isProductLowStock: (product: Product) => boolean
  formatProductPrice: (price: number) => string
}

// ============================================================================
// Products Store Implementation
// ============================================================================

export const useProductsStore = create<ProductsState>()(
  immer((set, get) => ({
    // Initial state
    products: [],
    selectedProduct: null,
    isLoading: false,
    error: null,
    
    // Pagination & Filtering
    currentPage: 1,
    totalPages: 0,
    totalProducts: 0,
    pageSize: 20,
    filters: {},
    searchQuery: '',
    
    // Categories
    categories: productService.getProductCategories(),
    selectedCategory: null,
    
    // Featured Products
    featuredProducts: [],
    
    // Low Stock Products
    lowStockProducts: [],

    // Actions - Products CRUD
    fetchProducts: async (pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await productService.getProducts(pagination, filters)

        if (response.success) {
          set((state) => {
            state.products = response.data || []
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalProducts = response.pagination?.total || (response.data || []).length
            state.filters = filters
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch products'
          })

          return { success: false, error: response.error || 'Failed to fetch products' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchProductById: async (productId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await productService.getProductById(productId)

        if (response.success && response.data) {
          set((state) => {
            state.selectedProduct = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch product'
          })

          return { success: false, error: response.error || 'Failed to fetch product' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    createProduct: async (productData: CreateProductData) => {
      console.log('🏪 ProductsStore.createProduct called')
      console.log('📦 Store received data:', JSON.stringify(productData, null, 2))

      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate product data
        console.log('🔍 Validating product data...')
        const validation = productService.validateProductData(productData)
        console.log('✅ Validation result:', validation)

        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          console.log('❌ Validation failed:', errorMessage)
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        console.log('📞 Calling productService.createProduct...')
        const response = await productService.createProduct(productData)
        console.log('📊 ProductService response:', JSON.stringify(response, null, 2))

        if (response.success && response.data) {
          set((state) => {
            state.products.unshift(response.data!)
            state.totalProducts += 1
            state.isLoading = false
            state.error = null
          })

          return { success: true, product: response.data }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to create product'
          })

          return { success: false, error: response.error || 'Failed to create product' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    updateProduct: async (productId: string, updates: UpdateProductData) => {
      console.log('🔄 ProductsStore.updateProduct called')
      console.log('📝 Product ID:', productId)
      console.log('📦 Updates:', JSON.stringify(updates, null, 2))

      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate product data
        console.log('🔍 Validating product data...')
        const validation = productService.validateProductData(updates)
        console.log('✅ Validation result:', validation)

        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          console.log('❌ Validation failed:', errorMessage)
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        console.log('🚀 Calling productService.updateProduct...')
        const response = await productService.updateProduct(productId, updates)
        console.log('📥 Update response:', JSON.stringify(response, null, 2))

        if (response.success && response.data) {
          set((state) => {
            const index = state.products.findIndex(p => p._id === productId)
            if (index !== -1) {
              state.products[index] = response.data!
            }
            
            if (state.selectedProduct?._id === productId) {
              state.selectedProduct = response.data!
            }
            
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to update product'
          })

          return { success: false, error: response.error || 'Failed to update product' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    deleteProduct: async (productId: string) => {
      console.log('🗑️ ProductsStore.deleteProduct called')
      console.log('📝 Product ID:', productId)

      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        console.log('🚀 Calling productService.deleteProduct...')
        const response = await productService.deleteProduct(productId)
        console.log('📥 Delete response:', JSON.stringify(response, null, 2))

        if (response.success) {
          set((state) => {
            state.products = state.products.filter(p => p._id !== productId)
            state.featuredProducts = state.featuredProducts.filter(p => p._id !== productId)
            state.lowStockProducts = state.lowStockProducts.filter(p => p._id !== productId)
            
            if (state.selectedProduct?._id === productId) {
              state.selectedProduct = null
            }
            
            state.totalProducts = Math.max(0, state.totalProducts - 1)
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to delete product'
          })

          return { success: false, error: response.error || 'Failed to delete product' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Categories
    fetchProductsByCategory: async (category: ProductCategory, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
        state.selectedCategory = category
      })

      try {
        const response = await productService.getProductsByCategory(category, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.products = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalProducts = response.pagination?.total || response.data!.length
            state.filters = { ...state.filters, category }
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch products by category'
          })

          return { success: false, error: response.error || 'Failed to fetch products by category' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    setSelectedCategory: (category: ProductCategory | null) => {
      set((state) => {
        state.selectedCategory = category
        if (category) {
          state.filters.category = category
        } else {
          delete state.filters.category
        }
      })
    },

    // Actions - Featured Products
    fetchFeaturedProducts: async (pagination = { page: 1, limit: 12 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await productService.getFeaturedProducts(pagination)

        if (response.success && response.data) {
          set((state) => {
            state.featuredProducts = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch featured products'
          })

          return { success: false, error: response.error || 'Failed to fetch featured products' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    toggleProductFeatured: async (productId: string, isFeatured: boolean) => {
      try {
        const response = await productService.toggleFeatured(productId, isFeatured)

        if (response.success && response.data) {
          set((state) => {
            // Update in main products list
            const productIndex = state.products.findIndex(p => p._id === productId)
            if (productIndex !== -1) {
              state.products[productIndex] = response.data!
            }
            
            // Update selected product
            if (state.selectedProduct?._id === productId) {
              state.selectedProduct = response.data!
            }
            
            // Update featured products list
            if (isFeatured) {
              const existingIndex = state.featuredProducts.findIndex(p => p._id === productId)
              if (existingIndex === -1) {
                state.featuredProducts.push(response.data!)
              } else {
                state.featuredProducts[existingIndex] = response.data!
              }
            } else {
              state.featuredProducts = state.featuredProducts.filter(p => p._id !== productId)
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to toggle featured status' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Stock Management
    fetchLowStockProducts: async (branchId?: string, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await productService.getLowStockProducts(branchId, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.lowStockProducts = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch low stock products'
          })

          return { success: false, error: response.error || 'Failed to fetch low stock products' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    updateProductStock: async (productId: string, stock: number) => {
      try {
        const response = await productService.updateProductStock(productId, stock)

        if (response.success && response.data) {
          set((state) => {
            // Update in main products list
            const productIndex = state.products.findIndex(p => p._id === productId)
            if (productIndex !== -1) {
              state.products[productIndex] = response.data!
            }
            
            // Update selected product
            if (state.selectedProduct?._id === productId) {
              state.selectedProduct = response.data!
            }
            
            // Update low stock products list
            const lowStockIndex = state.lowStockProducts.findIndex(p => p._id === productId)
            if (lowStockIndex !== -1) {
              if (get().isProductLowStock(response.data!)) {
                state.lowStockProducts[lowStockIndex] = response.data!
              } else {
                state.lowStockProducts.splice(lowStockIndex, 1)
              }
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update product stock' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    updateProductStatus: async (productId: string, status: ProductStatus) => {
      try {
        const response = await productService.updateProductStatus(productId, status)

        if (response.success && response.data) {
          set((state) => {
            // Update in main products list
            const productIndex = state.products.findIndex(p => p._id === productId)
            if (productIndex !== -1) {
              state.products[productIndex] = response.data!
            }
            
            // Update selected product
            if (state.selectedProduct?._id === productId) {
              state.selectedProduct = response.data!
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update product status' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Search & Filtering
    searchProducts: async (query: string, pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
        state.searchQuery = query
      })

      try {
        const response = await productService.searchProducts(query, pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.products = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalProducts = response.pagination?.total || response.data!.length
            state.filters = { ...filters, search: query }
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to search products'
          })

          return { success: false, error: response.error || 'Failed to search products' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    setFilters: (filters: Partial<ProductFilters>) => {
      set((state) => {
        state.filters = { ...state.filters, ...filters }
      })
    },

    clearFilters: () => {
      set((state) => {
        state.filters = {}
        state.selectedCategory = null
        state.searchQuery = ''
      })
    },

    setSearchQuery: (query: string) => {
      set((state) => {
        state.searchQuery = query
      })
    },

    // Actions - Pagination
    setCurrentPage: (page: number) => {
      set((state) => {
        state.currentPage = page
      })
    },

    setPageSize: (size: number) => {
      set((state) => {
        state.pageSize = size
      })
    },

    // Actions - UI State
    setSelectedProduct: (product: Product | null) => {
      set((state) => {
        state.selectedProduct = product
      })
    },

    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    setLoading: (loading: boolean) => {
      set((state) => {
        state.isLoading = loading
      })
    },

    // Utility Methods
    getProductsByBranch: (branchId: string) => {
      return get().products.filter(product => product.branchId === branchId)
    },

    getProductsByStatus: (status: ProductStatus) => {
      return get().products.filter(product => product.status === status)
    },

    getAvailableCategories: () => {
      return productService.getProductCategories()
    },

    getAvailableStatuses: () => {
      return productService.getProductStatuses()
    },

    calculateTotalValue: (products?: Product[]) => {
      const productsToCalculate = products || get().products
      return productsToCalculate.reduce((total, product) => total + (product.price * product.stock), 0)
    },

    isProductLowStock: (product: Product) => {
      return product.stock <= product.minStockLevel
    },

    formatProductPrice: (price: number) => {
      return productService.formatPrice(price)
    }
  }))
)
