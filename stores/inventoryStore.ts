// stores/inventoryStore.ts - Inventory management state with Zustand

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { inventoryService, handleServiceError } from '@/services/frontend'
import type {
  Inventory,
  CreateInventoryData,
  UpdateInventoryData,
  StockMovement,
  InventoryFilters,
  PaginationParams,
  ApiResponse
} from '@/types/frontend'

// Error types for better error handling
export interface InventoryError {
  type: 'network' | 'validation' | 'permission' | 'server' | 'unknown'
  message: string
  code?: string
  details?: any
  timestamp: string
  retryable: boolean
}

// ============================================================================
// Inventory Store Types
// ============================================================================

export interface InventoryState {
  // Inventory State
  inventoryItems: Inventory[]
  selectedItem: Inventory | null
  isLoading: boolean
  error: InventoryError | null

  // Loading states for different operations
  loadingStates: {
    fetching: boolean
    creating: boolean
    updating: boolean
    deleting: boolean
    stockUpdate: boolean
  }

  // Pagination & Filtering
  currentPage: number
  totalPages: number
  totalItems: number
  pageSize: number
  filters: InventoryFilters
  searchQuery: string

  // Retry mechanism
  retryCount: number
  maxRetries: number
  
  // Actions - Inventory CRUD
  fetchInventoryItems: (pagination?: PaginationParams, filters?: InventoryFilters) => Promise<{ success: boolean; error?: string }>
  fetchInventoryItemById: (itemId: string) => Promise<{ success: boolean; error?: string }>
  createInventoryItem: (itemData: CreateInventoryData) => Promise<{ success: boolean; error?: string; item?: Inventory }>
  updateInventoryItem: (itemId: string, updates: UpdateInventoryData) => Promise<{ success: boolean; error?: string }>

  // Actions - Stock Management
  updateStock: (itemId: string, movement: StockMovement) => Promise<{ success: boolean; error?: string }>

  // Actions - Inventory Queries
  fetchInventoryByBranch: (branchId: string, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>

  // Actions - Search & Filtering
  searchInventory: (query: string, pagination?: PaginationParams, filters?: Omit<InventoryFilters, 'search'>) => Promise<{ success: boolean; error?: string }>
  setFilters: (filters: Partial<InventoryFilters>) => void
  clearFilters: () => void
  setSearchQuery: (query: string) => void

  // Actions - Pagination
  setCurrentPage: (page: number) => void
  setPageSize: (size: number) => void

  // Actions - UI State
  setSelectedItem: (item: Inventory | null) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  setLoadingState: (operation: keyof InventoryState['loadingStates'], loading: boolean) => void

  // Error handling
  handleError: (error: any, context?: string) => InventoryError
  retryLastOperation: () => Promise<void>
  canRetry: () => boolean

  // Utility Methods
  getInventoryByBranch: (branchId: string) => Inventory[]
  getLowStockItems: (branchId?: string) => Inventory[]
  getOutOfStockItems: (branchId?: string) => Inventory[]
}

// ============================================================================
// Inventory Store Implementation
// ============================================================================

export const useInventoryStore = create<InventoryState>()(
  immer((set, get) => ({
    // Initial state
    inventoryItems: [],
    selectedItem: null,
    isLoading: false,
    error: null,

    // Loading states
    loadingStates: {
      fetching: false,
      creating: false,
      updating: false,
      deleting: false,
      stockUpdate: false
    },

    // Pagination & Filtering
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 20,
    filters: {},
    searchQuery: '',

    // Retry mechanism
    retryCount: 0,
    maxRetries: 3,

    // Actions - Inventory CRUD
    fetchInventoryItems: async (pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await inventoryService.getInventory(pagination, filters)



        if (response.success) {
          // Handle successful response - data can be empty array
          set((state) => {
            state.inventoryItems = response.data || []
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalItems = response.pagination?.total || 0
            state.filters = filters
            state.isLoading = false
            state.error = null
          })



          return { success: true }
        } else {
          // Handle API error response
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch inventory items'
          })

          return { success: false, error: response.error || 'Failed to fetch inventory items' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)

        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchInventoryItemById: async (itemId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await inventoryService.getInventoryItemById(itemId)

        if (response.success && response.data) {
          set((state) => {
            state.selectedItem = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch inventory item'
          })

          return { success: false, error: response.error || 'Failed to fetch inventory item' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    createInventoryItem: async (itemData: CreateInventoryData) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate inventory item data
        const validation = inventoryService.validateInventoryData(itemData)
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        const response = await inventoryService.createInventory(itemData)

        if (response.success && response.data) {
          set((state) => {
            state.inventoryItems.unshift(response.data!)
            state.totalItems += 1
            state.isLoading = false
            state.error = null
          })

          return { success: true, item: response.data }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to create inventory item'
          })

          return { success: false, error: response.error || 'Failed to create inventory item' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)

        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    updateInventoryItem: async (itemId: string, updates: UpdateInventoryData) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate inventory item data
        const validation = inventoryService.validateInventoryData(updates)
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        const response = await inventoryService.updateInventory(itemId, updates)

        if (response.success && response.data) {
          set((state) => {
            const index = state.inventoryItems.findIndex(item => item._id === itemId)
            if (index !== -1) {
              state.inventoryItems[index] = response.data!
            }

            if (state.selectedItem?._id === itemId) {
              state.selectedItem = response.data!
            }

            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to update inventory item'
          })

          return { success: false, error: response.error || 'Failed to update inventory item' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)

        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Stock Management
    updateStock: async (itemId: string, movement: StockMovement) => {
      try {
        const response = await inventoryService.updateStock(itemId, movement)

        if (response.success && response.data) {
          set((state) => {
            const index = state.inventoryItems.findIndex(item => item._id === itemId)
            if (index !== -1) {
              state.inventoryItems[index] = response.data!
            }

            if (state.selectedItem?._id === itemId) {
              state.selectedItem = response.data!
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update stock' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },





    // Actions - Inventory Queries
    fetchInventoryByBranch: async (branchId: string, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await inventoryService.getInventoryByBranch(branchId, pagination)

        if (response.success) {
          // Handle successful response - data can be empty array
          set((state) => {
            state.inventoryItems = response.data || []
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalItems = response.pagination?.total || 0
            state.filters = { ...state.filters, branchId }
            state.isLoading = false
            state.error = null
          })



          return { success: true }
        } else {
          // Handle API error response
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch inventory by branch'
          })

          return { success: false, error: response.error || 'Failed to fetch inventory by branch' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)

        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Search & Filtering
    searchInventory: async (query: string, pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
        state.searchQuery = query
      })

      try {
        const response = await inventoryService.searchInventory(query, pagination, filters)

        if (response.success) {
          // Handle successful response - data can be empty array
          set((state) => {
            state.inventoryItems = response.data || []
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalItems = response.pagination?.total || 0
            state.filters = { ...filters, search: query }
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          // Handle API error response
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to search inventory'
          })

          return { success: false, error: response.error || 'Failed to search inventory' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)

        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    setFilters: (filters: Partial<InventoryFilters>) => {
      set((state) => {
        state.filters = { ...state.filters, ...filters }
      })
    },

    clearFilters: () => {
      set((state) => {
        state.filters = {}
        state.searchQuery = ''
      })
    },

    setSearchQuery: (query: string) => {
      set((state) => {
        state.searchQuery = query
      })
    },

    // Actions - Pagination
    setCurrentPage: (page: number) => {
      set((state) => {
        state.currentPage = page
      })
    },

    setPageSize: (size: number) => {
      set((state) => {
        state.pageSize = size
      })
    },

    // Actions - UI State
    setSelectedItem: (item: Inventory | null) => {
      set((state) => {
        state.selectedItem = item
      })
    },

    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    setLoading: (loading: boolean) => {
      set((state) => {
        state.isLoading = loading
      })
    },

    setLoadingState: (operation: keyof InventoryState['loadingStates'], loading: boolean) => {
      set((state) => {
        state.loadingStates[operation] = loading
      })
    },

    // Error handling
    handleError: (error: any, context?: string): InventoryError => {
      const errorInfo = handleServiceError(error)

      let errorType: InventoryError['type'] = 'unknown'
      let retryable = false

      // Determine error type based on error details
      if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('fetch')) {
        errorType = 'network'
        retryable = true
      } else if (error?.status === 400 || error?.message?.includes('validation')) {
        errorType = 'validation'
        retryable = false
      } else if (error?.status === 403 || error?.status === 401) {
        errorType = 'permission'
        retryable = false
      } else if (error?.status >= 500) {
        errorType = 'server'
        retryable = true
      }

      const inventoryError: InventoryError = {
        type: errorType,
        message: errorInfo.message,
        code: error?.code || error?.status?.toString(),
        details: error,
        timestamp: new Date().toISOString(),
        retryable
      }

      set((state) => {
        state.error = inventoryError
        if (!retryable) {
          state.retryCount = 0
        }
      })

      return inventoryError
    },

    retryLastOperation: async () => {
      const state = get()
      if (!state.canRetry()) return

      set((state) => {
        state.retryCount += 1
        state.error = null
      })

      // This would need to store the last operation and retry it
      // For now, we'll just clear the error and let the user retry manually
    },

    canRetry: () => {
      const state = get()
      return state.error?.retryable && state.retryCount < state.maxRetries
    },

    // Utility Methods
    getInventoryByBranch: (branchId: string) => {
      return get().inventoryItems.filter(item => item.branchId === branchId)
    },

    getLowStockItems: (branchId?: string) => {
      const items = branchId
        ? get().inventoryItems.filter(item => item.branchId === branchId)
        : get().inventoryItems
      return items.filter(item => inventoryService.isLowStock(item))
    },

    getOutOfStockItems: (branchId?: string) => {
      const items = branchId
        ? get().inventoryItems.filter(item => item.branchId === branchId)
        : get().inventoryItems
      return items.filter(item => inventoryService.isOutOfStock(item))
    }


  }))
)
