// stores/notificationStore.ts - Notification management state with Zustand
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { apiClient } from '@/services/frontend'
import { socketClient } from '@/lib/socket/client'
import type {
  PlatformNotification,
  NotificationCategory,
  NotificationPriority,
  NotificationStatus,
  NotificationPreferences
} from '@/types/notifications'

export interface NotificationState {
  // Notifications data
  notifications: PlatformNotification[]
  unreadCount: number
  totalCount: number
  
  // Preferences
  preferences: NotificationPreferences | null
  
  // UI state
  isLoading: boolean
  isLoadingPreferences: boolean
  error: string | null
  
  // Filters and pagination
  filters: {
    category?: NotificationCategory
    priority?: NotificationPriority
    status?: NotificationStatus
    dateFrom?: Date
    dateTo?: Date
  }
  pagination: {
    limit: number
    skip: number
    hasMore: boolean
  }
  
  // Real-time state
  isConnected: boolean
  lastUpdated: Date | null
}

export interface NotificationActions {
  // Notification management
  fetchNotifications: (refresh?: boolean) => Promise<void>
  markAsRead: (notificationId: string) => Promise<void>
  markMultipleAsRead: (notificationIds: string[]) => Promise<void>
  markAllAsRead: () => Promise<void>
  
  // Real-time updates
  addNotification: (notification: PlatformNotification) => void
  updateNotification: (notificationId: string, updates: Partial<PlatformNotification>) => void
  removeNotification: (notificationId: string) => void
  
  // Preferences
  fetchPreferences: () => Promise<void>
  updatePreferences: (preferences: Partial<NotificationPreferences>) => Promise<void>
  resetPreferences: () => Promise<void>
  
  // Filters and pagination
  setFilters: (filters: Partial<NotificationState['filters']>) => void
  clearFilters: () => void
  loadMore: () => Promise<void>
  
  // UI state
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setConnected: (connected: boolean) => void
  
  // Socket.IO integration
  connectSocket: () => void
  disconnectSocket: () => void

  // Utility
  getNotificationsByCategory: (category: NotificationCategory) => PlatformNotification[]
  getNotificationsByPriority: (priority: NotificationPriority) => PlatformNotification[]
  getUnreadNotifications: () => PlatformNotification[]
  clearAll: () => void
}

export type NotificationStore = NotificationState & NotificationActions

export const useNotificationStore = create<NotificationStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        notifications: [],
        unreadCount: 0,
        totalCount: 0,
        preferences: null,
        isLoading: false,
        isLoadingPreferences: false,
        error: null,
        filters: {},
        pagination: {
          limit: 50,
          skip: 0,
          hasMore: false
        },
        isConnected: false,
        lastUpdated: null,

        // Actions
        fetchNotifications: async (refresh = false) => {
          const state = get()

          if (state.isLoading) return

          // Check if we have an auth token before making the request
          const authToken = apiClient.getAuthToken()
          if (!authToken) {
            console.warn('No auth token available for notifications')
            set({ error: 'Authentication required', isLoading: false })
            return
          }

          set({ isLoading: true, error: null })

          try {
            const params = new URLSearchParams()

            // Add filters
            if (state.filters.category) params.append('category', state.filters.category)
            if (state.filters.priority) params.append('priority', state.filters.priority)
            if (state.filters.status) params.append('status', state.filters.status)
            if (state.filters.dateFrom) params.append('dateFrom', state.filters.dateFrom.toISOString())
            if (state.filters.dateTo) params.append('dateTo', state.filters.dateTo.toISOString())

            // Add pagination
            params.append('limit', state.pagination.limit.toString())
            params.append('skip', refresh ? '0' : state.pagination.skip.toString())

            const response = await apiClient.get<{
              notifications: PlatformNotification[]
              total: number
              pagination?: { hasMore: boolean }
            }>(`/api/notifications?${params.toString()}`)

            if (response.success && response.data) {
              const newNotifications = response.data.notifications || []

              set({
                notifications: refresh ? newNotifications : [...state.notifications, ...newNotifications],
                totalCount: response.data.total || 0,
                unreadCount: newNotifications.filter((n: PlatformNotification) => n.status === 'unread').length,
                pagination: {
                  ...state.pagination,
                  skip: refresh ? newNotifications.length : state.pagination.skip + newNotifications.length,
                  hasMore: response.data.pagination?.hasMore || false
                },
                lastUpdated: new Date(),
                isLoading: false
              })
            } else {
              set({ error: response.error || 'Failed to fetch notifications', isLoading: false })
            }
          } catch (error) {
            console.error('Fetch notifications error:', error)
            set({ error: 'Failed to fetch notifications', isLoading: false })
          }
        },

        markAsRead: async (notificationId: string) => {
          try {
            // Check if we have an auth token before making the request
            const authToken = apiClient.getAuthToken()
            if (!authToken) {
              console.warn('No auth token available for marking notification as read')
              return
            }

            const response = await apiClient.patch('/api/notifications', {
              action: 'mark_read',
              notificationId
            })
            
            if (response.success) {
              set(state => ({
                notifications: state.notifications.map(n => 
                  n.id === notificationId 
                    ? { ...n, status: 'read' as NotificationStatus, readAt: new Date() }
                    : n
                ),
                unreadCount: Math.max(0, state.unreadCount - 1)
              }))
            }
          } catch (error) {
            console.error('Mark as read error:', error)
            set({ error: 'Failed to mark notification as read' })
          }
        },

        markMultipleAsRead: async (notificationIds: string[]) => {
          try {
            // Check if we have an auth token before making the request
            const authToken = apiClient.getAuthToken()
            if (!authToken) {
              console.warn('No auth token available for marking notifications as read')
              return
            }

            const response = await apiClient.patch('/api/notifications', {
              action: 'mark_read',
              notificationIds
            })
            
            if (response.success) {
              set(state => {
                const updatedNotifications = state.notifications.map(n => 
                  notificationIds.includes(n.id)
                    ? { ...n, status: 'read' as NotificationStatus, readAt: new Date() }
                    : n
                )
                
                const unreadReduction = notificationIds.filter(id => 
                  state.notifications.find(n => n.id === id && n.status === 'unread')
                ).length
                
                return {
                  notifications: updatedNotifications,
                  unreadCount: Math.max(0, state.unreadCount - unreadReduction)
                }
              })
            }
          } catch (error) {
            console.error('Mark multiple as read error:', error)
            set({ error: 'Failed to mark notifications as read' })
          }
        },

        markAllAsRead: async () => {
          const state = get()
          const unreadIds = state.notifications
            .filter(n => n.status === 'unread')
            .map(n => n.id)
          
          if (unreadIds.length > 0) {
            await get().markMultipleAsRead(unreadIds)
          }
        },

        addNotification: (notification: PlatformNotification) => {
          set(state => ({
            notifications: [notification, ...state.notifications],
            unreadCount: notification.status === 'unread' ? state.unreadCount + 1 : state.unreadCount,
            totalCount: state.totalCount + 1,
            lastUpdated: new Date()
          }))
        },

        updateNotification: (notificationId: string, updates: Partial<PlatformNotification>) => {
          set((state) => ({
            notifications: state.notifications.map(n =>
              n.id === notificationId
                ? { ...n, ...updates } as PlatformNotification
                : n
            ),
            lastUpdated: new Date()
          }))
        },

        removeNotification: (notificationId: string) => {
          set(state => {
            const notification = state.notifications.find(n => n.id === notificationId)
            const wasUnread = notification?.status === 'unread'
            
            return {
              notifications: state.notifications.filter(n => n.id !== notificationId),
              unreadCount: wasUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount,
              totalCount: Math.max(0, state.totalCount - 1),
              lastUpdated: new Date()
            }
          })
        },

        fetchPreferences: async () => {
          // Check if we have an auth token before making the request
          const authToken = apiClient.getAuthToken()
          if (!authToken) {
            console.warn('No auth token available for fetching preferences')
            return
          }

          set({ isLoadingPreferences: true, error: null })

          try {
            const response = await apiClient.get<{ preferences: NotificationPreferences }>('/api/notifications/preferences')

            if (response.success && response.data) {
              set({ preferences: response.data.preferences, isLoadingPreferences: false })
            } else {
              set({ error: response.error || 'Failed to fetch preferences', isLoadingPreferences: false })
            }
          } catch (error) {
            console.error('Fetch preferences error:', error)
            set({ error: 'Failed to fetch preferences', isLoadingPreferences: false })
          }
        },

        updatePreferences: async (preferences: Partial<NotificationPreferences>) => {
          // Check if we have an auth token before making the request
          const authToken = apiClient.getAuthToken()
          if (!authToken) {
            console.warn('No auth token available for updating preferences')
            return
          }

          set({ isLoadingPreferences: true, error: null })

          try {
            const response = await apiClient.put<{ preferences: NotificationPreferences }>('/api/notifications/preferences', preferences)

            if (response.success && response.data) {
              set({ preferences: response.data.preferences, isLoadingPreferences: false })
            } else {
              set({ error: response.error || 'Failed to update preferences', isLoadingPreferences: false })
            }
          } catch (error) {
            console.error('Update preferences error:', error)
            set({ error: 'Failed to update preferences', isLoadingPreferences: false })
          }
        },

        resetPreferences: async () => {
          // Check if we have an auth token before making the request
          const authToken = apiClient.getAuthToken()
          if (!authToken) {
            console.warn('No auth token available for resetting preferences')
            return
          }

          set({ isLoadingPreferences: true, error: null })

          try {
            const response = await apiClient.post<{ preferences: NotificationPreferences }>('/api/notifications/preferences/reset')

            if (response.success && response.data) {
              set({ preferences: response.data.preferences, isLoadingPreferences: false })
            } else {
              set({ error: response.error || 'Failed to reset preferences', isLoadingPreferences: false })
            }
          } catch (error) {
            console.error('Reset preferences error:', error)
            set({ error: 'Failed to reset preferences', isLoadingPreferences: false })
          }
        },

        setFilters: (filters: Partial<NotificationState['filters']>) => {
          set(state => ({
            filters: { ...state.filters, ...filters },
            pagination: { ...state.pagination, skip: 0 } // Reset pagination when filters change
          }))
          
          // Fetch notifications with new filters
          get().fetchNotifications(true)
        },

        clearFilters: () => {
          set({
            filters: {},
            pagination: { limit: 50, skip: 0, hasMore: false }
          })
          
          // Fetch notifications without filters
          get().fetchNotifications(true)
        },

        loadMore: async () => {
          const state = get()
          if (state.pagination.hasMore && !state.isLoading) {
            await get().fetchNotifications(false)
          }
        },

        setLoading: (loading: boolean) => set({ isLoading: loading }),
        setError: (error: string | null) => set({ error }),
        setConnected: (connected: boolean) => set({ isConnected: connected }),

        getNotificationsByCategory: (category: NotificationCategory) => {
          return get().notifications.filter(n => n.category === category)
        },

        getNotificationsByPriority: (priority: NotificationPriority) => {
          return get().notifications.filter(n => n.priority === priority)
        },

        getUnreadNotifications: () => {
          return get().notifications.filter(n => n.status === 'unread')
        },

        connectSocket: () => {
          const socket = socketClient.getSocket()
          if (!socket) return

          // Listen for real-time notifications
          socket.on('notification', (data: any) => {
            // Transform the socket data to a proper PlatformNotification
            const notification: PlatformNotification = {
              ...data,
              status: data.status || 'unread',
              target: data.target || { userIds: [] },
              createdBy: data.createdBy || 'system',
              source: data.source || 'socket',
              channels: data.channels || ['in_app'],
              deliveryStatus: data.deliveryStatus || { in_app: 'delivered' }
            } as PlatformNotification

            get().addNotification(notification)

            // Show browser notification if permission granted
            if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
              new Notification(notification.title, {
                body: notification.message,
                icon: notification.icon || '/favicon.ico',
                badge: notification.badge,
                tag: notification.id
              })
            }
          })

          // Listen for notification read events
          socket.on('notificationRead', ({ notificationId }) => {
            get().updateNotification(notificationId, { status: 'read', readAt: new Date() })
          })

          // Listen for notification count updates
          socket.on('notificationCountUpdated', ({ unreadCount, totalCount }) => {
            set({ unreadCount, totalCount })
          })

          // Join notification room
          socket.emit('joinNotificationRoom')

          set({ isConnected: true })
        },

        disconnectSocket: () => {
          const socket = socketClient.getSocket()
          if (!socket) return

          // Remove listeners
          socket.off('notification')
          socket.off('notificationRead')
          socket.off('notificationCountUpdated')

          // Leave notification room
          socket.emit('leaveNotificationRoom')

          set({ isConnected: false })
        },

        clearAll: () => {
          set({
            notifications: [],
            unreadCount: 0,
            totalCount: 0,
            preferences: null,
            isLoading: false,
            isLoadingPreferences: false,
            error: null,
            filters: {},
            pagination: { limit: 50, skip: 0, hasMore: false },
            lastUpdated: null
          })
        }
      }),
      {
        name: 'notification-store',
        partialize: (state) => ({
          preferences: state.preferences,
          filters: state.filters,
          pagination: state.pagination
        })
      }
    ),
    { name: 'notification-store' }
  )
)
