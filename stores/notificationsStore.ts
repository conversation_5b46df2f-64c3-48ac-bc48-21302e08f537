// stores/notificationsStore.ts - Notifications and real-time messaging state with <PERSON><PERSON><PERSON> and Socket.IO

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { notificationsService, socketService, handleServiceError } from '@/services/frontend'
import type { 
  Notification, 
  NotificationType,
  NotificationPriority,
  NotificationStatus,
  NotificationFilters,
  NotificationSettings,
  NotificationTemplate,
  NotificationChannel,
  RealTimeEvent,
  SocketEvent,
  PushSubscription,
  EmailNotification,
  SMSNotification,
  InAppNotification,
  SystemAlert,
  UserMessage,
  PaginationParams,
  ApiResponse 
} from '@/types/frontend'

// ============================================================================
// Notifications Store Types
// ============================================================================

export interface NotificationsState {
  // Notifications State
  notifications: Notification[]
  unreadNotifications: Notification[]
  selectedNotification: Notification | null
  isLoading: boolean
  error: string | null
  
  // Real-time State
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  lastHeartbeat: string | null
  
  // Notification Counts
  unreadCount: number
  totalCount: number
  priorityCount: {
    low: number
    medium: number
    high: number
    urgent: number
  }
  
  // Notification Types
  systemAlerts: SystemAlert[]
  userMessages: UserMessage[]
  emailNotifications: EmailNotification[]
  smsNotifications: SMSNotification[]
  inAppNotifications: InAppNotification[]
  
  // Settings & Templates
  notificationSettings: NotificationSettings | null
  notificationTemplates: NotificationTemplate[]
  enabledChannels: NotificationChannel[]
  
  // Push Notifications
  pushSubscription: PushSubscription | null
  pushEnabled: boolean
  
  // Pagination & Filtering
  currentPage: number
  totalPages: number
  pageSize: number
  filters: NotificationFilters
  searchQuery: string
  
  // Actions - Socket.IO Connection
  connectSocket: (userId: string, token: string) => Promise<{ success: boolean; error?: string }>
  disconnectSocket: () => void
  reconnectSocket: () => Promise<{ success: boolean; error?: string }>
  
  // Actions - Notifications CRUD
  fetchNotifications: (pagination?: PaginationParams, filters?: NotificationFilters) => Promise<{ success: boolean; error?: string }>
  fetchNotificationById: (notificationId: string) => Promise<{ success: boolean; error?: string }>
  createNotification: (notificationData: Partial<Notification>) => Promise<{ success: boolean; error?: string; notification?: Notification }>
  updateNotification: (notificationId: string, updates: Partial<Notification>) => Promise<{ success: boolean; error?: string }>
  deleteNotification: (notificationId: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Notification Management
  markAsRead: (notificationId: string) => Promise<{ success: boolean; error?: string }>
  markAsUnread: (notificationId: string) => Promise<{ success: boolean; error?: string }>
  markAllAsRead: () => Promise<{ success: boolean; error?: string }>
  archiveNotification: (notificationId: string) => Promise<{ success: boolean; error?: string }>
  deleteMultiple: (notificationIds: string[]) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Real-time Events
  sendNotification: (recipientId: string, notification: Partial<Notification>) => Promise<{ success: boolean; error?: string }>
  broadcastNotification: (notification: Partial<Notification>, userIds?: string[]) => Promise<{ success: boolean; error?: string }>
  sendSystemAlert: (alert: Partial<SystemAlert>) => Promise<{ success: boolean; error?: string }>
  sendUserMessage: (message: Partial<UserMessage>) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Push Notifications
  subscribeToPush: () => Promise<{ success: boolean; error?: string }>
  unsubscribeFromPush: () => Promise<{ success: boolean; error?: string }>
  sendPushNotification: (notification: Partial<Notification>) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Email & SMS
  sendEmailNotification: (email: Partial<EmailNotification>) => Promise<{ success: boolean; error?: string }>
  sendSMSNotification: (sms: Partial<SMSNotification>) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Settings & Templates
  fetchNotificationSettings: () => Promise<{ success: boolean; error?: string }>
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => Promise<{ success: boolean; error?: string }>
  fetchNotificationTemplates: () => Promise<{ success: boolean; error?: string }>
  createNotificationTemplate: (template: Partial<NotificationTemplate>) => Promise<{ success: boolean; error?: string }>
  updateNotificationTemplate: (templateId: string, updates: Partial<NotificationTemplate>) => Promise<{ success: boolean; error?: string }>
  deleteNotificationTemplate: (templateId: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Channels & Preferences
  enableNotificationChannel: (channel: NotificationChannel) => Promise<{ success: boolean; error?: string }>
  disableNotificationChannel: (channel: NotificationChannel) => Promise<{ success: boolean; error?: string }>
  updateChannelPreferences: (channel: NotificationChannel, preferences: any) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Search & Filtering
  searchNotifications: (query: string, pagination?: PaginationParams, filters?: Omit<NotificationFilters, 'search'>) => Promise<{ success: boolean; error?: string }>
  setFilters: (filters: Partial<NotificationFilters>) => void
  clearFilters: () => void
  setSearchQuery: (query: string) => void
  
  // Actions - Pagination
  setCurrentPage: (page: number) => void
  setPageSize: (size: number) => void
  
  // Actions - UI State
  setSelectedNotification: (notification: Notification | null) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  
  // Socket Event Handlers
  onNotificationReceived: (notification: Notification) => void
  onNotificationUpdated: (notification: Notification) => void
  onNotificationDeleted: (notificationId: string) => void
  onSystemAlert: (alert: SystemAlert) => void
  onUserMessage: (message: UserMessage) => void
  onConnectionStatusChanged: (status: string) => void
  
  // Utility Methods
  getNotificationsByType: (type: NotificationType) => Notification[]
  getNotificationsByPriority: (priority: NotificationPriority) => Notification[]
  getNotificationsByStatus: (status: NotificationStatus) => Notification[]
  getNotificationsByDateRange: (startDate: string, endDate: string) => Notification[]
  getUnreadNotifications: () => Notification[]
  getRecentNotifications: (limit?: number) => Notification[]
  calculateNotificationCounts: () => { total: number; unread: number; byPriority: Record<NotificationPriority, number> }
  formatNotificationTime: (timestamp: string) => string
  getNotificationIcon: (type: NotificationType) => string
  getNotificationColor: (priority: NotificationPriority) => string
  getPriorityLabel: (priority: NotificationPriority) => string
  isNotificationExpired: (notification: Notification) => boolean
  canUserAccessNotification: (notification: Notification, userId: string) => boolean
  getNotificationSummary: (notification: Notification) => string
  shouldShowNotification: (notification: Notification, settings: NotificationSettings) => boolean
  playNotificationSound: (priority: NotificationPriority) => void
  showBrowserNotification: (notification: Notification) => void
}

// ============================================================================
// Notifications Store Implementation
// ============================================================================

export const useNotificationsStore = create<NotificationsState>()(
  immer((set, get) => ({
    // Initial state
    notifications: [],
    unreadNotifications: [],
    selectedNotification: null,
    isLoading: false,
    error: null,
    
    // Real-time State
    isConnected: false,
    connectionStatus: 'disconnected',
    lastHeartbeat: null,
    
    // Notification Counts
    unreadCount: 0,
    totalCount: 0,
    priorityCount: {
      low: 0,
      medium: 0,
      high: 0,
      urgent: 0
    },
    
    // Notification Types
    systemAlerts: [],
    userMessages: [],
    emailNotifications: [],
    smsNotifications: [],
    inAppNotifications: [],
    
    // Settings & Templates
    notificationSettings: null,
    notificationTemplates: [],
    enabledChannels: [],
    
    // Push Notifications
    pushSubscription: null,
    pushEnabled: false,
    
    // Pagination & Filtering
    currentPage: 1,
    totalPages: 0,
    pageSize: 20,
    filters: {},
    searchQuery: '',

    // Actions - Socket.IO Connection
    connectSocket: async (userId: string, token: string) => {
      try {
        set((state) => {
          state.connectionStatus = 'connecting'
          state.error = null
        })

        const result = await socketService.connect(userId, token)

        if (result.success) {
          // Set up socket event listeners
          socketService.on('notification:received', get().onNotificationReceived)
          socketService.on('notification:updated', get().onNotificationUpdated)
          socketService.on('notification:deleted', get().onNotificationDeleted)
          socketService.on('system:alert', get().onSystemAlert)
          socketService.on('user:message', get().onUserMessage)
          socketService.on('connection:status', get().onConnectionStatusChanged)

          set((state) => {
            state.isConnected = true
            state.connectionStatus = 'connected'
            state.lastHeartbeat = new Date().toISOString()
          })

          return { success: true }
        } else {
          set((state) => {
            state.connectionStatus = 'error'
            state.error = result.error || 'Failed to connect to socket'
          })

          return { success: false, error: result.error || 'Failed to connect to socket' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.connectionStatus = 'error'
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    disconnectSocket: () => {
      socketService.disconnect()
      
      set((state) => {
        state.isConnected = false
        state.connectionStatus = 'disconnected'
        state.lastHeartbeat = null
      })
    },

    reconnectSocket: async () => {
      try {
        set((state) => {
          state.connectionStatus = 'connecting'
        })

        const result = await socketService.reconnect()

        if (result.success) {
          set((state) => {
            state.isConnected = true
            state.connectionStatus = 'connected'
            state.lastHeartbeat = new Date().toISOString()
          })

          return { success: true }
        } else {
          set((state) => {
            state.connectionStatus = 'error'
            state.error = result.error || 'Failed to reconnect'
          })

          return { success: false, error: result.error || 'Failed to reconnect' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.connectionStatus = 'error'
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Notifications CRUD
    fetchNotifications: async (pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await notificationsService.getNotifications(pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.notifications = response.data!
            state.unreadNotifications = response.data!.filter(n => n.status === 'unread')
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalCount = response.pagination?.total || response.data!.length
            state.unreadCount = state.unreadNotifications.length
            state.filters = filters
            state.isLoading = false
            state.error = null

            // Update priority counts
            const counts = get().calculateNotificationCounts()
            state.priorityCount = counts.byPriority
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch notifications'
          })

          return { success: false, error: response.error || 'Failed to fetch notifications' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchNotificationById: async (notificationId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await notificationsService.getNotificationById(notificationId)

        if (response.success && response.data) {
          set((state) => {
            state.selectedNotification = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch notification'
          })

          return { success: false, error: response.error || 'Failed to fetch notification' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    createNotification: async (notificationData: Partial<Notification>) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate notification data
        const validation = notificationsService.validateNotificationData(notificationData)
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        const response = await notificationsService.createNotification(notificationData)

        if (response.success && response.data) {
          set((state) => {
            state.notifications.unshift(response.data!)
            if (response.data!.status === 'unread') {
              state.unreadNotifications.unshift(response.data!)
              state.unreadCount += 1
            }
            state.totalCount += 1
            state.isLoading = false
            state.error = null

            // Update priority counts
            const counts = get().calculateNotificationCounts()
            state.priorityCount = counts.byPriority
          })

          // Send real-time notification if socket is connected
          if (get().isConnected && notificationData.recipientId) {
            socketService.emit('notification:send', {
              recipientId: notificationData.recipientId,
              notification: response.data
            })
          }

          return { success: true, notification: response.data }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to create notification'
          })

          return { success: false, error: response.error || 'Failed to create notification' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    updateNotification: async (notificationId: string, updates: Partial<Notification>) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await notificationsService.updateNotification(notificationId, updates)

        if (response.success && response.data) {
          set((state) => {
            const index = state.notifications.findIndex(n => n._id === notificationId)
            if (index !== -1) {
              const oldNotification = state.notifications[index]
              state.notifications[index] = response.data!

              // Update unread notifications
              if (oldNotification.status === 'unread' && response.data!.status === 'read') {
                state.unreadNotifications = state.unreadNotifications.filter(n => n._id !== notificationId)
                state.unreadCount -= 1
              } else if (oldNotification.status === 'read' && response.data!.status === 'unread') {
                state.unreadNotifications.push(response.data!)
                state.unreadCount += 1
              }
            }
            
            if (state.selectedNotification?._id === notificationId) {
              state.selectedNotification = response.data!
            }
            
            state.isLoading = false
            state.error = null

            // Update priority counts
            const counts = get().calculateNotificationCounts()
            state.priorityCount = counts.byPriority
          })

          // Emit real-time update
          if (get().isConnected) {
            socketService.emit('notification:update', {
              notificationId,
              updates: response.data
            })
          }

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to update notification'
          })

          return { success: false, error: response.error || 'Failed to update notification' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    deleteNotification: async (notificationId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await notificationsService.deleteNotification(notificationId)

        if (response.success) {
          set((state) => {
            const notification = state.notifications.find(n => n._id === notificationId)
            
            state.notifications = state.notifications.filter(n => n._id !== notificationId)
            state.unreadNotifications = state.unreadNotifications.filter(n => n._id !== notificationId)
            
            if (notification?.status === 'unread') {
              state.unreadCount -= 1
            }
            
            if (state.selectedNotification?._id === notificationId) {
              state.selectedNotification = null
            }
            
            state.totalCount = Math.max(0, state.totalCount - 1)
            state.isLoading = false
            state.error = null

            // Update priority counts
            const counts = get().calculateNotificationCounts()
            state.priorityCount = counts.byPriority
          })

          // Emit real-time deletion
          if (get().isConnected) {
            socketService.emit('notification:delete', { notificationId })
          }

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to delete notification'
          })

          return { success: false, error: response.error || 'Failed to delete notification' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Notification Management
    markAsRead: async (notificationId: string) => {
      return get().updateNotification(notificationId, { status: 'read', readAt: new Date().toISOString() })
    },

    markAsUnread: async (notificationId: string) => {
      return get().updateNotification(notificationId, { status: 'unread', readAt: null })
    },

    markAllAsRead: async () => {
      try {
        const response = await notificationsService.markAllAsRead()

        if (response.success) {
          set((state) => {
            state.notifications = state.notifications.map(n => ({
              ...n,
              status: 'read' as NotificationStatus,
              readAt: new Date().toISOString()
            }))
            state.unreadNotifications = []
            state.unreadCount = 0
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to mark all as read' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    archiveNotification: async (notificationId: string) => {
      return get().updateNotification(notificationId, { status: 'archived', archivedAt: new Date().toISOString() })
    },

    deleteMultiple: async (notificationIds: string[]) => {
      try {
        const response = await notificationsService.deleteMultiple(notificationIds)

        if (response.success) {
          set((state) => {
            const deletedUnreadCount = state.notifications.filter(n => 
              notificationIds.includes(n._id) && n.status === 'unread'
            ).length

            state.notifications = state.notifications.filter(n => !notificationIds.includes(n._id))
            state.unreadNotifications = state.unreadNotifications.filter(n => !notificationIds.includes(n._id))
            state.unreadCount -= deletedUnreadCount
            state.totalCount = Math.max(0, state.totalCount - notificationIds.length)

            // Update priority counts
            const counts = get().calculateNotificationCounts()
            state.priorityCount = counts.byPriority
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to delete notifications' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Real-time Events
    sendNotification: async (recipientId: string, notification: Partial<Notification>) => {
      try {
        const response = await notificationsService.sendNotification(recipientId, notification)

        if (response.success) {
          // Emit real-time notification
          if (get().isConnected) {
            socketService.emit('notification:send', {
              recipientId,
              notification: response.data
            })
          }

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to send notification' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    broadcastNotification: async (notification: Partial<Notification>, userIds?: string[]) => {
      try {
        const response = await notificationsService.broadcastNotification(notification, userIds)

        if (response.success) {
          // Emit real-time broadcast
          if (get().isConnected) {
            socketService.emit('notification:broadcast', {
              notification: response.data,
              userIds
            })
          }

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to broadcast notification' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    sendSystemAlert: async (alert: Partial<SystemAlert>) => {
      try {
        const response = await notificationsService.sendSystemAlert(alert)

        if (response.success && response.data) {
          set((state) => {
            state.systemAlerts.unshift(response.data!)
          })

          // Emit real-time system alert
          if (get().isConnected) {
            socketService.emit('system:alert', response.data)
          }

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to send system alert' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    sendUserMessage: async (message: Partial<UserMessage>) => {
      try {
        const response = await notificationsService.sendUserMessage(message)

        if (response.success && response.data) {
          set((state) => {
            state.userMessages.unshift(response.data!)
          })

          // Emit real-time user message
          if (get().isConnected) {
            socketService.emit('user:message', response.data)
          }

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to send user message' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Push Notifications
    subscribeToPush: async () => {
      try {
        const response = await notificationsService.subscribeToPush()

        if (response.success && response.data) {
          set((state) => {
            state.pushSubscription = response.data!
            state.pushEnabled = true
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to subscribe to push notifications' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    unsubscribeFromPush: async () => {
      try {
        const response = await notificationsService.unsubscribeFromPush()

        if (response.success) {
          set((state) => {
            state.pushSubscription = null
            state.pushEnabled = false
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to unsubscribe from push notifications' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    sendPushNotification: async (notification: Partial<Notification>) => {
      try {
        const response = await notificationsService.sendPushNotification(notification)

        if (response.success) {
          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to send push notification' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Email & SMS
    sendEmailNotification: async (email: Partial<EmailNotification>) => {
      try {
        const response = await notificationsService.sendEmailNotification(email)

        if (response.success && response.data) {
          set((state) => {
            state.emailNotifications.unshift(response.data!)
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to send email notification' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    sendSMSNotification: async (sms: Partial<SMSNotification>) => {
      try {
        const response = await notificationsService.sendSMSNotification(sms)

        if (response.success && response.data) {
          set((state) => {
            state.smsNotifications.unshift(response.data!)
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to send SMS notification' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Settings & Templates
    fetchNotificationSettings: async () => {
      try {
        const response = await notificationsService.getNotificationSettings()

        if (response.success && response.data) {
          set((state) => {
            state.notificationSettings = response.data!
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to fetch notification settings' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    updateNotificationSettings: async (settings: Partial<NotificationSettings>) => {
      try {
        const response = await notificationsService.updateNotificationSettings(settings)

        if (response.success && response.data) {
          set((state) => {
            state.notificationSettings = response.data!
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update notification settings' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    fetchNotificationTemplates: async () => {
      try {
        const response = await notificationsService.getNotificationTemplates()

        if (response.success && response.data) {
          set((state) => {
            state.notificationTemplates = response.data!
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to fetch notification templates' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    createNotificationTemplate: async (template: Partial<NotificationTemplate>) => {
      try {
        const response = await notificationsService.createNotificationTemplate(template)

        if (response.success && response.data) {
          set((state) => {
            state.notificationTemplates.unshift(response.data!)
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to create notification template' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    updateNotificationTemplate: async (templateId: string, updates: Partial<NotificationTemplate>) => {
      try {
        const response = await notificationsService.updateNotificationTemplate(templateId, updates)

        if (response.success && response.data) {
          set((state) => {
            const index = state.notificationTemplates.findIndex(t => t._id === templateId)
            if (index !== -1) {
              state.notificationTemplates[index] = response.data!
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update notification template' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    deleteNotificationTemplate: async (templateId: string) => {
      try {
        const response = await notificationsService.deleteNotificationTemplate(templateId)

        if (response.success) {
          set((state) => {
            state.notificationTemplates = state.notificationTemplates.filter(t => t._id !== templateId)
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to delete notification template' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Channels & Preferences
    enableNotificationChannel: async (channel: NotificationChannel) => {
      try {
        const response = await notificationsService.enableNotificationChannel(channel)

        if (response.success) {
          set((state) => {
            if (!state.enabledChannels.includes(channel)) {
              state.enabledChannels.push(channel)
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to enable notification channel' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    disableNotificationChannel: async (channel: NotificationChannel) => {
      try {
        const response = await notificationsService.disableNotificationChannel(channel)

        if (response.success) {
          set((state) => {
            state.enabledChannels = state.enabledChannels.filter(c => c !== channel)
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to disable notification channel' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    updateChannelPreferences: async (channel: NotificationChannel, preferences: any) => {
      try {
        const response = await notificationsService.updateChannelPreferences(channel, preferences)

        if (response.success) {
          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update channel preferences' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Search & Filtering
    searchNotifications: async (query: string, pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
        state.searchQuery = query
      })

      try {
        const response = await notificationsService.searchNotifications(query, pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.notifications = response.data!
            state.unreadNotifications = response.data!.filter(n => n.status === 'unread')
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalCount = response.pagination?.total || response.data!.length
            state.unreadCount = state.unreadNotifications.length
            state.filters = { ...filters, search: query }
            state.isLoading = false
            state.error = null

            // Update priority counts
            const counts = get().calculateNotificationCounts()
            state.priorityCount = counts.byPriority
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to search notifications'
          })

          return { success: false, error: response.error || 'Failed to search notifications' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    setFilters: (filters: Partial<NotificationFilters>) => {
      set((state) => {
        state.filters = { ...state.filters, ...filters }
      })
    },

    clearFilters: () => {
      set((state) => {
        state.filters = {}
        state.searchQuery = ''
      })
    },

    setSearchQuery: (query: string) => {
      set((state) => {
        state.searchQuery = query
      })
    },

    // Actions - Pagination
    setCurrentPage: (page: number) => {
      set((state) => {
        state.currentPage = page
      })
    },

    setPageSize: (size: number) => {
      set((state) => {
        state.pageSize = size
      })
    },

    // Actions - UI State
    setSelectedNotification: (notification: Notification | null) => {
      set((state) => {
        state.selectedNotification = notification
      })
    },

    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    setLoading: (loading: boolean) => {
      set((state) => {
        state.isLoading = loading
      })
    },

    // Socket Event Handlers
    onNotificationReceived: (notification: Notification) => {
      set((state) => {
        state.notifications.unshift(notification)
        if (notification.status === 'unread') {
          state.unreadNotifications.unshift(notification)
          state.unreadCount += 1
        }
        state.totalCount += 1

        // Update priority counts
        const counts = get().calculateNotificationCounts()
        state.priorityCount = counts.byPriority
      })

      // Show browser notification if enabled
      const settings = get().notificationSettings
      if (settings && get().shouldShowNotification(notification, settings)) {
        get().showBrowserNotification(notification)
        get().playNotificationSound(notification.priority)
      }
    },

    onNotificationUpdated: (notification: Notification) => {
      set((state) => {
        const index = state.notifications.findIndex(n => n._id === notification._id)
        if (index !== -1) {
          const oldNotification = state.notifications[index]
          state.notifications[index] = notification

          // Update unread notifications
          if (oldNotification.status === 'unread' && notification.status === 'read') {
            state.unreadNotifications = state.unreadNotifications.filter(n => n._id !== notification._id)
            state.unreadCount -= 1
          } else if (oldNotification.status === 'read' && notification.status === 'unread') {
            state.unreadNotifications.push(notification)
            state.unreadCount += 1
          }
        }

        // Update priority counts
        const counts = get().calculateNotificationCounts()
        state.priorityCount = counts.byPriority
      })
    },

    onNotificationDeleted: (notificationId: string) => {
      set((state) => {
        const notification = state.notifications.find(n => n._id === notificationId)
        
        state.notifications = state.notifications.filter(n => n._id !== notificationId)
        state.unreadNotifications = state.unreadNotifications.filter(n => n._id !== notificationId)
        
        if (notification?.status === 'unread') {
          state.unreadCount -= 1
        }
        
        state.totalCount = Math.max(0, state.totalCount - 1)

        // Update priority counts
        const counts = get().calculateNotificationCounts()
        state.priorityCount = counts.byPriority
      })
    },

    onSystemAlert: (alert: SystemAlert) => {
      set((state) => {
        state.systemAlerts.unshift(alert)
      })

      // Show critical system alerts immediately
      if (alert.priority === 'urgent') {
        get().showBrowserNotification(alert as any)
        get().playNotificationSound('urgent')
      }
    },

    onUserMessage: (message: UserMessage) => {
      set((state) => {
        state.userMessages.unshift(message)
      })
    },

    onConnectionStatusChanged: (status: string) => {
      set((state) => {
        state.connectionStatus = status as any
        state.isConnected = status === 'connected'
        if (status === 'connected') {
          state.lastHeartbeat = new Date().toISOString()
        }
      })
    },

    // Utility Methods
    getNotificationsByType: (type: NotificationType) => {
      return get().notifications.filter(notification => notification.type === type)
    },

    getNotificationsByPriority: (priority: NotificationPriority) => {
      return get().notifications.filter(notification => notification.priority === priority)
    },

    getNotificationsByStatus: (status: NotificationStatus) => {
      return get().notifications.filter(notification => notification.status === status)
    },

    getNotificationsByDateRange: (startDate: string, endDate: string) => {
      return get().notifications.filter(notification => {
        const notificationDate = new Date(notification.createdAt)
        return notificationDate >= new Date(startDate) && notificationDate <= new Date(endDate)
      })
    },

    getUnreadNotifications: () => {
      return get().unreadNotifications
    },

    getRecentNotifications: (limit = 10) => {
      return get().notifications.slice(0, limit)
    },

    calculateNotificationCounts: () => {
      const notifications = get().notifications
      const total = notifications.length
      const unread = notifications.filter(n => n.status === 'unread').length
      
      const byPriority: Record<NotificationPriority, number> = {
        low: notifications.filter(n => n.priority === 'low').length,
        medium: notifications.filter(n => n.priority === 'medium').length,
        high: notifications.filter(n => n.priority === 'high').length,
        urgent: notifications.filter(n => n.priority === 'urgent').length
      }

      return { total, unread, byPriority }
    },

    formatNotificationTime: (timestamp: string) => {
      return notificationsService.formatNotificationTime(timestamp)
    },

    getNotificationIcon: (type: NotificationType) => {
      return notificationsService.getNotificationIcon(type)
    },

    getNotificationColor: (priority: NotificationPriority) => {
      return notificationsService.getNotificationColor(priority)
    },

    getPriorityLabel: (priority: NotificationPriority) => {
      return notificationsService.getPriorityLabel(priority)
    },

    isNotificationExpired: (notification: Notification) => {
      return notificationsService.isNotificationExpired(notification)
    },

    canUserAccessNotification: (notification: Notification, userId: string) => {
      return notificationsService.canUserAccessNotification(notification, userId)
    },

    getNotificationSummary: (notification: Notification) => {
      return notificationsService.getNotificationSummary(notification)
    },

    shouldShowNotification: (notification: Notification, settings: NotificationSettings) => {
      return notificationsService.shouldShowNotification(notification, settings)
    },

    playNotificationSound: (priority: NotificationPriority) => {
      notificationsService.playNotificationSound(priority)
    },

    showBrowserNotification: (notification: Notification) => {
      notificationsService.showBrowserNotification(notification)
    }
  }))
)
