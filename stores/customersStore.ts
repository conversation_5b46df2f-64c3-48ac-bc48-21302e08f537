// stores/customersStore.ts - Customers management state with Zustand

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { customerService, handleServiceError } from '@/services/frontend'
import type { 
  Customer, 
  CustomerTier,
  CreateCustomerData,
  UpdateCustomerData,
  CustomerFilters,
  PaginationParams,
  ApiResponse 
} from '@/types/frontend'

// ============================================================================
// Customers Store Types
// ============================================================================

export interface CustomersState {
  // Customers State
  customers: Customer[]
  selectedCustomer: Customer | null
  isLoading: boolean
  error: string | null
  
  // Pagination & Filtering
  currentPage: number
  totalPages: number
  totalCustomers: number
  pageSize: number
  filters: CustomerFilters
  searchQuery: string
  
  // Customer Tiers
  vipCustomers: Customer[]
  goldCustomers: Customer[]
  silverCustomers: Customer[]
  bronzeCustomers: Customer[]
  regularCustomers: Customer[]
  
  // Customer Analytics
  topCustomers: Customer[]
  recentCustomers: Customer[]
  inactiveCustomers: Customer[]
  
  // Actions - Customers CRUD
  fetchCustomers: (pagination?: PaginationParams, filters?: CustomerFilters) => Promise<{ success: boolean; error?: string }>
  fetchCustomerById: (customerId: string) => Promise<{ success: boolean; error?: string }>
  createCustomer: (customerData: CreateCustomerData) => Promise<{ success: boolean; error?: string; customer?: Customer }>
  updateCustomer: (customerId: string, updates: UpdateCustomerData) => Promise<{ success: boolean; error?: string }>
  deleteCustomer: (customerId: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Customer Tiers & Loyalty
  fetchCustomersByTier: (tier: CustomerTier, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  updateCustomerTier: (customerId: string, tier: CustomerTier) => Promise<{ success: boolean; error?: string }>
  addLoyaltyPoints: (customerId: string, points: number, reason?: string) => Promise<{ success: boolean; error?: string }>
  redeemLoyaltyPoints: (customerId: string, points: number, reason?: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Customer Analytics
  fetchTopCustomers: (limit?: number, branchId?: string) => Promise<{ success: boolean; error?: string }>
  fetchRecentCustomers: (limit?: number, branchId?: string) => Promise<{ success: boolean; error?: string }>
  fetchInactiveCustomers: (daysSinceLastOrder?: number, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Customer Queries
  fetchCustomersByBranch: (branchId: string, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  fetchCustomerOrderHistory: (customerId: string, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Search & Filtering
  searchCustomers: (query: string, pagination?: PaginationParams, filters?: Omit<CustomerFilters, 'search'>) => Promise<{ success: boolean; error?: string }>
  setFilters: (filters: Partial<CustomerFilters>) => void
  clearFilters: () => void
  setSearchQuery: (query: string) => void
  
  // Actions - Pagination
  setCurrentPage: (page: number) => void
  setPageSize: (size: number) => void
  
  // Actions - UI State
  setSelectedCustomer: (customer: Customer | null) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  
  // Utility Methods
  getCustomersByBranch: (branchId: string) => Customer[]
  getCustomersByTier: (tier: CustomerTier) => Customer[]
  getActiveCustomers: () => Customer[]
  getInactiveCustomers: (daysSinceLastOrder?: number) => Customer[]
  getAvailableTiers: () => CustomerTier[]
  calculateCustomerValue: (customer: Customer) => number
  calculateCustomerLifetimeValue: (customer: Customer) => number
  getCustomerTierBenefits: (tier: CustomerTier) => string[]
  getNextTierRequirement: (customer: Customer) => { nextTier: CustomerTier | null; pointsNeeded: number }
  isCustomerActive: (customer: Customer, daysSinceLastOrder?: number) => boolean
  formatCustomerName: (customer: Customer) => string
  formatCustomerAddress: (customer: Customer) => string
  updateTierGroups: () => void
}

// ============================================================================
// Customers Store Implementation
// ============================================================================

export const useCustomersStore = create<CustomersState>()(
  immer((set, get) => ({
    // Initial state
    customers: [],
    selectedCustomer: null,
    isLoading: false,
    error: null,
    
    // Pagination & Filtering
    currentPage: 1,
    totalPages: 0,
    totalCustomers: 0,
    pageSize: 20,
    filters: {},
    searchQuery: '',
    
    // Customer Tiers
    vipCustomers: [],
    goldCustomers: [],
    silverCustomers: [],
    bronzeCustomers: [],
    regularCustomers: [],
    
    // Customer Analytics
    topCustomers: [],
    recentCustomers: [],
    inactiveCustomers: [],

    // Actions - Customers CRUD
    fetchCustomers: async (pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await customerService.getCustomers(pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.customers = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalCustomers = response.pagination?.total || response.data!.length
            state.filters = filters
            state.isLoading = false
            state.error = null
          })

          // Update tier groups
          get().updateTierGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch customers'
          })

          return { success: false, error: response.error || 'Failed to fetch customers' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchCustomerById: async (customerId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await customerService.getCustomerById(customerId)

        if (response.success && response.data) {
          set((state) => {
            state.selectedCustomer = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch customer'
          })

          return { success: false, error: response.error || 'Failed to fetch customer' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    createCustomer: async (customerData: CreateCustomerData) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate customer data
        const validation = customerService.validateCustomerData(customerData)
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        const response = await customerService.createCustomer(customerData)

        if (response.success && response.data) {
          set((state) => {
            state.customers.unshift(response.data!)
            state.totalCustomers += 1
            state.isLoading = false
            state.error = null
          })

          // Update tier groups
          get().updateTierGroups()

          return { success: true, customer: response.data }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to create customer'
          })

          return { success: false, error: response.error || 'Failed to create customer' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    updateCustomer: async (customerId: string, updates: UpdateCustomerData) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate customer data
        const validation = customerService.validateCustomerData(updates)
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        const response = await customerService.updateCustomer(customerId, updates)

        if (response.success && response.data) {
          set((state) => {
            const index = state.customers.findIndex(c => c._id === customerId)
            if (index !== -1) {
              state.customers[index] = response.data!
            }
            
            if (state.selectedCustomer?._id === customerId) {
              state.selectedCustomer = response.data!
            }
            
            state.isLoading = false
            state.error = null
          })

          // Update tier groups
          get().updateTierGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to update customer'
          })

          return { success: false, error: response.error || 'Failed to update customer' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    deleteCustomer: async (customerId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await customerService.deleteCustomer(customerId)

        if (response.success) {
          set((state) => {
            state.customers = state.customers.filter(c => c._id !== customerId)
            state.topCustomers = state.topCustomers.filter(c => c._id !== customerId)
            state.recentCustomers = state.recentCustomers.filter(c => c._id !== customerId)
            state.inactiveCustomers = state.inactiveCustomers.filter(c => c._id !== customerId)
            
            if (state.selectedCustomer?._id === customerId) {
              state.selectedCustomer = null
            }
            
            state.totalCustomers = Math.max(0, state.totalCustomers - 1)
            state.isLoading = false
            state.error = null
          })

          // Update tier groups
          get().updateTierGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to delete customer'
          })

          return { success: false, error: response.error || 'Failed to delete customer' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Customer Tiers & Loyalty
    fetchCustomersByTier: async (tier: CustomerTier, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await customerService.getCustomersByTier(tier, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.customers = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalCustomers = response.pagination?.total || response.data!.length
            state.filters = { ...state.filters, tier }
            state.isLoading = false
            state.error = null
          })

          // Update tier groups
          get().updateTierGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch customers by tier'
          })

          return { success: false, error: response.error || 'Failed to fetch customers by tier' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    updateCustomerTier: async (customerId: string, tier: CustomerTier) => {
      try {
        const response = await customerService.updateCustomerTier(customerId, tier)

        if (response.success && response.data) {
          set((state) => {
            const index = state.customers.findIndex(c => c._id === customerId)
            if (index !== -1) {
              state.customers[index] = response.data!
            }
            
            if (state.selectedCustomer?._id === customerId) {
              state.selectedCustomer = response.data!
            }
          })

          // Update tier groups
          get().updateTierGroups()

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update customer tier' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    addLoyaltyPoints: async (customerId: string, points: number, reason?: string) => {
      try {
        const response = await customerService.addLoyaltyPoints(customerId, points, reason)

        if (response.success && response.data) {
          set((state) => {
            const index = state.customers.findIndex(c => c._id === customerId)
            if (index !== -1) {
              state.customers[index] = response.data!
            }
            
            if (state.selectedCustomer?._id === customerId) {
              state.selectedCustomer = response.data!
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to add loyalty points' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    redeemLoyaltyPoints: async (customerId: string, points: number, reason?: string) => {
      try {
        const response = await customerService.redeemLoyaltyPoints(customerId, points, reason)

        if (response.success && response.data) {
          set((state) => {
            const index = state.customers.findIndex(c => c._id === customerId)
            if (index !== -1) {
              state.customers[index] = response.data!
            }
            
            if (state.selectedCustomer?._id === customerId) {
              state.selectedCustomer = response.data!
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to redeem loyalty points' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Customer Analytics
    fetchTopCustomers: async (limit = 10, branchId?: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await customerService.getTopCustomers(limit, branchId)

        if (response.success && response.data) {
          set((state) => {
            state.topCustomers = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch top customers'
          })

          return { success: false, error: response.error || 'Failed to fetch top customers' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchRecentCustomers: async (limit = 10, branchId?: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await customerService.getRecentCustomers(limit, branchId)

        if (response.success && response.data) {
          set((state) => {
            state.recentCustomers = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch recent customers'
          })

          return { success: false, error: response.error || 'Failed to fetch recent customers' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchInactiveCustomers: async (daysSinceLastOrder = 90, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await customerService.getInactiveCustomers(daysSinceLastOrder, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.inactiveCustomers = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch inactive customers'
          })

          return { success: false, error: response.error || 'Failed to fetch inactive customers' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Customer Queries
    fetchCustomersByBranch: async (branchId: string, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await customerService.getCustomersByBranch(branchId, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.customers = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalCustomers = response.pagination?.total || response.data!.length
            state.filters = { ...state.filters, branchId }
            state.isLoading = false
            state.error = null
          })

          // Update tier groups
          get().updateTierGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch customers by branch'
          })

          return { success: false, error: response.error || 'Failed to fetch customers by branch' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchCustomerOrderHistory: async (customerId: string, pagination = { page: 1, limit: 10 }) => {
      try {
        const response = await customerService.getCustomerOrderHistory(customerId, pagination)

        if (response.success && response.data) {
          // This would typically update a separate orders list for the customer
          // For now, we'll just return success
          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to fetch customer order history' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Search & Filtering
    searchCustomers: async (query: string, pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
        state.searchQuery = query
      })

      try {
        const response = await customerService.searchCustomers(query, pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.customers = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalCustomers = response.pagination?.total || response.data!.length
            state.filters = { ...filters, search: query }
            state.isLoading = false
            state.error = null
          })

          // Update tier groups
          get().updateTierGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to search customers'
          })

          return { success: false, error: response.error || 'Failed to search customers' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    setFilters: (filters: Partial<CustomerFilters>) => {
      set((state) => {
        state.filters = { ...state.filters, ...filters }
      })
    },

    clearFilters: () => {
      set((state) => {
        state.filters = {}
        state.searchQuery = ''
      })
    },

    setSearchQuery: (query: string) => {
      set((state) => {
        state.searchQuery = query
      })
    },

    // Actions - Pagination
    setCurrentPage: (page: number) => {
      set((state) => {
        state.currentPage = page
      })
    },

    setPageSize: (size: number) => {
      set((state) => {
        state.pageSize = size
      })
    },

    // Actions - UI State
    setSelectedCustomer: (customer: Customer | null) => {
      set((state) => {
        state.selectedCustomer = customer
      })
    },

    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    setLoading: (loading: boolean) => {
      set((state) => {
        state.isLoading = loading
      })
    },

    // Utility Methods
    getCustomersByBranch: (branchId: string) => {
      return get().customers.filter(customer => customer.preferredBranch === branchId)
    },

    getCustomersByTier: (tier: CustomerTier) => {
      return get().customers.filter(customer => customerService.getCustomerTier(customer) === tier)
    },

    getActiveCustomers: () => {
      return get().customers.filter(customer => customer.isActive)
    },

    getInactiveCustomers: (daysSinceLastOrder = 90) => {
      return get().customers.filter(customer => !get().isCustomerActive(customer, daysSinceLastOrder))
    },

    getAvailableTiers: () => {
      return customerService.getCustomerTiers()
    },

    calculateCustomerValue: (customer: Customer) => {
      return customerService.calculateCustomerValue(customer)
    },

    calculateCustomerLifetimeValue: (customer: Customer) => {
      return customerService.calculateCustomerLifetimeValue(customer)
    },

    getCustomerTierBenefits: (tier: CustomerTier) => {
      return customerService.getCustomerTierBenefits(tier)
    },

    getNextTierRequirement: (customer: Customer) => {
      return customerService.getNextTierRequirement(customer)
    },

    isCustomerActive: (customer: Customer, daysSinceLastOrder = 90) => {
      return customerService.isCustomerActive(customer, daysSinceLastOrder)
    },

    formatCustomerName: (customer: Customer) => {
      return customerService.formatCustomerName(customer)
    },

    formatCustomerAddress: (customer: Customer) => {
      return customerService.formatCustomerAddress(customer)
    },

    // Helper method to update tier groups
    updateTierGroups: () => {
      const state = get()
      const customers = state.customers
      
      set((draft) => {
        draft.vipCustomers = customers.filter(c => customerService.getCustomerTier(c) === 'VIP')
        draft.goldCustomers = customers.filter(c => customerService.getCustomerTier(c) === 'Gold')
        draft.silverCustomers = customers.filter(c => customerService.getCustomerTier(c) === 'Silver')
        draft.bronzeCustomers = customers.filter(c => customerService.getCustomerTier(c) === 'Bronze')
        draft.regularCustomers = customers.filter(c => customerService.getCustomerTier(c) === 'Regular')
      })
    }
  }))
)
