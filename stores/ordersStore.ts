// stores/ordersStore.ts - Orders management state with Zustand

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { orderService, handleServiceError } from '@/services/frontend'
import type { 
  Order, 
  OrderItem,
  OrderStatus,
  PaymentStatus,
  PaymentMethod,
  CreateOrderData,
  UpdateOrderData,
  OrderFilters,
  PaginationParams,
  ApiResponse 
} from '@/types/frontend'

// ============================================================================
// Orders Store Types
// ============================================================================

export interface OrdersState {
  // Orders State
  orders: Order[]
  selectedOrder: Order | null
  isLoading: boolean
  error: string | null
  
  // Pagination & Filtering
  currentPage: number
  totalPages: number
  totalOrders: number
  pageSize: number
  filters: OrderFilters
  searchQuery: string
  
  // Order Status Groups
  pendingOrders: Order[]
  processingOrders: Order[]
  shippedOrders: Order[]
  deliveredOrders: Order[]
  cancelledOrders: Order[]
  
  // Recent Orders
  recentOrders: Order[]
  
  // Actions - Orders CRUD
  fetchOrders: (pagination?: PaginationParams, filters?: OrderFilters) => Promise<{ success: boolean; error?: string }>
  fetchOrderById: (orderId: string) => Promise<{ success: boolean; error?: string }>
  createOrder: (orderData: CreateOrderData) => Promise<{ success: boolean; error?: string; order?: Order }>
  updateOrder: (orderId: string, updates: UpdateOrderData) => Promise<{ success: boolean; error?: string }>
  cancelOrder: (orderId: string, reason?: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Order Status Management
  updateOrderStatus: (orderId: string, status: OrderStatus) => Promise<{ success: boolean; error?: string }>
  updatePaymentStatus: (orderId: string, paymentStatus: PaymentStatus) => Promise<{ success: boolean; error?: string }>
  addTrackingNumber: (orderId: string, trackingNumber: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Order Queries
  fetchOrdersByCustomer: (customerId: string, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  fetchOrdersByBranch: (branchId: string, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  fetchOrdersByStatus: (status: OrderStatus, pagination?: PaginationParams) => Promise<{ success: boolean; error?: string }>
  fetchRecentOrders: (limit?: number, branchId?: string) => Promise<{ success: boolean; error?: string }>
  
  // Actions - Search & Filtering
  searchOrders: (query: string, pagination?: PaginationParams, filters?: Omit<OrderFilters, 'search'>) => Promise<{ success: boolean; error?: string }>
  setFilters: (filters: Partial<OrderFilters>) => void
  clearFilters: () => void
  setSearchQuery: (query: string) => void
  
  // Actions - Pagination
  setCurrentPage: (page: number) => void
  setPageSize: (size: number) => void
  
  // Actions - UI State
  setSelectedOrder: (order: Order | null) => void
  clearError: () => void
  setLoading: (loading: boolean) => void
  
  // Utility Methods
  getOrdersByCustomer: (customerId: string) => Order[]
  getOrdersByBranch: (branchId: string) => Order[]
  getOrdersByStatus: (status: OrderStatus) => Order[]
  getOrdersByPaymentStatus: (paymentStatus: PaymentStatus) => Order[]
  getAvailableStatuses: () => OrderStatus[]
  getAvailablePaymentStatuses: () => PaymentStatus[]
  getAvailablePaymentMethods: () => PaymentMethod[]
  calculateOrderTotals: (items: OrderItem[], taxRate?: number, shippingCost?: number, discountAmount?: number) => {
    subtotal: number
    tax: number
    shipping: number
    discount: number
    total: number
  }
  canCancelOrder: (order: Order) => boolean
  canReturnOrder: (order: Order) => boolean
  getOrderStatusColor: (status: OrderStatus) => string
  getPaymentStatusColor: (status: PaymentStatus) => string
  formatOrderTotal: (total: number) => string
  formatOrderNumber: (orderNumber: string) => string
}

// ============================================================================
// Orders Store Implementation
// ============================================================================

export const useOrdersStore = create<OrdersState>()(
  immer((set, get) => ({
    // Initial state
    orders: [],
    selectedOrder: null,
    isLoading: false,
    error: null,
    
    // Pagination & Filtering
    currentPage: 1,
    totalPages: 0,
    totalOrders: 0,
    pageSize: 20,
    filters: {},
    searchQuery: '',
    
    // Order Status Groups
    pendingOrders: [],
    processingOrders: [],
    shippedOrders: [],
    deliveredOrders: [],
    cancelledOrders: [],
    
    // Recent Orders
    recentOrders: [],

    // Actions - Orders CRUD
    fetchOrders: async (pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await orderService.getOrders(pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.orders = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalOrders = response.pagination?.total || response.data!.length
            state.filters = filters
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch orders'
          })

          return { success: false, error: response.error || 'Failed to fetch orders' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchOrderById: async (orderId: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await orderService.getOrderById(orderId)

        if (response.success && response.data) {
          set((state) => {
            state.selectedOrder = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch order'
          })

          return { success: false, error: response.error || 'Failed to fetch order' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    createOrder: async (orderData: CreateOrderData) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        // Validate order data
        const validation = orderService.validateOrderData(orderData)
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => e.message).join(', ')
          set((state) => {
            state.isLoading = false
            state.error = errorMessage
          })
          return { success: false, error: errorMessage }
        }

        const response = await orderService.createOrder(orderData)

        if (response.success && response.data) {
          set((state) => {
            state.orders.unshift(response.data!)
            state.totalOrders += 1
            state.isLoading = false
            state.error = null
          })

          // Update status groups and recent orders
          get().updateStatusGroups()

          return { success: true, order: response.data }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to create order'
          })

          return { success: false, error: response.error || 'Failed to create order' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    updateOrder: async (orderId: string, updates: UpdateOrderData) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await orderService.updateOrder(orderId, updates)

        if (response.success && response.data) {
          set((state) => {
            const index = state.orders.findIndex(o => o._id === orderId)
            if (index !== -1) {
              state.orders[index] = response.data!
            }
            
            if (state.selectedOrder?._id === orderId) {
              state.selectedOrder = response.data!
            }
            
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to update order'
          })

          return { success: false, error: response.error || 'Failed to update order' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    cancelOrder: async (orderId: string, reason?: string) => {
      try {
        const response = await orderService.cancelOrder(orderId, reason)

        if (response.success && response.data) {
          set((state) => {
            const index = state.orders.findIndex(o => o._id === orderId)
            if (index !== -1) {
              state.orders[index] = response.data!
            }
            
            if (state.selectedOrder?._id === orderId) {
              state.selectedOrder = response.data!
            }
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to cancel order' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Order Status Management
    updateOrderStatus: async (orderId: string, status: OrderStatus) => {
      try {
        const response = await orderService.updateOrderStatus(orderId, status)

        if (response.success && response.data) {
          set((state) => {
            const index = state.orders.findIndex(o => o._id === orderId)
            if (index !== -1) {
              state.orders[index] = response.data!
            }
            
            if (state.selectedOrder?._id === orderId) {
              state.selectedOrder = response.data!
            }
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update order status' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    updatePaymentStatus: async (orderId: string, paymentStatus: PaymentStatus) => {
      try {
        const response = await orderService.updatePaymentStatus(orderId, paymentStatus)

        if (response.success && response.data) {
          set((state) => {
            const index = state.orders.findIndex(o => o._id === orderId)
            if (index !== -1) {
              state.orders[index] = response.data!
            }
            
            if (state.selectedOrder?._id === orderId) {
              state.selectedOrder = response.data!
            }
          })

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to update payment status' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    addTrackingNumber: async (orderId: string, trackingNumber: string) => {
      try {
        const response = await orderService.addTrackingNumber(orderId, trackingNumber)

        if (response.success && response.data) {
          set((state) => {
            const index = state.orders.findIndex(o => o._id === orderId)
            if (index !== -1) {
              state.orders[index] = response.data!
            }
            
            if (state.selectedOrder?._id === orderId) {
              state.selectedOrder = response.data!
            }
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          return { success: false, error: response.error || 'Failed to add tracking number' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        return { success: false, error: errorInfo.message }
      }
    },

    // Helper method to update status groups
    updateStatusGroups: () => {
      const state = get()
      const orders = state.orders

      set((draft) => {
        draft.pendingOrders = orders.filter(o => o.status === 'Pending')
        draft.processingOrders = orders.filter(o => o.status === 'Processing')
        draft.shippedOrders = orders.filter(o => o.status === 'Shipped')
        draft.deliveredOrders = orders.filter(o => o.status === 'Delivered')
        draft.cancelledOrders = orders.filter(o => o.status === 'Cancelled')
      })
    },

    // Actions - Order Queries
    fetchOrdersByCustomer: async (customerId: string, pagination = { page: 1, limit: 10 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await orderService.getOrdersByCustomer(customerId, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.orders = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalOrders = response.pagination?.total || response.data!.length
            state.filters = { ...state.filters, customerId }
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch customer orders'
          })

          return { success: false, error: response.error || 'Failed to fetch customer orders' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchOrdersByBranch: async (branchId: string, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await orderService.getOrdersByBranch(branchId, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.orders = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalOrders = response.pagination?.total || response.data!.length
            state.filters = { ...state.filters, branchId }
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch branch orders'
          })

          return { success: false, error: response.error || 'Failed to fetch branch orders' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchOrdersByStatus: async (status: OrderStatus, pagination = { page: 1, limit: 20 }) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await orderService.getOrdersByStatus(status, pagination)

        if (response.success && response.data) {
          set((state) => {
            state.orders = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalOrders = response.pagination?.total || response.data!.length
            state.filters = { ...state.filters, status }
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch orders by status'
          })

          return { success: false, error: response.error || 'Failed to fetch orders by status' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    fetchRecentOrders: async (limit = 10, branchId?: string) => {
      set((state) => {
        state.isLoading = true
        state.error = null
      })

      try {
        const response = await orderService.getRecentOrders(limit, branchId)

        if (response.success && response.data) {
          set((state) => {
            state.recentOrders = response.data!
            state.isLoading = false
            state.error = null
          })

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to fetch recent orders'
          })

          return { success: false, error: response.error || 'Failed to fetch recent orders' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    // Actions - Search & Filtering
    searchOrders: async (query: string, pagination = { page: 1, limit: 20 }, filters = {}) => {
      set((state) => {
        state.isLoading = true
        state.error = null
        state.searchQuery = query
      })

      try {
        const response = await orderService.searchOrders(query, pagination, filters)

        if (response.success && response.data) {
          set((state) => {
            state.orders = response.data!
            state.currentPage = pagination.page
            state.pageSize = pagination.limit
            state.totalPages = response.pagination?.totalPages || 1
            state.totalOrders = response.pagination?.total || response.data!.length
            state.filters = { ...filters, search: query }
            state.isLoading = false
            state.error = null
          })

          // Update status groups
          get().updateStatusGroups()

          return { success: true }
        } else {
          set((state) => {
            state.isLoading = false
            state.error = response.error || 'Failed to search orders'
          })

          return { success: false, error: response.error || 'Failed to search orders' }
        }
      } catch (error) {
        const errorInfo = handleServiceError(error)
        
        set((state) => {
          state.isLoading = false
          state.error = errorInfo.message
        })

        return { success: false, error: errorInfo.message }
      }
    },

    setFilters: (filters: Partial<OrderFilters>) => {
      set((state) => {
        state.filters = { ...state.filters, ...filters }
      })
    },

    clearFilters: () => {
      set((state) => {
        state.filters = {}
        state.searchQuery = ''
      })
    },

    setSearchQuery: (query: string) => {
      set((state) => {
        state.searchQuery = query
      })
    },

    // Actions - Pagination
    setCurrentPage: (page: number) => {
      set((state) => {
        state.currentPage = page
      })
    },

    setPageSize: (size: number) => {
      set((state) => {
        state.pageSize = size
      })
    },

    // Actions - UI State
    setSelectedOrder: (order: Order | null) => {
      set((state) => {
        state.selectedOrder = order
      })
    },

    clearError: () => {
      set((state) => {
        state.error = null
      })
    },

    setLoading: (loading: boolean) => {
      set((state) => {
        state.isLoading = loading
      })
    },

    // Utility Methods
    getOrdersByCustomer: (customerId: string) => {
      return get().orders.filter(order => order.customerId === customerId)
    },

    getOrdersByBranch: (branchId: string) => {
      return get().orders.filter(order => order.branchId === branchId)
    },

    getOrdersByStatus: (status: OrderStatus) => {
      return get().orders.filter(order => order.status === status)
    },

    getOrdersByPaymentStatus: (paymentStatus: PaymentStatus) => {
      return get().orders.filter(order => order.paymentStatus === paymentStatus)
    },

    getAvailableStatuses: () => {
      return orderService.getOrderStatuses()
    },

    getAvailablePaymentStatuses: () => {
      return orderService.getPaymentStatuses()
    },

    getAvailablePaymentMethods: () => {
      return orderService.getPaymentMethods()
    },

    calculateOrderTotals: (items: OrderItem[], taxRate = 0.18, shippingCost = 0, discountAmount = 0) => {
      return orderService.calculateOrderTotals(items, taxRate, shippingCost, discountAmount)
    },

    canCancelOrder: (order: Order) => {
      return orderService.canCancelOrder(order)
    },

    canReturnOrder: (order: Order) => {
      return orderService.canReturnOrder(order)
    },

    getOrderStatusColor: (status: OrderStatus) => {
      return orderService.getOrderStatusColor(status)
    },

    getPaymentStatusColor: (status: PaymentStatus) => {
      return orderService.getPaymentStatusColor(status)
    },

    formatOrderTotal: (total: number) => {
      return orderService.formatOrderTotal(total)
    },

    formatOrderNumber: (orderNumber: string) => {
      return orderService.formatOrderNumber(orderNumber)
    }
  }))
)
