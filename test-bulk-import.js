const fs = require('fs');

async function testBulkImport() {
  try {
    // Read the CSV file
    const csvContent = fs.readFileSync('./test-inventory-5-items.csv', 'utf8');
    console.log('📄 CSV Content:');
    console.log(csvContent);
    console.log('\n' + '='.repeat(50));

    // First, let's test the login to get a valid token
    console.log('🔐 Testing authentication...');

    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '@Admin2020'
      })
    });

    const loginResult = await loginResponse.json();

    if (!loginResult.success) {
      console.log('❌ Login failed:', loginResult.error);
      return;
    }

    const authToken = loginResult.data.tokens.accessToken;
    console.log('✅ Login successful! Token obtained.');
    console.log('👤 User:', loginResult.data.user.name, `(${loginResult.data.user.role})`);

    // Create form data for file upload
    console.log('\n📤 Preparing file upload...');

    // Create a proper file buffer
    const fileBuffer = Buffer.from(csvContent, 'utf8');

    // Create form data manually
    const boundary = '----formdata-boundary-' + Math.random().toString(36);
    const formData = [
      `--${boundary}`,
      'Content-Disposition: form-data; name="file"; filename="test-inventory-5-items.csv"',
      'Content-Type: text/csv',
      '',
      csvContent,
      `--${boundary}--`
    ].join('\r\n');

    console.log('📊 Sending bulk import request...');

    // Make the API request
    const response = await fetch('http://localhost:3001/api/inventory/bulk-import', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': `multipart/form-data; boundary=${boundary}`
      },
      body: formData
    });

    const result = await response.json();

    console.log('\n📋 Response Status:', response.status);
    console.log('📋 Response Body:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('\n🎉 BULK IMPORT SUCCESSFUL!');
      console.log(`📊 Total items processed: ${result.data.total}`);
      console.log(`✅ Successfully imported: ${result.data.imported}`);
      console.log(`❌ Failed imports: ${result.data.failed}`);

      if (result.data.errors && result.data.errors.length > 0) {
        console.log('\n⚠️  Import Errors:');
        result.data.errors.forEach(error => {
          console.log(`  📍 Row ${error.row}: ${error.message} (Field: ${error.field})`);
        });
      } else {
        console.log('✨ No errors - all items imported successfully!');
      }
    } else {
      console.log('\n❌ BULK IMPORT FAILED:', result.error);
    }

  } catch (error) {
    console.error('💥 Error testing bulk import:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Add fetch polyfill for Node.js
if (typeof fetch === 'undefined') {
  global.fetch = require('node-fetch');
}

// Run the test
console.log('🚀 Starting Bulk Import Test...');
console.log('=' .repeat(50));
testBulkImport();
